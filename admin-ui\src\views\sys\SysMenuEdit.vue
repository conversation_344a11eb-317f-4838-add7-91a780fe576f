<template>
  <el-dialog :title="menuForm.id ? '编辑菜单' : '添加菜单'" :visible.sync="visible" width="600px" top="10vh" @close="closeDialog">
    <el-form :model="menuForm" :rules="menuRules" ref="menuForm" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="菜单名称" prop="menuName">
            <el-input v-model="menuForm.menuName" placeholder="请输入菜单名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="菜单类型" prop="menu">
            <el-select v-model="menuForm.menu" placeholder="请选择菜单类型" @change="handleTypeChange">
              <el-option :label="'菜单'" :value="true"></el-option>
              <el-option :label="'按钮'" :value="false"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上级菜单" prop="parentId">
            <el-cascader v-model="selectedParentId" :options="menuOptions" :props="cascaderProps"
              placeholder="请选择上级菜单" clearable @change="handleParentChange">
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortNum">
            <el-input-number v-model="menuForm.sortNum" :min="0" placeholder="排序"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="menuForm.menu === true">
        <el-col :span="12">
          <el-form-item label="路由" prop="routePath">
            <el-input v-model="menuForm.routePath" placeholder="请输入路由路径"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="组件路径">
            <el-input v-model="menuForm.component" placeholder="请输入组件路径"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="菜单图标" prop="icon">
            <el-input v-model="menuForm.icon" placeholder="请输入图标类名">
              <template slot="prepend">
                <i :class="menuForm.icon || 'el-icon-menu'"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="enable">
            <el-switch v-model="menuForm.enable" :active-value="true" :inactive-value="false" active-text="启用"
              inactive-text="禁用">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="权限标识" prop="permission" v-if="menuForm.menu === false">
        <el-input v-model="menuForm.permission" placeholder="请输入权限标识，如：user:add"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="saveMenu">确 定</el-button>
      <el-button @click="closeDialog">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { sysMenuUpdate, sysMenuInsert } from '@/api/SysMenu.js'

export default {
  name: 'SysMenuEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    menu: {
      type: Object,
      default: () => ({})
    },
    menuData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 级联选择器配置
      cascaderProps: {
        value: 'id',
        label: 'menuName',
        children: 'children',
        checkStrictly: true
      },

      // 选中的父菜单ID（级联选择器使用）
      selectedParentId: null,

      // 表单数据 - 使用与数据库字段一致的名称
      menuForm: {
        id: null,
        menuName: '',
        menu: true, // true表示菜单，false表示按钮 (对应数据库 is_menu)
        parentId: '0', // 默认为顶级菜单
        routePath: '',
        component: '',
        icon: '',
        sortNum: 0,
        enable: true,
        permission: ''
      },

      // 验证规则
      menuRules: {
        menuName: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' }
        ],
        menu: [
          { required: true, message: '请选择菜单类型', trigger: 'change' }
        ],
        sortNum: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    menuOptions() {
      // 构建级联选择器的菜单选项
      const options = [
        {
          id: '0',
          menuName: '顶级菜单',
          children: []
        }
      ]
      // 将menuData转换为适用于级联选择器的格式
      options[0].children = this.buildMenuOptions(this.menuData)
      return options
    }
  },
  watch: {
    menu: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          // 编辑模式，直接使用后端返回的数据结构
          this.menuForm = {
            id: val.id,
            menuName: val.menuName || val.name, // 兼容可能的字段名差异
            menu: val.menu !== undefined ? val.menu : (val.type === 'menu'), // 兼容字段名差异
            parentId: val.parentId || '0',
            routePath: val.routePath || val.path || '', // 兼容字段名差异
            component: val.component || '',
            icon: val.icon || '',
            sortNum: val.sortNum !== undefined ? val.sortNum : (val.sort || 0), // 兼容字段名差异
            enable: val.enable !== undefined ? val.enable : (val.status === 1), // 兼容字段名差异
            permission: val.permission || ''
          }

          // 设置级联选择器的值
          if (val.parentId && val.parentId !== '0') {
            this.selectedParentId = [val.parentId]
          } else {
            this.selectedParentId = ['0'] // 顶级菜单
          }
        } else {
          // 新增模式，重置表单
          this.resetMenuForm()
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 构建菜单选项
    buildMenuOptions(data) {
      return data.filter(item => {
        // 只显示菜单类型的项，不显示按钮
        return item.type === 'menu' || item.menu === true
      }).map(item => ({
        id: item.id,
        menuName: item.menuName || item.name,
        children: item.children ? this.buildMenuOptions(item.children) : []
      }))
    },

    // 处理父菜单变更
    handleParentChange(value) {
      // 当用户选择父菜单时，更新表单的parentId
      if (value && value.length > 0) {
        this.menuForm.parentId = value[value.length - 1]
      } else {
        this.menuForm.parentId = '0' // 默认为顶级菜单
      }
    },

    // 保存菜单
    saveMenu() {
      this.$refs.menuForm.validate((valid) => {
        if (valid) {
          // 准备提交到后端的数据，使用数据库字段名
          const formData = {
            id: this.menuForm.id,
            menuName: this.menuForm.menuName,
            parentId: this.menuForm.parentId || '0',
            menu: this.menuForm.menu,
            routePath: this.menuForm.routePath || '',
            permission: this.menuForm.permission || '',
            sortNum: this.menuForm.sortNum,
            enable: this.menuForm.enable,
            icon: this.menuForm.icon || ''
          }

          // 根据是否有ID决定是新增还是更新
          if (this.menuForm.id) {
            // 更新菜单
            sysMenuUpdate(formData).then(res => {
              if (res.success) {
                this.$message.success('菜单更新成功')
                this.$emit('save', formData)
                this.closeDialog()
                // 通知父组件刷新数据
                this.$emit('refresh')
              } else {
                this.$message.error(res.message || '菜单更新失败')
              }
            }).catch(err => {
              console.error('菜单更新错误:', err)
              this.$message.error('菜单更新失败')
            })
          } else {
            // 新增菜单
            sysMenuInsert(formData).then(res => {
              if (res.success) {
                this.$message.success('菜单添加成功')
                this.$emit('save', formData)
                this.closeDialog()
                // 通知父组件刷新数据
                this.$emit('refresh')
              } else {
                this.$message.error(res.message || '菜单添加失败')
              }
            }).catch(err => {
              console.error('菜单添加错误:', err)
              this.$message.error('菜单添加失败')
            })
          }
        }
      })
    },

    // 重置表单
    resetMenuForm() {
      this.menuForm = {
        id: null,
        menuName: '',
        menu: true,
        parentId: '0',
        routePath: '',
        component: '',
        icon: '',
        sortNum: 0,
        enable: true,
        permission: ''
      }
      this.selectedParentId = ['0']
    },

    // 处理类型变化
    handleTypeChange() {
      if (this.menuForm.menu === false) {
        // 按钮类型，清空路由相关字段
        this.menuForm.routePath = ''
        this.menuForm.component = ''
      } else {
        // 菜单类型，清空权限字段
        this.menuForm.permission = ''
      }
    },

    // 关闭对话框
    closeDialog() {
      this.$refs.menuForm && this.$refs.menuForm.resetFields()
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
/* 对话框样式 */
.dialog-footer {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

/* 表单样式优化 */
.el-form-item__content {
  display: flex;
  align-items: center;
}

.el-cascader {
  width: 100%;
}

.el-input-number {
  width: 100%;
}
</style>
