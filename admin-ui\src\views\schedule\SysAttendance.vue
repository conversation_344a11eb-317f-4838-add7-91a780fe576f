<template>
  <div class="admin-attendance-container">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">排班与考勤</el-breadcrumb-item>
      <el-breadcrumb-item>考勤管理</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="main-content">
      <!-- 左侧员工列表 -->
      <div class="left-panel">
        <div class="employee-section">
          <div class="section-header">
            <h3>客服列表</h3>
            <el-select
              v-model="roleFilter"
              placeholder="全部角色"
              size="small"
              clearable
              style="width: 120px;">
              <el-option label="全部角色" value=""></el-option>
              <el-option label="客服" value="客服"></el-option>
              <el-option label="客服主管" value="客服主管"></el-option>
            </el-select>
          </div>
          <!-- 添加筛选框 -->
          <div class="filter-box">
            <el-input
              v-model="employeeFilter"
              placeholder="搜索用户..."
              prefix-icon="el-icon-search"
              size="small"
              clearable
              style="margin-bottom: 15px;">
            </el-input>
          </div>
          <div class="employee-list">
            <div
              v-for="employee in filteredEmployeeList"
              :key="employee.id"
              :class="['employee-item', { active: isEmployeeSelected(employee) }]"
              @click="selectEmployee(employee)">
              <div class="employee-avatar">
                <i class="el-icon-user"></i>
              </div>
              <div class="employee-info">
                <div class="employee-name">{{ employee.name }}</div>
                <div class="employee-dept">{{ employee.roleName }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间排班列表 -->
      <div class="middle-panel">
        <div v-if="selectedEmployee" class="attendance-records-section">
          <h3 style="text-align: center;">{{ selectedEmployee.name }} {{ currentMonth }}月排班</h3>

          <div class="month-stats">
              <div v-if="selectedEmployee && selectedEmployee.loading" class="loading-stats">
                <el-icon class="is-loading"><i class="el-icon-loading"></i></el-icon>
                <span>加载中...</span>
              </div>
              <template v-else>
                <div class="stat-card">
                  <div class="stat-number">{{ getMonthlyStats().workDays }}</div>
                  <div class="stat-label">应出勤</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number">{{ getMonthlyStats().actualDays }}</div>
                  <div class="stat-label">实际出勤</div>
                </div>
                <div class="stat-card">
                  <div class="stat-number">{{ getMonthlyStats().absentDays }}</div>
                  <div class="stat-label">缺勤</div>
                </div>
              </template>
            </div>

          <!-- 每日考勤记录列表 - 一行一个 -->
          <div class="daily-records-list">
            <div
              v-for="record in getDailyAttendanceRecords()"
              :key="record.date"
              :class="['record-row', record.status]">

              <!-- 左侧：日期和状态 -->
              <div class="record-left">
                <div class="record-date">{{ formatDate(record.date) }}</div>
                <div class="record-status">
                  <span v-if="record.status === 'present'" class="status-present">
                    <i class="el-icon-circle-check"></i>
                    <span class="status-text">已打卡</span>
                  </span>
                  <span v-else-if="record.status === 'absent'" class="status-absent">
                    <i class="el-icon-circle-close"></i>
                    <span class="status-text">缺勤</span>
                  </span>
                  <span v-else-if="record.isToday" class="status-today">
                    <i class="el-icon-time"></i>
                    <span class="status-text">今天</span>
                  </span>
                  <span v-else class="status-none">
                    <i class="el-icon-minus"></i>
                    <span class="status-text">待打卡</span>
                  </span>
                </div>
              </div>

              <!-- 右侧：排班店铺列表 -->
              <div class="record-right">
                <div class="schedule-shops">
                  <div v-if="record.scheduleData && record.scheduleData.length > 0">
                    <div v-for="shop in record.scheduleData" :key="shop.shopId" class="shop-item">
                      {{ shop.shopName }}
                    </div>
                  </div>
                  <div v-else class="no-schedule">无排班</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-selection">
          <i class="el-icon-user"></i>
          <p>请选择员工查看考勤记录</p>
        </div>
      </div>

      <!-- 右侧详情区域 -->
      <div class="right-panel">
        <div v-if="selectedEmployee" class="detail-section">
        <!-- 状态说明图例 -->
        <div class="status-legend">
            <div class="legend-item">
              <span class="legend-icon status-present">
                <i class="el-icon-circle-check"></i>
              </span>
              <span class="legend-text">已打卡</span>
            </div>
            <div class="legend-number">
              <span class="number-value number-present">{{ getMonthlyStats().actualDays }}</span>
            </div>
            <div class="legend-item" style="margin-left: 100px;">
              <span class="legend-icon status-absent">
                <i class="el-icon-circle-close"></i>
              </span>
              <span class="legend-text">缺勤</span>
            </div>
            <div class="legend-number">
              <span class="number-value number-absent">{{ getMonthlyStats().absentDays }}</span>
            </div>
          </div>

          <!-- 考勤日历 -->
          <div class="calendar-section">
            <el-calendar
              v-model="calendarDate"
              @pick="onCalendarDateChange"
              :key="calendarKey"
            >
              <template slot="dateCell" slot-scope="{date, data}">
                <div class="custom-calendar-cell">
                  <div class="cell-date">{{ data.day.split('-').slice(2).join('-') }}</div>
                  <div class="cell-status">
                    <span v-if="getEmployeeAttendanceStatus(data.day) === 'present'" class="status-present">
                      <i class="el-icon-circle-check"></i>
                    </span>
                    <span v-else-if="getEmployeeAttendanceStatus(data.day) === 'absent'" class="status-absent">
                      <i class="el-icon-circle-close"></i>
                    </span>
                    <span v-else-if="data.day === getCurrentDate()" class="status-today">
                      <i class="el-icon-time"></i>
                    </span>
                  </div>
                  <!-- 排班圆点 -->
                  <div v-if="hasSchedule(data.day)" class="schedule-dot"></div>
                </div>
              </template>
            </el-calendar>
          </div>
        </div>

        <div v-else class="no-selection">
          <i class="el-icon-user"></i>
          <p>请选择员工查看考勤详情</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllStaffUsers, getAllSupervisorUsers } from '@/api/SysUser'
import { getMonthlyAttendanceData } from '@/api/SysShiftAttendance'
import { monthShift } from '@/api/SysShift'

export default {
  name: 'SysAttendance',
  data() {
    return {
      selectedEmployee: null,
      calendarDate: new Date(),
      employeeList: [],
      // 员工考勤数据
      employeeAttendanceData: {},
      // 员工排班数据
      employeeScheduleData: {},
      employeeFilter: '',
      roleFilter: '', // 角色过滤器
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      calendarKey: 0 // 用于强制重新渲染日历
    }
  },
  computed: {
    filteredEmployeeList() {
      let filtered = this.employeeList

      // 按角色过滤
      if (this.roleFilter) {
        filtered = filtered.filter(employee => employee.roleName === this.roleFilter)
      }

      // 按搜索关键字过滤
      if (this.employeeFilter) {
        filtered = filtered.filter(employee =>
          employee.name.toLowerCase().includes(this.employeeFilter.toLowerCase()) ||
          employee.roleName.toLowerCase().includes(this.employeeFilter.toLowerCase())
        )
      }

      return filtered
    }
  },
  created() {
    this.loadEmployeeList()
  },
  watch: {
    roleFilter() {
      // 当角色过滤器变化时，检查当前选中的员工是否仍在过滤结果中
      if (this.selectedEmployee) {
        const isStillVisible = this.filteredEmployeeList.some(emp =>
          this.isEmployeeSelected(emp)
        )

        // 如果当前选中的员工不在过滤结果中，清除选中状态
        if (!isStillVisible) {
          this.selectedEmployee = null
        }
      }
    },

    // 监听日历日期变化（包括翻页）
    calendarDate: {
      handler(newDate, oldDate) {
        if (oldDate) {
          // 检查年月是否发生变化
          const oldYear = oldDate.getFullYear()
          const oldMonth = oldDate.getMonth() + 1
          const newYear = newDate.getFullYear()
          const newMonth = newDate.getMonth() + 1

          if (oldYear !== newYear || oldMonth !== newMonth) {
            console.log(`日历月份变化: ${oldYear}-${oldMonth} -> ${newYear}-${newMonth}`)
            this.handleMonthChange(newYear, newMonth)
          }
        }
      },
      immediate: false
    }
  },
  methods: {
        // 检查员工是否被选中 - 解决ID数据类型匹配问题
    isEmployeeSelected(employee) {
      if (!this.selectedEmployee) return false

      // 获取所有可能的ID字段进行比较
      const employeeId = employee.id || employee.userId || employee.sysUserId || employee.employeeId
      const selectedId = this.selectedEmployee.id || this.selectedEmployee.userId || this.selectedEmployee.sysUserId || this.selectedEmployee.employeeId

      // 转换为字符串进行比较，避免数据类型不匹配问题
      const isSelected = String(employeeId) === String(selectedId)

      // 添加调试信息（可在生产环境中移除）
      // console.log('员工选择状态检查:', {
      //   employeeName: employee.name,
      //   employeeId: employeeId,
      //   selectedName: this.selectedEmployee.name,
      //   selectedId: selectedId,
      //   isSelected: isSelected
      // })

      return isSelected
    },

    async loadEmployeeList() {
      try {
        // 根据记忆中的角色映射关系：21-客服，22-客服主管
        const [staffRes, supervisorRes] = await Promise.all([
          getAllStaffUsers(),
          getAllSupervisorUsers()
        ])

        const allEmployees = []

        // 处理客服人员 - 分页结果在data.records中
        if (staffRes.success && staffRes.data && staffRes.data.records) {
          const staffUsers = staffRes.data.records.map(user => ({
            ...user,
            name: user.name || user.userName || user.realName || user.nickName || user.username || `用户${user.id || user.userId}`,
            roleName: '客服'
          }))
          allEmployees.push(...staffUsers)
          console.log('客服用户数据结构:', staffUsers.length > 0 ? staffUsers[0] : 'empty')
        }

        // 处理客服主管 - 分页结果在data.records中
        if (supervisorRes.success && supervisorRes.data && supervisorRes.data.records) {
          const supervisorUsers = supervisorRes.data.records.map(user => ({
            ...user,
            name: user.name || user.userName || user.realName || user.nickName || user.username || `用户${user.id || user.userId}`,
            roleName: '客服主管'
          }))
          allEmployees.push(...supervisorUsers)
          console.log('客服主管用户数据结构:', supervisorUsers.length > 0 ? supervisorUsers[0] : 'empty')
        }

        this.employeeList = allEmployees
        console.log('所有员工列表:', allEmployees)

        // 修复：不自动选择第一个员工，让用户手动选择
        // if (this.employeeList.length > 0) {
        //   this.selectEmployee(this.employeeList[0])
        // }
      } catch (error) {
        console.error('加载员工列表失败:', error)
        this.$message.error('加载员工列表失败')
      }
    },

    async selectEmployee(employee) {
      console.log('选中的员工对象:', employee)
      console.log('员工ID字段:', employee.id, '员工其他可能的ID字段:', {
        id: employee.id,
        userId: employee.userId,
        sysUserId: employee.sysUserId,
        employeeId: employee.employeeId
      })

      // 尝试不同的ID字段，并在employee对象上标准化为realUserId
      const userId = employee.id || employee.userId || employee.sysUserId || employee.employeeId
      console.log('实际使用的userId:', userId)

      if (userId) {
        // 立即清空当前选择，避免显示错误数据和错误的高亮状态
        this.selectedEmployee = null

        // 强制更新视图，确保高亮状态被清除
        this.$nextTick(() => {
          console.log('清空选择后的状态:', this.selectedEmployee)
        })

        // 检查是否已有缓存数据，如果没有则先加载
        if (!this.employeeAttendanceData[userId] || !this.employeeScheduleData[userId]) {
          // 显示加载状态
          this.selectedEmployee = { ...employee, realUserId: userId, loading: true }

          // 并行加载数据
          await Promise.all([
            this.loadEmployeeAttendanceData(userId),
            this.loadEmployeeScheduleData(userId)
          ])
        }

        // 数据加载完成后设置选中员工
        this.selectedEmployee = { ...employee, realUserId: userId, loading: false }

        // 强制重新渲染日历
        this.calendarKey++

        // 强制更新视图，确保日历重新渲染
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      } else {
        console.error('无法找到用户ID字段')
        this.$message.error('无法获取用户ID')
      }
    },

    async loadEmployeeAttendanceData(userId) {
      try {
        const res = await getMonthlyAttendanceData({
          userId: userId,
          year: this.currentYear,
          month: this.currentMonth
        })

        if (res.success && res.data) {
          this.employeeAttendanceData[userId] = {
            attendanceData: res.data.attendanceData || {},
            stats: {
              scheduledDays: res.data.scheduledDays || 0,
              checkedDays: res.data.checkedDays || 0,
              absentDays: res.data.absentDays || 0
            }
          }
        }
      } catch (error) {
        console.error('加载考勤数据失败:', error)
      }
    },

    async loadEmployeeScheduleData(userId) {
      try {
        const res = await monthShift({
          userId: userId,
          year: this.currentYear,
          month: this.currentMonth
        })

        if (res.success && res.data) {
          // 处理后端返回的scheduleData
          const scheduleData = res.data.scheduleData || {}
          this.employeeScheduleData[userId] = scheduleData
        }
      } catch (error) {
        console.error('加载排班数据失败:', error)
      }
    },

    getCurrentDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    getEmployeeAttendanceStatus(dateStr) {
      if (!this.selectedEmployee) {
        return 'none'
      }
      const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
      const employeeData = this.employeeAttendanceData[userId]
      const status = employeeData && employeeData.attendanceData ? employeeData.attendanceData[dateStr] || 'none' : 'none'



      // 将后端状态映射为前端状态
      if (status === 'CHECKED') return 'present'
      if (status === 'ABSENT') return 'absent'
      return 'none'
    },

    // 检查是否有排班
    hasSchedule(dateStr) {
      if (!this.selectedEmployee) {
        return false
      }
      const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
      const scheduleData = this.employeeScheduleData[userId]



      // 如果有排班数据，检查该日期是否有排班
      if (scheduleData && scheduleData[dateStr]) {
        return scheduleData[dateStr].length > 0
      }

      // 如果没有专门的排班数据，从考勤数据推断：如果有考勤记录（checked、absent），说明有排班
      const employeeData = this.employeeAttendanceData[userId]
      if (employeeData && employeeData.attendanceData) {
        const attendanceStatus = employeeData.attendanceData[dateStr]
        return attendanceStatus && attendanceStatus !== 'none'
      }

      return false
    },

    // 处理月份变化
    async handleMonthChange(newYear, newMonth) {
      console.log(`处理月份变化: ${this.currentYear}-${this.currentMonth} -> ${newYear}-${newMonth}`)
      console.log(`当前选中员工:`, this.selectedEmployee)

      // 更新当前年月
      this.currentYear = newYear
      this.currentMonth = newMonth

      // 如果有选中的员工，重新加载该员工的数据
      if (this.selectedEmployee) {
        const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
        if (userId) {
          // 清空缓存的数据，强制重新加载
          delete this.employeeAttendanceData[userId]
          delete this.employeeScheduleData[userId]

          // 显示加载状态
          this.selectedEmployee.loading = true

          try {
            // 重新加载数据
            await Promise.all([
              this.loadEmployeeAttendanceData(userId),
              this.loadEmployeeScheduleData(userId)
            ])
          } catch (error) {
            console.error('重新加载数据失败:', error)
          } finally {
            // 隐藏加载状态
            this.selectedEmployee.loading = false

            // 强制重新渲染日历
            this.calendarKey++
          }
        }
      }
    },

    async onCalendarDateChange(date) {
      this.calendarDate = date

      // 检查年月是否发生变化
      const newYear = date.getFullYear()
      const newMonth = date.getMonth() + 1

      if (newYear !== this.currentYear || newMonth !== this.currentMonth) {
        await this.handleMonthChange(newYear, newMonth)
      }
    },

    getDailyAttendanceRecords() {
      if (!this.selectedEmployee) return []

      const currentDateStr = this.getCurrentDate()

      const records = []
      const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
      const employeeAttendanceData = this.employeeAttendanceData[userId]
      const attendanceData = employeeAttendanceData && employeeAttendanceData.attendanceData ? employeeAttendanceData.attendanceData : {}
      const scheduleData = this.employeeScheduleData[userId] || {}

      // 只显示有排班的日期
      const allDates = new Set()

      // 添加所有有排班的日期（包括过去和未来日期）
      Object.keys(scheduleData).forEach(dateStr => {
        if (scheduleData[dateStr] && scheduleData[dateStr].length > 0) {
          allDates.add(dateStr)
        }
      })

      // 如果没有排班数据，但有考勤数据，也添加进来（说明有排班但排班数据可能没有返回）
      Object.keys(attendanceData).forEach(dateStr => {
        if (attendanceData[dateStr] && attendanceData[dateStr] !== 'none') {
          allDates.add(dateStr)
        }
      })

      // 转换为数组并排序（最新的在前）
      const sortedDates = Array.from(allDates).sort((a, b) => new Date(b) - new Date(a))

      sortedDates.forEach(dateStr => {
        const backendStatus = attendanceData[dateStr] || 'none'

        // 将后端状态映射为前端状态
        let status = 'none'
        if (backendStatus === 'CHECKED') status = 'present'
        else if (backendStatus === 'ABSENT') status = 'absent'

        const record = {
          date: dateStr,
          status: status,
          isToday: dateStr === currentDateStr,
          scheduleData: scheduleData[dateStr] || []
        }

        records.push(record)
      })

      return records
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    },

    getMonthlyStats() {
      if (!this.selectedEmployee) return { workDays: 0, actualDays: 0, absentDays: 0 }

      const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
      const employeeData = this.employeeAttendanceData[userId]

      // 优先使用后端统计的数据
      if (employeeData && employeeData.stats) {
        return {
          workDays: employeeData.stats.scheduledDays,
          actualDays: employeeData.stats.checkedDays,
          absentDays: employeeData.stats.absentDays
        }
      }

      // 如果没有后端统计数据，则基于排班记录计算（包含整个月）
      const scheduleData = this.employeeScheduleData[userId] || {}
      const attendanceData = employeeData ? employeeData.attendanceData : {}

      let workDays = 0
      let actualDays = 0
      let absentDays = 0

      console.log('排班数据:', scheduleData)
      console.log('考勤数据:', attendanceData)

      // 计算基于排班记录的应勤天数（包含所有排班日期，不仅仅是已过去的日期）
      Object.keys(scheduleData).forEach(dateStr => {
        const schedules = scheduleData[dateStr]
        if (schedules && schedules.length > 0) {
          workDays++

          const backendStatus = attendanceData[dateStr]
          if (backendStatus === 'checked') {
            actualDays++
          } else if (backendStatus === 'absent') {
            absentDays++
          }
        }
      })

      console.log('统计结果:', { workDays, actualDays, absentDays })

      return { workDays, actualDays, absentDays }
    }
  }
}
</script>

<style scoped>
/* 修复样式问题 */
.record-date {
  font-weight: bold !important;
  color: #333 !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
  margin-top: 4px !important;
}

/* 修改高度对齐问题 */
.main-content {
  margin-top: 20px !important;
  padding-top: 20px !important;
  display: flex !important;
  gap: 20px !important;
  max-width: 1400px !important;
  margin: 0 auto !important;
  height: calc(100vh - 160px) !important;
  align-items: stretch !important; /* 让所有子元素高度一致 */
}

.left-panel {
  flex: 0 0 300px !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 20px !important;
  height: 100% !important; /* 确保高度占满 */
}

.middle-panel {
  flex: 0 0 400px !important;
  background: white !important;
  border-radius: 8px !important;
  padding: 20px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  overflow-y: auto !important;
  height: 100% !important; /* 确保高度占满 */
  box-sizing: border-box !important;
}

.right-panel {
  flex: 1 !important;
  background: white !important;
  border-radius: 8px !important;
  padding: 20px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  overflow-y: auto !important;
  height: 100% !important; /* 确保高度占满 */
  box-sizing: border-box !important;
}

.employee-section {
  background: white !important;
  border-radius: 8px !important;
  padding: 20px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  height: 100% !important; /* 确保高度占满 */
  display: flex !important;
  flex-direction: column !important;
  box-sizing: border-box !important;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.employee-section h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.employee-list {
  max-height: calc(100% - 50px) !important;
  overflow-y: auto !important;
  flex: 1 !important; /* 让列表区域占据剩余空间 */
}

.employee-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.employee-item:hover {
  background: #f8f9fa;
  border-color: #409EFF;
}

.employee-item.active {
  background: #e8f4ff;
  border-color: #409EFF;
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.employee-dept {
  color: #666;
  font-size: 12px;
}

.month-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  padding: 0 10px;
  justify-content: space-around;
}

.stat-card {
  text-align: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 6px;
  min-width: 60px;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 12px;
}

.calendar-section {
  margin-top: 20px;
}

.status-legend {
  display: flex;
  justify-content: space-around;
  margin: 15px 0 20px 0;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.legend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
}

.legend-icon {
  margin-bottom: 4px;
  font-size: 16px;
}

.legend-text {
  color: #666;
  white-space: nowrap;
}

.legend-number {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px; /* 与legend-item高度保持一致 */
}

.number-value {
  font-size: 24px;
  font-weight: bold;
  min-width: 40px;
  text-align: center;
}

.number-present {
  color: #67C23A; /* 与已打卡图标颜色一致 */
}

.number-absent {
  color: #F56C6C; /* 与缺勤图标颜色一致 */
}

.calendar-section .el-calendar {
  --el-calendar-cell-width: 50px;
}

.calendar-section .el-calendar__header {
  padding: 8px 20px;
}

.calendar-section .el-calendar__body {
  padding: 5px 20px 10px;
}

.calendar-section .el-calendar-table td {
  height: 22px;
  text-align: center;
}

.calendar-section .el-calendar-day {
  height: 22px;
  padding: 0;
}

.custom-calendar-cell {
  text-align: center;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.cell-date {
  font-size: 11px;
  line-height: 1;
}

.cell-status {
  margin-top: 3px;
}

.cell-status i {
  font-size: 32px;
  font-weight: bold;
}

.status-present {
  color: #67C23A;
}

.status-absent {
  color: #F56C6C;
}

.status-today {
  color: #409EFF;
}

/* 排班圆点 */
.schedule-dot {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  background-color: #409EFF;
  border-radius: 50%;
}

.no-selection {
  text-align: center;
  color: #999;
  padding: 80px 0;
}

.no-selection i {
  font-size: 48px;
  margin-bottom: 20px;
}

.attendance-records-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
}

.daily-records-list {
  max-height: calc(100% - 120px);
  overflow-y: auto;
}

.record-row {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 8px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #eee;
  border-left: 4px solid #ddd;
  transition: all 0.3s;
}

.record-row:hover {
  background: #f8f9fa;
  border-color: #c6e2ff;
}

.record-row.present {
  border-left-color: #67C23A;
}

.record-row.absent {
  border-left-color: #F56C6C;
}

.record-row.today {
  border-left-color: #409EFF;
}

.record-left {
  flex: 0 0 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.record-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.record-status i {
  font-size: 16px;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.record-right {
  flex: 1;
  margin-left: 20px;
}

.schedule-shops {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shop-item {
  color: #333;
  font-size: 13px;
  padding: 2px 0;
}

.no-schedule {
  color: #999;
  font-size: 12px;
  font-style: italic;
}

.status-none {
  color: #909399;
}

.loading-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #409EFF;
  font-size: 14px;
  gap: 10px;
}

.loading-stats .el-icon {
  font-size: 24px;
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    height: auto;
  }

  .left-panel {
    flex: none;
    order: 1;
  }

  .middle-panel {
    flex: none;
    order: 2;
  }

  .right-panel {
    flex: none;
    order: 3;
  }

  .month-stats {
    justify-content: center;
  }

  .records-grid {
    grid-template-columns: 1fr;
  }

  .record-card {
    padding: 10px;
  }

  .status-legend {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}
</style>