<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.ErpOrderItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.ErpOrderItem">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="product_idx" property="productIdx" />
        <result column="item_id" property="itemId" />
        <result column="item_name" property="itemName" />
        <result column="item_image" property="itemImage" />
        <result column="item_price" property="itemPrice" />
        <result column="order_price" property="orderPrice" />
        <result column="amount" property="amount" />
        <result column="description" property="description" />
        <result column="model_id" property="modelId" />
        <result column="item_model_name" property="itemModelName" />
        <result column="item_model_sku" property="itemModelSku" />
        <result column="product_sku" property="productSku" />
        <result column="price_before_bundle" property="priceBeforeBundle" />
        <result column="price_before_discount" property="priceBeforeDiscount" />
        <result column="first_item_count" property="firstItemCount" />
        <result column="first_item_is_wholesale" property="firstItemIsWholesale" />
        <result column="first_item_model" property="firstItemModel" />
        <result column="first_item_name" property="firstItemName" />
        <result column="first_item_return" property="firstItemReturn" />
        <result column="goods_states" property="goodsStates" />
        <result column="goods_type" property="goodsType" />
        <result column="instant_buyercancel_toship" property="instantBuyercancelToship" />
        <result column="is_buyercancel_toship" property="isBuyercancelToship" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, product_idx, item_id, item_name, item_image, item_price, order_price, amount, description, model_id, item_model_name, item_model_sku, product_sku, price_before_bundle, price_before_discount, first_item_count, first_item_is_wholesale, first_item_model, first_item_name, first_item_return, goods_states, goods_type, instant_buyercancel_toship, is_buyercancel_toship, create_by, create_time, update_by, update_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemPageVO">
		SELECT
			id, order_id, product_idx, item_id, item_name, item_image, item_price, order_price, amount, description, model_id, item_model_name, item_model_sku, product_sku, price_before_bundle, price_before_discount, first_item_count, first_item_is_wholesale, first_item_model, first_item_name, first_item_return, goods_states, goods_type, instant_buyercancel_toship, is_buyercancel_toship, create_by, create_time, update_by, update_time
		FROM
			erp_order_item AS t1
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="orderId != null and orderId != ''">
	           	AND t1.order_id = #{orderId}
            </if>
	        <if test="productIdx != null and productIdx != ''">
	           	AND t1.product_idx = #{productIdx}
            </if>
	        <if test="itemId != null and itemId != ''">
	           	AND t1.item_id = #{itemId}
            </if>
	        <if test="itemName != null and itemName != ''">
	           	AND t1.item_name = #{itemName}
            </if>
	        <if test="itemImage != null and itemImage != ''">
	           	AND t1.item_image = #{itemImage}
            </if>
	        <if test="itemPrice != null and itemPrice != ''">
	           	AND t1.item_price = #{itemPrice}
            </if>
	        <if test="orderPrice != null and orderPrice != ''">
	           	AND t1.order_price = #{orderPrice}
            </if>
	        <if test="amount != null and amount != ''">
	           	AND t1.amount = #{amount}
            </if>
	        <if test="description != null and description != ''">
	           	AND t1.description = #{description}
            </if>
	        <if test="modelId != null and modelId != ''">
	           	AND t1.model_id = #{modelId}
            </if>
	        <if test="itemModelName != null and itemModelName != ''">
	           	AND t1.item_model_name = #{itemModelName}
            </if>
	        <if test="itemModelSku != null and itemModelSku != ''">
	           	AND t1.item_model_sku = #{itemModelSku}
            </if>
	        <if test="productSku != null and productSku != ''">
	           	AND t1.product_sku = #{productSku}
            </if>
	        <if test="priceBeforeBundle != null and priceBeforeBundle != ''">
	           	AND t1.price_before_bundle = #{priceBeforeBundle}
            </if>
	        <if test="priceBeforeDiscount != null and priceBeforeDiscount != ''">
	           	AND t1.price_before_discount = #{priceBeforeDiscount}
            </if>
	        <if test="firstItemCount != null and firstItemCount != ''">
	           	AND t1.first_item_count = #{firstItemCount}
            </if>
	        <if test="firstItemIsWholesale != null and firstItemIsWholesale != ''">
	           	AND t1.first_item_is_wholesale = #{firstItemIsWholesale}
            </if>
	        <if test="firstItemModel != null and firstItemModel != ''">
	           	AND t1.first_item_model = #{firstItemModel}
            </if>
	        <if test="firstItemName != null and firstItemName != ''">
	           	AND t1.first_item_name = #{firstItemName}
            </if>
	        <if test="firstItemReturn != null and firstItemReturn != ''">
	           	AND t1.first_item_return = #{firstItemReturn}
            </if>
	        <if test="goodsStates != null and goodsStates != ''">
	           	AND t1.goods_states = #{goodsStates}
            </if>
	        <if test="goodsType != null and goodsType != ''">
	           	AND t1.goods_type = #{goodsType}
            </if>
	        <if test="instantBuyercancelToship != null and instantBuyercancelToship != ''">
	           	AND t1.instant_buyercancel_toship = #{instantBuyercancelToship}
            </if>
	        <if test="isBuyercancelToship != null and isBuyercancelToship != ''">
	           	AND t1.is_buyercancel_toship = #{isBuyercancelToship}
            </if>
	        <if test="createBy != null and createBy != ''">
	           	AND t1.create_by = #{createBy}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
	        <if test="updateBy != null and updateBy != ''">
	           	AND t1.update_by = #{updateBy}
            </if>
	        <if test="updateTime != null and updateTime != ''">
	           	AND t1.update_time = #{updateTime}
            </if>
        </where>
    </select>

</mapper>
