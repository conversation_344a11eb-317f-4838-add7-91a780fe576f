import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 分页查询用户证书
export const sysUserCertificationPage = (params) => { return reqGet("/sys-user-certification/page", params) };

// 新增用户证书
export const sysUserCertificationInsert = (params) => { return reqPost("/sys-user-certification", params) };

// 修改用户证书
export const sysUserCertificationUpdate = (params) => { return reqPut("/sys-user-certification", params) };

// 删除用户证书
export const sysUserCertificationDelete = (params) => { return reqDelete("/sys-user-certification", params) };
