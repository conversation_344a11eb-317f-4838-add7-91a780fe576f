package com.my.crossborder.controller.dto.sys_user;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_user.SysUserPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_系统用户
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserPageDTO 
						extends PageDTO<SysUserPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer userId;

    /**
     * 岗位(角色id)
     */
    private Integer roleId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 人员状态
     */
    private String status;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 备用联系电话
     */
    private String phoneSecondary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除（1-已删除，0-未删除）
     */
    private Boolean disable;

    /**
     * 逻辑删除时间
     */
    private LocalDateTime disableTime;

    /**
     * 排除的角色ID（用于排除特定角色的用户）
     */
    private Integer excludeRoleId;

}
