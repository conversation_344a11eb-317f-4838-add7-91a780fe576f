package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountDeleteDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountInsertDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountPageDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountDetailVO;
import com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountPageVO;
import com.my.crossborder.service.SysErpAccountService;

import lombok.RequiredArgsConstructor;

/**
 * 禾宸物流接口账号 
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/api/sys-erp-account")
@RequiredArgsConstructor
public class SysErpAccountController {

    private final SysErpAccountService sysErpAccountService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysErpAccountInsertDTO insertDTO) {
    	this.sysErpAccountService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody SysErpAccountUpdateDTO updateDTO) {
    	this.sysErpAccountService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<SysErpAccountDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.sysErpAccountService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysErpAccountPageVO>> page(SysErpAccountPageDTO pageDTO) {
        Page<SysErpAccountPageVO> page = this.sysErpAccountService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysErpAccountDeleteDTO deleteDTO) {
    	this.sysErpAccountService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
