package com.my.crossborder.controller.vo.ord_refund;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_退款表
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdRefundPageVO extends ErpOrderPageVO {

    private static final long serialVersionUID = 1L;

    /**
     * 申请状态 (0:待申请 1:已申请 2:不退采买改为入库)
     */
    private String applyStatus;

    /**
     * 申请退款金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请人id
     */
    private Integer applyUserId;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 结果状态 (1:退款成功 2:退款失败 3:确认已入库)
     */
    private String resultStatus;

    /**
     * 结果时间
     */
    private LocalDateTime resultTime;

    /**
     * 退款失败备注
     */
    private String refundFailReason;

    /**
     * 退款成功金额
     */
    private BigDecimal refundSuccessAmount;

    /**
     * 结果填写人
     */
    private Integer resultUserId;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 结果填写人姓名
     */
    private String resultUserName;

    /**
     * 结算状态 (-1:无需结算 0:待结算 1:已结算)
     */
    private String settlementFlag;

}
