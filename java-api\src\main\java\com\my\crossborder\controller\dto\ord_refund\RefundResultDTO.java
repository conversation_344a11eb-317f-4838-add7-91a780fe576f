package com.my.crossborder.controller.dto.ord_refund;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款结果DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class RefundResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private String orderSn;

    /**
     * 退款是否成功
     */
    @NotNull(message = "退款结果不能为空")
    private Boolean success;

    /**
     * 退款失败备注
     */
    private String refundFailReason;

    /**
     * 退款成功金额
     */
    @DecimalMin(value = "0", message = "退款成功金额不能为负数")
    private BigDecimal refundSuccessAmount;

}
