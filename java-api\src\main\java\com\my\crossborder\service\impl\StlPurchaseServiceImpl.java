package com.my.crossborder.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedPageDTO;
import com.my.crossborder.controller.dto.stl_purchase.StlPurchaseInsertDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedPageVO;
import com.my.crossborder.controller.vo.stl_purchase.DailyPurchaseDetail;
import com.my.crossborder.controller.vo.stl_purchase.MonthlyPurchaseDataVO;
import com.my.crossborder.controller.vo.stl_purchase.StlPurchaseDetailVO;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.mybatis.entity.StlPurchase;
import com.my.crossborder.mybatis.mapper.StlPurchaseMapper;
import com.my.crossborder.service.OrdPurchaseCentralizedService;
import com.my.crossborder.service.StlPurchaseService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;

/**
 * 结算_采购结算表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@RequiredArgsConstructor
public class StlPurchaseServiceImpl extends ServiceImpl<StlPurchaseMapper, StlPurchase> implements StlPurchaseService {

	private final OrdPurchaseCentralizedService ordPurchaseCentralizedService;
	

	@Transactional
	@Override
	public void insert(StlPurchaseInsertDTO insertDTO) {
		if (insertDTO.getPurchaseDate().isEqual(LocalDate.now())) {
			BusinessException.by("不允许结算今日的采购!");
		}
		
		StlPurchase entity = BeanUtil.copyProperties(insertDTO, StlPurchase.class);
		entity.setCreateUserId(StpUtil.getLoginIdAsInt());
		entity.setSettlementDate(LocalDateTime.now());
		this.save(entity);
	}

	@Override
	public StlPurchaseDetailVO detail(Integer id) {
		StlPurchase entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, StlPurchaseDetailVO.class);
	}

//	@Transactional
//	@Override
//	public void delete(StlPurchaseDeleteDTO deleteDTO) {
//		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
//	}
	
	@Override
	public List<MonthlyPurchaseDataVO> monthlyPurchaseData(Integer userId, Integer year, Integer month) {
		return this.baseMapper.monthlyPurchaseData(userId, year, month);
	}

	@Override
	public DailyPurchaseDetail dailyPurchaseDetails(Integer userId, LocalDate purchaseDate) {
		// 单笔采购详情
		DailyPurchaseDetail result = new DailyPurchaseDetail();
		List<DailyPurchaseDetail.Purchase> purchaseData = this.baseMapper.dailyPurchaseDetails(userId, purchaseDate);
		result.setPurchaseData(purchaseData);

		// 集中采购详情
		OrdPurchaseCentralizedPageDTO pageDTO = OrdPurchaseCentralizedPageDTO.builder()
				.purchaseUserId(userId)
				.purchaseDateStart(purchaseDate)
				.purchaseDateEnd(purchaseDate)
				.build();
		pageDTO.setCurrent(1L);
		pageDTO.setSize(10000L);
		List<OrdPurchaseCentralizedPageVO> purchaseCentralizedData = this.ordPurchaseCentralizedService.page(pageDTO).getRecords();
		result.setPurchaseCentralizedData(purchaseCentralizedData);
		
		return result;
	}
}
