import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 物流理赔分页查询
 * @param {Object} params 查询参数
 */
export function getOrdClaimPage(params) {
  return reqGet('/ord-claim/page', params)
}

/**
 * 新增物流理赔
 * @param {Object} data 物流理赔数据
 */
export function insertOrdClaim(data) {
  return reqPost('/ord-claim', data)
}

/**
 * 修改物流理赔
 * @param {Object} data 物流理赔数据
 */
export function updateOrdClaim(data) {
  return reqPut('/ord-claim', data)
}

/**
 * 删除物流理赔
 * @param {Object} data 删除参数
 */
export function deleteOrdClaim(data) {
  return reqDelete('/ord-claim', data)
}

/**
 * 根据ID查询物流理赔详情
 * @param {String} id 物流理赔ID
 */
export function getOrdClaimDetail(id) {
  return reqGet(`/ord-claim/${id}`)
}

/**
 * 确认完成物流理赔
 * @param {String} id 物流理赔ID
 */
export function confirmCompleteOrdClaim(id) {
  return reqPut(`/ord-claim/confirm-complete/${id}`)
}
