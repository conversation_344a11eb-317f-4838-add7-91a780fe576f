import axios from 'axios';
import ElementUI from "element-ui";
import qs from 'qs';  // qs文档 https://storm4542.github.io/archives/7b89c88d.html

// 用于标记是否已经显示过登录失效提示，避免重复弹窗
let hasShownLogoutWarning = false;

// 根据环境设置API前缀
const API_PREFIX = process.env.NODE_ENV === 'production'
    ? "/api"
    : "/api";

// 域内get
const reqGet = (url, params) => {
    return axios({
        method: "get",
        url: API_PREFIX + url,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            token: localStorage.getItem('logintoken')
        },
        params: params,
        paramsSerializer: params => {
            return qs.stringify(params, { allowDots: true, skipNulls: true })
        },
        traditional: true,
    }).then(res => res.data);
};

// 域内post
const reqPost = (url, params, method) => {
    return reqJson(url ,params, "post");
};

// 域内put
const reqPut = (url, params) => {
    return reqJson(url ,params, "put");
};

// 域内delete
const reqDelete = (url, params) => {
    return reqJson(url ,params, "delete");
};

// data format =================================

// 文件上传
const reqUpload = (url, params, method) => {
    return axios({
        method: "post",
        url: API_PREFIX + url,
        headers: {
            'Content-Type': 'multipart/form-data;charset=utf-8',
            token: localStorage.getItem('logintoken')
        },
        data: params,
        // traditional: true,
    }).then(res => res.data);
};

// json格式请求数据
const reqJson = (url, params, method) => {
    return axios({
        method: method,
        url: API_PREFIX + url,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            token: localStorage.getItem('logintoken')
        },
        data: JSON.stringify(params),
        traditional: true,
    }).then(res => res.data);
};

// respone拦截器 我们主要拦截异常，对异常信息统一处理
axios.interceptors.response.use(
    (response) => {
        // token续期
        if (response.headers.token){
            localStorage.setItem("logintoken", response.headers.token);
        }
        // 如果请求成功，重置登录失效提示标记
        hasShownLogoutWarning = false;
        return response;
    },
    (error) => {
        // 如果token为空，则退出登录
        if (error.response && error.response.status == 401){
            // 只有在未显示过提示时才显示，避免重复弹窗
            if (!hasShownLogoutWarning) {
                hasShownLogoutWarning = true;
                // 提示信息
                ElementUI.Message({"message": "登录失效，请重新登录", "type": "warning"})
                // 清空登录并跳转
                setTimeout(() => {
                    localStorage.setItem("userInfo", "");
                    localStorage.setItem("logintoken", "");
                    localStorage.setItem("userEntity", "");
                    // 使用vuex清除登录状态
                    if(window.$store) {
                        window.$store.commit("logout", "false");
                    }
                    // 使用路由跳转到登录页
                    if(window.$router) {
                        window.$router.push("/login");
                    } else {
                        // 兜底方案，直接修改URL
                        window.location.href = "/#/login";
                    }
                    // 跳转后重置标记，为下次登录做准备
                    hasShownLogoutWarning = false;
                }, 1500)
            }
        } else if (error.response && (error.response.status == 502 || error.response.status == 504)) {
            ElementUI.Message({"message": "连接失败, 请检查后台服务!", "type": "error"})
        } else if (!error.response || error.code === 'ECONNABORTED') {
            // 网络不通或请求超时的情况
            ElementUI.Message({"message": "连接失败, 请检查后台服务!", "type": "error"})
        } else {
            ElementUI.Message({"message": error.response.data.errMsg, "type": "error"})
        }

        return Promise.reject(error);
    }
);

export {
    reqPost,
    reqGet,
    reqPut,
    reqDelete,
    reqUpload,
}
