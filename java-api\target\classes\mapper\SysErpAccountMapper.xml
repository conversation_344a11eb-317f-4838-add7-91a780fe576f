<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysErpAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysErpAccount">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, password
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountPageVO">
		SELECT
			id, username, password
		FROM
			sys_erp_account AS t1
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="username != null and username != ''">
	           	AND t1.username like concat('%', #{username}, '%')
            </if>
        </where>
    </select>

</mapper>
