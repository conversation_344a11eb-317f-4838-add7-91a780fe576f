package com.my.crossborder.service.check.impl;

import java.lang.reflect.Field;

import org.apache.commons.lang3.StringUtils;

import com.my.crossborder.service.check.AbstractCheck;
import com.my.crossborder.service.check.result.CheckResult;

/**
 * 文件上传保存路径
 * <AUTHOR>
 */
public class FileUploadPathCheck extends AbstractCheck {

	
    @Override
    protected CheckResult doCheck(Object fieldValue, Field field, Object obj) {
        String val = (String) fieldValue;
        
        if (StringUtils.isBlank(val)) {
            return fail(this.fieldLabel + "不能为空");
        }
        
        return success();
    }
}
