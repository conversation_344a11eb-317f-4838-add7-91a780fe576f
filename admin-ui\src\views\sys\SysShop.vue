<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>店铺管理</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="店铺号">
          <el-input v-model="formInline.id" placeholder="请输入店铺号"></el-input>
        </el-form-item>
        <el-form-item label="店铺名称">
          <el-input v-model="formInline.shopName" placeholder="请输入店铺名称"></el-input>
        </el-form-item>
        <el-form-item label="合伙人">
          <el-select v-model="formInline.partnerId" placeholder="请选择合伙人" clearable style="width: 200px">
            <el-option v-for="partner in partnerOptions" :key="partner.userId" :label="partner.realName"
              :value="partner.userId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" border style="width: 100%; margin-top: 20px;" v-loading="loading">
        <el-table-column prop="id" label="店铺号" min-width="120" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="shopName" label="店铺名称" min-width="120" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="openingDate" label="起店时间" min-width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.openingDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="合伙人" min-width="200" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.partnerNames || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" align="center">
          <template slot-scope="scope">
            <el-button v-permission="['sys-shop:update']" size="mini" type="text" @click="handleEdit(scope.row)">
              <i class="el-icon-edit"></i> 修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

      <!-- 店铺编辑对话框 -->
      <el-dialog title="修改店铺信息" :visible.sync="shopEditDialogVisible" width="600px">
        <el-form :model="editShopForm" :rules="editShopRules" ref="editShopForm" label-width="120px">
          <el-form-item label="店铺名称" prop="shopName">
            {{ editShopForm.shopName }}
          </el-form-item>
          <el-form-item label="店铺号" prop="id">
            {{ editShopForm.id }}
          </el-form-item>
          <el-form-item label="合伙人" prop="partnerIds">
            <el-select v-model="editShopForm.partnerIds" multiple placeholder="请选择合伙人" style="width: 100%">
              <el-option v-for="partner in partnerOptions" :key="partner.userId" :label="partner.realName"
                :value="partner.userId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="起店时间" prop="openingDate">
            <el-date-picker v-model="editShopForm.openingDate" type="date" placeholder="请选择起店时间"
              value-format="yyyy-MM-dd" style="width: 100%">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="shopEditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmShopEdit" :loading="editLoading">确定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getSysShopPartners, pageSysShop, updateSysShop } from '../../api/SysShop';
import Pagination from '../../components/Pagination';

export default {
  name: 'SysShop',
  components: {
    Pagination
  },
  data() {
    return {
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        shopName: '',
        partnerId: ''
      },
      // 合伙人选项
      partnerOptions: [],
      // 店铺编辑对话框
      shopEditDialogVisible: false,
      // 编辑加载状态
      editLoading: false,
      // 编辑店铺表单数据
      editShopForm: {
        id: '',
        shopName: '',
        partnerIds: [],
        openingDate: ''
      },
      // 编辑店铺表单验证规则
      editShopRules: {
        openingDate: [
          { required: true, message: '必填', trigger: 'blur' }
        ]
      },
      // 表格数据
      tableData: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 加载状态
      loading: false
    }
  },
  created() {
    // 加载合作人数据
    this.loadPartners();
    // 获取分页数据
    this.getPageData();
  },
  methods: {
    // 加载合作人数据
    async loadPartners() {
      try {
        const response = await getSysShopPartners();
        if (response.success) {
          this.partnerOptions = response.data || [];
        }
      } catch (error) {
        console.error('加载合作人数据失败:', error);
      }
    },

    // 获取分页数据
    getPageData(parameter) {
      this.loading = true;
      if (!parameter) {
        parameter = this.formInline;
      }

      pageSysShop(parameter)
        .then(res => {
          this.loading = false;
          this.tableData = res.data.records || [];
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size;
          this.pageParam.total = res.data.total;
        })
        .catch(err => {
          this.loading = false;
          this.$message.error('获取数据失败：' + err.message);
        });
    },

    // 查询
    onSearch() {
      this.formInline.current = 1;
      this.getPageData();
    },

    // 重置
    onReset() {
      this.formInline = {
        current: 1,
        size: 10,
        id: '',
        shopName: '',
        partnerId: ''
      };
      this.getPageData();
    },

    // 编辑店铺
    handleEdit(row) {
      // 加载原来的数据到编辑表单
      this.editShopForm = {
        id: row.id,
        shopName: row.shopName,
        partnerIds: row.partnerIds || [], // 需要后端返回合作人ID数组
        openingDate: row.openingDate
      };
      this.shopEditDialogVisible = true;
    },

    // 确认编辑店铺
    confirmShopEdit() {
      this.$refs.editShopForm.validate((valid) => {
        if (valid) {
          this.editLoading = true;

          updateSysShop(this.editShopForm)
            .then(res => {
              this.editLoading = false;
              this.shopEditDialogVisible = false;
              this.$message.success('修改成功!');
              this.getPageData();
            })
            .catch(err => {
              this.editLoading = false;
              this.$message.error('修改失败：' + err.message);
            });
        } else {
          this.$message.error('请检查表单信息');
          return false;
        }
      });
    },

    // 分页回调
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage;
      this.formInline.size = parm.pageSize;
      this.getPageData();
    }
  }
}
</script>

<style scoped>
.content-container {
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}



.el-button+.el-button {
  margin-left: 10px;
}
</style>
