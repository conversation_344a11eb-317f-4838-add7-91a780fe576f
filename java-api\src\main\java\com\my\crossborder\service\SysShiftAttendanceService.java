package com.my.crossborder.service;

import java.time.LocalDate;
import java.util.Map;
import java.util.Set;

import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_shift_attendance.SysShiftAttendanceDeleteDTO;
import com.my.crossborder.controller.dto.sys_shift_attendance.SysShiftAttendanceInsertDTO;
import com.my.crossborder.controller.dto.sys_shift_attendance.SysShiftAttendancePageDTO;
import com.my.crossborder.controller.dto.sys_shift_attendance.SysShiftAttendanceUpdateDTO;
import com.my.crossborder.controller.vo.sys_shift_attendance.MonthAttendanceFull;
import com.my.crossborder.controller.vo.sys_shift_attendance.MonthAttendanceSummary;
import com.my.crossborder.controller.vo.sys_shift_attendance.SysShiftAttendanceDetailVO;
import com.my.crossborder.controller.vo.sys_shift_attendance.SysShiftAttendancePageVO;
import com.my.crossborder.controller.vo.sys_shift_attendance.TodayAttendanceStatus;
import com.my.crossborder.mybatis.entity.SysShiftAttendance;

/**
 * 考勤表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface SysShiftAttendanceService extends IService<SysShiftAttendance> {

	/**
	 * 不存在则新增
	 * @param attendanceSet
	 */
	void createIfAbsense(Set<SysShiftAttendance> attendanceSet);

	/**
	 * 不存在则新增
	 * @param dto
	 */
	void createIfAbsense(SysShiftAttendance dto);
	
	/**
	 * 根据用户ID和日期删除考勤记录
	 * @param shiftDay 排班日期
	 * @param userId 用户ID
	 */
	void removeByUserId(LocalDate shiftDay, Integer userId);

	/**
	 * 今日考勤状态
	 * @param userId
	 * @param shiftDay
	 * @return
	 */
	TodayAttendanceStatus todayAttendanceStatus(Integer userId, LocalDate shiftDay);

	/**
	 * 打卡
	 * @param userId
	 */
	void punchIn();

	/**
	 * 月度统计
	 * @param userId
	 * @param year
	 * @param month
	 * @return
	 */
	MonthAttendanceSummary monthAttendanceSummary(Integer userId, Integer year, Integer month);

	/**
	 * 月度统计 + 每日状态
	 * @param userId
	 * @param year
	 * @param month
	 * @return
	 */
	MonthAttendanceFull monthlyAttendanceFull(Integer userId, Integer year, Integer month);

	/**
	 * 某天的排班
	 * @param userId
	 * @param date
	 * @return
	 */
	Map<String, Object> dateShift(Integer userId, String date);

	/**
	 * 将今天以前未打卡的记录标记为缺勤
	 */
	void autoAbsentStatus();

}
