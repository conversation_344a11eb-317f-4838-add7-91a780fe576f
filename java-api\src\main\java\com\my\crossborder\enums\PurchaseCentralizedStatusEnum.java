package com.my.crossborder.enums;

/**
 * 集中采购状态枚举
 * PURCHASE_CENTRALIZED_STATUS
 */
public enum PurchaseCentralizedStatusEnum {
    
    /**
     * 待入库
     */
    PENDING_WAREHOUSE("0", "待入库"),
    
    /**
     * 已入库
     */
    WAREHOUSED("1", "已入库"),
    
    /**
     * 数据有误正在确认
     */
    DATA_ERROR_CONFIRMING("2", "数据有误正在确认");
    
    private final String code;
    private final String desc;
    
    PurchaseCentralizedStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static PurchaseCentralizedStatusEnum getByCode(String code) {
        for (PurchaseCentralizedStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     */
    public static String getDescByCode(String code) {
        PurchaseCentralizedStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : null;
    }
}
