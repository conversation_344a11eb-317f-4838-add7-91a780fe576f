<template>
  <el-dialog :title="dialogTitle" top="8vh" :visible.sync="dialogVisible" width="650px" @close="handleDialogClose">
    <el-form :model="itemForm" :rules="itemRules" ref="itemForm" label-width="140px">
      <el-form-item label="采购单号" prop="purchaseNumber">
        <el-input v-model="itemForm.purchaseNumber" placeholder="请输入采购单号" maxlength="50"></el-input>
      </el-form-item>

      <el-form-item label="品名" prop="productName">
        <el-input v-model="itemForm.productName" placeholder="请输入品名" maxlength="100"></el-input>
      </el-form-item>

      <el-form-item label="数量" prop="quantity">
        <el-input-number v-model="itemForm.quantity" :min="1" :max="99999" placeholder="请输入数量" style="width: 200px;"></el-input-number>
      </el-form-item>

      <el-form-item label="采购总金额" prop="totalAmount">
        <el-input-number v-model="itemForm.totalAmount" :precision="2" :min="0" :max="999999" placeholder="请输入总金额" style="width: 200px;"></el-input-number>
      </el-form-item>

      <el-form-item label="采购途径" prop="purchaseChannel">
        <dict-select v-model="itemForm.purchaseChannel" category-id="PURCHASE_CENTRALIZED_CHANNEL" placeholder="请选择采购途径" style="width: 200px;" />
      </el-form-item>

      <el-form-item label="关联订单" prop="orderSnList">
        <multi-order-selector v-model="itemForm.orderSnList" />
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { insertOrdPurchaseCentralized, updateOrdPurchaseCentralized } from '../../api/OrdPurchaseCentralized'
import DictSelect from '../../components/DictSelect'
import MultiOrderSelector from '../../components/MultiOrderSelector'

export default {
  name: 'OrdPurchaseCentralizedEdit',
  components: {
    DictSelect,
    MultiOrderSelector
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      itemForm: {
        id: null,
        purchaseNumber: '',
        productName: '',
        quantity: 1,
        totalAmount: 0,
        purchaseChannel: '',
        purchaseDate: '',
        orderSnList: []
      },
      itemRules: {
        purchaseNumber: [
          { required: true, message: '请输入采购单号', trigger: 'blur' }
        ],
        productName: [
          { required: true, message: '请输入品名', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        totalAmount: [
          { required: true, message: '请输入总金额', trigger: 'blur' },
          { validator: (_, value, callback) => {
            if (value <= 0) {
              callback(new Error('总金额必须大于0'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ],
        purchaseChannel: [
          { required: true, message: '请选择采购途径', trigger: 'change' }
        ],
        purchaseDate: [
          { required: true, message: '请选择采购日期', trigger: 'change' }
        ],
        orderSnList: [
          { required: true, validator: this.validateOrderSnList, trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return this.isEdit ? '编辑集中采购' : '新增集中采购'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    // 验证订单号列表
    validateOrderSnList(_, value, callback) {
      if (!value || !Array.isArray(value) || value.length === 0) {
        callback(new Error('至少需要关联一个订单'))
      } else {
        callback()
      }
    },

    initForm() {
      if (this.isEdit && this.editData) {
        this.itemForm = {
          id: this.editData.id,
          purchaseNumber: this.editData.purchaseNumber || '',
          productName: this.editData.productName || '',
          quantity: this.editData.quantity || 1,
          totalAmount: this.editData.totalAmount || 0,
          purchaseChannel: this.editData.purchaseChannel || '',
          purchaseDate: this.editData.purchaseDate ? this.editData.purchaseDate.split(' ')[0] : '',
          orderSnList: this.editData.orderSnList ? [...this.editData.orderSnList] : []
        }
      } else {
        this.itemForm = {
          id: null,
          purchaseNumber: '',
          productName: '',
          quantity: 1,
          totalAmount: 0,
          purchaseChannel: '',
          purchaseDate: '',
          orderSnList: []
        }
      }
    },
    handleSubmit() {
      this.$refs.itemForm.validate((valid) => {
        if (valid) {
          // 额外检查订单号列表
          if (!this.itemForm.orderSnList || this.itemForm.orderSnList.length === 0) {
            this.$message.error('至少需要关联一个订单')
            return
          }

          const submitData = { ...this.itemForm }

          if (this.isEdit) {
            updateOrdPurchaseCentralized(submitData).then(() => {
              this.$message.success('修改成功')
              this.$emit('submit')
            })
          } else {
            insertOrdPurchaseCentralized(submitData).then(() => {
              this.$message.success('添加成功')
              this.$emit('submit')
            })
          }
        }
      })
    },
    handleCancel() {
      this.dialogVisible = false
    },
    handleDialogClose() {
      this.$refs.itemForm.resetFields()
    }
  }
}
</script>

<style scoped>
/* 样式已移至 MultiOrderSelector 组件中 */
</style>
