<template>
  <div class="attendance-calendar-dialog">


    <!-- 状态说明图例 -->
    <div class="status-legend">
      <div class="legend-item">
        <span class="legend-icon status-present">
          <i class="el-icon-circle-check"></i>
        </span>
        <span class="legend-text">已打卡</span>
      </div>
      <div class="legend-number">
        <span class="number-value number-present">{{ monthStats.checkedDays }}</span>
      </div>
      <div class="legend-item" style="margin-left: 100px;">
        <span class="legend-icon status-absent">
          <i class="el-icon-circle-close"></i>
        </span>
        <span class="legend-text">缺勤</span>
      </div>
      <div class="legend-number">
        <span class="number-value number-absent">{{ monthStats.absentDays }}</span>
      </div>
    </div>

    <el-calendar v-model="selectedDate" @pick="onDateChange">
      <template slot="dateCell" slot-scope="{date, data}">
        <div class="custom-calendar-cell" @click="handleDateClick(data.day)">
          <div class="cell-date">{{ data.day.split('-').slice(2).join('-') }}</div>
          <div class="cell-status">
            <span v-if="getAttendanceStatus(data.day) === 'CHECKED'" class="status-present">
              <i class="el-icon-circle-check"></i>
            </span>
            <span v-else-if="getAttendanceStatus(data.day) === 'ABSENT'" class="status-absent">
              <i class="el-icon-circle-close"></i>
            </span>
            <!-- <span v-else-if="data.day === getCurrentDate()" class="status-today">
              <i class="el-icon-time"></i>
            </span> -->
          </div>
          <!-- 排班圆点 -->
          <div v-if="hasSchedule(data.day)" class="schedule-dot"></div>
        </div>
      </template>
    </el-calendar>
  </div>
</template>

<script>
import * as AttendanceAPI from '../../api/SysShiftAttendance'

export default {
  name: 'DashBoardAttendanceCalendar',
  props: {
    userId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      selectedDate: new Date(),
      monthStats: {
        scheduledDays: 0,
        checkedDays: 0,
        absentDays: 0
      },
      // 考勤数据：key为日期字符串，value为状态
      attendanceData: {},
      // 排班数据：key为日期字符串，value为是否有排班
      scheduleData: {},
      loading: false
    }
  },
  computed: {
    currentMonth() {
      const year = this.selectedDate.getFullYear()
      const month = this.selectedDate.getMonth() + 1
      return `${year}年${month}月`
    },
    isToday() {
      return this.formatDate(this.selectedDate) === this.getCurrentDate()
    }
  },
  watch: {
    selectedDate: {
      handler() {
        this.loadMonthAttendanceData()
      },
      immediate: true
    },
    userId: {
      handler() {
        if (this.userId) {
          this.loadMonthAttendanceData()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 刷新数据
    refreshData() {
      this.loadMonthAttendanceData()
    },

    // 加载月度考勤数据
    async loadMonthAttendanceData() {
      if (!this.userId) return

      this.loading = true
      try {
        const year = this.selectedDate.getFullYear()
        const month = this.selectedDate.getMonth() + 1

        const response = await AttendanceAPI.getMonthlyAttendanceData({
          userId: this.userId,
          year: year,
          month: month
        })

        if (response && response.data) {
          const data = response.data
          this.attendanceData = data.attendanceData || {}
          this.scheduleData = data.scheduleData || {}
          this.monthStats = {
            scheduledDays: data.scheduledDays || 0,
            checkedDays: data.checkedDays || 0,
            absentDays: data.absentDays || 0
          }
        }
      } catch (error) {
        console.error('加载月度考勤数据失败:', error)
        this.$message.error('加载考勤数据失败')
      } finally {
        this.loading = false
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 获取当前日期
    getCurrentDate() {
      return this.formatDate(new Date())
    },

    // 获取考勤状态
    getAttendanceStatus(dateStr) {
      return this.attendanceData[dateStr] || 'none'
    },

    // 检查是否有排班
    hasSchedule(dateStr) {
      // 如果有专门的排班数据，使用排班数据
      if (this.scheduleData[dateStr] !== undefined) {
        return this.scheduleData[dateStr]
      }

      // 否则从考勤数据推断：如果有考勤记录（CHECKED、PENDING_CHECK、ABSENT），说明有排班
      const attendanceStatus = this.attendanceData[dateStr]
      return attendanceStatus && attendanceStatus !== 'none'
    },

    // 日期变更
    onDateChange(date) {
      this.selectedDate = date
    },

    // 处理日期点击
    handleDateClick(dateStr) {
      const status = this.getAttendanceStatus(dateStr)
      if (status && status !== 'none') {
        this.$emit('date-click', dateStr)
      }
    }
  }
}
</script>

<style scoped>
.attendance-calendar-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

/* 顶部统计区域 */
.stats-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.stats-title {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number.main {
  font-size: 48px;
  font-weight: bold;
  color: #E74C3C;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label.main {
  font-size: 16px;
  color: #666;
  font-weight: normal;
}

.stats-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-value.success {
  color: #67C23A;
}

.stat-value.primary {
  color: #409EFF;
}

.stat-value.warning {
  color: #E6A23C;
}

.stat-value.info {
  color: #909399;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

/* 日历区域 */
.calendar-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.calendar-header {
  margin-bottom: 15px;
}

.month-nav {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.current-month {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  min-width: 100px;
  text-align: center;
}

.status-legend {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin: 15px 0 20px 0;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.legend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
}

.legend-icon {
  margin-bottom: 4px;
  font-size: 16px;
}

.legend-text {
  color: #666;
  white-space: nowrap;
}

.legend-number {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px; /* 与legend-item高度保持一致 */
}

.number-value {
  font-size: 24px;
  font-weight: bold;
  min-width: 40px;
  text-align: center;
}

.number-present {
  color: #67C23A; /* 与已打卡图标颜色一致 */
}

.number-absent {
  color: #F56C6C; /* 与缺勤图标颜色一致 */
}

/* 日历样式 */
.custom-calendar-cell {
  text-align: center;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.cell-date {
  font-size: 11px;
  line-height: 1;
}

.cell-status {
  margin-top: 3px;
}

/* 排班圆点 */
.schedule-dot {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  background-color: #409EFF;
  border-radius: 50%;
}

.cell-status i {
  font-size: 32px;
  font-weight: bold;
}

.status-present {
  color: #67C23A;
}

.status-absent {
  color: #F56C6C;
}

.status-today {
  color: #409EFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row {
    flex-direction: column;
    gap: 15px;
  }

  .status-legend {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
