<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.WkbNoteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.WkbNote">
        <id column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="content" property="content" />
        <result column="scene" property="scene" />
        <result column="scene_complete" property="sceneComplete" />
        <result column="scene_complete_time" property="sceneCompleteTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 详情查询映射结果 -->
    <resultMap id="DetailResultMap" type="com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO">
        <id column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="content" property="content" />
        <result column="scene" property="scene" />
        <result column="scene_complete" property="sceneComplete" />
        <result column="scene_complete_time" property="sceneCompleteTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_sn, content, scene, scene_complete, scene_complete_time, create_user_id, create_time, update_time
    </sql>
    
    <!-- 详情查询结果列(带用户名) -->
    <sql id="Detail_Column_List">
        n.id, n.order_sn, n.content, n.scene, n.scene_complete, n.scene_complete_time, n.create_user_id, 
        u.real_name as create_user_name, n.create_time, n.update_time
    </sql>

	<!-- 分页 -->
    <select id="page" resultType="com.my.crossborder.controller.vo.wkb_note.WkbNotePageVO">
        SELECT
            id,
            order_sn,
            content,
            scene,
            tDict.item_name as scene_name,
            scene_complete,
            scene_complete_time,
            create_user_id,
            create_time,
            update_time
        FROM
            wkb_note AS t1
            LEFT JOIN sys_dict_item tDict ON t1.scene = tDict.item_value and tDict.category_id = 'scene'
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="orderSn != null and orderSn != ''">
	           	AND t1.order_sn = #{orderSn}
            </if>
	        <if test="content != null and content != ''">
	           	AND t1.content = #{content}
            </if>
	        <if test="scene != null and scene != ''">
	           	AND t1.scene = #{scene}
            </if>
	        <if test="sceneComplete != null">
	           	AND t1.scene_complete = #{sceneComplete}
            </if>
	        <if test="sceneCompleteTime != null and sceneCompleteTime != ''">
	           	AND t1.scene_complete_time = #{sceneCompleteTime}
            </if>
	        <if test="createUserId != null and createUserId != ''">
	           	AND t1.create_user_id = #{createUserId}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
	        <if test="updateTime != null and updateTime != ''">
	           	AND t1.update_time = #{updateTime}
            </if>
        </where>
        ORDER BY t1.id DESC
    </select>
    
    <!-- 根据订单号查询所有笔记 -->
    <select id="selectNotesByOrderSn" resultMap="DetailResultMap">
        SELECT
            <include refid="Detail_Column_List" />
        FROM
            wkb_note n
        LEFT JOIN
            sys_user u ON n.create_user_id = u.user_id
        WHERE
            n.order_sn = #{orderSn}
        ORDER BY
            n.id DESC
    </select>

    <!-- 批量查询订单备注信息 -->
    <select id="selectByOrderSnSet" resultMap="DetailResultMap">
        SELECT
            <include refid="Detail_Column_List" />
        FROM
            wkb_note n
        LEFT JOIN
            sys_user u ON n.create_user_id = u.user_id
        WHERE
            n.order_sn IN
        <foreach collection="orderSnList" item="orderSn" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
        ORDER BY n.id DESC
    </select>

</mapper>
