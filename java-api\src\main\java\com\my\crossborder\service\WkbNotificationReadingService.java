package com.my.crossborder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingDeleteDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingInsertDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingPageDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingUpdateDTO;
import com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingDetailVO;
import com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingPageVO;
import com.my.crossborder.mybatis.entity.WkbNotificationReading;

/**
 * 通知阅读表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface WkbNotificationReadingService extends IService<WkbNotificationReading> {

	/**
	 * 新增
	 */
	void insert(WkbNotificationReadingInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(WkbNotificationReadingUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	WkbNotificationReadingDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<WkbNotificationReadingPageVO> page(WkbNotificationReadingPageDTO pageDTO);	

	/**
	 * 删除我的已读
	 */
	void deleteMy(WkbNotificationReadingDeleteDTO deleteDTO);

	/**
	 * 添加未读公告
	 * @param notificationId
	 * @param roleIdList
	 */
	void insertNotRead(Integer notificationId, List<Integer> roleIdList);

	/**
	 * 根据通知id做删除
	 * @param notificationIdList
	 */
	void deleteByNotificatinId(List<Integer> notificationIdList);

	/**
	 * 标记为已读
	 * @param notificationId
	 */
	void markRead(Integer notificationId);	

}
