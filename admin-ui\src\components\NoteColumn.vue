<template>
  <el-table-column prop="note" label="备注" width="120" align="center">
    <template slot-scope="scope">
      <el-popover
        placement="top"
        width="250"
        trigger="hover"
        :disabled="!getAllSceneNotes(scope.row.notes).length">
        <div slot="reference" class="note-cell">
          <div v-if="getAllSceneNotes(scope.row.notes).length === 0" class="no-note">
            -
          </div>
          <div v-else class="note-list">
            <div
              v-for="(note, index) in getAllSceneNotes(scope.row.notes)"
              :key="note.id || index"
              class="note-item">
              <span class="note-user">{{ note.createUserName }}</span>
              <span class="note-content" :title="note.content">{{ note.content }}</span>
            </div>
          </div>
        </div>
        <!-- Popover内容 -->
        <div class="note-popover-content">
          <div
            v-for="(note, index) in getAllSceneNotes(scope.row.notes)"
            :key="note.id || index"
            class="note-detail-item">
            <div class="note-header">
              <span class="note-user-name">{{ note.createUserName }}</span>
              <span class="note-status" :class="getStatusClass(note.sceneComplete)">
                {{ note.sceneComplete ? '已处理' : '待处理' }}
              </span>
              <span class="note-time">{{ formatDateTime(note.createTime) }}</span>
            </div>
            <div class="note-content-detail">{{ note.content }}</div>
          </div>
        </div>
      </el-popover>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: 'NoteColumn',
  props: {
    // 当前场景代码，用于过滤备注
    currentScene: {
      type: String,
      required: true,
      default: '02'
    },
    // 备注列宽度
    width: {
      type: String,
      default: '120'
    },
    // 备注列标签
    label: {
      type: String,
      default: '备注'
    },
    // popover宽度
    popoverWidth: {
      type: String,
      default: '200'
    }
  },
  methods: {
    /**
     * 获取当前场景下所有人的备注
     * @param {Array} notes - 备注数组
     * @returns {Array} 备注列表
     */
    getAllSceneNotes(notes) {
      if (!notes || !Array.isArray(notes)) return []

      // 过滤当前场景的备注，按创建时间倒序排列
      return notes
        .filter(note => note.scene === this.currentScene)
        .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
    },

    /**
     * 获取当前用户在当前场景下的备注内容（保留兼容性）
     * @param {Array} notes - 备注数组
     * @returns {String} 备注内容
     */
    getCurrentUserSceneNote(notes) {
      if (!notes || !Array.isArray(notes)) return ''

      // 获取当前登录用户ID
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      const currentUserId = userEntity.id || userEntity.userId

      // 查找当前场景下当前用户的备注
      const userNote = notes.find(note =>
        note.scene === this.currentScene &&
        note.createUserId === currentUserId
      )

      return userNote ? userNote.content : ''
    },

    /**
     * 格式化日期时间
     * @param {String} dateTime - 日期时间字符串
     * @returns {String} 格式化后的日期时间
     */
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}/${month}/${day} ${hours}:${minutes}`
    },

    /**
     * 获取状态样式类
     * @param {Boolean} sceneComplete - 是否完成
     * @returns {String} 样式类名
     */
    getStatusClass(sceneComplete) {
      return sceneComplete ? 'status-completed' : 'status-pending'
    }
  }
}
</script>

<style scoped>
/* 备注单元格样式 */
.note-cell {
  max-width: 100px;
  cursor: pointer;
  font-size: 12px;
  line-height: 1.4;
  padding: 2px 4px;
  border-radius: 2px;
}

.note-cell:hover {
  background-color: #f5f7fa;
}

/* 无备注状态 */
.no-note {
  color: #C0C4CC;
  font-style: italic;
  text-align: center;
}

/* 备注列表 */
.note-list {
  max-height: 60px;
  overflow: hidden;
}

.note-item {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 11px;
  line-height: 1.3;
}

.note-item:last-child {
  margin-bottom: 0;
}

.note-user {
  color: #409EFF;
  font-weight: bold;
  margin-right: 4px;
  flex-shrink: 0;
  min-width: 30px;
}

.note-content {
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* Popover内容样式 */
.note-popover-content {
  max-height: 300px;
  overflow-y: auto;
}

.note-detail-item {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #EBEEF5;
}

.note-detail-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.note-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.note-user-name {
  color: #409EFF;
  font-weight: bold;
  margin-right: 8px;
}

.note-status {
  padding: 1px 6px;
  border-radius: 10px;
  font-size: 10px;
  margin-right: 8px;
}

.status-completed {
  background-color: #F0F9FF;
  color: #67C23A;
}

.status-pending {
  background-color: #FDF6EC;
  color: #E6A23C;
}

.note-time {
  color: #909399;
  font-size: 11px;
  margin-left: auto;
}

.note-content-detail {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
