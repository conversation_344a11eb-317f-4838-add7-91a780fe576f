<template>
  <div class="leftnav-container">
    <!-- 折叠按钮 -->
    <div class="collapse-button" @click="toggle">
      <i :class="collapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
    </div>

    <el-menu default-active="2" :collapse="collapsed" collapse-transition router :default-active="$route.path" unique-opened
      class="el-menu-vertical-demo" background-color="#334157" text-color="#fff" active-text-color="#ffd04b">
      <el-submenu v-for="menu in allmenu" :key="menu.menuid" :index="menu.menuname">
        <template slot="title">
          <i :class="menu.icon"></i>
          <span>{{ menu.menuname }}</span>
        </template>
        <el-menu-item-group>
          <template v-for="chmenu in menu.menus">
            <!-- 没有三级菜单的情况 -->
            <el-menu-item v-if="!chmenu.isExternal && chmenu.hasThird !== 'Y'" :index="'/' + chmenu.url" :key="'menu-' + chmenu.menuid">
              <i :class="chmenu.icon"></i>
              <span>{{ chmenu.menuname }}</span>
            </el-menu-item>
            <!-- 有三级菜单的情况 -->
            <el-submenu v-else-if="chmenu.hasThird === 'Y'" :index="chmenu.menuname" :key="'submenu-' + chmenu.menuid">
              <template slot="title">
                <i :class="chmenu.icon"></i>
                <span>{{ chmenu.menuname }}</span>
              </template>
              <el-menu-item v-for="thirdMenu in chmenu.menus" :key="'third-menu-' + thirdMenu.menuid" :index="'/' + thirdMenu.url">
                <i :class="thirdMenu.icon"></i>
                <span>{{ thirdMenu.menuname }}</span>
              </el-menu-item>
            </el-submenu>
            <!-- 外部链接菜单 -->
            <el-menu-item v-else :index="chmenu.url" :key="'external-' + chmenu.menuid" @click.native="handleExternalLink(chmenu.externalLink)">
              <i :class="chmenu.icon"></i>
              <span>{{ chmenu.menuname }}</span>
            </el-menu-item>
          </template>
        </el-menu-item-group>
      </el-submenu>
    </el-menu>
  </div>
</template>
<script>
import { sysMenuTree } from '@/api/SysMenu'
export default {
  name: 'leftnav',
  data() {
    return {
      collapsed: false,
      allmenu: []
    }
  },
  created() {
    this.getMenu();
    this.$root.Bus.$on('toggle', value => {
      this.collapsed = !value
    })
  },
  methods:{
    // 切换折叠状态
    toggle() {
      this.collapsed = !this.collapsed
      // 发送事件通知其他组件（如果需要的话）
      this.$root.Bus.$emit('sidebarToggle', this.collapsed)
    },
    // 处理外部链接点击
    handleExternalLink(url) {
      window.open(url, '_blank')
    },
    // 查询菜单
    getMenu(){
      // this.allmenu = this.getMockMenu();
      // return;
      sysMenuTree().then(res => {
        if (res.success) {
          this.allmenu = res.data;
          console.log("获取菜单成功", this.allmenu);
        } else {
          console.log("获取菜单失败");
          this.allmenu = this.getMockMenu();
        }
      });
    },
    getMockMenu() {
      let  mockMenuArr = [
        {
            "menuid": "01000",
            "icon": "iconfont li-icon-jichuguanli",
            "menuname": "工作台",
            "hasThird": null,
            "url": null,
            "externalLink": "0",
            "isExternal": false,
            "menus": [
                {
                    "menuid": "01010",
                    "icon": "iconfont li-icon-xitongguanli",
                    "menuname": "工作台",
                    "hasThird": null,
                    "url": "DashBoard",
                    "externalLink": "01000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "01020",
                    "icon": "iconfont icon-cs-manage",
                    "menuname": "工作笔记",
                    "hasThird": null,
                    "url": "WkbNote",
                    "externalLink": "01000",
                    "isExternal": false,
                    "menus": []
                }
            ]
        },
        {
            "menuid": "02000",
            "icon": "iconfont li-icon-jichuguanli",
            "menuname": "系统管理",
            "hasThird": null,
            "url": null,
            "externalLink": "0",
            "isExternal": false,
            "menus": [
                {
                    "menuid": "02010",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "公告管理",
                    "hasThird": null,
                    "url": "SysNotification",
                    "externalLink": "02000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "02020",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "系统参数",
                    "hasThird": null,
                    "url": "SysParam",
                    "externalLink": "02000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "02030",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "菜单与权限",
                    "hasThird": null,
                    "url": "SysMenu",
                    "externalLink": "02000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "02040",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "字典管理",
                    "hasThird": null,
                    "url": "SysDictItem",
                    "externalLink": "02000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "02050",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "系统日志",
                    "hasThird": null,
                    "url": "SysLog",
                    "externalLink": "02000",
                    "isExternal": false,
                    "menus": []
                }
            ]
        },
        {
            "menuid": "03000",
            "icon": "iconfont li-icon-jichuguanli",
            "menuname": "人员与店铺",
            "hasThird": null,
            "url": null,
            "externalLink": "0",
            "isExternal": false,
            "menus": [
                {
                    "menuid": "03010",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "人员管理",
                    "hasThird": null,
                    "url": "SysUser",
                    "externalLink": "03000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "03020",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "店铺管理",
                    "hasThird": null,
                    "url": "SysShop",
                    "externalLink": "03000",
                    "isExternal": false,
                    "menus": []
                }
            ]
        },
        {
            "menuid": "04000",
            "icon": "iconfont li-icon-jichuguanli",
            "menuname": "排班与考勤",
            "hasThird": null,
            "url": null,
            "externalLink": "0",
            "isExternal": false,
            "menus": [
                {
                    "menuid": "04010",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "排班管理",
                    "hasThird": null,
                    "url": "SysShift",
                    "externalLink": "04000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "04020",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "考勤管理",
                    "hasThird": null,
                    "url": "SysAttendance",
                    "externalLink": "04000",
                    "isExternal": false,
                    "menus": []
                }
            ]
        },
        {
            "menuid": "05000",
            "icon": "iconfont li-icon-jichuguanli",
            "menuname": "结算管理",
            "hasThird": null,
            "url": null,
            "externalLink": "0",
            "isExternal": false,
            "menus": [
                {
                    "menuid": "05010",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "采购结算管理",
                    "hasThird": null,
                    "url": "StlPurchase",
                    "externalLink": "05000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "05020",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "退款结算管理",
                    "hasThird": null,
                    "url": "StlRefund",
                    "externalLink": "05000",
                    "isExternal": false,
                    "menus": []
                }
            ]
        },
        {
            "menuid": "06000",
            "icon": "iconfont li-icon-jichuguanli",
            "menuname": "订单管理",
            "hasThird": null,
            "url": null,
            "externalLink": "0",
            "isExternal": false,
            "menus": [
                {
                    "menuid": "06010",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "货物已出库售后表",
                    "hasThird": null,
                    "url": "OrdGoodsAfterSales",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06020",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "货物已入库未出库表",
                    "hasThird": null,
                    "url": "OrdGoodsNotOutbound",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06030",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "货物已入库重新采购表",
                    "hasThird": null,
                    "url": "OrdGoodsRepurchase",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06040",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "物流理赔表",
                    "hasThird": null,
                    "url": "OrdLogisticsClaim",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06050",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "物流编号未完整填写表",
                    "hasThird": null,
                    "url": "OrdLogisticsIncomplete",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06060",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "物流编号已填未入库表",
                    "hasThird": null,
                    "url": "OrdLogisticsNotWarehoused",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06070",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "采购登记表",
                    "hasThird": null,
                    "url": "OrdPurchase",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06080",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "采购但出库前取消表",
                    "hasThird": null,
                    "url": "OrdPurchaseButCancelled",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06090",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "集中采购登记表",
                    "hasThird": null,
                    "url": "OrdPurchaseCentralized",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                },
                {
                    "menuid": "06100",
                    "icon": "iconfont li-icon-xiangmuguanli",
                    "menuname": "台湾上架物品表",
                    "hasThird": null,
                    "url": "OrdTaiwan",
                    "externalLink": "06000",
                    "isExternal": false,
                    "menus": []
                }
            ]
        }
      ];
      return mockMenuArr;
    }
  }
}
</script>
<style>
.leftnav-container {
  height: 100%;
  /* background-color: rgb(51, 65, 87); */
  display: flex;
  flex-direction: column;
}

.collapse-button {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(51, 65, 87);
  color: #fff;
  cursor: pointer;
  border-bottom: 1px solid #1f2d3d;
  transition: background-color 0.3s;
  flex-shrink: 0;
}

.collapse-button:hover {
  background-color: #1f2d3d;
}

.collapse-button i {
  font-size: 18px;
  transition: transform 0.3s;
}

.el-menu-vertical-demo {
  flex: 1;
  overflow-y: auto;
  border: none !important;
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 250px;
  border: none;
  text-align: left;
}

.el-menu-item-group__title {
  padding: 0px;
}

.el-menu-bg {
  background-color: #1f2d3d !important;
}

.el-menu {
  border: none;
}

.logobox {
  height: 20px;
  line-height: 40px;
  color: #9d9d9d;
  font-size: 16px;
  text-align: center;
  padding: 15px 12% 20px 12%;
}

.logoimg {
  height: 40px;
}

.external-link-item {
  padding: 0 20px;
  font-size: 14px;
  color: #fff;
  height: 56px;
  line-height: 56px;
  cursor: pointer;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.external-link-item:hover {
  color: #ffd04b;
  background-color: #263445;
}

/* 折叠状态下的菜单样式 */
.el-menu--collapse .el-menu-item,
.el-menu--collapse .el-submenu > .el-submenu__title {
  padding: 0 20px !important;
  text-align: center;
}

.el-menu--collapse .el-menu-item span,
.el-menu--collapse .el-submenu__title span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

.el-menu-item [class^=el-icon-] {
  margin-right: -2px;
}
</style>


