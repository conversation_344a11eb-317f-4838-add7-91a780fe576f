<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdTaiwanMapper">


	<!-- 分页 -->
    <select id="defaultPage" resultType="com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanPageVO">
		SELECT
			t1.order_item_id,
			t1.amount,
			t1.waybill_number,
			t1.status,
			t1.warehouse_number,
			t1.online_time,
			t1.offline_time,
			t3.shop_name as originalShopName,
			t3.shop_id as originalShopId,
			t3.order_sn as originalOrderNumber,
			t2.item_name as productName,
			t2.item_model_name as productSpec
		FROM
			ord_taiwan AS t1
		LEFT JOIN erp_order_item AS t2 ON t1.order_item_id = t2.id
		LEFT JOIN erp_order AS t3 ON t2.order_id = t3.order_id
		<where>
        	1=1
	        <if test="orderItemId != null and orderItemId != ''">
	           	AND t1.order_item_id = #{orderItemId}
            </if>
	        <if test="amount != null and amount != ''">
	           	AND t1.amount = #{amount}
            </if>
	        <if test="waybillNumber != null and waybillNumber != ''">
	           	AND t1.waybill_number = #{waybillNumber}
            </if>
	        <if test="status != null and status != ''">
	           	AND t1.status = #{status}
            </if>
	        <if test="warehouseNumber != null and warehouseNumber != ''">
	           	AND t1.warehouse_number = #{warehouseNumber}
            </if>
	        <if test="onlineTime != null and onlineTime != ''">
	           	AND t1.online_time = #{onlineTime}
            </if>
	        <if test="offlineTime != null and offlineTime != ''">
	           	AND t1.offline_time = #{offlineTime}
            </if>
	        <if test="originalOrderNumber != null and originalOrderNumber != ''">
	           	AND t3.order_sn = #{originalOrderNumber}
            </if>
	        <if test="productName != null and productName != ''">
	           	AND t2.item_name LIKE CONCAT('%', #{productName}, '%')
            </if>
	        <if test="shopIds != null and shopIds.size() > 0">
	           	AND t3.shop_id IN
	           	<foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
	           		#{shopId}
	           	</foreach>
            </if>
        </where>
        ORDER BY t3.order_sn DESC
    </select>

	<!-- 分页 -->
    <select id="page" resultType="com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanPageVO">
		SELECT
			t1.order_item_id,
			t1.amount,
			t1.waybill_number,
			t1.status,
			t1.warehouse_number,
			t1.online_time,
			t1.offline_time,
			t3.shop_name as originalShopName,
			t3.shop_id as originalShopId,
			t3.order_sn as originalOrderNumber,
			t2.item_name as productName,
			t2.item_model_name as productSpec,
			t2.item_image as productImage,
			t2.item_price as productPrice
		FROM
			ord_taiwan AS t1
		LEFT JOIN erp_order_item AS t2 ON t1.order_item_id = t2.id
		LEFT JOIN erp_order AS t3 ON t2.order_id = t3.order_id
		<where>
        	1=1
	        <if test="orderItemId != null and orderItemId != ''">
	           	AND t1.order_item_id = #{orderItemId}
            </if>
	        <if test="amount != null and amount != ''">
	           	AND t1.amount = #{amount}
            </if>
	        <if test="waybillNumber != null and waybillNumber != ''">
	           	AND t1.waybill_number = #{waybillNumber}
            </if>
	        <if test="status != null and status != ''">
	           	AND t1.status = #{status}
            </if>
	        <if test="warehouseNumber != null and warehouseNumber != ''">
	           	AND t1.warehouse_number = #{warehouseNumber}
            </if>
	        <if test="onlineTime != null and onlineTime != ''">
	           	AND t1.online_time = #{onlineTime}
            </if>
	        <if test="offlineTime != null and offlineTime != ''">
	           	AND t1.offline_time = #{offlineTime}
            </if>
	        <if test="originalOrderNumber != null and originalOrderNumber != ''">
	           	AND t3.order_sn = #{originalOrderNumber}
            </if>
	        <if test="productName != null and productName != ''">
	           	AND t2.item_name LIKE CONCAT('%', #{productName}, '%')
            </if>
	        <if test="shopIds != null and shopIds.size() > 0">
	           	AND t3.shop_id IN
	           	<foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
	           		#{shopId}
	           	</foreach>
            </if>
        </where>
        ORDER BY t3.order_sn DESC
    </select>

</mapper>
