package com.my.crossborder.controller.dto.sys_exchange_rate;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_exchange_rate.SysExchangeRatePageVO;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_汇率表
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysExchangeRatePageDTO 
						extends PageDTO<SysExchangeRatePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
    private LocalDate day;

    /**
     * 汇率（CNY/TWD的值，比如：4.0814）
     */
    private BigDecimal exchangeRate;

}
