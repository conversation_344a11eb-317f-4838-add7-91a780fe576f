package com.my.crossborder.controller.vo.stl_purchase;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_结算_采购结算表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class StlPurchaseDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 采购员工ID
     */
    private Integer purchaseUserId;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 结算日期
     */
    private LocalDateTime settlementDate;

    /**
     * 备注( 使用常见标签快速填写）
     */
    private String remark;

    /**
     * 结算操作员用户id
     */
    private Integer createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
