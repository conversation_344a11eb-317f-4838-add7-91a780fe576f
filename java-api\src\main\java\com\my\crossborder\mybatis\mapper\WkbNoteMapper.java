package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.wkb_note.WkbNotePageDTO;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;
import com.my.crossborder.controller.vo.wkb_note.WkbNotePageVO;
import com.my.crossborder.mybatis.entity.WkbNote;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 工作台_工作笔记表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface WkbNoteMapper extends BaseMapper<WkbNote> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<WkbNotePageVO> page(WkbNotePageDTO pageDTO);
	
	/**
	 * 根据订单号查询所有笔记
	 * @param orderSn 订单号
	 * @return 笔记列表
	 */
	List<WkbNoteDetailVO> selectNotesByOrderSn(String orderSn);

	/**
	 * 批量查询订单备注信息
	 *
	 * @param orderSnList 订单号集合
	 * @return 备注信息列表
	 */
	List<WkbNoteDetailVO> selectByOrderSnSet(@Param("orderSnList") Set<String> orderSnList);
}
