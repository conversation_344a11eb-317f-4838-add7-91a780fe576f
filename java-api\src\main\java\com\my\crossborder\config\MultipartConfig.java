package com.my.crossborder.config;

import javax.servlet.MultipartConfigElement;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import com.my.crossborder.service.SysParamService;

import lombok.RequiredArgsConstructor;

/**
 * 文件上传大小限制
 *
 */
@Configuration
@RequiredArgsConstructor
public class MultipartConfig {

    private final SysParamService sysParamService;

    
	// 运维请注意: nginx和springboot都有请求最大限制
    @Bean
    public MultipartConfigElement multipartConfigElement() {
		String fileMaxSize = this.sysParamService.get().getFileMaxSize();
		Integer maxSize = new Integer(fileMaxSize);
        MultipartConfigFactory factory = new MultipartConfigFactory();
        // 单个文件最大(MB)
        factory.setMaxFileSize(DataSize.ofMegabytes(maxSize));
        // 设置总上传数据总大小
        factory.setMaxRequestSize(DataSize.ofMegabytes(maxSize));
        return factory.createMultipartConfig();
    }
}
