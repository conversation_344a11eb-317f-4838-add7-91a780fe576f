import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 禾宸账号管理API
 */

// 分页查询禾宸账号列表
export function pageSysErpAccount(params) {
  return reqGet('/sys-erp-account/page', params)
}

// 根据ID查询禾宸账号详情
export function getSysErpAccountById(id) {
  return reqGet(`/sys-erp-account/${id}`)
}

// 新增禾宸账号
export function insertSysErpAccount(data) {
  return reqPost('/sys-erp-account', data)
}

// 修改禾宸账号
export function updateSysErpAccount(data) {
  return reqPut('/sys-erp-account', data)
}

// 删除禾宸账号
export function deleteSysErpAccount(data) {
  return reqDelete('/sys-erp-account', data)
}
