package com.my.crossborder.controller;


import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_log.SysLogPageDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_log.SysLogDetailVO;
import com.my.crossborder.controller.vo.sys_log.SysLogPageVO;
import com.my.crossborder.service.SysLogService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 操作日志表 
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@RestController
@RequestMapping("/api/sys-log")
@RequiredArgsConstructor
public class SysLogController {

    private final SysLogService sysLogService;

            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @SaCheckPermission("sys-log:view")
    @GetMapping("/{id}")
    public StdResp<SysLogDetailVO> detail(@PathVariable Long id) {
    	return StdResp.success(this.sysLogService.detail(id));
    }
	
    /**
     * 分页
     */
    @SaCheckPermission("sys-log:view")
    @GetMapping(value = "page")
    public StdResp<Page<SysLogPageVO>> page(SysLogPageDTO pageDTO) {
        Page<SysLogPageVO> page = this.sysLogService.page(pageDTO);
        return StdResp.success(page);
    }
    
    
}