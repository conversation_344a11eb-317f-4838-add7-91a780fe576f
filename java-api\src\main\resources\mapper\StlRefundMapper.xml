<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.StlRefundMapper">

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.stl_refund.StlRefundPageVO">
		SELECT
			id, refund_user_id, refund_date_start, refund_date_end, settlement_amount, settlement_date, remark, create_user_id, create_time
		FROM
			stl_refund AS t1
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="refundUserId != null and refundUserId != ''">
	           	AND t1.refund_user_id = #{refundUserId}
            </if>
	        <if test="refundDateStart != null and refundDateStart != ''">
	           	AND t1.refund_date_start = #{refundDateStart}
            </if>
	        <if test="refundDateEnd != null and refundDateEnd != ''">
	           	AND t1.refund_date_end = #{refundDateEnd}
            </if>
	        <if test="settlementAmount != null and settlementAmount != ''">
	           	AND t1.settlement_amount = #{settlementAmount}
            </if>
	        <if test="settlementDate != null and settlementDate != ''">
	           	AND t1.settlement_date = #{settlementDate}
            </if>
	        <if test="remark != null and remark != ''">
	           	AND t1.remark = #{remark}
            </if>
	        <if test="createUserId != null and createUserId != ''">
	           	AND t1.create_user_id = #{createUserId}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
        </where>
    </select>

</mapper>
