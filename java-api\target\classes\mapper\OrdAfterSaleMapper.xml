<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdAfterSaleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.OrdAfterSale">
        <id column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="issue_type" property="issueType" />
        <result column="close_way" property="closeWay" />
        <result column="close_status" property="closeStatus" />
        <result column="refund_amount" property="refundAmount" />
        <result column="settlement_amount" property="settlementAmount" />
        <result column="new_order_sn" property="newOrderSn" />
        <result column="issue_time" property="issueTime" />
        <result column="close_time" property="closeTime" />
        <result column="issue_user_id" property="issueUserId" />
        <result column="close_user_id" property="closeUserId" />
        <result column="confirm_user_id" property="confirmUserId" />
        <result column="confirm_time" property="confirmTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_sn, issue_type, close_way, close_status, refund_amount, settlement_amount, new_order_sn, issue_time, close_time, issue_user_id, close_user_id, confirm_user_id, confirm_time
    </sql>

	<!-- 分页 -->
    <select id="page" resultType="com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSalePageVO">
		SELECT
			t1.id,
			t1.order_sn,
			t1.issue_type,
			t1.close_way,
			t1.close_status,
			t1.refund_amount,
			t1.settlement_amount,
			t1.new_order_sn,
			t1.issue_time,
			t1.issue_user_id,
			t1.close_user_id,
			(select real_name from sys_user where user_id = t1.issue_user_id) as issue_user_name,
			(select real_name from sys_user where user_id = t1.close_user_id) as close_user_name,
			(select real_name from sys_user where user_id = t1.confirm_user_id) as confirm_user_name,
			t1.close_time,
			t1.confirm_user_id,
			t1.confirm_time,
			COALESCE ( t2.shop_name, '未知店铺' ) AS shop_name,
			t2.shop_id,
			t2.order_id,
			t2.create_time
		FROM
			ord_after_sale AS t1
			LEFT JOIN erp_order AS t2 ON t1.order_sn = t2.order_sn
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="orderSn != null and orderSn != ''">
	           	AND t1.order_sn LIKE CONCAT('%', #{orderSn}, '%')
            </if>
	        <if test="shopId != null and shopId != ''">
	           	AND t2.shop_id = #{shopId}
            </if>
	        <if test="issueType != null and issueType != ''">
	           	AND t1.issue_type = #{issueType}
            </if>
	        <if test="refundAmount != null and refundAmount != ''">
	           	AND t1.refund_amount = #{refundAmount}
            </if>
	        <if test="issueTime != null and issueTime != ''">
	           	AND t1.issue_time = #{issueTime}
            </if>
	        <if test="closeTime != null and closeTime != ''">
	           	AND t1.close_time = #{closeTime}
            </if>
	        <if test="issueUserId != null and issueUserId != ''">
	           	AND t1.issue_user_id = #{issueUserId}
            </if>
	        <if test="closeUserId != null and closeUserId != ''">
	           	AND t1.close_user_id = #{closeUserId}
            </if>
	        <if test="confirmUserId != null and confirmUserId != ''">
	           	AND t1.confirm_user_id = #{confirmUserId}
            </if>
			<if test="closeWay != null and closeWay != ''">
                AND t1.close_way = #{closeWay}
            </if>
            <if test="closeStatus != null and closeStatus != ''">
                AND t1.close_status = #{closeStatus}
            </if>
        </where>
        ORDER BY t1.id DESC
    </select>

</mapper>
