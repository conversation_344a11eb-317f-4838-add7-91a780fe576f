package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.OrdRefund;
import com.my.crossborder.mybatis.mapper.OrdRefundMapper;
import com.my.crossborder.service.OrdRefundService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_refund.OrdRefundInsertDTO;
import com.my.crossborder.controller.dto.ord_refund.OrdRefundPageDTO;
import com.my.crossborder.controller.dto.ord_refund.OrdRefundUpdateDTO;
import com.my.crossborder.controller.dto.ord_refund.OrdRefundDeleteDTO;
import com.my.crossborder.controller.dto.ord_refund.ApplyRefundDTO;
import com.my.crossborder.controller.dto.ord_refund.ApplyPutInDTO;
import com.my.crossborder.controller.dto.ord_refund.RefundResultDTO;
import com.my.crossborder.controller.dto.ord_refund.ConfirmPutInDTO;
import com.my.crossborder.controller.dto.ord_refund.WeeklySettlementStatsDTO;
import com.my.crossborder.controller.vo.ord_refund.OrdRefundDetailVO;
import com.my.crossborder.controller.vo.ord_refund.OrdRefundPageVO;
import com.my.crossborder.controller.vo.ord_refund.WeeklySettlementStatsVO;
import com.my.crossborder.enums.RefundApplyStatus;
import com.my.crossborder.enums.RefundResultStatus;
import com.my.crossborder.enums.RefundSettlementStatus;
import com.my.crossborder.controller.dto.ord_refund_log.OrdRefundLogInsertDTO;
import com.my.crossborder.service.OrdRefundLogService;
import com.my.crossborder.service.ErpOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.DayOfWeek;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 退款表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
@RequiredArgsConstructor
public class OrdRefundServiceImpl extends ServiceImpl<OrdRefundMapper, OrdRefund> implements OrdRefundService {

	private final OrdRefundLogService ordRefundLogService;
	private final ErpOrderService erpOrderService;


	@Override
	public Page<OrdRefundPageVO> page(OrdRefundPageDTO pageDTO) {
		Page<OrdRefundPageVO> page = this.baseMapper.page(pageDTO);
		this.erpOrderService.batchSetOrderItemsAndNotes(page);
		return page;
	}

	@Transactional
	@Override
	public void delete(OrdRefundDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Transactional
	@Override
	public void applyRefund(ApplyRefundDTO applyRefundDTO) {
		String orderSn = applyRefundDTO.getOrderSn();
		Integer currentUserId = StpUtil.getLoginIdAsInt();
		LocalDateTime now = LocalDateTime.now();

		// saveOrUpdate
		OrdRefund refund = this.getById(orderSn);
		if (refund == null) {
			// 新建
			refund = OrdRefund.builder()
					.orderSn(orderSn)
					.applyStatus(RefundApplyStatus.APPLIED.getValue()) 
					.applyAmount(applyRefundDTO.getApplyAmount())
					.applyUserId(currentUserId)
					.applyTime(now)
					.settlementStatus(RefundSettlementStatus.NO_NEED.getValue())
					.build();
			this.save(refund);
		} else {
			// 更新
			refund.setApplyStatus(RefundApplyStatus.APPLIED.getValue()); 
			refund.setApplyAmount(applyRefundDTO.getApplyAmount());
			refund.setApplyUserId(currentUserId);
			refund.setApplyTime(now);
			this.updateById(refund);
		}

		// 记录日志
		OrdRefundLogInsertDTO logDTO = OrdRefundLogInsertDTO.builder()
				.orderSn(orderSn)
				.opName("申请退款")
				.opDetail("申请退款金额：" + applyRefundDTO.getApplyAmount())
				.opUserId(currentUserId)
				.opTime(now)
				.build();
		this.ordRefundLogService.insert(logDTO);
	}

	@Transactional
	@Override
	public void applyPutIn(ApplyPutInDTO applyPutInDTO) {
		String orderSn = applyPutInDTO.getOrderSn();
		Integer currentUserId = StpUtil.getLoginIdAsInt();
		LocalDateTime now = LocalDateTime.now();

		// saveOrUpdate
		OrdRefund refund = this.getById(orderSn);
		if (refund == null) {
			// 新建记录
			refund = OrdRefund.builder()
					.orderSn(orderSn)
					.applyStatus(RefundApplyStatus.PUT_IN_STORAGE.getValue()) 
					.applyUserId(currentUserId)
					.applyTime(now)
					.settlementStatus(RefundSettlementStatus.NO_NEED.getValue())
					.build();
			this.save(refund);
		} else {
			// 更新
			refund.setApplyStatus(RefundApplyStatus.PUT_IN_STORAGE.getValue()); // 不退采买改为入库
			refund.setApplyUserId(currentUserId);
			refund.setApplyTime(now);
			this.updateById(refund);
		}

		// 记录日志
		OrdRefundLogInsertDTO logDTO = OrdRefundLogInsertDTO.builder()
				.orderSn(orderSn)
				.opName("申请入库")
				.opDetail("申请将订单商品入库")
				.opUserId(currentUserId)
				.opTime(now)
				.build();
		this.ordRefundLogService.insert(logDTO);
	}

	@Transactional
	@Override
	public void refundResult(RefundResultDTO refundResultDTO) {
		String orderSn = refundResultDTO.getOrderSn();
		Integer currentUserId = StpUtil.getLoginIdAsInt();
		LocalDateTime now = LocalDateTime.now();

		// 获取退款记录
		OrdRefund refund = this.getById(orderSn);
		if (refund == null) {
			throw new RuntimeException("退款记录不存在");
		}

		// 更新退款结果
		if (refundResultDTO.getSuccess()) {
			refund.setResultStatus(RefundResultStatus.REFUND_SUCCESS.getValue()); // 退款成功
			refund.setRefundSuccessAmount(refundResultDTO.getRefundSuccessAmount());
			refund.setSettlementStatus(RefundSettlementStatus.PENDING.getValue()); // 待结算
		} else {
			refund.setResultStatus(RefundResultStatus.REFUND_FAILED.getValue()); // 退款失败
			refund.setRefundFailReason(refundResultDTO.getRefundFailReason());
			refund.setSettlementStatus(RefundSettlementStatus.NO_NEED.getValue());
		}
		refund.setResultTime(now);
		refund.setResultUserId(currentUserId);
		this.updateById(refund);

		// 记录日志
		String opDetail = refundResultDTO.getSuccess()
				? "退款成功，金额：" + refundResultDTO.getRefundSuccessAmount()
				: "退款失败，原因：" + refundResultDTO.getRefundFailReason();

		OrdRefundLogInsertDTO logDTO = OrdRefundLogInsertDTO.builder()
				.orderSn(orderSn)
				.opName("退款结果")
				.opDetail(opDetail)
				.opUserId(currentUserId)
				.opTime(now)
				.build();
		this.ordRefundLogService.insert(logDTO);
	}

	@Transactional
	@Override
	public void confirmPutIn(ConfirmPutInDTO confirmPutInDTO) {
		String orderSn = confirmPutInDTO.getOrderSn();
		Integer currentUserId = StpUtil.getLoginIdAsInt();
		LocalDateTime now = LocalDateTime.now();

		// 获取退款记录
		OrdRefund refund = this.getById(orderSn);
		if (refund == null) {
			throw new RuntimeException("退款记录不存在");
		}

		// 更新为确认已入库
		refund.setResultStatus(RefundResultStatus.CONFIRMED_IN_STORAGE.getValue()); // 确认已入库
		refund.setResultTime(now);
		refund.setResultUserId(currentUserId);
		refund.setSettlementStatus(RefundSettlementStatus.NO_NEED.getValue());
		this.updateById(refund);

		// 记录日志
		OrdRefundLogInsertDTO logDTO = OrdRefundLogInsertDTO.builder()
				.orderSn(orderSn)
				.opName("确认已入库")
				.opDetail("确认商品已入库")
				.opUserId(currentUserId)
				.opTime(now)
				.build();
		this.ordRefundLogService.insert(logDTO);
	}

	@Override
	public List<WeeklySettlementStatsVO> getWeeklySettlementStats(WeeklySettlementStatsDTO dto) {
		// 计算指定月份的自然周
		List<WeeklySettlementStatsVO> weeks = calculateNaturalWeeksInMonth(dto.getYear(), dto.getMonth());

		// 查询该员工在指定月份的退款数据
		List<OrdRefund> refundData = getRefundDataByUserAndMonth(dto.getUserId(), dto.getYear(), dto.getMonth());

		// 按周分组统计数据
		Map<Integer, List<OrdRefund>> weeklyData = groupRefundDataByWeek(refundData, weeks);

		// 填充统计信息
		for (WeeklySettlementStatsVO week : weeks) {
			List<OrdRefund> weekRefunds = weeklyData.getOrDefault(week.getWeekNumber(), new ArrayList<>());
			fillWeekStatistics(week, weekRefunds);
		}

		return weeks;
	}

	@Override
	public void settlementDone(Integer userId, LocalDate dateStart, LocalDate dateEnd) {
		// 更新该周期内的所有退款记录的结算状态
		this.lambdaUpdate()
				.eq(OrdRefund::getApplyUserId, userId)
				.between(OrdRefund::getResultTime, dateStart, dateEnd)
				.eq(OrdRefund::getResultStatus, RefundResultStatus.REFUND_SUCCESS.getValue())
				.set(OrdRefund::getSettlementStatus, RefundSettlementStatus.DONE.getValue())
				.update();
	}

	/**
	 * 计算指定月份的自然周（周一开始）
	 */
	private List<WeeklySettlementStatsVO> calculateNaturalWeeksInMonth(Integer year, Integer month) {
		List<WeeklySettlementStatsVO> weeks = new ArrayList<>();
		LocalDate now = LocalDate.now();

		// 获取月份第一天和最后一天
		LocalDate firstDay = LocalDate.of(year, month, 1);
		LocalDate lastDay = firstDay.withDayOfMonth(firstDay.lengthOfMonth());

		// 计算第一周的开始日期（周一）
		LocalDate firstMondayDate = firstDay;
		if (firstDay.getDayOfWeek() != DayOfWeek.MONDAY) {
			firstMondayDate = firstDay.minusDays(firstDay.getDayOfWeek().getValue() - 1);
		}

		int weekNumber = 1;
		LocalDate currentWeekStart = firstMondayDate;

		while (currentWeekStart.isBefore(lastDay) || currentWeekStart.isEqual(lastDay)) {
			LocalDate currentWeekEnd = currentWeekStart.plusDays(6);

			// 判断是否为当前周
			boolean isCurrentWeek = (year.equals(now.getYear()) && month.equals(now.getMonthValue()))
					&& (!now.isBefore(currentWeekStart) && !now.isAfter(currentWeekEnd));

			WeeklySettlementStatsVO week = WeeklySettlementStatsVO.builder()
					.weekNumber(weekNumber)
					.startDate(currentWeekStart)
					.endDate(currentWeekEnd)
					.dateRange(formatDateRange(currentWeekStart, currentWeekEnd))
					.isCurrentWeek(isCurrentWeek)
					.settlementStatus(RefundSettlementStatus.NO_NEED.getValue()) // 默认无需结算
					.totalAmount(BigDecimal.ZERO)
					.settledAmount(BigDecimal.ZERO)
					.unsettledAmount(BigDecimal.ZERO)
					.totalCount(0)
					.settledCount(0)
					.unsettledCount(0)
					.build();

			weeks.add(week);
			weekNumber++;
			currentWeekStart = currentWeekStart.plusDays(7);
		}

		return weeks;
	}

	/**
	 * 格式化日期范围
	 */
	private String formatDateRange(LocalDate startDate, LocalDate endDate) {
		return String.format("%d月%d日 - %d月%d日",
				startDate.getMonthValue(), startDate.getDayOfMonth(),
				endDate.getMonthValue(), endDate.getDayOfMonth());
	}

	/**
	 * 查询员工在指定月份的退款数据
	 */
	private List<OrdRefund> getRefundDataByUserAndMonth(Integer userId, Integer year, Integer month) {
		LocalDate startDate = LocalDate.of(year, month, 1);
		LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
		LocalDateTime startDateTime = startDate.atStartOfDay();
		LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

		return this.lambdaQuery()
				.eq(OrdRefund::getApplyUserId, userId)
				.between(OrdRefund::getResultTime, startDateTime, endDateTime)
				.eq(OrdRefund::getResultStatus, RefundResultStatus.REFUND_SUCCESS.getValue())
				.list();
	}

	/**
	 * 按周分组退款数据
	 */
	private Map<Integer, List<OrdRefund>> groupRefundDataByWeek(List<OrdRefund> refundData, List<WeeklySettlementStatsVO> weeks) {
		Map<Integer, List<OrdRefund>> weeklyData = new java.util.HashMap<>();

		for (OrdRefund refund : refundData) {
			if (refund.getResultTime() != null) {
				LocalDate refundDate = refund.getResultTime().toLocalDate();

				// 找到该日期属于哪一周
				for (WeeklySettlementStatsVO week : weeks) {
					if (!refundDate.isBefore(week.getStartDate()) && !refundDate.isAfter(week.getEndDate())) {
						weeklyData.computeIfAbsent(week.getWeekNumber(), k -> new ArrayList<>()).add(refund);
						break;
					}
				}
			}
		}

		return weeklyData;
	}

	/**
	 * 填充周统计信息
	 */
	private void fillWeekStatistics(WeeklySettlementStatsVO week, List<OrdRefund> weekRefunds) {
		if (weekRefunds.isEmpty()) {
			week.setSettlementStatus(RefundSettlementStatus.NO_NEED.getValue()); // 无需结算
			return;
		}

		BigDecimal totalAmount = BigDecimal.ZERO;
		BigDecimal settledAmount = BigDecimal.ZERO;
		BigDecimal unsettledAmount = BigDecimal.ZERO;
		int settledCount = 0;
		int unsettledCount = 0;
		int noNeedCount = 0;

		for (OrdRefund refund : weekRefunds) {
			BigDecimal amount = refund.getRefundSuccessAmount() != null ? refund.getRefundSuccessAmount() : BigDecimal.ZERO;
			totalAmount = totalAmount.add(amount);

			String settlementStatus = refund.getSettlementStatus();
			if (RefundSettlementStatus.DONE.getValue().equals(settlementStatus)) {
				settledAmount = settledAmount.add(amount);
				settledCount++;
			} else if (RefundSettlementStatus.PENDING.getValue().equals(settlementStatus)) {
				unsettledAmount = unsettledAmount.add(amount);
				unsettledCount++;
			} else {
				// 无需结算的记录
				noNeedCount++;
			}
		}

		week.setTotalAmount(totalAmount);
		week.setSettledAmount(settledAmount);
		week.setUnsettledAmount(unsettledAmount);
		week.setTotalCount(weekRefunds.size());
		week.setSettledCount(settledCount);
		week.setUnsettledCount(unsettledCount);

		// 设置整体结算状态逻辑：
		// 1. 如果全部都是无需结算，则整体为无需结算
		// 2. 如果有待结算的，则整体为待结算
		// 3. 如果没有待结算的，但有已结算的，则整体为已结算
		if (noNeedCount == weekRefunds.size()) {
			week.setSettlementStatus(RefundSettlementStatus.NO_NEED.getValue()); // 无需结算
		} else if (unsettledCount > 0) {
			week.setSettlementStatus(RefundSettlementStatus.PENDING.getValue()); // 待结算
		} else {
			week.setSettlementStatus(RefundSettlementStatus.DONE.getValue()); // 已结算
		}
	}

}
