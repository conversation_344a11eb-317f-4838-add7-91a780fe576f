package com.my.crossborder.forest.siliconflow;

import java.math.BigDecimal;
import java.util.List;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.dtflys.forest.http.ForestRequest;
import com.my.crossborder.controller.vo.sys_param.SysParamVO;
import com.my.crossborder.forest.siliconflow.dto.ChatCompletionsDTO;
import com.my.crossborder.forest.siliconflow.dto.component.ImageMessage;
import com.my.crossborder.forest.siliconflow.dto.component.Message;
import com.my.crossborder.forest.siliconflow.dto.component.TextMessage;
import com.my.crossborder.forest.siliconflow.onerrror.SiliconflowOnError;
import com.my.crossborder.forest.siliconflow.vo.ChatCompletionsVO;
import com.my.crossborder.service.SysParamService;

import lombok.extern.slf4j.Slf4j;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class SiliconflowClientTest {

	@Autowired
	SiliconflowClient siliconflowClient;
	
	@Autowired
	SysParamService sysParamService;
	
	
	String apiKey;
	SiliconflowOnError onErr;
	
	@BeforeEach
	void init() {
		this.apiKey = "sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud";
		this.onErr = new SiliconflowOnError();
	}
	
	/**
	 * 对话补全(文字参数)
	 */
	@Test
	void testTextChatCompletions() {
		// 系统参数
		SysParamVO sysParam = this.sysParamService.get();
		String model = sysParam.getAiModel();
		String aiApiKey = sysParam.getAiApiKey();
		String aiUserPrompt = sysParam.getAiUserPrompt();
		// DTO参数
		Double temperature = 0.01D;
		aiUserPrompt = aiUserPrompt.replaceAll("\\$\\{content\\}", java.util.regex.Matcher.quoteReplacement(""));
		List<Message> messages = Lists.newArrayList(
			TextMessage.user(aiUserPrompt)
		);
		ChatCompletionsDTO chatCompletionsDTO = ChatCompletionsDTO.builder()
			.model(model)
			.messages(messages)
			.temperature(temperature) 
			.build();
		// 请求
		ChatCompletionsVO vo = this.siliconflowClient.chatCompletions(chatCompletionsDTO, aiApiKey, this.onErr);
		if (this.onErr.isError()) {
			log.info(this.onErr.getErrMsg());
		} else {
			log.info(JSON.toJSONString(vo));
		}
	}
	
	/**
	 * 对话补全(图片参数)
	 */
	@Test
	void testImageChatCompletions() {
		// 系统参数
		SysParamVO sysParamVO = this.sysParamService.get();
		String model = sysParamVO.getAiModel();
		String aiApiKey = sysParamVO.getAiApiKey();
		String aiUserPrompt = sysParamVO.getAiUserPrompt();
		// DTO参数
		String base64Image = "data:image/jpeg;base64,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";
		List<Message> messages = Lists.newArrayList(
			ImageMessage.user(base64Image, aiUserPrompt)
		);
		ChatCompletionsDTO chatCompletionsDTO = ChatCompletionsDTO.builder()
				.model(model)
				.messages(messages)
				.build();
		// 请求
		ChatCompletionsVO vo = this.siliconflowClient.chatCompletions(chatCompletionsDTO, aiApiKey, this.onErr);
		if (this.onErr.isError()) {
			log.info(this.onErr.getErrMsg());
		} else {
			log.info(JSON.toJSONString(vo));
		}
	}

	@Test
	void testGetBalance() {
		BigDecimal balance = this.siliconflowClient.getBalance(this.apiKey, this.onErr);
		log.info("{} 元", balance);
	}
	
	@Test
	void testWrongApiKey() {
		BigDecimal balance = this.siliconflowClient.getBalance("heheh", this.onErr);
		if (this.onErr.isError()) {
			log.info(this.onErr.getErrMsg());
		} else {
			log.info("{} 元", balance);
		}
	}

}
