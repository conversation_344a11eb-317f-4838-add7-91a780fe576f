package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.sys_shop_partner.SysShopPartnerPageDTO;
import com.my.crossborder.controller.vo.sys_shop_partner.SysShopPartnerPageVO;
import com.my.crossborder.mybatis.entity.SysShopPartner;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 店铺合伙人表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface SysShopPartnerMapper extends BaseMapper<SysShopPartner> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysShopPartnerPageVO> page(SysShopPartnerPageDTO pageDTO);
	
}
