package com.my.crossborder.controller.page;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.vo.StdResp;

import lombok.extern.slf4j.Slf4j;

@ControllerAdvice
@Slf4j
public class PageResponseAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true; // 或者添加判断只处理返回Page的方法
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {
        if (body instanceof StdResp) {
            StdResp<?> resp = (StdResp<?>) body;
            Object data = resp.getData();
            
            if (data instanceof Page) {
                handlePageResponse(resp, (Page<?>) data);
            }
        }
        
        return body;
    }
    
    /**
     * 处理Page类型的响应数据
     * @param resp 响应对象
     * @param page 分页对象
     */
    @SuppressWarnings("unchecked")
    private <T> void handlePageResponse(StdResp<?> resp, Page<?> page) {
        try {
            // 创建具有相同泛型类型的新Page对象
//            Page<T> typedPage = (Page<T>) page;
            Page<T> cleanPage = new Page<>();
            
            // 设置基本属性
            cleanPage.setRecords((List<T>) page.getRecords());
            cleanPage.setTotal(page.getTotal());
            cleanPage.setCurrent(page.getCurrent());
            cleanPage.setSize(page.getSize());
            cleanPage.setPages(page.getPages());
            
            // 处理需要保留字段的特殊情况
            Set<String> fieldsToKeep = getFieldsToKeep(page);
            if (!fieldsToKeep.isEmpty()) {
                // 复制需要保留的字段
                for (String fieldName : fieldsToKeep) {
                    try {
                        java.lang.reflect.Field field = getField(page.getClass(), fieldName);
                        if (field != null) {
                            field.setAccessible(true);
                            Object value = field.get(page);
                            
                            java.lang.reflect.Field targetField = getField(cleanPage.getClass(), fieldName);
                            if (targetField != null) {
                                targetField.setAccessible(true);
                                targetField.set(cleanPage, value);
                            }
                        }
                    } catch (Exception e) {
                        // 记录日志，但不中断处理
                        log.error("Error copying field: " + fieldName + ", " + e.getMessage());
                    }
                }
            }
            
            // 使用反射设置数据，避免泛型类型不匹配问题
            java.lang.reflect.Method setDataMethod = resp.getClass().getMethod("setData", Object.class);
            setDataMethod.invoke(resp, cleanPage);
        } catch (Exception e) {
            log.error("Error handling page response: " + e.getMessage());
        }
    }
    
    /**
     * 获取需要保留的字段集合
     */
    private Set<String> getFieldsToKeep(Page<?> page) {
        Set<String> fieldsToKeep = new HashSet<>();
        
        // 检查类上是否有KeepField注解
        KeepField klassAnnotation = page.getClass().getAnnotation(KeepField.class);
        if (klassAnnotation != null && klassAnnotation.value().length > 0) {
            fieldsToKeep.addAll(Arrays.asList(klassAnnotation.value()));
        }
        
        // 检查字段上是否有KeepField注解
        for (java.lang.reflect.Field field : getAllFields(page.getClass())) {
            KeepField fieldAnnotation = field.getAnnotation(KeepField.class);
            if (fieldAnnotation != null) {
                fieldsToKeep.add(field.getName());
            }
        }
        
        return fieldsToKeep;
    }
    
    /**
     * 获取类及其所有父类的字段
     */
    private List<java.lang.reflect.Field> getAllFields(Class<?> type) {
        List<java.lang.reflect.Field> fields = new ArrayList<>();
        for (Class<?> c = type; c != null; c = c.getSuperclass()) {
            fields.addAll(Arrays.asList(c.getDeclaredFields()));
        }
        return fields;
    }
    
    /**
     * 获取类中指定名称的字段，包括父类
     */
    private java.lang.reflect.Field getField(Class<?> clazz, String fieldName) {
        for (Class<?> c = clazz; c != null; c = c.getSuperclass()) {
            try {
                return c.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 继续查找父类
            }
        }
        return null;
    }
}