package com.my.crossborder.tmp;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 独立的PageVO分析工具，用于查找所有以PageVO结尾的类并打印它们是否含有id字段
 */
public class PageVOIdFieldAnalyzer {
    
    public static void main(String[] args) {
        try {
            // 指定VO类所在的目录路径
            String voDirectoryPath = "D:/git.fxxf.online/bep-pipe-system/java-api/src/main/java/com/my/crossborder/controller/vo";
            
            // 存储找到的PageVO类的信息
            List<ClassInfo> pageVOClasses = new ArrayList<>();
            
            // 扫描目录查找PageVO类
            File voDirectory = new File(voDirectoryPath);
            scanDirectoryForPageVOClasses(voDirectory, pageVOClasses);
            
            // 打印结果
            System.out.println("找到以PageVO结尾的类总数: " + pageVOClasses.size());
            System.out.println("--------------------------------------");
            
            for (ClassInfo classInfo : pageVOClasses) {
                System.out.println("类名: " + classInfo.getFullClassName());
                System.out.print("是否有id字段: " + (classInfo.hasIdField() ? "是" : "否") + "\t");
                if (classInfo.hasIdField()) {
                    System.out.println("ID字段名称: " + classInfo.getIdFieldName());
                } else {
                    System.out.println();
                }
                System.out.print("父类: " + classInfo.getParentClassName() + "\t");
                
                if (!classInfo.getInterfaces().isEmpty()) {
                    System.out.print("实现的接口: ");
                    for (int i = 0; i < classInfo.getInterfaces().size(); i++) {
                        System.out.print(classInfo.getInterfaces().get(i));
                        if (i < classInfo.getInterfaces().size() - 1) {
                            System.out.print(", ");
                        }
                    }
                    System.out.println();
                }
                System.out.println("--------------------------------------");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 扫描目录查找以PageVO结尾的类
     */
    private static void scanDirectoryForPageVOClasses(File directory, List<ClassInfo> classes) {
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    String fileName = file.getName();
                    if (file.isDirectory()) {
                        // 递归处理子目录
                        scanDirectoryForPageVOClasses(file, classes);
                    } else if (fileName.endsWith(".java") && fileName.endsWith("PageVO.java")) {
                        // 分析Java源文件
                        try {
                            ClassInfo classInfo = analyzeJavaFile(file);
                            if (classInfo != null) {
                                classes.add(classInfo);
                            }
                        } catch (Exception e) {
                            System.err.println("分析文件失败: " + file.getPath() + ", 错误: " + e.getMessage());
                        }
                    }
                }
            }
        } else {
            System.out.println("目录不存在: " + directory.getPath());
        }
    }
    
    /**
     * 分析Java源文件，提取类信息
     */
    private static ClassInfo analyzeJavaFile(File file) throws Exception {
        String fileName = file.getName();
        String className = fileName.substring(0, fileName.length() - 5); // 去掉.java后缀
        
        // 获取包名
        String packageName = "";
        
        // 读取文件内容
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
                
                // 提取包名
                if (line.startsWith("package ")) {
                    packageName = line.substring("package ".length(), line.indexOf(';')).trim();
                }
            }
        }
        
        String fileContent = content.toString();
        String fullClassName = packageName + "." + className;
        
        // 检查是否有id字段
        boolean hasIdField = false;
        String idFieldName = "";
        
        // 尝试查找常见的id字段模式
        Pattern[] idPatterns = {
            Pattern.compile("private\\s+(?:Integer|Long|String)\\s+id;"),
            Pattern.compile("private\\s+(?:Integer|Long|String)\\s+(\\w*[Ii]d);")
        };
        
        for (Pattern pattern : idPatterns) {
            Matcher matcher = pattern.matcher(fileContent);
            if (matcher.find()) {
                hasIdField = true;
                if (matcher.groupCount() > 0) {
                    idFieldName = matcher.group(1);
                } else {
                    idFieldName = "id";
                }
                break;
            }
        }
        
        // 提取父类信息
        String parentClassName = "java.lang.Object"; // 默认父类
        Pattern extendsPattern = Pattern.compile("class\\s+" + className + "\\s+extends\\s+(\\w+)");
        Matcher extendsMatcher = extendsPattern.matcher(fileContent);
        if (extendsMatcher.find()) {
            parentClassName = extendsMatcher.group(1);
            
            // 尝试找到完整的父类名（包括包名）
            Pattern importPattern = Pattern.compile("import\\s+([\\w\\.]+\\." + parentClassName + ");");
            Matcher importMatcher = importPattern.matcher(fileContent);
            if (importMatcher.find()) {
                parentClassName = importMatcher.group(1);
            }
        }
        
        // 提取接口信息
        List<String> interfaces = new ArrayList<>();
        Pattern implementsPattern = Pattern.compile("class\\s+" + className + "(?:\\s+extends\\s+\\w+)?\\s+implements\\s+([^{]+)");
        Matcher implementsMatcher = implementsPattern.matcher(fileContent);
        if (implementsMatcher.find()) {
            String implementsSection = implementsMatcher.group(1).trim();
            String[] interfaceNames = implementsSection.split(",");
            
            for (String interfaceName : interfaceNames) {
                interfaceName = interfaceName.trim();
                interfaces.add(interfaceName);
                
                // 尝试找到完整的接口名（包括包名）
                Pattern importInterfacePattern = Pattern.compile("import\\s+([\\w\\.]+\\." + interfaceName + ");");
                Matcher importInterfaceMatcher = importInterfacePattern.matcher(fileContent);
                if (importInterfaceMatcher.find()) {
                    // 替换为完整的接口名
                    interfaces.remove(interfaceName);
                    interfaces.add(importInterfaceMatcher.group(1));
                }
            }
        }
        
        return new ClassInfo(fullClassName, parentClassName, interfaces, hasIdField, idFieldName);
    }
    
    /**
     * 类信息实体
     */
    private static class ClassInfo {
        private final String fullClassName;
        private final String parentClassName;
        private final List<String> interfaces;
        private final boolean hasIdField;
        private final String idFieldName;
        
        public ClassInfo(String fullClassName, String parentClassName, List<String> interfaces, boolean hasIdField, String idFieldName) {
            this.fullClassName = fullClassName;
            this.parentClassName = parentClassName;
            this.interfaces = interfaces;
            this.hasIdField = hasIdField;
            this.idFieldName = idFieldName;
        }
        
        public String getFullClassName() {
            return fullClassName;
        }
        
        public String getParentClassName() {
            return parentClassName;
        }
        
        public List<String> getInterfaces() {
            return interfaces;
        }
        
        public boolean hasIdField() {
            return hasIdField;
        }
        
        public String getIdFieldName() {
            return idFieldName;
        }
    }
} 