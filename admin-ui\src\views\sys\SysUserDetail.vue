<template>
  <el-dialog
    title="用户详情"
    :visible.sync="dialogVisible"
    width="50%"
    top="8vh"
    @close="handleClose">
    <div class="user-detail-container">
      <!-- 基本信息区域 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-user"></i>
          <span>基本信息</span>
        </div>
        <el-divider></el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">用户名：</span>
              <span class="detail-value">{{ userInfo.username }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">姓名：</span>
              <span class="detail-value">{{ userInfo.realName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">工号：</span>
              <span class="detail-value">{{ userInfo.workNumber }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">入职日期：</span>
              <span class="detail-value">{{ userInfo.entryDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">岗位：</span>
              <span class="detail-value">
                <dict-tag category-id="ROLE_ID" :value="userInfo.roleId"></dict-tag>
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">人员状态：</span>
              <span class="detail-value">
                <dict-tag category-id="USER_STATUS" :value="userInfo.status"></dict-tag>
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="20">
            <div class="detail-item">
              <span class="detail-label">备注：</span>
              <span class="detail-value">{{ userInfo.remark || '无' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 证书信息区域 -->
      <div class="detail-section">
        <div class="section-header">
          <i class="el-icon-document"></i>
          <span>证书信息</span>
        </div>
        <el-divider></el-divider>

        <div v-if="userInfo.certList && userInfo.certList.length > 0">
          <el-tabs type="border-card">
            <el-tab-pane v-for="(cert, index) in userInfo.certList" :key="index">
              <span slot="label">
                {{ cert.certType }}
                <el-tag v-if="isExpired(cert.certExpireDate)" size="mini" type="danger" effect="dark">已过期</el-tag>
              </span>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <span class="detail-label">证书类型：</span>
                    <span class="detail-value">{{ cert.certType }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <span class="detail-label">证书编号：</span>
                    <span class="detail-value">{{ cert.certNumber }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <span class="detail-label">发证机关：</span>
                    <span class="detail-value">{{ cert.certIssuingOrg }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <span class="detail-label">有效期至：</span>
                    <span class="detail-value" :class="{ 'expired': isExpired(cert.certExpireDate) }">{{ cert.certExpireDate }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <span class="detail-label">附件：</span>
                    <span class="detail-value">
                      <template v-if="cert.attachmentId">
                        <a :href="'/api/sys-attachment/download/' + cert.attachmentId" target="_blank">
                          {{ cert.fileOriginalName || '查看附件' }} <i class="el-icon-paperclip"></i>
                        </a>
                      </template>
                      <template v-else>
                        无附件
                      </template>
                    </span>
                  </div>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div v-else class="no-data">
          <i class="el-icon-document"></i>
          <span>暂无证书信息</span>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import DictTag from '../../components/DictTag.vue'
import { sysUserCertificationPage } from '@/api/SysUserCertification'

export default {
  name: 'SysUserDetail',
  components: {
    DictTag,
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      userInfo: {
        userId: null,
        username: '',
        workNumber: '',
        realName: '',
        entryDate: '',
        roleId: '',
        status: '',
        remark: '',
        certList: []
      },
      loading: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val && this.userData) {
        this.initUserData();
      }
    }
  },
  methods: {
    initUserData() {
      // 处理基本数据
      const basicsData = { ...this.userData };

      // 转换字段名
      if (basicsData.id) {
        basicsData.userId = basicsData.id;
        delete basicsData.id;
      }

      if (basicsData.name) {
        basicsData.realName = basicsData.name;
        delete basicsData.name;
      }

      if (basicsData.jobNumber) {
        basicsData.workNumber = basicsData.jobNumber;
        delete basicsData.jobNumber;
      }

      if (basicsData.position) {
        // 使用角色名称映射到ID
        basicsData.roleName = basicsData.position;
        basicsData.roleId = this.getRoleIdByName(basicsData.position);
        delete basicsData.position;
      }

      if (basicsData.hireDate) {
        basicsData.entryDate = basicsData.hireDate;
        delete basicsData.hireDate;
      }

      this.userInfo = { ...basicsData, certList: [] };

      // 如果有userId，加载用户的证书列表
      if (this.userInfo.userId) {
        this.loadUserCertifications(this.userInfo.userId);
      }
    },

    // 加载用户证书列表
    loadUserCertifications(userId) {
      this.loading = true;
      // 查询用户证书列表
      sysUserCertificationPage({ userId })
        .then(res => {
          if (res.success && res.data && res.data.records) {
            if (res.data.records.length > 0) {
              // 使用后端返回的证书列表
              this.userInfo.certList = res.data.records;
            }
          }
        })
        .catch(err => {
          console.error('加载用户证书失败', err);
          this.$message.error('加载用户证书失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },

    getRoleIdByName(positionName) {
      const roleMap = {
        '单位主要负责人': 21,
        '安全总监': 22,
        '安全员': 23,
        '电站锅炉司炉': 31,
        '锅炉水处理作业': 32,
        '技术人员': 40
      };
      return roleMap[positionName] || '';
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.resetData()
    },

    resetData() {
      this.userInfo = {
        userId: null,
        username: '',
        workNumber: '',
        realName: '',
        entryDate: '',
        roleId: '',
        status: '',
        remark: '',
        certList: []
      }
    },

    isExpired(expireDate) {
      if (!expireDate) return false;
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time part to compare dates only
      const expire = new Date(expireDate);
      return expire < today;
    }
  }
}
</script>

<style scoped>
.user-detail-container {
  padding: 0 10px;
}

.detail-section {
  margin-bottom: 30px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.section-header i {
  margin-right: 8px;
  font-size: 18px;
}

.detail-item {
  margin: 15px 0;
}

.detail-label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.detail-value {
  color: #303133;
}

.detail-remark {
  display: block;
  margin-top: 5px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 40px;
  line-height: 20px;
}

.no-data {
  text-align: center;
  padding: 30px 0;
  color: #909399;
}

.no-data i {
  font-size: 40px;
  margin-bottom: 10px;
  display: block;
}

/* 标签页样式 */
.el-tabs--border-card {
  box-shadow: none;
  border: 1px solid #dcdfe6;
}

.el-tabs__item {
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

.expired {
  color: #F56C6C;
  font-weight: bold;
}
</style>
