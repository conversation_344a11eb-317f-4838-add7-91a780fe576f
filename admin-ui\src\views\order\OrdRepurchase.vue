<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>货物已入库重新采购</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="店铺">
          <el-select v-model="formInline.shopIds" placeholder="请选择店铺" clearable multiple style="width: 180px;">
            <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="formInline.orderSn" placeholder="请输入订单号" clearable style="width: 180px;"></el-input>
        </el-form-item>
        <!-- <el-form-item label="订单状态">
          <el-select v-model="formInline.orderStates" placeholder="订单状态" clearable style="width: 120px;">
            <el-option v-for="item in orderStatusList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="处理办法">
          <dict-select v-model="formInline.closeWay" category-id="REPURCHASE_CLOSE_WAY" placeholder="处理办法"
            style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item label="处理状态">
          <dict-select v-model="formInline.closeStatus" category-id="REPURCHASE_CLOSE_STATUS" placeholder="处理状态"
            style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
          <el-button v-permission="['ord-repurchase:insert']" type="primary" icon="el-icon-plus" @click="handleAddRepurchase">添加</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table ref="table" :data="tableData" border style="width: 100%;" v-loading="loading"
        @expand-change="handleExpandChange">
        <!-- 展开列 -->
        <el-table-column type="expand">
          <template slot-scope="scope">
            <div class="order-items-section">
              <el-table :data="scope.row.orderItems" border style="width: 100%; ">
                <el-table-column label="产品图片" width="150" align="center">
                  <template slot-scope="item">
                    <img :src="item.row.itemImage || '/static/img/default-product.png'"
                      style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;"
                      @error="handleImageError" />
                  </template>
                </el-table-column>
                <el-table-column prop="itemName" label="产品名称" min-width="200" show-overflow-tooltip>
                  <template slot-scope="item">
                    <span class="item-name clickable-item-name"
                          @click="copyItemName(item.row.itemName)"
                          :title="'点击复制商品名称: ' + item.row.itemName">
                      {{ item.row.itemName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="itemModelName" label="规格" min-width="150" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="amount" label="数量" width="90" align="center">
                </el-table-column>
                <el-table-column prop="itemPrice" label="单价" width="100" align="center"></el-table-column>
                <el-table-column prop="expressNo" label="快递编号" min-width="150" align="center">
                  <template slot-scope="item">
                    <span v-if="item.row.expressNo"
                          class="express-no clickable-express"
                          @click="copyExpressNo(item.row.expressNo)"
                          :title="'点击复制快递号: ' + item.row.expressNo">
                      {{ item.row.expressNo }}
                    </span>
                    <span v-else class="no-express">未填写</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="shopName" label="店铺名称" width="130" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="orderSn" label="订单号" width="180" align="center">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
                  @click="copyOrderSn(scope.row.orderSn)"
                  :title="'点击复制订单号'">
              {{ scope.row.orderSn }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="下单时间" width="150" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="issue" label="问题描述" width="200" align="center" show-overflow-tooltip>
        </el-table-column>
        <dict-table-column prop="closeWay" category-id="REPURCHASE_CLOSE_WAY" label="处理办法" width="140" align="center" />
        <el-table-column prop="closeStatus" label="处理状态" width="110" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.closeStatus === '1'"
                        :content="`确认人：${scope.row.closeUserName || '未知'}, ${formatDate(scope.row.closeTime) || '-'}`"
                        placement="top">
              <dict-tag :value="scope.row.closeStatus" category-id="REPURCHASE_CLOSE_STATUS" style="cursor: pointer;"></dict-tag>
            </el-tooltip>
            <dict-tag v-else :value="scope.row.closeStatus" category-id="REPURCHASE_CLOSE_STATUS"></dict-tag>
          </template>
        </el-table-column>
        <el-table-column prop="closeTime" label="处理时长" width="120" align="center">
          <template slot-scope="scope">
            <span :class="getTimeDurationClass(scope.row.issueTime, scope.row.closeTime)">
              {{ calculateTimeDuration(scope.row.issueTime, scope.row.closeTime) }}
            </span>
          </template>
        </el-table-column>
        <!-- 使用NoteColumn组件 -->
        <NoteColumn :current-scene="currentScene" />
        <el-table-column label="操作" min-width="150" align="center">
          <template slot-scope="scope">
            <!-- <el-button size="mini" type="primary" @click="openEditNoteDialog(scope.row)">编辑备注</el-button> -->
            <!-- 处理状态为"0"（未处理）时显示编辑、删除、确认完成按钮 -->
            <template v-if="scope.row.closeStatus === '0'">
              <el-button v-permission="['ord-repurchase:update']" size="mini" type="text" @click="handleEditRepurchase(scope.row)">
                <i class="el-icon-edit"></i> 编辑
              </el-button>
              <el-button v-permission="['ord-repurchase:delete']" size="mini" type="text" @click="handleDeleteRepurchase(scope.row)" style="color: #F56C6C">
                <i class="el-icon-delete"></i> 删除
              </el-button>
              <el-button v-permission="['ord-repurchase:confirm']" size="mini" type="text" @click="handleConfirmComplete(scope.row)"
                :disabled="!canConfirmComplete">
                <i class="el-icon-check"></i> 确认完成
              </el-button>
            </template>
            <!-- 处理状态为"1"（已处理）时不显示操作按钮 -->
            <!-- <template v-else-if="scope.row.closeStatus === '1'">
              <span style="color: #909399;">-</span>
            </template> -->
            <!-- 备注按钮 - 所有状态都显示 -->
            <el-button size="mini" type="text" @click="openNotesDrawer(scope.row)" icon="el-icon-tickets">备注</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-table-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

    <!-- 使用EditNoteDialog组件 -->
    <EditNoteDialog :visible.sync="noteDialogVisible" :orderSn="currentOrderSn" :scene="currentScene" sceneName="重新采购订单"
      @saved="handleNoteSaved" />

    <!-- 重新采购编辑对话框 -->
    <OrderRepurchaseEdit :visible.sync="repurchaseDialogVisible" :editData="currentRepurchaseData"
      :isEdit="isEditRepurchase" @submit="handleRepurchaseSubmit" />

    <!-- 备注抽屉 -->
    <el-drawer
      title="订单备注"
      :visible.sync="notesDrawerVisible"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose">
      <div style="padding: 20px;">
        <OrderNotesDrawer
          :notes="currentOrderNotes"
          :orderSn="currentOrderSn"
          :currentScene="currentScene"
          :showDebug="false"
          @note-updated="handleNoteUpdated" />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getOrderStates, repurchasePageWithItems } from '../../api/ErpOrder'
import { deleteOrdRepurchase, confirmCompleteOrdRepurchase } from '../../api/OrdRepurchase'
import { getAllEnabledShops } from '../../api/SysShop'
import { getByOrderSnAndScene } from '../../api/WkbNote'
import EditNoteDialog from '../../components/EditNoteDialog'
import DictSelect from '../../components/DictSelect.vue'
import DictTag from '../../components/DictTag'
import OrderRepurchaseEdit from './OrderRepurchaseEdit'
import Pagination from '../../components/Pagination'
import NoteColumn from '../../components/NoteColumn'
import noteDrawerMixin from '../../mixins/noteDrawerMixin'
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'OrdRepurchase',
  mixins: [copyMixin, noteDrawerMixin],
  components: {
    Pagination,
    EditNoteDialog,
    OrderRepurchaseEdit,
    DictSelect,
    DictTag,
    NoteColumn
  },
  data() {
    return {
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        orderStates: '',
        closeWay: '',
        closeStatus: ''
      },
      // 表格数据
      tableData: [],
      // 店铺列表
      shopList: [],
      // 订单状态列表
      orderStatusList: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      // 备注弹窗
      noteDialogVisible: false,
      currentOrderSn: '',
      currentScene: '06',  // 货物已入库重新采购
      currentOrder: null,
      // 重新采购对话框
      repurchaseDialogVisible: false,
      currentRepurchaseData: {},
      isEditRepurchase: false
    }
  },
  computed: {
    // 检查是否可以确认完成（roleId=22时可用）
    canConfirmComplete() {
      // 这里需要从用户信息中获取roleId，假设存储在vuex或localStorage中
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.roleId === 22
    }
  },
  created() {
    // 检查URL参数中是否有orderSn
    if (this.$route.query.orderSn) {
      this.formInline.orderSn = this.$route.query.orderSn;
    }
    this.getPageData()
    this.loadShops()
    this.loadOrderStates()
  },
  methods: {
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = {
          current: this.formInline.current,
          size: this.formInline.size,
          shopIds: this.formInline.shopIds && this.formInline.shopIds.length > 0 ? this.formInline.shopIds.map(id => String(id)) : undefined,
          orderSn: this.formInline.orderSn || undefined,
          orderStates: this.formInline.orderStates || undefined,
          closeWay: this.formInline.closeWay || undefined,
          closeStatus: this.formInline.closeStatus || undefined
        }
      }

      repurchasePageWithItems(parameter)
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
          this.pageParam.currentPage = res.data.current
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
        .catch(err => {
          this.loading = false
          this.$message.error('获取数据失败：' + err.message)
        })
    },

    // 打开编辑备注弹窗
    openEditNoteDialog(row) {
      this.currentOrder = row
      this.currentOrderSn = row.orderSn
      this.currentScene = '03'
      this.noteDialogVisible = true
    },

    // 处理备注保存成功事件
    handleNoteSaved(noteData) {
      console.log('备注已保存:', noteData)

      // 如果有当前订单对象，更新其备注数据
      if (this.currentOrder) {
        // 查询该订单的指定场景的备注
        getByOrderSnAndScene({
          orderSn: this.currentOrder.orderSn,
          scene: '03'
        }).then(res => {
          if (res.success && res.data) {
            // 初始化notes数组（如果不存在）
            if (!this.currentOrder.notes) {
              this.currentOrder.notes = [];
            }

            // 查找是否已有相同场景的备注
            const existingNoteIndex = this.currentOrder.notes.findIndex(note => note.scene === '03');
            if (existingNoteIndex !== -1) {
              // 替换已有的备注
              this.currentOrder.notes[existingNoteIndex] = res.data;
            } else {
              // 添加新的备注
              this.currentOrder.notes.push(res.data);
            }

            // 强制刷新视图
            this.$forceUpdate();
          }
        });
      }

      // 刷新列表数据（可选，取决于业务需求）
      setTimeout(() => {
        this.getPageData()
      }, 500)
    },

    // 获取特定场景的备注
    getSceneNotes(notes, scene) {
      if (!notes || !Array.isArray(notes)) return []
      return notes.filter(note => note.scene === scene)
    },

    // 加载店铺数据
    loadShops() {
      getAllEnabledShops()
        .then(res => {
          if (res.success && res.data && res.data.records) {
            this.shopList = res.data.records
          }
        })
        .catch(err => {
          console.error('加载店铺数据失败：', err)
        })
    },

    // 加载订单状态数据
    loadOrderStates() {
      getOrderStates()
        .then(res => {
          if (res.success && res.data) {
            this.orderStatusList = res.data
          }
        })
        .catch(err => {
          console.error('加载订单状态数据失败：', err)
        })
    },

    // 分页回调
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },

    // 搜索
    onSearch() {
      this.formInline.current = 1
      this.getPageData()
    },

    // 重置
    onReset() {
      this.formInline = {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        orderStates: '',
        closeWay: '',
        closeStatus: ''
      }
      this.getPageData()
    },

    // 表格展开变化事件
    handleExpandChange(row, expandedRows) {
      console.log('展开行变化：', row, expandedRows)
    },

    // 切换行展开状态
    toggleRowExpansion(row) {
      this.$refs.table ? this.$refs.table.toggleRowExpansion(row) : null
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        'WAIT_BUYER_CONFIRM_GOODS': 'warning',
        'WAIT_SELLER_SEND_GOODS': 'info',
        'TRADE_BUYER_SIGNED': 'success',
        'TRADE_CLOSED': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png'
    },

    // 添加重新采购
    handleAddRepurchase() {
      this.currentRepurchaseData = {}
      this.isEditRepurchase = false
      this.repurchaseDialogVisible = true
    },

    // 编辑重新采购
    handleEditRepurchase(row) {
      this.currentRepurchaseData = { ...row }
      this.isEditRepurchase = true
      this.repurchaseDialogVisible = true
    },

    // 删除重新采购
    handleDeleteRepurchase(row) {
      this.$confirm(`确定要删除订单号为 ${row.orderSn} 的重新采购记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除API
        deleteOrdRepurchase({ idList: [row.id] })
          .then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.getPageData()
            } else {
              this.$message.error('删除失败：' + (res.message || '未知错误'))
            }
          })
          .catch(err => {
            this.$message.error('删除失败：' + (err.message || '网络错误'))
          })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 重新采购提交回调
    handleRepurchaseSubmit() {
      this.getPageData()
    },

    // 确认完成
    handleConfirmComplete(row) {
      this.$confirm(`确定要将订单号为 ${row.orderSn} 的重新采购记录标记为完成吗？`, '确认完成', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用确认完成API
        confirmCompleteOrdRepurchase(row.id)
          .then(res => {
            if (res.success) {
              this.$message.success('确认完成成功')
              this.getPageData()
            } else {
              this.$message.error('确认完成失败：' + (res.message || '未知错误'))
            }
          })
          .catch(err => {
            this.$message.error('确认完成失败：' + (err.message || '网络错误'))
          })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    // 计算未处理时长
    calculateTimeDuration(issueTime, closeTime) {
      if (!issueTime || !closeTime) return ''

      const orderTime = new Date(issueTime)
      const clzTime = new Date(closeTime)
      const diffInMs = clzTime - orderTime

      // 转换为小时
      const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60))

      if (diffInHours < 24) {
        return `${diffInHours}小时`
      } else {
        const days = Math.floor(diffInHours / 24)
        const remainingHours = diffInHours % 24
        return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`
      }
    },

    // 获取时长样式类
    getTimeDurationClass(issueTime, closeTime) {
      if (!issueTime || !closeTime) return ''

      const orderTime = new Date(issueTime)
      const clzTime = new Date(closeTime)
      const diffInMs = clzTime - orderTime
      const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60))

      if (diffInHours >= 72) return 'duration-critical'  // 72小时以上：紧急
      if (diffInHours >= 48) return 'duration-warning'   // 48-72小时：警告
      if (diffInHours >= 24) return 'duration-attention' // 24-48小时：注意
      return 'duration-normal'                           // 24小时内：正常
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}

.empty-table-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

/* 订单项展开区域样式 */
.order-items-section {
  background-color: #f8f9fa;
  border-radius: 6px;
}

.express-no {
  color: #67C23A;
  font-weight: bold;
  background-color: #F0F9FF;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #67C23A;
}

.no-express {
  color: #909399;
  font-style: italic;
}

.clickable-express {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-express:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

.duration-attention {
  color: #E6A23C;
  font-weight: bold;
}

.duration-warning {
  color: #F56C6C;
  font-weight: bold;
}

.duration-critical {
  color: #F56C6C;
  font-weight: bold;
  background-color: #FEF0F0;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #F56C6C;
}

.demo-form-inline {
  margin-bottom: 15px;
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>
