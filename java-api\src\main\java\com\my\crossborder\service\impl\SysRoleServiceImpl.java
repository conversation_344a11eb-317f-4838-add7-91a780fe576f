package com.my.crossborder.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.sys_role.SysRoleDeleteDTO;
import com.my.crossborder.controller.dto.sys_role.SysRoleInsertDTO;
import com.my.crossborder.controller.dto.sys_role.SysRolePageDTO;
import com.my.crossborder.controller.dto.sys_role.SysRoleUpdateDTO;
import com.my.crossborder.controller.vo.sys_role.SysRoleDetailVO;
import com.my.crossborder.controller.vo.sys_role.SysRolePageVO;
import com.my.crossborder.mybatis.entity.SysRole;
import com.my.crossborder.mybatis.mapper.SysRoleMapper;
import com.my.crossborder.service.SysRoleService;

import cn.hutool.core.bean.BeanUtil;

/**
 * 系统角色表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {


	@Transactional
	@Override
	public void insert(SysRoleInsertDTO insertDTO) {
		SysRole entity = BeanUtil.copyProperties(insertDTO, SysRole.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(SysRoleUpdateDTO updateDTO) {
		SysRole entity = BeanUtil.copyProperties(updateDTO, SysRole.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public SysRoleDetailVO detail(Integer id) {
		SysRole entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SysRoleDetailVO.class);
	}

	@Override
	public Page<SysRolePageVO> page(SysRolePageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysRoleDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
