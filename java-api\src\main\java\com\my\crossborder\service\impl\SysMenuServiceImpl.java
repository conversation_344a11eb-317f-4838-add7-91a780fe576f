package com.my.crossborder.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.my.crossborder.cache.SysMenuCache;
import com.my.crossborder.controller.dto.sys_menu.SysMenuDeleteDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuInsertDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuPageDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuUpdateDTO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuDetailVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuNavVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuPageVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuTreeVO;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.mybatis.entity.SysMenu;
import com.my.crossborder.mybatis.entity.SysMenuRefRole;
import com.my.crossborder.mybatis.mapper.SysMenuMapper;
import com.my.crossborder.service.SysMenuRefRoleService;
import com.my.crossborder.service.SysMenuService;
import com.my.crossborder.util.RegExpUtil;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;

/**
 * 系统菜单表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

	private final SysMenuCache sysMenuCache;
	private final SysMenuRefRoleService sysMenuRefRoleService;
	

	@Transactional
	@Override
	public void insert(SysMenuInsertDTO insertDTO) {
		SysMenu entity = BeanUtil.copyProperties(insertDTO, SysMenu.class);
		this.save(entity);
		// 重置缓存
		this.sysMenuCache.reloadCache();
	}

	@Transactional
	@Override
	public void update(SysMenuUpdateDTO updateDTO) {
		SysMenu entity = BeanUtil.copyProperties(updateDTO, SysMenu.class);
		this.baseMapper.updateById(entity);
		// 重置缓存
		this.sysMenuCache.reloadCache();
	}

	@Override
	public SysMenuDetailVO detail(String id) {
		SysMenu entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SysMenuDetailVO.class);
	}

	@Override
	public Page<SysMenuPageVO> page(SysMenuPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysMenuDeleteDTO deleteDTO) {
		// 先删除关联表中的记录
		for (String menuId : deleteDTO.getIdList()) {
			LambdaQueryWrapper<SysMenuRefRole> wrapper = new LambdaQueryWrapper<>();
			wrapper.eq(SysMenuRefRole::getMenuId, menuId);
			sysMenuRefRoleService.remove(wrapper);
		}
		
		// 再删除菜单本身
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
		
		// 重置缓存
		this.sysMenuCache.reloadCache();
	}
	
	@Override
	public List<SysMenuTreeVO> treeNodes() {
		// 1. 获取所有菜单列表
		List<SysMenu> menuList = this.list();
		
		// 2. 转换为TreeVO对象
		List<SysMenuTreeVO> voList = menuList.stream()
			.filter(t -> !t.getId().startsWith("01"))  // 给管理员的授权界面，忽略【工作台】和其子菜单
			.map(menu -> {
				SysMenuTreeVO vo = new SysMenuTreeVO();
				vo.setId(menu.getId());
				vo.setName(menu.getMenuName());
				vo.setPath(menu.getRoutePath());
				// 组件路径可能需要从routePath构建或者其他方式获取
				vo.setComponent(StrUtil.isNotBlank(menu.getRoutePath()) ? menu.getRoutePath().substring(1) : null);
				vo.setIcon(menu.getIcon());
				vo.setType(menu.getMenu() != null && menu.getMenu() ? "menu" : "button");
				vo.setSort(menu.getSortNum());
				vo.setStatus(menu.getEnable() != null && menu.getEnable() ? 1 : 0);
				vo.setPermission(menu.getPermission());
				vo.setCreateTime(menu.getCreateTime());
				vo.setChildren(new ArrayList<>());
				return vo;
			}).collect(Collectors.toList());
		
		// 3. 构建父子关系
		// 先按parentId分组
		Map<String, List<SysMenuTreeVO>> parentMap = voList.stream()
				.collect(Collectors.groupingBy(item -> {
					SysMenu menu = menuList.stream()
							.filter(m -> m.getId().equals(item.getId()))
							.findFirst()
							.orElse(null);
					return menu != null ? (menu.getParentId() == null ? "0" : menu.getParentId()) : "0";
				}));
		
		// 找出所有顶级菜单
		List<SysMenuTreeVO> rootList = parentMap.getOrDefault("0", new ArrayList<>());
		
		// 递归构建树
		rootList.forEach(root -> buildChildrenTree(root, parentMap));
		
		// 4. 按sort排序
		sortTreeBySort(rootList);
		
		return rootList;
	}
	
	@Override
	public List<SysMenu> listByMenuIds(List<String> menuIds) {
		return this.sysMenuCache.listByIds(menuIds);
	}
	
	@Override
	public List<SysMenuNavVO> tree() {
    	Integer roleId = Integer.parseInt(StpUtil.getRoleList().get(0));
    	// 获取角色对应的菜单ID列表
    	List<String> menuIds = SpringUtil.getBean(SysMenuRefRoleService.class).getMenuIds(roleId);
    	List<SysMenu> menuList = this.listByMenuIds(menuIds)
    			.stream()
    			.filter(t -> t.getMenu())
    			.collect(Collectors.toList());
    	
    	// 将菜单列表转换为前端导航菜单结构
    	return this.buildTree(menuList);
	}
	
	/**
	 * 递归构建子树
	 */
	private void buildChildrenTree(SysMenuTreeVO parent, Map<String, List<SysMenuTreeVO>> parentMap) {
		List<SysMenuTreeVO> children = parentMap.getOrDefault(parent.getId(), new ArrayList<>());
		if (!children.isEmpty()) {
			parent.setChildren(children);
			children.forEach(child -> buildChildrenTree(child, parentMap));
		}
	}
	
	/**
	 * 递归排序树
	 */
	private void sortTreeBySort(List<SysMenuTreeVO> menuList) {
		if (menuList != null && !menuList.isEmpty()) {
			menuList.sort(Comparator.comparing(SysMenuTreeVO::getSort, Comparator.nullsLast(Comparator.naturalOrder())));
			menuList.forEach(menu -> {
				if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
					sortTreeBySort(menu.getChildren());
				}
			});
		}
	}
	
	// ------ 前端菜单树
    /**
     * 返回leftnav用的菜单树
     * @param menuList
     * @return
     */
    private List<SysMenuNavVO> buildTree(List<SysMenu> menuList) {
    	List<SysMenuNavVO> lv1Nodes = this.nodes(1, menuList);
    	List<SysMenuNavVO> lv2Nodes = this.nodes(2, menuList);
    	lv1Nodes.forEach(lv1 -> {
    		List<SysMenuNavVO> children = lv2Nodes.stream()
    			.filter(t -> t.getExternalLink().equals(lv1.getMenuid()))
    			.collect(Collectors.toList());
    		lv1.getMenus().addAll(children);
    	});
		return lv1Nodes;
	}
    
    /**
     * 获取第几级深度的节点
     * @param deepth
     * @param menuList
     * @return
     */
    private List<SysMenuNavVO> nodes(int deepth, List<SysMenu> menuList){
    	if (deepth == 1) {
        	return menuList.stream()
				.filter(t -> t.getMenu() && t.getParentId().equals("0"))
				.map(t -> SysMenuNavVO.convert(t))
				.collect(Collectors.toList());
    	} else if (deepth == 2) {
    		return menuList.stream()
				.filter(t -> t.getMenu() && !t.getParentId().equals("0"))
				.map(t -> SysMenuNavVO.convert(t))
				.collect(Collectors.toList());
    	} else {
    		BusinessException.by("暂未实现{}级菜单的递归查询", deepth);
    		return null;
    	}
    }

	@Override
	public List<String> dashboardMenuIds() {
		Wrapper<SysMenu> wrapper = Wrappers.lambdaQuery(SysMenu.class)
			.likeRight(SysMenu::getId, "01");
		List<SysMenu> menus = this.baseMapper.selectList(wrapper);
		return menus.stream()
				.map(t -> t.getId())
				.collect(Collectors.toList());
	}
}
