package com.my.crossborder.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRateDeleteDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRateInsertDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRatePageDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRateUpdateDTO;
import com.my.crossborder.controller.vo.sys_exchange_rate.SysExchangeRatePageVO;
import com.my.crossborder.mybatis.entity.SysExchangeRate;
import com.my.crossborder.mybatis.mapper.SysExchangeRateMapper;
import com.my.crossborder.service.SysExchangeRateService;

/**
 * 汇率表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class SysExchangeRateServiceImpl extends ServiceImpl<SysExchangeRateMapper, SysExchangeRate> implements SysExchangeRateService {



	@Transactional
	@Override
	public void delete(SysExchangeRateDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}
}
