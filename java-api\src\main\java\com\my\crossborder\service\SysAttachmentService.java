package com.my.crossborder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentDeleteDTO;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentInsertDTO;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentPageDTO;
import com.my.crossborder.controller.vo.sys_attachment.SysAttachmentDetailVO;
import com.my.crossborder.controller.vo.sys_attachment.SysAttachmentPageVO;
import com.my.crossborder.mybatis.entity.SysAttachment;

/**
 * 附件表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface SysAttachmentService extends IService<SysAttachment> {

	/**
	 * 新增并返回主键
	 */
	Integer insertAndReturnId(SysAttachmentInsertDTO insertDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysAttachmentDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<SysAttachmentPageVO> page(SysAttachmentPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysAttachmentDeleteDTO deleteDTO);	

	/**
	 * 填充附件表的数据源ID
	 * @param dataSourceId 实体主键ID
	 * @param attachmentIdList 附件ID列表
	 * @param tableName 数据表名
	 */
	void fillDataSourceId(String dataSourceId, List<Integer> attachmentIdList, String tableName);

	/**
	 * 清理附件表的数据源ID
	 * @param tableName 数据表名
	 * @param dataSourceId 实体主键ID
	 */
	void clearDatasourceId(String tableName, String dataSourceId);

}
