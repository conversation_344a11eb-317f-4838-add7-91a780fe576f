package com.my.crossborder.mybatis.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数据字典-字典项表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_dict_item")
public class SysDictItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类别id (联合主键)
     */
    private String categoryId;

    /**
     * 字典项值 (联合主键)
     */
    private String itemValue;

    /**
     * 字典项名称
     */
    private String itemName;

    /**
     * 排序编号
     */
    private Integer sortNum;

    /**
     * 选项颜色
     */
    private String color;
    


}
