package com.my.crossborder.controller.dto.ord_purchase_centralized;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 修改_集中采购
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseCentralizedUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
	@NotNull(message="id不能为空")
    private Integer id;

    /**
     * 采购单号
     */
	@NotNull(message="purchaseNumber不能为空")
    private String purchaseNumber;

    /**
     * 品名
     */
	@NotNull(message="productName不能为空")
    private String productName;

    /**
     * 数量
     */
	@NotNull(message="quantity不能为空")
    private Integer quantity;

    /**
     * 总金额
     */
	@NotNull(message="totalAmount不能为空")
    private BigDecimal totalAmount;

    /**
     * 采购途径 字典PURCHASE_CENTRALIZED_CHANNEL
     */
	@NotNull(message="purchaseChannel不能为空")
    private String purchaseChannel;

    /**
     * 选中的订单号列表
     */
    private List<String> orderSnList;

}
