package com.my.crossborder.controller.dto.sys_attachment;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_attachment.SysAttachmentPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_附件表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysAttachmentPageDTO 
						extends PageDTO<SysAttachmentPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer attachmentId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 数据源id
     */
    private String dataSourceId;

    /**
     * 文件原始名称
     */
    private String fileOriginalName;

    /**
     * 创建时间（开始时间）
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间（结束时间）
     */
    private LocalDateTime createTimeEnd;

}
