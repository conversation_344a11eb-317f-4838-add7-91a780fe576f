package com.my.crossborder.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSON;
import com.my.crossborder.controller.vo.sys_user.SysUserDetailVO;
import com.my.crossborder.service.SysUserService;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;

//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@RunWith(SpringRunner.class)
@Slf4j
class SysUserServiceTest {
	
	@Autowired
	SysUserService sysUserService;

	@Test
	void testGet() {
//		fail("Not yet implemented");
	}
	
	@Test
	void testDetail() {
		Integer userid = 1;
		SysUserDetailVO detailVO = this.sysUserService.detail(userid);
		detailVO = this.sysUserService.detail(userid);
		detailVO = this.sysUserService.detail(userid);
		log.info("{}", JSON.toJSONString(detailVO));
		log.info("{}", JSON.toJSONString(detailVO));
		log.info("{}", JSON.toJSONString(detailVO));
	}

	@Test
	void md5() {
		String password = "admin";
		String md5Password = DigestUtil.md5Hex(password);
		log.info("md5({}) => {}", password, md5Password);
	}
	
}
