<template>
  <div class="dashboard-container">
    <!-- 顶部问候 -->
    <div class="greeting-section">
      <div class="greeting-content">
        <h2>欢迎回来，<span id="realname">{{ realName }}</span></h2>
        <div class="weather-date">
          <i class="el-icon-calendar"></i>
          <span id="weather-date">{{ currentDate }}</span>
        </div>
      </div>

      <!-- 考勤统计区域 -->
      <div class="greeting-attendance-stats" v-if="needsAttendanceFeature">
        <!-- 打卡按钮 -->
        <div class="punch-section">
          <el-button type="primary" size="medium" :disabled="todayChecked || !needAttendance" @click="handlePunchIn"
            :class="['punch-btn', { 'punch-btn-animate': punchButtonAnimating }]" :loading="punchLoading">
            <i class="el-icon-circle-check"></i>
            {{ getPunchButtonText() }}
          </el-button>
          <div v-if="todayPunchTime" class="punch-time">
            打卡时间：{{ todayPunchTime }}
          </div>
        </div>

        <div class="greeting-stat-item" @click="showAttendanceCalendar">
          <div class="greeting-stat-value warning">{{ attendanceStats.absentDays }}</div>
          <div class="greeting-stat-label">缺勤</div>
        </div>
        <div class="greeting-stat-item" @click="showMonthlySchedule">
          <div class="greeting-stat-value info">{{ attendanceStats.scheduledDays }}</div>
          <div class="greeting-stat-label">本月排班</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧快捷入口 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>快捷入口</h3>
          <div>
            <!-- 预留扩展空间 -->
          </div>
        </div>

        <!-- 快捷入口内容 -->
        <div class="quick-access-content">
          <div v-if="quickAccessItems.length > 0" class="quick-access-grid">
            <div v-for="item in quickAccessItems" :key="item.id" :class="['quick-access-item', item.className]"
              @click="handleQuickAccess(item)">
              <div class="item-icon">
                <i :class="item.icon"></i>
              </div>
              <div class="item-name">{{ item.name }}</div>
            </div>
          </div>
          <div v-else class="empty-placeholder">
            <i class="el-icon-lock" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无可用的快捷入口</p>
          </div>
        </div>
      </div>

      <!-- 右侧通知公告 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>通知公告</h3>
          <div>
            <el-link @click="viewAllNotifications" style="margin-left: 10px;">查看全部
              <i class="el-icon-arrow-right"></i></el-link>
          </div>
        </div>

        <div class="notification-list">
          <div v-for="item in notifications" :key="item.id" class="notification-item" @click="handleNotification(item)">
            <div class="notification-indicator">
              <i class="el-icon-bell" :class="item.isNew ? 'new-notification' : ''"></i>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ item.title }}</div>
              <div class="notification-time">{{ item.time }}</div>
            </div>
          </div>
          <div v-if="notifications.length === 0" class="empty-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 考勤日历对话框 -->
    <el-dialog title="考勤日历" top="10vh" :visible.sync="attendanceCalendarVisible" width="550px"
      @close="closeAttendanceCalendar">
      <DashBoardAttendanceCalendar ref="attendanceCalendar" :userId="currentUserId" />
    </el-dialog>

    <!-- 本月排班对话框 -->
    <el-dialog title="本月排班详情" :visible.sync="monthlyScheduleVisible" width="700px" @close="closeMonthlySchedule">
      <DashBoardMonthlySchedule
        ref="monthlySchedule"
        :userId="currentUserId"
        v-if="monthlyScheduleVisible" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="monthlyScheduleVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 排班详情对话框 -->
    <el-dialog title="排班详情" :visible.sync="scheduleDetailVisible" width="600px">
      <div v-if="selectedDateSchedule">
        <div class="schedule-detail">
          <div class="detail-item">
            <label>日期：</label>
            <span>{{ selectedDateSchedule.date }}</span>
          </div>
          <div class="detail-item">
            <label>店铺：</label>
            <span>{{ selectedDateSchedule.shopName }}</span>
          </div>
          <div class="detail-item">
            <label>角色：</label>
            <span>{{ selectedDateSchedule.role }}</span>
          </div>
          <div class="detail-item">
            <label>状态：</label>
            <el-tag :type="getStatusType(selectedDateSchedule.status)">
              {{ getStatusText(selectedDateSchedule.status) }}
            </el-tag>
          </div>
          <div v-if="selectedDateSchedule.clockTime" class="detail-item">
            <label>打卡时间：</label>
            <span>{{ selectedDateSchedule.clockTime }}</span>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="scheduleDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as AttendanceAPI from '../../api/SysShiftAttendance'
import { notificationReadingPage } from '../../api/WkbNotificationReading'
import { todoPage } from '../../api/WkbTodo'
import DashBoardAttendanceCalendar from './DashBoardAttendanceCalendar.vue'
import DashBoardMonthlySchedule from './DashBoardMonthlySchedule.vue'

export default {
  name: 'Dashboard',
  components: {
    DashBoardAttendanceCalendar,
    DashBoardMonthlySchedule
  },
  data() {
    return {
      activeTab: 'all',
      todos: [],
      todoCount: 0,
      notifications: [],
      notificationCount: 0,
      notificationLoading: false,
      todoLoading: false,
      notificationParams: {
        current: 1,
        size: 5
      },
      todoParams: {
        current: 1,
        size: 5
      },
      realName: '',
      currentDate: '',
      currentUserId: null,
      userRole: null, // 用户角色ID

      // 打卡考勤相关
      needAttendance: false, // 今天是否需要打卡
      todayChecked: false, // 今天是否已打卡
      todayPunchTime: '', // 今天打卡时间
      punchLoading: false, // 打卡按钮加载状态
      attendanceStats: {
        absentDays: 0, // 缺勤
        scheduledDays: 0 // 本月排班天数
      },
      attendanceCalendarVisible: false,
      scheduleDetailVisible: false,
      selectedDateSchedule: null,
      monthlyScheduleVisible: false, // 本月排班对话框显示状态
      punchButtonAnimating: false, // 打卡按钮动画状态
      animationTimer: null, // 动画定时器

      // 快捷入口数据（原始数据，需要根据权限过滤）
      allQuickAccessItems: [
        {
          id: 0,
          name: '订单状态查询',
          icon: 'el-icon-search',
          route: '/OrderStatusSummary',
          className: 'info'
        },
        {
          id: 1,
          name: '采购登记',
          icon: 'el-icon-edit',
          route: '/OrdPurchase',
          className: 'primary'
        },
        {
          id: 2,
          name: '集中采购登记',
          icon: 'el-icon-shopping-cart-full',
          route: '/OrdPurchaseCentralized',
          className: 'success'
        },
        {
          id: 3,
          name: '未完整填写物流编号',
          icon: 'el-icon-warning',
          route: '/OrdLogisticsNotComplete',
          className: 'warning'
        },
        {
          id: 4,
          name: '已填物流编号未入库',
          icon: 'el-icon-truck',
          route: '/OrdLogisticsDoneNotWarehoused',
          className: 'info'
        },
        {
          id: 5,
          name: '货物已入库未出库',
          icon: 'el-icon-box',
          route: '/OrdWarehousedNotOutbound',
          className: 'success'
        },
        {
          id: 6,
          name: '已采购但出库前取消',
          icon: 'el-icon-close',
          route: '/OrdRefund',
          className: 'danger'
        },
        {
          id: 7,
          name: '货物已入库重新采购',
          icon: 'el-icon-refresh',
          route: '/OrdRepurchase',
          className: 'primary'
        },
        {
          id: 8,
          name: '已出库售后',
          icon: 'el-icon-service',
          route: '/OrdAfterSale',
          className: 'warning'
        },
        {
          id: 9,
          name: '物流理赔',
          icon: 'el-icon-document-checked',
          route: '/OrdClaim',
          className: 'danger'
        },
        {
          id: 10,
          name: '台湾上架物品',
          icon: 'el-icon-goods',
          route: '/OrdTaiwan',
          className: 'info'
        }
      ],
      userMenus: [] // 用户菜单权限
    }
  },
  created() {
    this.loadUserInfo()
    this.setCurrentDate()
    this.loadNotifications()
    this.loadTodos()

    // 只有需要考勤功能的角色才加载考勤数据
    this.$nextTick(() => {
      if (this.needsAttendanceFeature) {
        this.loadTodayAttendanceStatus()
        this.loadMonthlyAttendanceStats()
      }
    })
  },
  watch: {
    // 监听是否需要动画的变化
    shouldAnimatePunchButton(newValue) {
      if (newValue) {
        this.startPunchButtonAnimation()
      } else {
        this.stopPunchButtonAnimation()
      }
    }
  },
  beforeDestroy() {
    // 组件销毁时清理定时器
    this.stopPunchButtonAnimation()
  },
  computed: {
    filteredTodos() {
      if (this.activeTab === 'all') {
        return this.todos
      }
      return this.todos.filter(todo => todo.type === this.activeTab)
    },

    // 是否需要显示打卡功能
    needsAttendanceFeature() {
      // 合伙人、管理员不需要打卡功能
      return this.userRole !== 12 && this.userRole !== 11
    },

    // 是否需要打卡按钮动画
    shouldAnimatePunchButton() {
      return this.needsAttendanceFeature && this.needAttendance && !this.todayChecked && !this.punchLoading
    },

        // 根据用户菜单权限过滤快捷入口
    quickAccessItems() {
      // 如果是管理员或合伙人，显示所有快捷入口
      if (this.userRole === 11 || this.userRole === 12) {
        return this.allQuickAccessItems
      }

      // 如果没有菜单权限数据，作为降级处理显示所有（可根据实际需求调整）
      if (!this.userMenus || this.userMenus.length === 0) {
        console.warn('未找到用户菜单权限数据，作为降级处理显示所有快捷入口')
        return this.allQuickAccessItems
      }

      // 根据菜单权限过滤
      const filteredItems = this.allQuickAccessItems.filter(item => {
        return this.hasMenuPermission(item.route)
      })

      console.log('权限过滤后的快捷入口:', filteredItems)
      return filteredItems
    }
  },
  methods: {
    // 开始打卡按钮动画
    startPunchButtonAnimation() {
      // 如果已经有定时器在运行，先清除
      this.stopPunchButtonAnimation()

      // 立即触发一次动画
      this.triggerPunchButtonBounce()

      // 每3秒触发一次动画
      this.animationTimer = setInterval(() => {
        this.triggerPunchButtonBounce()
      }, 3000)
    },

    // 停止打卡按钮动画
    stopPunchButtonAnimation() {
      if (this.animationTimer) {
        clearInterval(this.animationTimer)
        this.animationTimer = null
      }
      this.punchButtonAnimating = false
    },

    // 触发打卡按钮跳动动画
    triggerPunchButtonBounce() {
      if (!this.shouldAnimatePunchButton) return

      this.punchButtonAnimating = true

      // 1秒后停止动画（动画持续1秒，跳两下）
      setTimeout(() => {
        this.punchButtonAnimating = false
      }, 1000)
    },

    // 处理快捷入口点击
    handleQuickAccess(item) {
      console.log('点击快捷入口:', item)
      // 这里可以根据需要进行路由跳转或其他操作
      if (item.route) {
        this.$router.push(item.route)
      }
    },

    loadUserInfo() {
      // 从缓存中获取用户信息
      const userEntity = JSON.parse(localStorage.getItem('userEntity') || '{}')
      this.realName = userEntity.realName || '用户'
      this.currentUserId = userEntity.userId
      this.userRole = userEntity.roleId // 修改为roleId字段

      // 加载用户菜单权限
      this.loadUserMenus()

      console.log('用户信息加载:', {
        realName: this.realName,
        userId: this.currentUserId,
        roleId: this.userRole,
        needsAttendance: this.userRole !== 12
      })
    },

    // 加载用户菜单权限
    loadUserMenus() {
      try {
        // 从localStorage获取用户菜单权限
        const storedMenus = localStorage.getItem('userMenus')
        if (storedMenus) {
          this.userMenus = JSON.parse(storedMenus)
        } else {
          // 如果localStorage中没有，尝试从其他地方获取
          const userRolePermissions = localStorage.getItem('userRolePermissions')
          if (userRolePermissions) {
            const permissions = JSON.parse(userRolePermissions)
            this.userMenus = permissions.menus || []
          } else {
            this.userMenus = []
          }
        }

        console.log('用户菜单权限加载:', this.userMenus)
      } catch (error) {
        console.error('加载用户菜单权限失败:', error)
        this.userMenus = []
      }
    },

        // 检查用户是否有指定菜单的权限
    hasMenuPermission(route) {
      if (!this.userMenus || this.userMenus.length === 0) {
        return false
      }

      // 递归检查菜单权限
      const checkMenu = (menuList) => {
        return menuList.some(menu => {
          // 支持多种路由字段名
          const menuPath = menu.path || menu.url || menu.route || menu.component || menu.name

          // 直接匹配路由
          if (menuPath === route) {
            return true
          }

          // 模糊匹配（去掉开头的/）
          const normalizedRoute = route.replace(/^\/+/, '')
          const normalizedMenuPath = menuPath ? menuPath.replace(/^\/+/, '') : ''
          if (normalizedMenuPath === normalizedRoute) {
            return true
          }

          // 如果菜单有children，递归检查
          if (menu.children && menu.children.length > 0) {
            return checkMenu(menu.children)
          }

          return false
        })
      }

      const hasPermission = checkMenu(this.userMenus)

      // 添加调试信息
      if (!hasPermission) {
        console.debug(`路由 ${route} 未找到对应的菜单权限`)
      }

      return hasPermission
    },
    setCurrentDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const day = now.getDate()
      const weekDays = ['日', '一', '二', '三', '四', '五', '六']
      const weekDay = weekDays[now.getDay()]
      this.currentDate = `${year}年${month}月${day}日 星期${weekDay}`
    },

    // 获取打卡按钮文本
    getPunchButtonText() {
      if (!this.needAttendance) {
        return '今日无排班'
      }
      if (this.todayChecked) {
        return '今日已打卡'
      }
      return '立即打卡'
    },

    // 加载今日考勤状态
    async loadTodayAttendanceStatus() {
      // 合伙人不需要加载考勤状态
      if (!this.needsAttendanceFeature) return

      try {
        const today = this.formatDate(new Date())
        const response = await AttendanceAPI.getTodayAttendanceStatus({
          userId: this.currentUserId,
          date: today
        })

        if (response && response.data) {
          const data = response.data
          this.needAttendance = data.needAttendance
          this.todayChecked = data.clockTime !== null
          this.todayPunchTime = data.clockTime ? this.formatTime(data.clockTime) : ''

          // 检查是否需要启动动画
          this.$nextTick(() => {
            if (this.shouldAnimatePunchButton) {
              this.startPunchButtonAnimation()
            }
          })
        }
      } catch (error) {
        console.error('加载今日考勤状态失败:', error)
      }
    },

    // 加载本月考勤统计
    async loadMonthlyAttendanceStats() {
      // 合伙人不需要加载考勤统计
      if (!this.needsAttendanceFeature) return

      try {
        const now = new Date()
        const year = now.getFullYear()
        const month = now.getMonth() + 1

        const response = await AttendanceAPI.getMonthlyAttendanceStats({
          userId: this.currentUserId,
          year: year,
          month: month
        })

        if (response && response.data) {
          this.attendanceStats = response.data
        }
      } catch (error) {
        console.error('加载本月考勤统计失败:', error)
      }
    },

    // 处理打卡
    async handlePunchIn() {
      if (!this.needAttendance || this.todayChecked) {
        return
      }

      // 验证用户ID
      if (!this.currentUserId) {
        this.$message.error('用户信息缺失，请重新登录')
        return
      }

      this.punchLoading = true
      try {
        const today = this.formatDate(new Date())
        const punchData = {
          userId: this.currentUserId,
          date: today
        }

        console.log('打卡请求数据:', punchData)

        const response = await AttendanceAPI.punchIn(punchData)

        console.log('打卡响应数据:', response)

        if (response && response.success === true) {
          this.todayChecked = true
          this.todayPunchTime = this.formatTime(new Date())
          this.$message.success('打卡成功！')

          // 打卡成功后停止动画
          this.stopPunchButtonAnimation()

          // 重新加载统计数据
          this.loadMonthlyAttendanceStats()

          // 刷新今日考勤状态
          this.loadTodayAttendanceStatus()

          // 如果考勤日历已经打开，刷新其数据
          if (this.attendanceCalendarVisible && this.$refs.attendanceCalendar) {
            this.$refs.attendanceCalendar.refreshData()
          }
        } else {
          throw new Error((response && response.errMsg) || '打卡失败')
        }
      } catch (error) {
        console.error('打卡失败:', error)
        this.$message.error('打卡失败：' + (error.message || '未知错误'))
      } finally {
        this.punchLoading = false
      }
    },

    // 显示考勤日历
    showAttendanceCalendar() {
      // 合伙人不需要查看考勤日历
      if (!this.needsAttendanceFeature) {
        this.$message.warning('您的角色无需使用考勤功能')
        return
      }

      this.attendanceCalendarVisible = true
      // 等待组件渲染完成后刷新数据
      this.$nextTick(() => {
        if (this.$refs.attendanceCalendar) {
          this.$refs.attendanceCalendar.refreshData()
        }
      })
    },

    // 关闭考勤日历
    closeAttendanceCalendar() {
      this.attendanceCalendarVisible = false
    },

    // 显示本月排班
    showMonthlySchedule() {
      // 只有需要考勤功能的角色才能查看排班
      if (!this.needsAttendanceFeature) {
        this.$message.warning('您的角色无需使用考勤功能')
        return
      }

      this.monthlyScheduleVisible = true
      // 组件会通过 watch 自动加载数据，无需手动刷新
    },

    // 关闭本月排班
    closeMonthlySchedule() {
      this.monthlyScheduleVisible = false
    },

    // // 处理日历日期点击
    // async handleCalendarDateClick(date) {
    //   try {
    //     const response = await AttendanceAPI.getDateScheduleDetail({
    //       userId: this.currentUserId,
    //       date: date
    //     })

    //     if (response && response.data) {
    //       this.selectedDateSchedule = response.data
    //       this.scheduleDetailVisible = true
    //     } else {
    //       this.$message.info('该日期无排班信息')
    //     }
    //   } catch (error) {
    //     console.error('获取排班详情失败:', error)
    //     this.$message.error('获取排班详情失败')
    //   }
    // },

    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case 1: return 'warning' // 待打卡
        case 2: return 'success' // 已打卡
        case 3: return 'danger'  // 缺勤
        default: return 'info'
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 1: return '待打卡'
        case 2: return '已打卡'
        case 3: return '缺勤'
        default: return '未知'
      }
    },

    // 格式化日期
    formatDate(date) {
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const d = new Date(time)
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      return `${hours}:${minutes}`
    },

    viewAllTodos() {
      this.$router.push('/WkbTodoDetail')
    },
    viewAllNotifications() {
      this.$router.push('/WkbNotificationDetail')
    },
    handleTodo(item) {
      this.$router.push({
        path: '/WkbTodoDetail',
        query: { id: item.id }
      })
    },
    handleNotification(item) {
      this.$router.push({
        path: '/WkbNotificationDetail',
        query: { id: item.id }
      })
    },
    async loadNotifications() {
      this.notificationLoading = true
      try {
        const res = await notificationReadingPage(this.notificationParams)
        if (res.success) {
          // 如果是第一页，直接赋值；否则，追加数据
          if (this.notificationParams.current === 1) {
            this.notifications = res.data.records.map(item => ({
              ...item,
              time: this.formatTime(item.publish_time)
            }))
          } else {
            const newNotifications = res.data.records.map(item => ({
              ...item,
              time: this.formatTime(item.publish_time)
            }))
            this.notifications = [...this.notifications, ...newNotifications]
          }

          // 更新未读通知数量
          this.notificationCount = res.data.total
        }
      } catch (error) {
        this.$message.error('加载通知列表失败：' + (error.message || '未知错误'))
      }
      this.notificationLoading = false
    },
    async loadTodos() {
      this.todoLoading = true
      try {
        const res = await todoPage(this.todoParams)
        if (res.success) {
          this.todos = res.data.records
          this.todoCount = res.data.total
        }
      } catch (error) {
        this.$message.error('加载待办列表失败：' + (error.message || '未知错误'))
        this.todos = []
      }
      this.todoLoading = false
    },
    formatTime(timeStr) {
      if (!timeStr) return ''
      const now = new Date()
      const time = new Date(timeStr)
      const diff = now - time

      // 一分钟内
      if (diff < 60000) {
        return '刚刚'
      }
      // 一小时内
      if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前'
      }
      // 一天内
      if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前'
      }
      // 一周内
      if (diff < 604800000) {
        return Math.floor(diff / 86400000) + '天前'
      }
      // 超过一周
      return time.toLocaleDateString()
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

/* 问候区域 */
.greeting-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.greeting-content h2 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.weather-date {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #606266;
}

.weather-date i {
  margin-right: 8px;
}

/* 考勤统计区域 */
.greeting-attendance-stats {
  display: flex;
  align-items: center;
  margin-right: 30px;
  /* gap: 20px; */
}

.greeting-stat-item {
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
  padding: 10px 0px;
  border-radius: 8px;
  min-width: 80px;
}

.greeting-stat-item:hover {
  transform: translateY(-2px);
  background: #f5f7fa;
}

.greeting-stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
  line-height: 1;
}

.greeting-stat-value.warning {
  color: #E6A23C;
}

.greeting-stat-value.info {
  color: #909399;
}

.greeting-stat-label {
  font-size: 14px;
  color: #606266;
}

/* 打卡按钮样式 */
.punch-section {
  text-align: center;
  margin-right: 20px;
}

.punch-btn {
  width: 120px;
  height: 40px;
  font-size: 14px;
  border-radius: 20px;
  transition: transform 0.3s ease;
}

/* 打卡按钮跳动动画 */
.punch-btn-animate {
  animation: punchButtonBounce 1s ease-in-out;
}

@keyframes punchButtonBounce {
  0% { transform: translateY(0); }
  15% { transform: translateY(-8px); }
  30% { transform: translateY(0); }
  45% { transform: translateY(-6px); }
  60% { transform: translateY(0); }
  100% { transform: translateY(0); }
}

.punch-time {
  margin-top: 8px;
  color: #67C23A;
  font-weight: bold;
  font-size: 12px;
}

.quick-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.left-panel,
.right-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.left-panel {
  flex: 2;
}

.right-panel {
  flex: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
}

/* 快捷入口样式 */
.quick-access-content {
  /* 移除滚动条限制 */
}

.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  max-width: 100%;
}

/* 根据数量调整列数 */
@media (min-width: 768px) {
  .quick-access-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1200px) {
  .quick-access-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.quick-access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 10px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 95px;
}

.quick-access-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #409EFF;
  background: #f9fbff;
}

.item-icon {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409EFF, #66b1ff);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 18px;
  color: white;
  transition: all 0.3s ease;
}

.quick-access-item:hover .item-icon {
  transform: scale(1.1);
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  text-align: center;
}

/* 通知公告样式 */
.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #EBEEF5;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-indicator {
  margin-right: 15px;
  margin-top: 3px;
}

.notification-indicator i {
  font-size: 16px;
  color: #C0C4CC;
}

.notification-indicator .new-notification {
  color: #409EFF;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.empty-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 排班详情样式 */
.schedule-detail {
  padding: 20px 0;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  width: 100px;
  font-weight: 600;
  color: #333;
}

.detail-item span {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .greeting-section {
    flex-direction: column;
    text-align: center;
  }

  .greeting-attendance-stats {
    margin-top: 20px;
  }

  .quick-stats {
    margin-top: 15px;
  }

  .quick-access-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .quick-access-item {
    padding: 14px 10px;
    min-height: 85px;
  }

  .item-icon {
    width: 35px;
    height: 35px;
    margin-bottom: 8px;
    font-size: 16px;
  }

  .item-name {
    font-size: 12px;
  }
}
</style>
