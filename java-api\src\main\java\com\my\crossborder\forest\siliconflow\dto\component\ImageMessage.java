package com.my.crossborder.forest.siliconflow.dto.component;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class ImageMessage 
					extends Message {

	private String role;
	
	private List<Content> content;
	
	
	private ImageMessage() {
	}

	private ImageMessage(String role, List<Content> content) {
		super();
		this.role = role;
		this.content = content;
	}
	
	
	/**
	 * 构建系统提示词请求参数
	 * @param imageUrl
	 * @param prompt
	 * @return
	 */
	public static ImageMessage system(String imageUrl, String prompt) {
		List<Content> list = Lists.newArrayList(
			Content.imgageUrl(imageUrl),
			Content.text(prompt)
		);
		return new ImageMessage("system", list);
	}
	
	/**
	 * 构建用户提示词请求参数 
	 * @param content
	 * @return
	 */
	public static ImageMessage user(String imageUrl, String prompt) {
		List<Content> list = Lists.newArrayList(
			Content.imgageUrl(imageUrl),
			Content.text(prompt)
		);
		return new ImageMessage("user", list);
	}
	
	
	/**
	 * Content参数对象
	 */
	private static abstract class Content {
		
		/**
		 * 图片子类
		 */
		public static Content imgageUrl(String imageUrl) {
			return new ImageContent(imageUrl);
		}
		
		/**
		 * 文本子类
		 */
		public static Content text(String prompt) {
			return new TextContent(prompt);
		}
	}
	
	@Data @EqualsAndHashCode(callSuper=false)
	private static class ImageContent extends Content {
		
		String type;
		
		@JSONField(name = "image_url")
		ImageObj imageObj;
		
		
		
		public ImageContent(String imageUrl) {
			this.type = "image_url";
			this.imageObj = new ImageObj(imageUrl);
		}
	}
	
	@Data @AllArgsConstructor @EqualsAndHashCode(callSuper=false)
	private static class TextContent extends Content  {
		
		String type;
		
		String text;
		
		
		public TextContent(String text) {
			this.type = "text";
			this.text = text;
		}
	}
	
	
	@AllArgsConstructor @Data
	private static class ImageObj {
		
		String url;
	}
}
