package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.OrdRefundLog;
import com.my.crossborder.mybatis.mapper.OrdRefundLogMapper;
import com.my.crossborder.service.OrdRefundLogService;
import com.my.crossborder.controller.dto.ord_refund_log.OrdRefundLogInsertDTO;
import com.my.crossborder.controller.vo.ord_refund_log.OrdRefundLogPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 退款日志表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class OrdRefundLogServiceImpl extends ServiceImpl<OrdRefundLogMapper, OrdRefundLog> implements OrdRefundLogService {


	@Transactional
	@Override
	public void insert(OrdRefundLogInsertDTO insertDTO) {
		OrdRefundLog entity = BeanUtil.copyProperties(insertDTO, OrdRefundLog.class);
		this.save(entity);
	}

	@Override
	public List<OrdRefundLogPageVO> listByOrderSn(String orderSn) {
		LambdaQueryWrapper<OrdRefundLog> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(OrdRefundLog::getOrderSn, orderSn)
					.orderByDesc(OrdRefundLog::getOpTime);

		List<OrdRefundLog> logs = this.list(queryWrapper);
		return logs.stream()
				.map(log -> BeanUtil.copyProperties(log, OrdRefundLogPageVO.class))
				.collect(Collectors.toList());
	}

}
