package com.my.crossborder.controller.dto.sys_exchange_rate_day_range;

import java.util.List;

import javax.validation.constraints.NotNull;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_汇率日期区间
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysExchangeRateDayRangeDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
	@NotNull(message="startDay不能为空")
    private LocalDate startDay;

    /**
     * 结束日期
     */
	@NotNull(message="endDay不能为空")
    private LocalDate endDay;

}
