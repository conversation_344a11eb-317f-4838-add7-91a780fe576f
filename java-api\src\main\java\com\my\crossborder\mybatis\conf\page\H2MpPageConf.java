package com.my.crossborder.mybatis.conf.page;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;

/**
 * mybatisPlus分页配置
 */
@Profile({"h2"})
@Configuration
public class H2MpPageConf {
	
	
	// 配置后total才不会为0
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        PaginationInnerInterceptor pg = new PaginationInnerInterceptor();
        pg.setOverflow(true);
        pg.setOptimizeJoin(false);
        pg.setDbType(DbType.H2);
		interceptor.addInnerInterceptor(pg);

        return interceptor;
    }

}
