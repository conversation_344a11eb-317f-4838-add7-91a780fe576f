<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysExchangeRateDayRangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysExchangeRateDayRange">
        <id column="start_day" property="startDay" />
        <result column="end_day" property="endDay" />
        <result column="exchange_rate" property="exchangeRate" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        start_day, end_day, exchange_rate, update_user_id, update_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_exchange_rate_day_range.SysExchangeRateDayRangePageVO">
		SELECT
			start_day, end_day, exchange_rate, update_user_id, update_time
		FROM
			sys_exchange_rate_day_range AS t1
		<where>
        	1=1
	        <if test="day != null">
	           	AND t1.start_day &gt;= #{day} and t1.end_day &lt;= #{day}
            </if>
        </where>
    </select>

</mapper>
