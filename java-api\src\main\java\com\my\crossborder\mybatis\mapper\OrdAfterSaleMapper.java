package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSalePageDTO;
import com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSalePageVO;
import com.my.crossborder.mybatis.entity.OrdAfterSale;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 售后表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface OrdAfterSaleMapper extends BaseMapper<OrdAfterSale> {

	/**
	 * 分页
	 * @param page 分页参数
	 * @param pageDTO 查询参数
	 */
	Page<OrdAfterSalePageVO> page(OrdAfterSalePageDTO pageDTO);
	
}
