package com.my.crossborder.controller.dto.wkb_tag;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 修改_工作台_订单标签
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbTagUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
	@NotNull(message="id不能为空")
    private Integer id;

    /**
     * 订单号
     */
	@NotNull(message="orderSn不能为空")
    private String orderSn;

    /**
     * 场景
     */
	@NotNull(message="scene不能为空")
    private String scene;

    /**
     * 标签 (字典 SCENE_01_TAG)
     */
	@NotNull(message="tag不能为空")
    private String tag;

    /**
     * 创建人用户ID
     */
	@NotNull(message="createUserId不能为空")
    private Integer createUserId;

    /**
     * 创建时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

}
