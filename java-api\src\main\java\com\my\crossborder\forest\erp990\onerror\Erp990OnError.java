package com.my.crossborder.forest.erp990.onerror;

import java.net.SocketTimeoutException;

import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.my.crossborder.forest.BaseOnError;

import lombok.extern.slf4j.Slf4j;

/**
 * 接口异常处理
 */
@Slf4j
public class Erp990OnError extends BaseOnError {


	@SuppressWarnings("rawtypes")
	@Override
	public void onError(ForestRuntimeException e, ForestRequest req, ForestResponse response) {
	    Throwable cause = e.getCause();
	    if (cause instanceof SocketTimeoutException) {
	        this.errMsg = "[接口超时]";
	        return;
	    }
	    // TODO 处理禾宸物流接口401
//	    if (401 == response.getStatusCode()) {
//			this.errMsg = "[密钥无效]";
//			return;
//		}
		this.errMsg = e.getMessage();
		log.error("接口异常：{}", errMsg);
	}
	

}
