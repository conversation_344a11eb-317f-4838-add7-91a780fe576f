package com.my.crossborder.controller.dto.sys_attachment;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import com.my.crossborder.controller.dto.AttachmentIdListDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_附件表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysAttachmentInsertDTO extends AttachmentIdListDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 文件名
     */
	@NotNull(message="fileName不能为空")
    private String fileName;

    /**
     * 文件路径
     */
	@NotNull(message="path不能为空")
    private String path;

    /**
     * 表名
     */
	@NotNull(message="tableName不能为空")
    private String tableName;

    /**
     * 数据源id
     */
	@NotNull(message="dataSourceId不能为空")
    private String dataSourceId;

    /**
     * 文件原始名称
     */
	@NotNull(message="fileOriginalName不能为空")
    private String fileOriginalName;

	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

}
