package com.my.crossborder.controller.dto.wkb_notification;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_通知表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<Integer> idList;
	
}
