package com.my.crossborder.aopattachment;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.mybatis.entity.SysAttachment;
import com.my.crossborder.service.SysAttachmentService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 附件ID service.page切面
 * 拦截指定service的page  在pagevo在存入attachmentId, fileOriginalName
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class AttachmentServicePageAspect {
	
	private final SysAttachmentService sysAttachmentService;
	

    /**
     * 拦截多个service的insert方法
     */
    @Around("   "
		+ "  execution(* com.my.crossborder.service.OrdClaimService.page(..)) "
    )
    public Object interceptServiceMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行原方法
        Object result = joinPoint.proceed();
        
        // 获取对应实体类的表名
        String tableName = getEntityTableName(joinPoint.getTarget());
        
        // 处理分页结果
        if (result instanceof com.baomidou.mybatisplus.extension.plugins.pagination.Page) {
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<?> pageVO = (com.baomidou.mybatisplus.extension.plugins.pagination.Page<?>) result;
            
            if (pageVO.getRecords() != null && !pageVO.getRecords().isEmpty()) {
                // 提取所有记录的ID
                List<Integer> idList = new ArrayList<>();
                for (Object record : pageVO.getRecords()) {
                    try {
                        // 获取id字段的值
                        Method getId = record.getClass().getMethod("getId");
                        Object id = getId.invoke(record);
                        if (id != null) {
                            idList.add((Integer) id);
                        }
                    } catch (Exception e) {
                        log.error("获取记录ID失败: {}", e.getMessage());
                    }
                }
                
                if (!idList.isEmpty() && tableName != null) {
                    // 查询附件表数据
                    QueryWrapper<SysAttachment> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("table_name", tableName)
                               .in("data_source_id", idList);
                    
                    List<SysAttachment> attachments = sysAttachmentService.list(queryWrapper);
                    
                    // 建立数据源ID到附件的映射关系
                    Map<String, SysAttachment> attachmentMap = new HashMap<>();
                    for (SysAttachment attachment : attachments) {
                        attachmentMap.put(attachment.getDataSourceId(), attachment);
                    }
                    
                    // 将附件信息设置到每条记录中
                    for (Object record : pageVO.getRecords()) {
                        try {
                            // 获取记录ID
                            Method getId = record.getClass().getMethod("getId");
                            Object id = getId.invoke(record);
                            
                            if (id != null) {
                                String idStr = id.toString();
                                SysAttachment attachment = attachmentMap.get(idStr);
                                
                                if (attachment != null) {
                                    // 设置附件ID
                                    Method setAttachmentId = record.getClass().getMethod("setAttachmentId", Integer.class);
                                    setAttachmentId.invoke(record, attachment.getAttachmentId());
                                    
                                    // 设置文件原始名称
                                    Method setFileOriginalName = record.getClass().getMethod("setFileOriginalName", String.class);
                                    setFileOriginalName.invoke(record, attachment.getFileOriginalName());
                                }
                            }
                        } catch (Exception e) {
                            log.error("设置附件信息失败: {}", e.getMessage(), e);
                        }
                    }
                }
            }
        }
        
        return result;
    }

    /**
     * 获取最新插入记录的主键ID
     */
    private String getLatestPrimaryKeyId(Object serviceInstance, String serviceName) {
        try {
            if (serviceInstance instanceof IService) {
                @SuppressWarnings("unchecked")
                IService<Object> service = (IService<Object>) serviceInstance;
                
                QueryWrapper<Object> queryWrapper = new QueryWrapper<>();
                
                // 根据不同的服务确定主键字段名和排序字段
                String primaryKeyField = getPrimaryKeyField(serviceName);
                String orderByField = getOrderByField(serviceName);
                
                queryWrapper.orderByDesc(orderByField).last("LIMIT 1");
                Object latestEntity = service.getOne(queryWrapper);
                
                if (latestEntity != null) {
                    // 使用反射获取主键值
                    Method getter = getGetterMethod(latestEntity.getClass(), primaryKeyField);
                    if (getter != null) {
                        Object primaryKeyValue = getter.invoke(latestEntity);
                        return primaryKeyValue != null ? primaryKeyValue.toString() : null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取最新主键ID失败: {}", serviceName, e);
        }
        return null;
    }

    /**
     * 根据服务名获取主键字段名
     */
    private String getPrimaryKeyField(String serviceName) {
        return "id";
    }

    /**
     * 根据服务名获取排序字段名（用于获取最新记录）
     */
    private String getOrderByField(String serviceName) {
        return "id";
    }

    /**
     * 获取getter方法
     */
    private Method getGetterMethod(Class<?> clazz, String fieldName) {
        try {
            String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            return clazz.getMethod(methodName);
        } catch (Exception e) {
            log.debug("获取getter方法失败: {}.{}", clazz.getSimpleName(), fieldName);
            return null;
        }
    }

    /**
     * 获取实体类的表名
     */
    private String getEntityTableName(Object serviceInstance) {
        try {
            if (serviceInstance instanceof IService) {
                // 获取泛型类型
                Class<?> entityClass = getEntityClass(serviceInstance);
                if (entityClass != null) {
                    // 从TableName注解获取表名
                    TableName tableNameAnnotation = entityClass.getAnnotation(TableName.class);
                    if (tableNameAnnotation != null) {
                        return tableNameAnnotation.value();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取表名失败", e);
        }
        return null;
    }
    
    /**
     * 获取Service的实体类类型
     */
    private Class<?> getEntityClass(Object serviceInstance) {
        try {
            // 获取IService接口的第一个泛型参数，即实体类
            Class<?> clazz = serviceInstance.getClass();
            
            // 检查直接接口的泛型参数
            for (Class<?> itf : clazz.getInterfaces()) {
                if (IService.class.isAssignableFrom(itf)) {
                    // 获取第一个泛型参数
                    java.lang.reflect.Type[] genericInterfaces = clazz.getGenericInterfaces();
                    for (java.lang.reflect.Type type : genericInterfaces) {
                        if (type instanceof java.lang.reflect.ParameterizedType) {
                            java.lang.reflect.ParameterizedType parameterizedType = (java.lang.reflect.ParameterizedType) type;
                            java.lang.reflect.Type[] typeArguments = parameterizedType.getActualTypeArguments();
                            if (typeArguments.length > 0) {
                                // 第一个泛型参数就是实体类型
                                return (Class<?>) typeArguments[0];
                            }
                        }
                    }
                    break;
                }
            }
            
            // 如果直接接口没有找到，检查父类的泛型参数（处理ServiceImpl的情况）
            java.lang.reflect.Type genericSuperclass = clazz.getGenericSuperclass();
            if (genericSuperclass instanceof java.lang.reflect.ParameterizedType) {
                java.lang.reflect.ParameterizedType parameterizedType = (java.lang.reflect.ParameterizedType) genericSuperclass;
                java.lang.reflect.Type[] typeArguments = parameterizedType.getActualTypeArguments();
                if (typeArguments.length > 1) {
                    // ServiceImpl的第二个泛型参数是实体类型
                    return (Class<?>) typeArguments[1];
                }
            }
            
        } catch (Exception e) {
            log.error("获取实体类类型失败", e);
        }
        return null;
    }

} 