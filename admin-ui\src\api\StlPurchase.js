import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 采购结算API
 */

// 分页查询采购结算列表
export function pageStlPurchase(params) {
  return reqGet('/stl-purchase/page', params)
}

// 根据ID查询采购结算详情
export function getStlPurchaseById(id) {
  return reqGet(`/stl-purchase/${id}`)
}

// 新增采购结算
export function insertStlPurchase(data) {
  return reqPost('/stl-purchase', data)
}

// 修改采购结算
export function updateStlPurchase(data) {
  return reqPut('/stl-purchase', data)
}

// 删除采购结算
export function deleteStlPurchase(data) {
  return reqDelete('/stl-purchase', data)
}

/**
 * 获取用户月度采购数据（旧接口，保留兼容性）
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} params.year - 年份
 * @param {number} params.month - 月份
 */
export function getMonthlyPurchaseData(params) {
  return reqGet('/stl-purchase/monthly-purchase-data', params)
}

/**
 * 获取用户月度结算详情
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} params.year - 年份
 * @param {number} params.month - 月份
 */
export function getMonthlyPurchase(params) {
  return reqGet('/stl-purchase/monthly-purchase', params)
}

/**
 * 获取用户月度结算状态
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} params.year - 年份
 * @param {number} params.month - 月份
 */
export function getMonthlyPurchaseStatus(params) {
  return reqGet('/stl-purchase/monthly-purchase-status', params)
}

/**
 * 获取用户指定日期的采购详情
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {string} params.purchaseDate - 采购日期 (YYYY-MM-DD)
 */
export function getDailyPurchaseDetails(params) {
  return reqGet('/stl-purchase/daily-purchase-details', params)
}

