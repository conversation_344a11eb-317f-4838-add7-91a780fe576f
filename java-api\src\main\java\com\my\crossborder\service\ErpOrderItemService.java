package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemDeleteDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemInsertDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemPageDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemUpdateDTO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemDetailVO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemPageVO;
import com.my.crossborder.mybatis.entity.ErpOrderItem;

/**
 * 订单项表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ErpOrderItemService extends IService<ErpOrderItem> {

	/**
	 * 新增
	 */
	void insert(ErpOrderItemInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(ErpOrderItemUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	ErpOrderItemDetailVO detail(Long id);

	/**
	 * 分页
	 */
	Page<ErpOrderItemPageVO> page(ErpOrderItemPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(ErpOrderItemDeleteDTO deleteDTO);	

}
