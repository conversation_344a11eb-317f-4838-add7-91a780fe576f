<template>
  <el-dialog title="修改密码" :visible.sync="editFormVisible" width="25%" top="10vh" @click="closeDialog">
    <el-form label-width="120px" :model="editForm" :rules="rules" ref="editForm">
      <el-form-item label="新密码" prop="password">
        <el-input size="small" v-model="editForm.password" type="password" maxlength="16" auto-complete="off" placeholder="password" show-password></el-input>
      </el-form-item>
      <el-form-item label="确认新密码" prop="passwordConfirm">
        <el-input size="small" v-model="editForm.passwordConfirm" type="password" maxlength="16" auto-complete="off" placeholder="passwordConfirm" show-password></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="editFormVisible = false">取消</el-button>
      <el-button size="small" type="primary" :loading="loading" class="title" @click="submitForm('editForm')">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { sysUserChangeMyPassword } from '../../api/SysUser'
export default {
  name: 'MyPasswordEdit',
  props: ['childMsg'],
  data() {
    return {
      loading: false,
      editFormVisible: false,
      editForm: {
        password: undefined,
        passwordConfirm: undefined,
      },
      rules: {
        password: [ { required: true, message: '必填', trigger: 'blur' }],
        passwordConfirm: [
          { required: true, message: '必填', trigger: 'blur' },
          { min: 6, max: 16, message: '长度在 6 到 16 个字符', trigger: 'blur' }
        ],
      },
    }
  },

created() {
},

// 方法
methods: {
 submitForm(editData) {
    let _this = this;
    this.$refs[editData].validate(valid => {
      if (!valid) {
        return false;
      }
      sysUserChangeMyPassword(this.editForm)
        .then(res => {
          _this.editFormVisible = false
          _this.loading = false
          _this.$message({type: 'success', message: 'Success'})
          setTimeout(() => {
              this.$store.commit('logout', 'false')
              this.$router.push({ path: '/login' })
              this.$message({
                type: 'success',
                message: '退出登录成功!'
              })
            }, 1000)
        })
    })
  },
  closeDialog() {
    this.editFormVisible = false
  },
  show(){
    this.editFormVisible = true;
  },
}
}
</script>

<style>
.el-dialog .el-dialog__body {
    padding: 0px 20px!important;
}
</style>