<template>
  <el-radio-group
    :value="value"
    @input="handleInput"
    :disabled="disabled"
    :size="size">
    <el-radio
      v-for="item in options"
      :key="item.value"
      :label="item.value">{{ item.label }}</el-radio>
  </el-radio-group>
</template>

<script>
import { dictCategoryItems } from '../api/SysDictItem'

export default {
  name: 'DictRadioGroup',
  props: {
    // 字典分类ID
    categoryId: {
      type: String,
      required: true
    },
    // 当前值
    value: {
      type: [String, Number],
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 组件大小
    size: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      options: [],
      loading: false
    }
  },
  watch: {
    categoryId: {
      handler(newVal) {
        if (newVal) {
          this.loadOptions()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 处理选择值变化
    handleInput(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    },

    // 加载字典选项
    loadOptions() {
      if (!this.categoryId) {
        this.options = []
        return
      }

      this.loading = true
      dictCategoryItems({ categoryId: this.categoryId })
        .then(res => {
          this.loading = false
          // 根据接口返回格式调整数据结构
          if (res.data && Array.isArray(res.data)) {
            this.options = res.data.map(item => ({
              label: item.itemName || item.label || item.name,
              value: item.itemValue || item.value || item.code,
              ...item
            }))
          } else {
            this.options = []
          }
        })
        .catch(err => {
          this.loading = false
          console.error('加载字典数据失败:', err)
          this.$message.error('加载字典数据失败')
          this.options = []
        })
    }
  }
}
</script> 