package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_log.SysLogPageDTO;
import com.my.crossborder.controller.vo.sys_log.SysLogPageVO;
import com.my.crossborder.mybatis.entity.SysLog;

/**
 * 操作日志表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface SysLogMapper extends BaseMapper<SysLog> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysLogPageVO> page(SysLogPageDTO pageDTO);
	
}
