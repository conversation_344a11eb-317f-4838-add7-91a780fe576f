package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_log.SysLogDeleteDTO;
import com.my.crossborder.controller.dto.sys_log.SysLogInsertDTO;
import com.my.crossborder.controller.dto.sys_log.SysLogPageDTO;
import com.my.crossborder.controller.vo.sys_log.SysLogDetailVO;
import com.my.crossborder.controller.vo.sys_log.SysLogPageVO;
import com.my.crossborder.mybatis.entity.SysLog;

/**
 * 操作日志表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface SysLogService extends IService<SysLog> {

	/**
	 * 新增
	 */
	void insert(SysLogInsertDTO insertDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysLogDetailVO detail(Long id);

	/**
	 * 分页
	 */
	Page<SysLogPageVO> page(SysLogPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysLogDeleteDTO deleteDTO);	

}
