<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysAttachment">
        <id column="attachment_id" property="attachmentId" />
        <result column="file_name" property="fileName" />
        <result column="path" property="path" />
        <result column="table_name" property="tableName" />
        <result column="data_source_id" property="dataSourceId" />
        <result column="file_original_name" property="fileOriginalName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        attachment_id, file_name, path, table_name, data_source_id, file_original_name, create_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_attachment.SysAttachmentPageVO">
		SELECT
			attachment_id, file_name, path, table_name, data_source_id, file_original_name, create_time
		FROM
			sys_attachment AS t1
		<where>
        	1=1
	        <if test="attachmentId != null and attachmentId != ''">
	           	AND t1.attachment_id = #{attachmentId}
            </if>
	        <if test="fileName != null and fileName != ''">
	           	AND t1.file_name like concat('%', #{fileName}, '%')
            </if>
	        <if test="path != null and path != ''">
	           	AND t1.path like concat('%', #{path}, '%')
            </if>
	        <if test="tableName != null and tableName != ''">
	           	AND t1.table_name like concat('%', #{tableName}, '%')
            </if>
	        <if test="dataSourceId != null and dataSourceId != ''">
	           	AND t1.data_source_id = #{dataSourceId}
            </if>
	        <if test="fileOriginalName != null and fileOriginalName != ''">
	           	AND t1.file_original_name like concat('%', #{fileOriginalName}, '%')
            </if>
	        <if test="createTimeStart != null">
	           	AND t1.create_time &gt;= #{createTimeStart}
            </if>
	        <if test="createTimeEnd != null">
	           	AND t1.create_time &lt;= #{createTimeEnd}
            </if>
        </where>
    </select>

</mapper>
