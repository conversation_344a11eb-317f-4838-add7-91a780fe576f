package com.my.crossborder.controller.dto.stl_refund;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.stl_refund.StlRefundPageVO;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_结算_退款结算表
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class StlRefundPageDTO 
						extends PageDTO<StlRefundPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 退款员工ID
     */
    private Integer refundUserId;

    /**
     * 退款日期_开始
     */
    private LocalDate refundDateStart;

    /**
     * 退款日期_结束
     */
    private LocalDate refundDateEnd;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 结算日期
     */
    private LocalDateTime settlementDate;

    /**
     * 备注( 使用常见标签快速填写）
     */
    private String remark;

    /**
     * 结算操作员用户id
     */
    private Integer createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
