package com.my.crossborder.controller;


import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryDeleteDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryInsertDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryPageDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryDetailVO;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryItemsVO;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryPageVO;
import com.my.crossborder.service.SysDictCategoryService;
import com.my.crossborder.service.SysDictItemService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 数据字典-类别表 
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@RestController
@RequestMapping("/api/sys-dict-category")
@RequiredArgsConstructor
public class SysDictCategoryController {

    private final SysDictCategoryService sysDictCategoryService;
    private final SysDictItemService sysDictItemService;
    

    /**
    * 新增
    */
    @SaCheckPermission("sys-dict:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysDictCategoryInsertDTO insertDTO) {
    	this.sysDictCategoryService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("sys-dict:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody SysDictCategoryUpdateDTO updateDTO) {
    	this.sysDictCategoryService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<SysDictCategoryDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.sysDictCategoryService.detail(id));
    }
    
    /**
     * 查询所有数据
     */
    @GetMapping("/list")
    public StdResp<List<SysDictCategoryPageVO>> list() {
        List<SysDictCategoryPageVO> list = this.sysDictCategoryService.listAll();
        return StdResp.success(list);
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysDictCategoryPageVO>> page(SysDictCategoryPageDTO pageDTO) {
        Page<SysDictCategoryPageVO> page = this.sysDictCategoryService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("sys-dict:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysDictCategoryDeleteDTO deleteDTO) {
    	this.sysDictCategoryService.delete(deleteDTO);
		return StdResp.success();
    }
    
    /**
     * 查询字典项
     * @param categoryId
     */
    @GetMapping(value = "items")
    public StdResp<List<SysDictCategoryItemsVO>> items(@RequestParam("categoryId")String categoryId) {
    	List<SysDictCategoryItemsVO> data = this.sysDictItemService.listByCategoryId(categoryId)
    			.stream()
    			.map(t -> new SysDictCategoryItemsVO(t.getItemValue(), t.getItemName(), t.getColor()))
    			.collect(Collectors.toList());
        return StdResp.success(data);
    }
    
    
}