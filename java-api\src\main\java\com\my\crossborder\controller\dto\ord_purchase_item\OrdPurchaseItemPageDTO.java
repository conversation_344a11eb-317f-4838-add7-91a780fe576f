package com.my.crossborder.controller.dto.ord_purchase_item;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.ord_purchase_item.OrdPurchaseItemPageVO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_采购订单明细表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseItemPageDTO 
						extends PageDTO<OrdPurchaseItemPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单明细id
     */
    private String orderItemId;

    /**
     * 采购途径 字典PURCHASE_CHANNEL
     */
    private String purchaseChannel;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购人id
     */
    private Integer purchaseUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
