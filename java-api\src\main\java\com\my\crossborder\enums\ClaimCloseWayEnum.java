package com.my.crossborder.enums;

/**
 * 理赔处理方法枚举
 * CLAIM_CLOSE_WAY
 */
public enum ClaimCloseWayEnum {
    
    /**
     * 补寄
     */
    RESEND("1", "补寄"),

    /**
     * 退款
     */
    REFUND("2", "退款");
    
    private final String code;
    private final String desc;
    
    ClaimCloseWayEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static ClaimCloseWayEnum getByCode(String code) {
        for (ClaimCloseWayEnum way : values()) {
            if (way.getCode().equals(code)) {
                return way;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     */
    public static String getDescByCode(String code) {
        ClaimCloseWayEnum way = getByCode(code);
        return way != null ? way.getDesc() : null;
    }
}
