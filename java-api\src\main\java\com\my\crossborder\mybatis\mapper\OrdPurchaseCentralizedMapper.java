package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedPageDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedPageVO;
import com.my.crossborder.mybatis.entity.OrdPurchaseCentralized;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 集中采购 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface OrdPurchaseCentralizedMapper extends BaseMapper<OrdPurchaseCentralized> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<OrdPurchaseCentralizedPageVO> page(OrdPurchaseCentralizedPageDTO pageDTO);
	
}
