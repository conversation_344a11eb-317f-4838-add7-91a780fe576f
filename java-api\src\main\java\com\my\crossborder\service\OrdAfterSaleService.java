package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.OrdAfterSale;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleInsertDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSalePageDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleUpdateDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleDeleteDTO;
import com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSaleDetailVO;
import com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSalePageVO;

/**
 * 售后表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface OrdAfterSaleService extends IService<OrdAfterSale> {

	/**
	 * 新增
	 */
	void insert(OrdAfterSaleInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(OrdAfterSaleUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	OrdAfterSaleDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<OrdAfterSalePageVO> page(OrdAfterSalePageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(OrdAfterSaleDeleteDTO deleteDTO);

	/**
	 * 确认完成
	 * @param id 售后记录ID
	 */
	void confirmComplete(Integer id);

}
