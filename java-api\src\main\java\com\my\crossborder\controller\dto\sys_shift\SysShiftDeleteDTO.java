package com.my.crossborder.controller.dto.sys_shift;

import java.io.Serializable;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_排班日期表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 排班日期
     */
	@NotNull(message="shiftDay不能为空")
    private LocalDate shiftDay;

    /**
     * 店铺ID
     */
	@NotNull(message="shopId不能为空")
    private Integer shopId;

}
