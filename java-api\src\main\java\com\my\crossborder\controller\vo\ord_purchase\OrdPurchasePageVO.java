package com.my.crossborder.controller.vo.ord_purchase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_采购订单主表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchasePageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单状态
     */
    private String orderStates;
    
    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 下单时间
     */
    private LocalDateTime createTime;

    /**
     * 订单总金额（erp_order表total_price）
     */
    private BigDecimal totalPrice;

    /**
     * 订单入账金额
     */
    private BigDecimal incomeAmount;

    /**
     * 预计重量
     */
    private BigDecimal expectWeight;

    /**
     * 采购总金额（ord_purchase_item表purchase_amount求和）
     */
    private BigDecimal totalPurchaseAmount;

    /**
     * 预计利润（计算="入账金额"-"采购总金额"-"实际运费"）
     */
    private BigDecimal expectedProfit;

    /**
     * 出库时间（erp_order表out_time）
     */
    private LocalDateTime outTime;

    /**
     * 实际重量
     */
    private BigDecimal chargeWeight;

    /**
     * 贴标费
     */
    private BigDecimal stickFee;
    
    /**
     * 进店费
     */
    private BigDecimal inShopeeFee;
    
    /**
     * 运费
     */
    private BigDecimal unitFee;

    /**
     * 实际利润（计算="入账金额"-"采购总金额"-"实际运费"）
     */
    private BigDecimal actualProfit;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 订单明细列表
     */
    private List<OrdPurchaseItemWithExpressVO> orderItems;
 
    /**
     * 工作笔记列表
     */
    private List<WkbNoteDetailVO> notes;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;
}
