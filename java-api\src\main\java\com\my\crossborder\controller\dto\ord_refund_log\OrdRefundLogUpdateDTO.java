package com.my.crossborder.controller.dto.ord_refund_log;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 修改_退款日志表
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdRefundLogUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
	@NotNull(message="id不能为空")
    private Integer id;

    /**
     * 订单号
     */
	@NotNull(message="orderSn不能为空")
    private String orderSn;

    /**
     * 操作名称
     */
	@NotNull(message="opName不能为空")
    private String opName;

    /**
     * 操作详情
     */
	@NotNull(message="opDetail不能为空")
    private String opDetail;

    /**
     * 操作人id
     */
	@NotNull(message="opUserId不能为空")
    private Integer opUserId;

    /**
     * 操作时间
     */
	@NotNull(message="opTime不能为空")
    private LocalDateTime opTime;

}
