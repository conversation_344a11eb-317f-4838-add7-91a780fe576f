package com.my.crossborder.controller.dto.wkb_notification;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.wkb_notification.WkbNotificationPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_通知表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationPageDTO 
						extends PageDTO<WkbNotificationPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知id
     */
    private Integer id;

    /**
     * 类别
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布用户id
     */
    private Integer publishUserId;

}
