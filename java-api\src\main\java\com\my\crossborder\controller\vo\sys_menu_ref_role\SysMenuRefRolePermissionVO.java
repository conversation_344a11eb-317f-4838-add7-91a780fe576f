package com.my.crossborder.controller.vo.sys_menu_ref_role;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * menuId和permission
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenuRefRolePermissionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色id
     */
    private Integer roleId;

    /**
     * 菜单id
     */
    private String menuId;
    
    /**
     * 是否菜单
     */
    private Boolean menu;
    
    /**
     * 权限
     */
    private String permission;

}
