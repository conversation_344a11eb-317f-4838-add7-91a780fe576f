package com.my.crossborder.controller.vo.erp_order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemDetailVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_订单主表
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpOrderDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 平台订单ID
     */
    private String orderId;

    /**
     * 订单ID1
     */
    private String orderId1;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺其他名称
     */
    private String shopOtherName;

    /**
     * 店铺登录类型
     */
    private String shopLoginType;

    /**
     * 订单总价
     */
    private BigDecimal totalPrice;

    /**
     * 第三方托管价格
     */
    private BigDecimal escrowPrice;

    /**
     * 订单状态码
     */
    private String orderStates;

    /**
     * 订单状态名称
     */
    private String orderStatesName;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 地区
     */
    private String region;

    /**
     * 买家地址姓名
     */
    private String buyerAddressName;

    /**
     * 买家地址电话
     */
    private String buyerAddressPhone;

    /**
     * 买家取消原因
     */
    private String buyerCancelReason;

    /**
     * 买家是否已评价
     */
    private String buyerIsRated;

    /**
     * 买家最后修改地址时间
     */
    private LocalDateTime buyerLastChangeAddressTime;

    /**
     * 买家交易手续费
     */
    private BigDecimal buyerTxnFee;

    /**
     * 收货地址
     */
    private String shippingAddress;

    /**
     * 实际承运商
     */
    private String actualCarrier;

    /**
     * 配送方式
     */
    private String shippingMethod;

    /**
     * 平台配送方式
     */
    private String platShipping;

    /**
     * 平台配送方法
     */
    private String platShippingMethod;

    /**
     * 包裹编号
     */
    private String packageNumber;

    /**
     * 第三方单号
     */
    private String thirdNo;

    /**
     * 第三方交易号
     */
    private String thirdPartyTn;

    /**
     * 安排取件截止日期
     */
    private LocalDateTime arrangePickupByDate;

    /**
     * 自动取消3PL确认日期
     */
    private Long autoCancel3plAckDate;

    /**
     * 自动取消安排发货日期
     */
    private LocalDateTime autoCancelArrangeShipDate;

    /**
     * 付款截止日期
     */
    private LocalDateTime paybyDate;

    /**
     * 评价截止日期
     */
    private LocalDateTime rateByDate;

    /**
     * 取件时间
     */
    private LocalDateTime pickupTime;

    /**
     * 取件截止时间
     */
    private LocalDateTime pickupCutoffTime;

    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 发货确认时间
     */
    private LocalDateTime shippingConfirmTime;

    /**
     * 出库时间
     */
    private LocalDateTime outTime;

    /**
     * 实际出库时间
     */
    private LocalDateTime realOutTime;

    /**
     * 清理时间
     */
    private LocalDateTime cleanTime;

    /**
     * Shopee申请邮寄标识
     */
    private String shopeeApplyMailFlag;

    /**
     * Shopee申请邮寄时间
     */
    private LocalDateTime shopeeApplyMailTime;

    /**
     * Shopee入库标识
     */
    private String shopeeInShopeFlag;

    /**
     * Shopee入库时间
     */
    private LocalDateTime shopeeInShopeTime;

    /**
     * Shopee备注
     */
    private String shopeeRemark;

    /**
     * 平台申请邮寄日期
     */
    private LocalDate platApplyMailDate;

    /**
     * 平台申请邮寄时间
     */
    private LocalDateTime platApplyMailTime;

    /**
     * 申请邮寄标识
     */
    private String applyMailFlag;

    /**
     * 发货标识
     */
    private String deliverFlag;

    /**
     * 配送标识
     */
    private String deliveryFlag;

    /**
     * 打包标识
     */
    private Integer packFlag;

    /**
     * 拣货标识
     */
    private String pickFlag;

    /**
     * 出库标识
     */
    private String outFlag;

    /**
     * 实际出库标识
     */
    private String realOutFlag;

    /**
     * 清理标识
     */
    private String cleanFlag;

    /**
     * 隔离标识
     */
    private String isolateFlag;

    /**
     * 关联标识
     */
    private String correlationFlag;

    /**
     * 保险标识
     */
    private String baoFlag;

    /**
     * 预售标识
     */
    private String preFlag;

    /**
     * 部分发货标识
     */
    private String partFlag;

    /**
     * 一对多标识
     */
    private String oneManyFlag;

    /**
     * 删除标识
     */
    private String deleteFlag;

    /**
     * 快递100打印标识
     */
    private String kuaidi100PrintFlag;

    /**
     * 快递100打印时间
     */
    private LocalDateTime kuaidi100PrintTime;

    /**
     * 入库标识
     */
    private String putInFalg;

    /**
     * 验货状态
     */
    private Integer examineGoodsStatus;

    /**
     * 超额金额
     */
    private BigDecimal overAmount;

    /**
     * 平台替代标识
     */
    private String palt;

    /**
     * 平台标识
     */
    private Integer pf;

    /**
     * Go信标识
     */
    private Integer goXin;

    /**
     * 包装箱尺寸
     */
    private String cartonSize;

    /**
     * 硬币抵扣
     */
    private BigDecimal coinOffset;

    /**
     * 取件尝试次数
     */
    private Integer pickupAttempts;

    /**
     * 销售渠道
     */
    private String channel;

    /**
     * 列表类型
     */
    private String listType;

    /**
     * 物流状态
     */
    private String logisticsStatus;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态扩展
     */
    private String statusExt;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 手工订单标识
     */
    private String handOrder;

    /**
     * 换货ID
     */
    private String exchangeId;

    /**
     * 附加交易ID
     */
    private String addOnDealId;

    /**
     * 促销活动ID
     */
    private String promotionId;

    /**
     * 快照ID
     */
    private String snapshotId;

    /**
     * 消息
     */
    private String message;

    /**
     * 用户消息
     */
    private String userMessage;

    /**
     * 订单详情
     */
    private String orderDetail;

    /**
     * 订单表单
     */
    private String orderForm;

    /**
     * 备注
     */
    private String remark;

    /**
     * 取消原因扩展
     */
    private String cancelReasonExt;

    /**
     * 取消用户ID
     */
    private String cancelUserid;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 系统用户ID
     */
    private Long sysUserId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 平台用户ID
     */
    private String platUserId;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 角色
     */
    private String role;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 订单商品列表
     */
    private List<ErpOrderItemDetailVO> orderItems;

}
