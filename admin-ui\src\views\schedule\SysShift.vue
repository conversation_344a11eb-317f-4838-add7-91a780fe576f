<template>
  <div class="schedule-management">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">排班与考勤</el-breadcrumb-item>
      <el-breadcrumb-item>排班管理</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 操作工具栏 -->
    <div class="toolbar" style="margin-top: 5px;">
    </div>

    <!-- 主内容区域 -->
    <div class="content-container">
      <!-- 左侧日历 -->
      <SysShiftCalendar
        v-model="selectedDate"
        :schedule-data="scheduleData"
        @date-pick="handleDatePick"
        @month-change="handleMonthChange"
      />

      <!-- 右侧排班表格 -->
      <div class="schedule-table-container">
        <div class="table-header">
          <h3>{{ formatDate(selectedDate) }} 排班列表</h3>
          <div class="table-actions">
            <el-button v-permission="['sys-shift:insert']" type="primary" size="small" @click="addSchedule">
              <i class="el-icon-plus"></i> 添加排班
            </el-button>
            <el-button v-permission="['sys-shift:copy']" type="success" size="small" @click="quickCopySchedule" style="margin-left: 10px;">
              <i class="el-icon-copy-document"></i> 快速复制
            </el-button>
          </div>
        </div>
        <el-table :data="filteredScheduleData" :key="formatDateForKey(selectedDate)" style="width: 100%" border
          v-loading="loading">
          <el-table-column prop="shopName" label="店铺名称" min-width="120" align="center">
          </el-table-column>
          <el-table-column label="客服" min-width="120" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isEditing">
                <el-select v-model="scope.row.serviceUserId" placeholder="请选择" filterable
                  @change="(val) => handleStaffChange(val, scope.row)">
                  <el-option v-for="item in staffOptions" :key="item.id" :label="item.name" :value="item.id"
                    :disabled="isStaffssignedInOtherShops(item.id, scope.row.shopId)">
                    <span>{{ item.name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">
                      <span v-if="isStaffssignedInOtherShops(item.id, scope.row.shopId)">已分配</span>
                    </span>
                  </el-option>
                </el-select>
              </div>
              <div v-else>
                {{ getStaffName(scope.row.serviceUserId) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="supervisorUserId" label="客服主管" min-width="120" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isEditing">
                <el-select v-model="scope.row.supervisorUserId" placeholder="请选择" filterable>
                  <el-option v-for="item in supervisorOptions" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </div>
              <div v-else>
                {{ getStaffName(scope.row.supervisorUserId) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template slot-scope="scope">
              <div v-if="canEditSchedule(scope.row)">
                <div v-if="scope.row.isEditing">
                  <el-button size="mini" type="text" @click="saveRowEdit(scope.row)">
                    <i class="el-icon-check"></i> 保存
                  </el-button>
                  <el-button size="mini" type="text" @click="cancelRowEdit(scope.row)">
                    <i class="el-icon-close"></i> 取消
                  </el-button>
                </div>
                <div v-else>
                  <el-button v-permission="['sys-shift:update']" size="mini" type="text" @click="handleRowEdit(scope.row)">
                    <i class="el-icon-edit"></i> 编辑
                  </el-button>
                  <el-button v-permission="['sys-shift:delete']" size="mini" type="text" @click="handleDelete(scope.row)" style="color: #F56C6C;">
                    <i class="el-icon-delete"></i> 删除
                  </el-button>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty-table-placeholder">
              <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-top: 40px; margin-bottom: 0px;"></i>
              <p>当前日期暂无排班数据</p>
            </div>
          </template>
        </el-table>
      </div>
    </div>

    <!-- 添加排班弹窗组件 -->
    <SysShiftEdit
      :visible.sync="scheduleDialogVisible"
      :selected-date="selectedDate"
      :available-shops="availableShops"
      :staff-options="staffOptions"
      :supervisor-options="supervisorOptions"
      @save-success="handleScheduleSaveSuccess"
      @close="handleScheduleDialogClose"
    />

  </div>
</template>

<script>
import * as SysShiftAPI from '@/api/SysShift.js'
import { getAllEnabledShops } from '@/api/SysShop'
import { getAllSupervisorUsers, getAvailableStaffForSchedule } from '@/api/SysUser'
import SysShiftEdit from './SysShiftEdit.vue'
import SysShiftCalendar from './SysShiftCalendar.vue'

export default {
  name: 'SysShift',
  components: {
    SysShiftEdit,
    SysShiftCalendar
  },
  data() {
    return {
      // 日历相关
      selectedDate: new Date(),

      // 排班数据
      scheduleData: [],

      // 店铺数据
      shopData: [],

      // 员工数据
      staffData: [],

      // 加载状态
      loading: false,

      // 添加排班对话框
      scheduleDialogVisible: false,

      // 行编辑临时数据
      editingRowData: null,

      // 可用于排班的店铺
      availableShops: []
    }
  },
  computed: {
    // 过滤当前选中日期的排班数据，默认只显示已排班的店铺
    filteredScheduleData() {
      const dateStr = this.formatDateForKey(this.selectedDate);

      // 获取当前日期的已排班数据
      let scheduledData = this.scheduleData.filter(item => item.date === dateStr);

      // 只保留有设置人员的记录
      scheduledData = scheduledData.filter(item => item.serviceUserId);

      return scheduledData;
    },

    // 可用于排班的店铺（未排班的店铺）
    availableShops() {
      const dateStr = this.formatDateForKey(this.selectedDate);
      const scheduledShopIds = this.scheduleData
        .filter(item => item.date === dateStr && (item.serviceUserId))
        .map(item => item.shopId);

      return this.shopData
        .filter(shop => !scheduledShopIds.includes(shop.id));
    },

    // 可用于排班的店铺数据（带有排班相关字段）
    availableShopsData() {
      return this.availableShops.map(shop => ({
        shopId: shop.id,
        shopName: shop.shopName,
        shopNo: shop.shopNo,
        serviceUserId: null,
        supervisorUserId: null
      }));
    },

    // 客服人员选项（后端已排除主管客服角色22，前端排除合伙人角色12）
    staffOptions() {
      return this.staffData.filter(staff =>
        staff.position !== 'supervisor' && staff.roleId !== 12
      );
    },

    // 主管选项（只显示客服主管角色22）
    supervisorOptions() {
      return this.staffData.filter(staff => staff.position === 'supervisor');
    },


  },
  created() {
    // 初始化数据
    this.initializeData();
  },
  watch: {
    // 监听日期变化，更新数据
    selectedDate() {
      // 强制重新渲染表格
      this.$nextTick(() => {
        // 使用$nextTick确保DOM更新后再进行操作
        this.$set(this, 'scheduleData', [...this.scheduleData]);
      });
    }
  },
  methods: {
    // 格式化日期
    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 格式化日期为中文显示
    formatDateChinese(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = d.getMonth() + 1;
      const day = d.getDate();
      const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
      const weekday = weekdays[d.getDay()];
      return `${year}年${month}月${day}日 (周${weekday})`;
    },



    // 格式化日期为数据键
    formatDateForKey(date) {
      return this.formatDate(date);
    },

    // 处理日期选择
    handleDatePick(date) {
      this.selectedDate = date;
    },

    // 处理月份变化
    async handleMonthChange(date) {
      console.log('月份变化，重新加载排班数据:', date);
      await this.loadScheduleDataFromServer();
    },

    // 获取员工姓名
    getStaffName(staffId) {
      if (!staffId) return '';
      const staff = this.staffData.find(s => s.id === staffId);
      return staff ? staff.name : '';
    },

    // 获取员工状态
    getStaffStatus(staffId, date = null) {
      if (!staffId) return 'normal';

      // 直接返回员工默认状态
      const staff = this.staffData.find(s => s.id === staffId);
      return staff ? staff.status : 'normal';
    },

    // 检查是否可以编辑排班
    canEditSchedule(row) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const rowDate = new Date(row.date);
      rowDate.setHours(0, 0, 0, 0);

      // 允许当天和未来的日期进行编辑
      // 确保当天可以修改，允许当天及以后的日期编辑
      console.log('编辑权限检查:', {
        rowDate: row.date,
        today: this.formatDate(today),
        rowDateObj: rowDate,
        todayObj: today,
        canEdit: rowDate >= today
      });

      return rowDate >= today;
    },

    // 检查员工是否已分配
    isStaffssigned(staffId) {
      return this.availableShopsData.some(shop =>
        shop.serviceUserId === staffId
      );
    },

    // 添加排班
    addSchedule() {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDay = new Date(this.selectedDate);
      selectedDay.setHours(0, 0, 0, 0);

      if (selectedDay < today) {
        this.$message({
          message: '不能为过去的日期添加排班',
          type: 'warning'
        });
        return;
      }

      // 获取当前日期
      const dateStr = this.formatDateForKey(this.selectedDate);

      console.log('当前日期:', dateStr);
      console.log('所有店铺:', JSON.stringify(this.shopData));

      // 获取当前日期已排班的店铺ID (或B岗有人员)
      const scheduledData = this.scheduleData.filter(item => item.date === dateStr);
      const scheduledShopIds = scheduledData
        .filter(item => item.serviceUserId)
        .map(item => item.shopId);

      console.log('已排班店铺IDs:', scheduledShopIds);

      // 获取未排班的店铺
      const unscheduledShops = this.shopData.filter(shop => !scheduledShopIds.includes(shop.id));
      console.log('未排班店铺:', unscheduledShops);

      if (unscheduledShops.length === 0) {
        this.$message({
          message: '当前日期所有店铺已排班',
          type: 'warning'
        });
        return;
      }

      // 准备未排班的店铺数据
      this.availableShops = unscheduledShops;

      this.scheduleDialogVisible = true;
    },

    // 处理排班保存成功事件
    async handleScheduleSaveSuccess(insertData) {
      console.log('排班保存成功，重新加载数据:', insertData);
      // 重新加载数据以刷新界面
      await this.loadScheduleDataFromServer();
    },

    // 处理排班弹窗关闭事件
    handleScheduleDialogClose() {
      // 可以在这里添加关闭时的清理逻辑
      console.log('排班弹窗已关闭');
    },

    // 快速复制排班
    quickCopySchedule() {
      const currentDateStr = this.formatDateForKey(this.selectedDate);

      // 检查当前日期是否有排班数据
      const currentScheduleData = this.scheduleData.filter(item => item.date === currentDateStr);
      if (currentScheduleData.length === 0) {
        this.$message({
          message: '当前日期没有排班数据，无法复制',
          type: 'warning'
        });
        return;
      }

      // 显示确认对话框
      this.$confirm(
        `将复制当前日期(${currentDateStr})的排班安排到今天往后7个自然日。\n\n` +
        `当前日期共有 ${currentScheduleData.length} 个店铺的排班安排。\n` +
        `如果目标日期已有排班，将会被覆盖。\n\n` +
        `确认执行此操作吗？`,
        '快速复制排班确认',
        {
          confirmButtonText: '确认复制',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      ).then(async () => {
        try {
          this.loading = true;

          // 调用快速复制API
          const response = await SysShiftAPI.quickCopy({
            sourceDate: currentDateStr
          });

          console.log('快速复制响应:', response);

          // 显示成功消息
          this.$message({
            type: 'success',
            message: (response.data && response.data.message) || '排班复制成功！',
            duration: 4000
          });

          // 重新加载排班数据以刷新界面
          await this.loadScheduleDataFromServer();

        } catch (error) {
          console.error('快速复制排班失败:', error);
          this.$message.error('快速复制失败：' + (error.message || '未知错误'));
        } finally {
          this.loading = false;
        }
      }).catch(() => {
        // 用户取消操作
        console.log('用户取消了快速复制操作');
      });
    },

    // 删除排班
    async handleDelete(row) {
      this.$confirm('确认删除该排班?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.loading = true;

          // 删除行
          await SysShiftAPI.deleteShift({ shiftDay: row.date, shopId: row.shopId });

          console.log('删除API调用成功，开始更新本地数据');

          // 方案1：从本地数据中删除（快速响应）
          const index = this.scheduleData.findIndex(item =>
            item.date === row.date &&
            item.shopId === row.shopId
          );
          if (index > -1) {
            this.scheduleData.splice(index, 1);
            console.log('已从本地数据中删除排班记录:', row);
          }

          // 方案2：重新加载数据（确保数据一致性）
          // 可选择使用方案1或方案2，这里同时使用以确保数据正确性
          await this.loadScheduleDataFromServer();
          console.log('已重新加载排班数据');

          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        } catch (error) {
          console.error('删除失败:', error);
          this.$message({
            type: 'error',
            message: '删除失败，请重试'
          });
        } finally {
          this.loading = false;
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    // 检查员工是否已分配在其他店铺
    isStaffssignedInOtherShops(staffId, shopId) {
      // 获取当前选中日期的字符串表示
      const dateStr = this.formatDateForKey(this.selectedDate);

      // 允许一个人员同时服务多个店铺，所以不做限制
      // 只检查B岗是否重复分配
      return this.scheduleData.some(item =>
        item.date === dateStr &&
        item.shopId !== shopId &&
        false
      );
    },


    // ===== 行编辑功能方法 =====
    // 开始编辑某一行
    handleRowEdit(row) {
      console.log('开始编辑行:', row);

      // 保存编辑前的原始数据，用于取消时恢复
      this.editingRowData = JSON.parse(JSON.stringify({
        serviceUserId: row.serviceUserId,
        supervisorUserId: row.supervisorUserId
      }));

      // 设置为编辑状态
      this.$set(row, 'isEditing', true);

      console.log('行编辑状态已开启');
    },

    // 保存行编辑的更改
    async saveRowEdit(row) {
      console.log('保存行编辑:', row);

      // 验证客服和客服主管必须同时非空或同时为空
      const hasStaff = row.serviceUserId && row.serviceUserId !== '';
      const hasSupervisor = row.supervisorUserId && row.supervisorUserId !== '';

      if (hasStaff !== hasSupervisor) {
        this.$message({
          message: '客服和客服主管必须同时填写或同时为空',
          type: 'warning'
        });
        return;
      }

      try {
        this.loading = true;

        // 准备保存数据
        const scheduleData = {
          shiftDay: row.date,
          shopId: parseInt(row.shopId),
          shopName: row.shopName,
          serviceUserId: row.serviceUserId ? parseInt(row.serviceUserId) : null,
          supervisorUserId: row.supervisorUserId ? parseInt(row.supervisorUserId) : null,
          remark: row.remark || null
        };

        console.log('准备保存的排班数据:', scheduleData);

        // 调用API保存
        const response = await SysShiftAPI.update(scheduleData);
        console.log('保存行编辑响应:', response);
        console.log('响应类型:', typeof response);
        console.log('响应结构:', Object.keys(response || {}));

        // 兼容多种响应格式的成功判断
        const isSuccess = (
          // 格式1: { code: 0 }
          (response && response.code === 0) ||
          // 格式2: { success: true }
          (response && response.success === true) ||
          // 格式3: { data: {...} } (有数据就认为成功)
          (response && response.data) ||
          // 格式4: HTTP 2xx 状态码 (axios默认判断)
          (response && response.status >= 200 && response.status < 300) ||
          // 格式5: 直接返回数据对象
          (response && !response.code && !response.success && typeof response === 'object')
        );

        if (isSuccess) {
          console.log('判断为保存成功');
          this.$message.success('排班保存成功');

          // 退出编辑状态
          this.$set(row, 'isEditing', false);

          // 清空编辑数据
          this.editingRowData = null;

          // 重新加载当前日期的排班数据
          await this.loadScheduleDataFromServer();

          console.log('行编辑保存成功');
        } else {
          console.log('判断为保存失败');
          throw new Error((response && (response.message || response.msg)) || '保存失败');
        }

      } catch (error) {
        console.error('保存行编辑失败:', error);
        this.$message.error('保存失败：' + (error.message || '未知错误'));
      } finally {
        this.loading = false;
      }
    },

    // 取消行编辑
    cancelRowEdit(row) {
      console.log('取消行编辑:', row);

      // 恢复原始数据
      if (this.editingRowData) {
        row.serviceUserId = this.editingRowData.serviceUserId;
        row.supervisorUserId = this.editingRowData.supervisorUserId;
      }

      // 退出编辑状态
      this.$set(row, 'isEditing', false);

      // 清空编辑数据
      this.editingRowData = null;

      console.log('行编辑已取消，数据已恢复');
    },





    // 初始化数据
    async initializeData() {
      try {
        this.loading = true;
        console.log('开始初始化排班管理数据...');

        // 并行加载店铺数据、员工数据和排班数据
        await Promise.all([
          this.loadShopData(),
          this.loadStaffData(),
          this.loadScheduleDataFromServer()
        ]);

        console.log('数据初始化完成！');
        this.logCalendarStatus();
      } catch (error) {
        console.error('从服务器加载数据失败:', error);
        this.$message.error('加载数据失败，请检查网络连接');
      } finally {
        this.loading = false;
      }
    },

    // 加载店铺数据
    async loadShopData() {
      try {
        console.log('开始加载店铺数据...');
        const response = await getAllEnabledShops();
        console.log('店铺数据API响应:', response);

        if (response && response.data && response.data.records) {
          // 转换后端数据格式为前端使用的格式
          this.shopData = response.data.records.map(item => ({
            id: String(item.id), // 转换为字符串以保持一致性
            shopName: item.shopName,
            shopNo: item.id || item.shopNo // 兼容字段名差异
          }));
          console.log('店铺数据加载成功:', this.shopData);
        } else {
          this.shopData = [];
          console.log('没有店铺数据，初始化为空数组');
        }
      } catch (error) {
        console.error('加载店铺数据失败:', error);
        this.$message.error('加载店铺数据失败');
        this.shopData = []; // 确保为空数组
      }
    },

    // 加载员工数据
    async loadStaffData() {
      try {
        console.log('开始加载员工数据...');
        
        // 并行加载可用员工和主管数据
        const [staffResponse, supervisorResponse] = await Promise.all([
          getAvailableStaffForSchedule(),  // 获取排班可用员工（后端已排除主管客服角色）
          getAllSupervisorUsers()          // 获取客服主管（角色ID=22）
        ]);
        
        console.log('可用员工数据API响应:', staffResponse);
        console.log('主管数据API响应:', supervisorResponse);

        // 合并员工数据
        const allStaffData = [];
        
        // 处理可用员工数据（已在后端排除主管客服角色）
        if (staffResponse && staffResponse.data && staffResponse.data.records) {
          const staffUsers = staffResponse.data.records.map(item => {
            let position = 'other'; // 默认为其他角色
            
            // 根据角色ID设置position
            if (item.roleId === 21) {
              position = 'staff'; // 客服
            } else if (item.roleId === 11) {
              position = 'admin'; // 系统管理员
            } else if (item.roleId === 12) {
              position = 'partner'; // 合伙人
            }
            
            return {
              id: String(item.userId), // 转换为字符串以保持一致性
              name: item.realName || item.username, // 优先使用真实姓名，回退到用户名
              position: position,
              status: item.status || 'normal', // 使用状态字段
              roleId: item.roleId
            };
          });
          allStaffData.push(...staffUsers);
          console.log('加载可用员工:', staffUsers.length, '人（后端已排除主管客服）');
        }

        // 处理主管数据
        if (supervisorResponse && supervisorResponse.data && supervisorResponse.data.records) {
          const supervisorUsers = supervisorResponse.data.records.map(item => ({
            id: String(item.userId), // 转换为字符串以保持一致性
            name: item.realName || item.username, // 优先使用真实姓名，回退到用户名
            position: 'supervisor', // 主管
            status: item.status || 'normal', // 使用状态字段
            roleId: item.roleId
          }));
          allStaffData.push(...supervisorUsers);
          console.log('加载主管人员:', supervisorUsers.length, '人');
        }

        this.staffData = allStaffData;
        console.log('员工数据加载成功，总计:', this.staffData.length, '人');
        console.log('各角色统计:', {
          客服: this.staffData.filter(u => u.roleId === 21).length,
          主管: this.staffData.filter(u => u.roleId === 22).length,
          系统管理员: this.staffData.filter(u => u.roleId === 11).length,
          合伙人: this.staffData.filter(u => u.roleId === 12).length,
          其他: this.staffData.filter(u => ![11, 12, 21, 22].includes(u.roleId)).length
        });
        console.log('可选择的客服数量:', this.staffOptions.length, '人（后端已排除主管客服角色，前端已排除合伙人角色）');
        
      } catch (error) {
        console.error('加载员工数据失败:', error);
        this.$message.error('加载员工数据失败');
        this.staffData = []; // 确保为空数组
      }
    },

    // 输出日历状态统计（用于调试）
    logCalendarStatus() {
      console.log('=== 日历状态统计 ===');
      console.log('总店铺数:', this.shopData.length);
      console.log('总员工数:', this.staffData.length);
      console.log('客服人员数:', this.staffOptions.length);
      console.log('主管人员数:', this.supervisorOptions.length);
      console.log('总排班记录数:', this.scheduleData.length);

      // 统计各日期的排班情况
      const dateStats = {};
      this.scheduleData.forEach(item => {
        if (!dateStats[item.date]) {
          dateStats[item.date] = 0;
        }
        if (item.serviceUserId) {
          dateStats[item.date]++;
        }
      });

      console.log('各日期排班统计:');
      Object.keys(dateStats).sort().forEach(date => {
        const count = dateStats[date];
        const total = this.shopData.length;
        const status = count === 0 ? '无排班' : count === total ? '排满班' : '部分排班';
        console.log(`  ${date}: ${count}/${total} (${status})`);
      });
      console.log('===================');
    },

    // 从服务器加载排班数据
    async loadScheduleDataFromServer() {
      try {
        // 计算日期范围（前一个月1号到后一个月最后一天）
        const selectedDate = this.selectedDate || new Date();

        // 前一个月1号
        const startDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth() - 1, 1);

        // 后一个月最后一天
        const endDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 2, 0);

        const startDateStr = this.formatDateForKey(startDate);
        const endDateStr = this.formatDateForKey(endDate);

        console.log('查询排班数据 - 日期范围:', startDateStr, '到', endDateStr);

        // 使用分页查询API，添加日期范围参数
        const params = {
          current: 1,
          size: 1000, // 加载大量数据
          startDate: startDateStr,
          endDate: endDateStr
        };

        const response = await SysShiftAPI.page(params);
        console.log('排班数据API响应:', response);

        if (response && response.data && response.data.records) {
          console.log('原始排班数据:', response.data.records);

          // 将后端数据转换为前端格式
          this.scheduleData = response.data.records.map(item => {
            const converted = {
              id: item.id,
              date: item.date, // 后端已经格式化为YYYY-MM-DD
              shopId: String(item.shopId), // 转换为字符串保持一致性
              shopName: item.shopName,
              serviceUserId: item.serviceUserId ? String(item.serviceUserId) : null, // 转换为字符串
              supervisorUserId: item.supervisorUserId ? String(item.supervisorUserId) : null, // 转换为字符串
              shopCount: item.shopCount,
              isEditing: false
            };
            console.log('数据转换:', item, '->', converted);
            return converted;
          });

          console.log('转换后的排班数据:', this.scheduleData);
          console.log('排班数据加载成功，共', this.scheduleData.length, '条记录');
        } else {
          // 如果没有数据，初始化为空数组
          this.scheduleData = [];
          console.log('没有排班数据，初始化为空数组');
        }
      } catch (error) {
        console.error('加载排班数据失败:', error);
        this.scheduleData = [];
        throw error; // 重新抛出错误以便上层处理
      }
    }
  },
  created() {
    // 初始化数据
    this.initializeData();
  },
  watch: {
    // 监听日期变化，更新数据
    selectedDate() {
      // 强制重新渲染表格
      this.$nextTick(() => {
        // 使用$nextTick确保DOM更新后再进行操作
        this.$set(this, 'scheduleData', [...this.scheduleData]);
      });
    }
  }
}
</script>

<style scoped>
.schedule-management {
  /* padding: 20px; */
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.selected-date {
  font-size: 14px;
  font-weight: bold;
  color: #606266;
}

.content-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 600px;
}



.schedule-table-container {
  width: 60%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.table-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  color: #303133;
}

.table-actions {
  display: flex;
  align-items: center;
}



.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.staff-assignment {
  margin-bottom: 20px;
}

.staff-assignment h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
}

.leave-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>