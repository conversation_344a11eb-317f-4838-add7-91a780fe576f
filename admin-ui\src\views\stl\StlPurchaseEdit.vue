<template>
  <el-dialog
    title="结算"
    :visible.sync="visible"
    width="500px"
    :before-close="handleClose"
    @close="handleClose">
    
    <div class="settlement-form">
      <div class="form-row">
        <label class="form-label">采购日期：</label>
        <span class="form-value">{{ formatDate(purchaseData.date) }}</span>
      </div>
      
      <div class="form-row">
        <label class="form-label">采购金额：</label>
        <span class="form-value">¥ {{ purchaseData.amount.toFixed(2) }}</span>
      </div>
      
      <div class="form-row">
        <label class="form-label">结算金额：</label>
        <el-input
          v-model="form.settlementAmount"
          placeholder="请输入结算金额"
          type="number"
          step="0.01"
          style="width: 200px;">
          <template slot="prepend">¥</template>
        </el-input>
        <span class="required-mark">*</span>
      </div>
      
      <div class="form-row">
        <label class="form-label">当前状态：</label>
        <el-tag type="warning">未结算</el-tag>
      </div>
      
      <div class="form-row">
        <label class="form-label">备注：</label>
        <span class="required-mark">*</span>
        <div style="flex: 1; margin-left: 8px;">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入结算备注（必填）"
            maxlength="200"
            show-word-limit>
          </el-input>

          <!-- 常用备注标签 -->
          <div class="remark-tags" v-if="remarkOptions.length > 0">
            <div class="tags-label">常用备注：</div>
            <div class="tags-container">
              <el-tag
                v-for="option in remarkOptions"
                :key="option.value"
                class="remark-tag"
                @click="selectRemark(option.label)"
                :type="form.remark === option.label ? 'primary' : ''"
                effect="plain">
                {{ option.label }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button v-permission="['stl-purchase:insert']" type="primary" @click="handleSubmit" :loading="submitting">确认已结算</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { insertStlPurchase } from '@/api/StlPurchase'
import { dictCategoryItems } from '@/api/SysDictItem'

export default {
  name: 'StlPurchaseEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    purchaseData: {
      type: Object,
      default: () => ({
        date: '',
        amount: 0,
        userId: null
      })
    }
  },
  data() {
    return {
      form: {
        settlementAmount: '',
        remark: ''
      },
      remarkOptions: [], // 备注选项
      submitting: false
    }
  },
  created() {
    // 加载备注字典项
    this.loadRemarkOptions()
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
        // 默认结算金额等于采购金额
        this.form.settlementAmount = this.purchaseData.amount || ''
      }
    }
  },
  methods: {
    // 加载备注选项
    async loadRemarkOptions() {
      try {
        const res = await dictCategoryItems({ categoryId: 'SETTLEMENT_PURCHASE_REMARK' })
        if (res.success && res.data) {
          this.remarkOptions = res.data
        }
      } catch (error) {
        console.error('加载备注选项失败:', error)
      }
    },

    // 选择备注
    selectRemark(remarkText) {
      this.form.remark = remarkText
    },

    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    },
    
    resetForm() {
      this.form = {
        settlementAmount: '',
        remark: ''
      }
    },
    
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    
    async handleSubmit() {
      // 表单验证
      if (!this.form.settlementAmount || this.form.settlementAmount <= 0) {
        this.$message.error('请输入有效的结算金额')
        return
      }

      if (!this.form.remark || this.form.remark.trim() === '') {
        this.$message.error('请输入结算备注')
        return
      }

      // 确认对话框
      try {
        await this.$confirm(
          `确认结算金额为 ¥${parseFloat(this.form.settlementAmount).toFixed(2)} 吗？结算后将无法修改。`,
          '确认结算',
          {
            confirmButtonText: '确认结算',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      } catch (error) {
        // 用户取消确认
        return
      }

      this.submitting = true

      try {
        const params = {
          purchaseUserId: this.purchaseData.userId,
          purchaseDate: this.purchaseData.date,
          settlementAmount: parseFloat(this.form.settlementAmount),
          remark: this.form.remark.trim()
        }

        const res = await insertStlPurchase(params)

        if (res.success) {
          this.$message.success('结算记录保存成功')
          this.$emit('success', params)
          this.handleClose()
        } else {
          this.$message.error(res.errMsg || '保存失败')
        }
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.settlement-form {
  padding: 20px 0;
}

.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  min-height: 32px;
}

.form-label {
  width: 100px;
  text-align: right;
  margin-right: 12px;
  color: #333;
  font-size: 14px;
}

.form-value {
  color: #666;
  font-size: 14px;
}

.required-mark {
  color: #f56c6c;
  margin-left: 4px;
}

.dialog-footer {
  text-align: right;
}

.remark-tags {
  margin-top: 12px;
}

.tags-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.remark-tag {
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #dcdfe6;
}

.remark-tag:hover {
  border-color: #409eff;
  color: #409eff;
}
</style>
