package com.my.crossborder.forest.erp990.dto;

import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

@NoArgsConstructor @AllArgsConstructor @Builder @Data 
public class LoginDTO {

    @JSONField(name = "code", ordinal = 1)
    String code;

    @JSONField(name = "password", ordinal = 2)
    String password;

    @JSONField(name = "username", ordinal = 3)
    String username;

    @JSONField(name = "uuid", ordinal = 4)
    String uuid;

}