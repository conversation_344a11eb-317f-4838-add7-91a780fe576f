import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 分页查询待办事项
export const todoPage = (params) => { return reqGet("/wkb-todo/page", params) };

// 创建待办事项
export const createTodo = (params) => { return reqPost("/wkb-todo", params) };

// 更新待办事项
export const updateTodo = (params) => { return reqPut("/wkb-todo", params) };

// 删除待办事项
export const todoDelete = (params) => { return reqDelete("/wkb-todo", params) };

// 标记通知为已读
export const todoDetail = (id) => { return reqGet("/wkb-todo/" + id) };

// 查询所有我的未读待办
export const todoAllUnread = (params) => { return reqGet("/wkb-todo/all-unread", params) };