package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_role.SysRoleDeleteDTO;
import com.my.crossborder.controller.dto.sys_role.SysRoleInsertDTO;
import com.my.crossborder.controller.dto.sys_role.SysRolePageDTO;
import com.my.crossborder.controller.dto.sys_role.SysRoleUpdateDTO;
import com.my.crossborder.controller.vo.sys_role.SysRoleDetailVO;
import com.my.crossborder.controller.vo.sys_role.SysRolePageVO;
import com.my.crossborder.mybatis.entity.SysRole;

/**
 * 系统角色表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface SysRoleService extends IService<SysRole> {

	/**
	 * 新增
	 */
	void insert(SysRoleInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysRoleUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysRoleDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<SysRolePageVO> page(SysRolePageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysRoleDeleteDTO deleteDTO);	

}
