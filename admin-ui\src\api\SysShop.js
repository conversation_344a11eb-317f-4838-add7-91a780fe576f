import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 店铺管理API
 */

// 分页查询店铺列表
export function pageSysShop(params) {
  return reqGet('/sys-shop/page', params)
}

// 获取所有启用的店铺
export function getAllEnabledShops() {
  return reqGet('/sys-shop/page', {
    disable: false,
    pageSize: 1000  // 获取所有店铺
  })
}

// 根据ID查询店铺详情
export function getSysShopById(id) {
  return reqGet(`/sys-shop/${id}`)
}

// 新增店铺
export function insertSysShop(data) {
  return reqPost('/sys-shop', data)
}

// 修改店铺
export function updateSysShop(data) {
  return reqPut('/sys-shop', data)
}

// 删除店铺
export function deleteSysShop(data) {
  return reqDelete('/sys-shop', data)
}

// 获取合作人列表
export function getSysShopPartners() {
  return reqGet('/sys-user/partners')
}
