package com.my.crossborder.controller.dto.sys_dict_item;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_dict_item.SysDictItemPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_数据字典-字典项表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictItemPageDTO 
						extends PageDTO<SysDictItemPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类别id
     */
    private String categoryId;

    /**
     * 字典项值
     */
    private String itemValue;

    /**
     * 字典项名称
     */
    private String itemName;

    /**
     * 排序编号
     */
    private Integer sortNum;

}
