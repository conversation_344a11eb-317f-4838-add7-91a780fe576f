package com.my.crossborder.forest.erp990.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入库状态枚举
 * 用于表示订单商品的入库处理状态
 * 
 * <AUTHOR>
 * @date 2025
 */
@Getter
@AllArgsConstructor
public enum PutInStatusEnum {
    
    /** 无需处理 */
    NO_NEED_PROCESS(99, "无需处理"),
    
    /** 未入库 */
    NOT_PUT_IN(100, "未入库"),
    
    /** 部分入库 */
    PARTIAL_PUT_IN(101, "部分入库"),
    
    /** 完全入库 */
    FULLY_PUT_IN(102, "完全入库");
    
    /** 状态代码 */
    private final Integer code;
    
    /** 状态描述 */
    private final String description;
    
    /**
     * 根据代码获取枚举
     * @param code 状态代码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static PutInStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PutInStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 