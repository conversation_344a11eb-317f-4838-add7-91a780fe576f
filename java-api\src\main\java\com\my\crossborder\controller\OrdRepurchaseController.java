package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseDeleteDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseInsertDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchasePageDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchaseDetailVO;
import com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchasePageVO;
import com.my.crossborder.service.OrdRepurchaseService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 重新采购 
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/api/ord-repurchase")
@RequiredArgsConstructor
public class OrdRepurchaseController {

    private final OrdRepurchaseService ordRepurchaseService;

    /**
    * 新增
    */
    @SaCheckPermission("ord-repurchase:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody OrdRepurchaseInsertDTO insertDTO) {
    	this.ordRepurchaseService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("ord-repurchase:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody OrdRepurchaseUpdateDTO updateDTO) {
    	this.ordRepurchaseService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<OrdRepurchaseDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.ordRepurchaseService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<OrdRepurchasePageVO>> page(OrdRepurchasePageDTO pageDTO) {
        Page<OrdRepurchasePageVO> page = this.ordRepurchaseService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("ord-repurchase:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody OrdRepurchaseDeleteDTO deleteDTO) {
    	this.ordRepurchaseService.delete(deleteDTO);
		return StdResp.success();
    }

    /**
    * 确认完成
    * @param id 重新采购记录ID
    */
    @SaCheckPermission("ord-repurchase:confirm")
    @PostMapping("/confirm/{id}")
    public StdResp<?> confirmComplete(@PathVariable Integer id) {
    	this.ordRepurchaseService.confirmComplete(id);
    	return StdResp.success();
    }

}
