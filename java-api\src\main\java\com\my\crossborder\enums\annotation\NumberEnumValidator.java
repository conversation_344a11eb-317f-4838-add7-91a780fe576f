package com.my.crossborder.enums.annotation;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 整数作为key的枚举
 *
 * <AUTHOR>
 * @date 2019年7月29日
 */
@Slf4j
public class NumberEnumValidator implements ConstraintValidator<NumberEnumCheck, Number> {

    private Class<?> enumClass;

    @Override
    public void initialize(NumberEnumCheck constraintAnnotation) {
        this.enumClass = constraintAnnotation.enumClass();
    }

    /**
     * 检测enumClass
     *
     * @return
     */
    private boolean checkEnumClass(Type[] types) {
        for (Type type : types) {
            if (type.getTypeName().equals(NumberEnum.class.getName())) {
                return true;
            }
        }
        throw new RuntimeException("枚举配置有误. enumClass:" + this.enumClass.getSimpleName() + "不是NumberEnum");
    }

    @Override
    public boolean isValid(Number val, ConstraintValidatorContext context) {
        Type[] types = enumClass.getGenericInterfaces();
        this.checkEnumClass(types);

        if (val == null) {
        	return true;
        }
        try {
            Method method = enumClass.getMethod("values");
            NumberEnum enums[] = (NumberEnum[]) method.invoke(null);
            for (NumberEnum en : enums) {
                if (en.getNumber().toString().equals(val.toString())) {
                    return true;
                }
            }
            List<Number> keys = Arrays.asList(enums).stream().map(e -> e.getNumber()).collect(Collectors.toList());
            log.error("枚举【{}】的取值范围是:{}", this.enumClass.getSimpleName(), keys);
        } catch (Exception e) {
            //ignore
        }
        return false;
    }

}
