package com.my.crossborder.controller.dto.ord_repurchase;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchasePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_重新采购
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdRepurchasePageDTO 
						extends PageDTO<OrdRepurchasePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 问题描述
     */
    private String issue;

    /**
     * 处理办法 字典参数repurchase_close_way
     */
    private String closeWay;

    /**
     * 是否已处理
     */
    private String closeStatus;

    /**
     * 录入人id
     */
    private Integer issueUserId;

    /**
     * 录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理人id
     */
    private Integer closeUserId;

    /**
     * 处理时间
     */
    private LocalDateTime closeTime;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

}
