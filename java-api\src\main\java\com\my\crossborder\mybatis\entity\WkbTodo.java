package com.my.crossborder.mybatis.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 通知表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wkb_todo")
public class WkbTodo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类别
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 截止日期
     */
    private LocalDate deadline;
    
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布用户id
     */
    private Integer publishUserId;

    /**
     * 接收用户id
     */
    private Integer receiveUserId;

    /**
     * 是否已读
     */
    @TableField(value = "is_read")
    private Boolean read;

    /**
     * 已读时间
     */
    private LocalDateTime readTime;


}
