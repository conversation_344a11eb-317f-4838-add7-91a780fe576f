<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item>订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>采购登记</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="店铺">
          <el-select v-model="formInline.shopIds" placeholder="店铺" clearable multiple style="width: 80px;">
            <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="formInline.orderSn" placeholder="订单号" clearable style="width: 130px;"></el-input>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="formInline.orderStateList" placeholder="订单状态" clearable multiple style="width: 120px;">
            <el-option v-for="status in orderStatusList" :key="status.value" :label="status.label" :value="status.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购途径">
          <dict-select v-model="formInline.purchaseChannel" category-id="PURCHASE_CHANNEL" placeholder="采购途径" style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item label="采购人">
          <user-selector v-model="selectedUsers" :multiple="true" placeholder="请选择采购人" style="width: 120px;" @select="handleUserSelect">
          </user-selector>
        </el-form-item>
        <el-form-item label="采购日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 240px;">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际利润">
          <el-select v-model="formInline.actualProfitLessThanZero" placeholder="请选择" clearable style="width: 120px;">
            <el-option label="小于0" :value="true"></el-option>
            <el-option label="大于等于0" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
        :row-key="getRowKey"
        :expand-row-keys="expandedRows"
        @expand-change="handleExpandChange">

        <el-table-column type="expand">
          <template slot-scope="props">
            <div class="order-items-section">
              <el-table :data="props.row.orderItems" border style="width: 100%">
                <el-table-column prop="itemImage" label="产品图片" width="150" align="center">
                  <template slot-scope="scope">
                    <img v-if="scope.row.itemImage" :src="scope.row.itemImage"
                         style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;" />
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="itemName" label="产品名称" min-width="200" show-overflow-tooltip>
                  <template slot-scope="item">
                    <span class="item-name clickable-item-name"
                          @click="copyItemName(item.row.itemName)"
                          :title="'点击复制商品名称: ' + item.row.itemName">
                      {{ item.row.itemName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="itemModelName" label="规格" min-width="100" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="amount" label="数量" width="90" align="center">
                </el-table-column>
                <dict-table-column prop="purchaseChannel" label="采购途径" align="center" width="120"
                  category-id="PURCHASE_CHANNEL">
                </dict-table-column>
                <el-table-column prop="purchaseAmount" label="采购金额" width="100" align="center">
                  <template slot-scope="scope">
                    {{ scope.row.purchaseAmount > 0 
                        ? scope.row.purchaseAmount.toFixed(2) 
                        : scope.row.purchaseAmount === 0 ? 0 : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="purchaseDate" label="采购日期" width="120" align="center">
                  <template slot-scope="scope">
                    {{ scope.row.purchaseDate ? scope.row.purchaseDate.substring(0, 10) : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="settlementFlag" label="结算状态" width="100" align="center">
                  <template slot-scope="scope">
                    <span v-if="!scope.row.purchaseChannel"> - </span>
                    <el-tag type="info" v-else-if="scope.row.purchaseChannel != 2 && scope.row.purchaseChannel != 3"> 无需结算 </el-tag>
                    <el-tag v-else :type="scope.row.settlementFlag ? 'success' : 'warning'">
                      {{ scope.row.settlementFlag ? '已结算' : '未结算' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="expressNo" label="快递单号" min-width="150" align="center">
                  <template slot-scope="scope">
                    <span v-if="scope.row.expressNo"
                          class="express-no clickable-express"
                          @click="copyExpressNo(scope.row.expressNo)"
                          :title="'点击复制快递号: ' + scope.row.expressNo">
                      {{ scope.row.expressNo }}
                    </span>
                    <span v-else class="no-express">未填写</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="120" align="center">
                  <template slot-scope="scope">
                    <!-- 采购明细表没有记录、或者只有未结算的 && 是自己创建的，才显示编辑和删除按钮 -->
                    <template v-if="scope.row.settlementFlag == null
                            || scope.row.purchaseUserId === currentUserId  && !scope.row.settlementFlag">
                      <el-button v-permission="['ord-purchase:update']" size="mini" type="text" icon="el-icon-edit" @click="editPurchaseItem(scope.row)">编辑</el-button>
                      <el-button v-permission="['ord-purchase:delete']" size="mini" type="text" icon="el-icon-delete" @click="deletePurchaseItem(scope.row)" style="color: #f56c6c;">删除采购信息</el-button>
                    </template>
                    <span v-else-if="scope.row.purchaseChannel && scope.row.settlementFlag" style="color: #909399;">已结算</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="shopName" label="店铺名称" width="80" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="orderSn" label="订单号" width="160" align="center">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
              @click="copyOrderSn(scope.row.orderSn)" :title="'点击复制订单号'">
              {{ scope.row.orderSn }}
            </span>
          </template>
        </el-table-column>
        <dict-table-column prop="orderStates" label="订单状态" category-id="ERP_ORDER_STATUS" width="70" align="center">
        </dict-table-column>
        <el-table-column prop="createTime" label="下单时间" width="140" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="订单总金额" width="80" align="center">
          <template slot-scope="scope">
            <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.totalPrice ? scope.row.totalPrice.toFixed(2) : '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="incomeAmount" label="入账金额" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.row.incomeAmount ? scope.row.incomeAmount.toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="expectWeight" label="预计重量" width="60" align="center">
          <template slot-scope="scope">
            {{ scope.row.expectWeight ? scope.row.expectWeight.toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPurchaseAmount" label="采购总金额" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.row.totalPurchaseAmount > 0 
                ? scope.row.totalPurchaseAmount.toFixed(2) 
                : (scope.row.totalPurchaseAmount == 0 ) ? 0 : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="expectedProfit" label="预计利润" width="100" align="center">
          <template slot-scope="scope">
            <el-popover
              placement="top"
              width="400"
              trigger="hover"
              v-if="canCalculateExpectedProfit(scope.row)">
              <div style="line-height: 1.8;">
                <div style="font-weight: bold; margin-bottom: 8px;">预计利润计算公式：</div>
                <div style="color: #606266;">入账金额 × 台湾提款手续费比例 ÷ 当日汇率 - 预计重量 × 每吨运费 - 贴标费 - 进店费 - 采购总金额 = 预计利润</div>
                <div style="color: #409EFF; margin-top: 4px;">
                  {{ scope.row.incomeAmount || 0 }} × {{ taiwanWithdrawFeeRatio || 1 }} ÷ {{ scope.row.exchangeRate || 1 }} - {{ scope.row.expectWeight || 0 }} × {{ getExpectedUnitFee(scope.row) }} - {{ getExpectedStickFee(scope.row) }} - {{ getExpectedInShopeeFee(scope.row) }} - {{ scope.row.totalPurchaseAmount || 0 }} = {{ calculateExpectedProfit(scope.row).toFixed(2) }}
                </div>
              </div>
              <span slot="reference" :style="{ color: calculateExpectedProfit(scope.row) >= 0 ? '#67C23A' : '#F56C6C', cursor: 'pointer' }">
               ￥ {{ calculateExpectedProfit(scope.row).toFixed(2) }}
              </span>
            </el-popover>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="outTime" label="出库时间" width="140" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.outTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="chargeWeight" label="实际重量" width="60" align="center">
          <template slot-scope="scope">
            {{ scope.row.chargeWeight ? scope.row.chargeWeight.toFixed(1) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="actualShippingFee" label="实际运费" width="60" align="center">
          <template slot-scope="scope">
            {{ scope.row.chargeWeight ? (scope.row.stickFee + scope.row.inShopeeFee + scope.row.unitFee).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="actualProfit" label="实际利润" width="100" align="center">
          <template slot-scope="scope">
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <div style="line-height: 1.8;">
                <div style="font-weight: bold; margin-bottom: 8px;">实际利润计算公式：</div>
                <div style="color: #606266;">入账金额 × 台湾提款手续费比例 ÷ 当日汇率 - 实际重量 × 每吨运费 - 贴标费 - 进店费 - 采购总金额 = 实际利润</div>
                <div style="color: #409EFF; margin-top: 4px;">
                  {{ getActualProfitFormula(scope.row) }}
                </div>
              </div>
              <span slot="reference" v-if="scope.row.actualProfit !== null && scope.row.actualProfit !== undefined" :style="{ color: scope.row.actualProfit >= 0 ? '#67C23A' : '#F56C6C', cursor: 'pointer' }">
                ￥ {{ scope.row.actualProfit.toFixed(2) }}
              </span>
              <span slot="reference" v-else>-</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="实际利润率" width="100" align="center">
          <template slot-scope="scope">
            <el-popover
              placement="top"
              width="350"
              trigger="hover"
              v-if="scope.row.actualProfit !== null && scope.row.actualProfit !== undefined && scope.row.totalPrice && scope.row.totalPrice > 0">
              <div style="line-height: 1.8;">
                <div style="font-weight: bold; margin-bottom: 8px;">实际利润率计算公式：</div>
                <div style="color: #606266;">实际利润 ÷ 订单总金额 × 100% = 实际利润率</div>
                <div style="color: #409EFF; margin-top: 4px;">
                  {{ scope.row.actualProfit.toFixed(2) }} ÷ {{ scope.row.totalPrice.toFixed(2) }} × 100% = {{ ((scope.row.actualProfit / scope.row.totalPrice) * 100).toFixed(2) }}%
                </div>
              </div>
              <span slot="reference" :style="{ color: scope.row.actualProfit >= 0 ? '#67C23A' : '#F56C6C', cursor: 'pointer' }">
                {{ ((scope.row.actualProfit / scope.row.totalPrice) * 100).toFixed(2) }}%
              </span>
            </el-popover>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <!-- 使用NoteColumn组件 -->
        <NoteColumn :current-scene="currentScene" />
        <el-table-column label="操作" min-width="150" align="center">
          <template slot-scope="scope">
            <el-button
              v-permission="['ord-purchase:update']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="editPurchase(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="openNotesDrawer(scope.row)" icon="el-icon-tickets">备注</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-table-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

    <!-- 编辑采购订单对话框 -->
    <OrdPurchaseEdit :visible.sync="purchaseDialogVisible" :editData="currentPurchaseData" :isEdit="isEditPurchase"
      @submit="handlePurchaseSubmit" />

    <!-- 编辑采购明细对话框 -->
    <OrdPurchaseItemEdit :visible.sync="purchaseItemDialogVisible" :editData="currentPurchaseItemData" :isEdit="isEditPurchaseItem"
      @submit="handlePurchaseItemSubmit" />

    <!-- 备注抽屉 -->
    <el-drawer
      title="订单备注"
      :visible.sync="notesDrawerVisible"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose">
      <div style="padding: 20px;">
        <OrderNotesDrawer
          :notes="currentOrderNotes"
          :orderSn="currentOrderSn"
          :currentScene="currentScene"
          :showDebug="false"
          @note-updated="handleNoteUpdated" />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getOrdPurchasePage, deleteOrdPurchaseItem } from '../../api/OrdPurchase'
import { getAllEnabledShops } from '../../api/SysShop'
import { getOrderStates } from '../../api/ErpOrder'
import { sysParamGet } from '../../api/SysParam'
import { getByOrderSnAndScene } from '../../api/WkbNote'
import Pagination from '../../components/Pagination'
import DictSelect from '../../components/DictSelect'
import DictTableColumn from '../../components/DictTableColumn'
import UserSelector from '../../components/UserSelector'
import NoteColumn from '../../components/NoteColumn'
import OrderNotesDrawer from './OrderNotesDrawer'
import OrdPurchaseEdit from './OrdPurchaseEdit'
import OrdPurchaseItemEdit from './OrdPurchaseItemEdit'
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'OrdPurchase',
  mixins: [copyMixin],
  components: {
    Pagination,
    DictSelect,
    DictTableColumn,
    UserSelector,
    NoteColumn,
    OrderNotesDrawer,
    OrdPurchaseEdit,
    OrdPurchaseItemEdit
  },
  data() {
    return {
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        orderStateList: ['001'], // 默认选中"待出货"状态
        purchaseChannel: '',
        purchaseUserIdList: [],
        purchaseDateStart: '',
        purchaseDateEnd: '',
        actualProfitLessThanZero: null
      },
      // 表格数据
      tableData: [],
      // 店铺列表
      shopList: [],
      // 订单状态列表
      orderStatusList: [],
      // 选中的用户列表
      selectedUsers: [],
      // 日期范围
      dateRange: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      // 展开的行
      expandedRows: [],

      // 采购订单对话框
      purchaseDialogVisible: false,
      currentPurchaseData: {},
      isEditPurchase: false,

      // 采购明细对话框
      purchaseItemDialogVisible: false,
      currentPurchaseItemData: {},
      isEditPurchaseItem: false,

      // 备注抽屉
      notesDrawerVisible: false,
      currentOrderNotes: [],
      currentOrderSn: '',
      currentScene: '01',  // 采购登记场景

      // 台湾提款手续费比例
      taiwanWithdrawFeeRatio: 1
    }
  },
  computed: {
    // 获取当前登录用户ID
    currentUserId() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.userId || userEntity.id
    }
  },
  created() {
    // 检查URL参数中是否有orderSn
    if (this.$route.query.orderSn) {
      this.formInline.orderSn = this.$route.query.orderSn
    }
    this.loadShops()
    this.loadOrderStates()
    this.loadSysParams()
    this.getPageData()
  },
  methods: {
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = {
          current: this.formInline.current,
          size: this.formInline.size,
          shopIds: this.formInline.shopIds && this.formInline.shopIds.length > 0 ? this.formInline.shopIds : undefined,
          orderSn: this.formInline.orderSn || undefined,
          orderStateList: this.formInline.orderStateList && this.formInline.orderStateList.length > 0 ? this.formInline.orderStateList : undefined,
          purchaseChannel: this.formInline.purchaseChannel || undefined,
          purchaseUserIdList: this.formInline.purchaseUserIdList.length > 0 ? this.formInline.purchaseUserIdList : undefined,
          purchaseDateStart: this.formInline.purchaseDateStart || undefined,
          purchaseDateEnd: this.formInline.purchaseDateEnd || undefined,
          actualProfitLessThanZero: this.formInline.actualProfitLessThanZero
        }
      }

      getOrdPurchasePage(parameter)
        .then(res => {
          this.loading = false
          if (res.success && res.data) {
            this.tableData = res.data.records
            // 处理每个订单的采购总金额
            this.tableData.forEach(order => {
              // 调试信息
              console.log(`订单 ${order.orderSn}:`)
              console.log('- 后端返回的采购总金额:', order.totalPurchaseAmount)
              console.log('- 订单项数量:', order.orderItems ? order.orderItems.length : 0)
              if (order.orderItems && order.orderItems.length > 0) {
                console.log('- 订单项采购金额:', order.orderItems.map(item => item.purchaseAmount))
              }

              // 优先使用后端返回的采购总金额，如果没有则前端计算
              if (order.totalPurchaseAmount === undefined || order.totalPurchaseAmount === null) {
                this.calculateTotalPurchaseAmount(order)
                console.log('- 前端计算后的采购总金额:', order.totalPurchaseAmount)
              }
            })
            this.pageParam.currentPage = res.data.current
            this.pageParam.pageSize = res.data.size
            this.pageParam.total = res.data.total
          }
        })
        .catch(err => {
          this.loading = false
          this.$message.error('获取数据失败：' + err.message)
        })
    },

    // 加载店铺数据
    loadShops() {
      getAllEnabledShops()
        .then(res => {
          if (res.success && res.data && res.data.records) {
            this.shopList = res.data.records
          }
        })
        .catch(err => {
          console.error('加载店铺数据失败：', err)
        })
    },

    // 加载订单状态数据
    loadOrderStates() {
      getOrderStates()
        .then(res => {
          if (res.success && res.data) {
            this.orderStatusList = res.data
          }
        })
        .catch(err => {
          console.error('加载订单状态数据失败：', err)
        })
    },

    // 加载系统参数
    loadSysParams() {
      sysParamGet().then(res => {
        if (res.success && res.data) {
          this.taiwanWithdrawFeeRatio = parseFloat(res.data.taiwanWithdrawFeeRatio) || 1
        }
      }).catch(err => {
        console.error('加载系统参数失败:', err)
        this.taiwanWithdrawFeeRatio = 1 // 默认值
      })
    },

    // 用户选择回调
    handleUserSelect(users) {
      this.selectedUsers = users
      this.formInline.purchaseUserIdList = users.map(user => user.userId)
    },

    // 分页回调
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },

    // 查询
    onSearch() {
      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        this.formInline.purchaseDateStart = this.dateRange[0]
        this.formInline.purchaseDateEnd = this.dateRange[1]
      } else {
        this.formInline.purchaseDateStart = ''
        this.formInline.purchaseDateEnd = ''
      }

      this.formInline.current = 1
      this.getPageData()
    },

    // 重置
    onReset() {
      this.formInline = {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        orderStateList: ['001'], // 重置时默认选中"待出货"状态
        purchaseChannel: '',
        purchaseUserIdList: [],
        purchaseDateStart: '',
        purchaseDateEnd: '',
        actualProfitLessThanZero: null
      }
      this.selectedUsers = []
      this.dateRange = []
      this.getPageData()
    },

    // 获取行key
    getRowKey(row) {
      return row.orderSn
    },

    // 展开/折叠处理
    handleExpandChange(row, expandedRows) {
      this.expandedRows = expandedRows.map(r => r.orderSn)
    },



    // 编辑采购订单
    editPurchase(row) {
      this.currentPurchaseData = { ...row }
      this.isEditPurchase = true
      this.purchaseDialogVisible = true
    },

    // 编辑采购明细
    editPurchaseItem(row) {
      this.currentPurchaseItemData = { ...row }
      this.isEditPurchaseItem = true
      this.purchaseItemDialogVisible = true
    },

    // 删除采购明细
    deletePurchaseItem(row) {
      this.$confirm('确认删除该采购明细吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除API
        const deleteData = {
          idList: [row.id] // 使用订单明细ID
        }
        deleteOrdPurchaseItem(deleteData)
          .then(res => {
            if (res.success) {
              this.$message.success('删除采购明细成功')
              this.getPageData()
            } else {
              this.$message.error('删除失败：' + (res.message || '未知错误'))
            }
          })
          .catch(err => {
            this.$message.error('删除失败：' + err.message)
          })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 采购订单提交回调
    handlePurchaseSubmit() {
      this.purchaseDialogVisible = false
      this.getPageData()
    },

    // 采购明细提交回调
    handlePurchaseItemSubmit() {
      this.purchaseItemDialogVisible = false
      this.getPageData()
    },

    // 计算采购总金额
    calculateTotalPurchaseAmount(order) {
      if (!order.orderItems || order.orderItems.length === 0) {
        order.totalPurchaseAmount = null
        return
      }

      // 计算所有子项的采购金额之和
      let total = 0
      let hasPurchaseData = false

      order.orderItems.forEach(item => {
        // 只有当采购金额存在且有效时才计算
        if (item.purchaseAmount !== null && item.purchaseAmount !== undefined && item.purchaseAmount !== '') {
          const amount = parseFloat(item.purchaseAmount)
          if (!isNaN(amount)) {
            total += amount
            hasPurchaseData = true
          }
        }
      })

      // 如果没有采购数据，设置为null，显示为"-"
      order.totalPurchaseAmount = hasPurchaseData ? total : null
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 判断是否可以计算预计利润
    canCalculateExpectedProfit(row) {
      return row.incomeAmount && row.exchangeRate && row.expectWeight && row.totalPurchaseAmount !== null && this.taiwanWithdrawFeeRatio
    },

    // 获取实际利润公式显示
    getActualProfitFormula(row) {
      const incomeAmount = row.incomeAmount || '空'
      const taiwanWithdrawFeeRatio = this.taiwanWithdrawFeeRatio || '空'
      const exchangeRate = row.exchangeRate || '空'
      const chargeWeight = row.chargeWeight || '空'
      const unitFee = row.chargeWeight && row.unitFee ? (row.unitFee / row.chargeWeight).toFixed(2) : '空'
      const stickFee = row.stickFee || '空'
      const inShopeeFee = row.inShopeeFee || '空'
      const totalPurchaseAmount = row.totalPurchaseAmount || '空'
      const actualProfit = row.actualProfit !== null && row.actualProfit !== undefined ? row.actualProfit.toFixed(2) : '空'

      return `${incomeAmount} × ${taiwanWithdrawFeeRatio} ÷ ${exchangeRate} - ${chargeWeight} × ${unitFee} - ${stickFee} - ${inShopeeFee} - ${totalPurchaseAmount} = ${actualProfit}`
    },

    // 获取预计每吨运费
    getExpectedUnitFee(row) {
      // 当实际重量、实际运费有值时，每吨运费 = unitFee / chargeWeight
      // 当实际重量、实际运费没有值时，每吨运费 = 11
      if (row.chargeWeight && row.unitFee) {
        return (row.unitFee / row.chargeWeight).toFixed(2)
      }
      return 11
    },

    // 获取预计贴标费
    getExpectedStickFee(row) {
      // 当实际重量、实际运费有值时，贴标费 = stickFee
      // 当实际重量、实际运费没有值时，贴标费 = 2
      if (row.chargeWeight && row.unitFee) {
        return row.stickFee || 0
      }
      return 2
    },

    // 获取预计进店费
    getExpectedInShopeeFee(row) {
      // 当实际重量、实际运费有值时，进店费 = inShopeeFee
      // 当实际重量、实际运费没有值时，进店费 = 2
      if (row.chargeWeight && row.unitFee) {
        return row.inShopeeFee || 0
      }
      return 2
    },



    // 计算预计利润
    calculateExpectedProfit(row) {
      if (!this.canCalculateExpectedProfit(row)) {
        return 0
      }

      // 新公式：入账金额 × 台湾提款手续费比例 ÷ 当日汇率 - 预计重量 × 每吨运费 - 贴标费 - 进店费 - 采购总金额 = 预计利润
      const incomeAmountWithFee = (row.incomeAmount || 0) * (this.taiwanWithdrawFeeRatio || 1)
      const incomeAmountInCny = incomeAmountWithFee / (row.exchangeRate || 1)
      const expectedWeight = row.expectWeight || 0
      const unitFee = this.getExpectedUnitFee(row)
      const stickFee = this.getExpectedStickFee(row)
      const inShopeeFee = this.getExpectedInShopeeFee(row)
      const totalPurchaseAmount = row.totalPurchaseAmount || 0

      return incomeAmountInCny - (expectedWeight * unitFee) - stickFee - inShopeeFee - totalPurchaseAmount
    },

    // 打开备注抽屉
    openNotesDrawer(row) {
      this.currentOrderSn = row.orderSn
      this.currentOrderNotes = row.notes || []
      this.notesDrawerVisible = true
    },

    // 关闭抽屉
    handleDrawerClose() {
      this.notesDrawerVisible = false
      this.currentOrderNotes = []
    },

    // 处理备注更新事件
    handleNoteUpdated() {
      console.log('备注已更新，刷新页面数据')

      // 刷新列表数据
      setTimeout(() => {
        this.getPageData()

        // 如果抽屉是打开状态，在数据刷新后更新抽屉中的备注数据
        if (this.notesDrawerVisible && this.currentOrderSn) {
          // 从刷新后的表格数据中找到当前订单并更新备注
          const updatedOrder = this.tableData.find(order => order.orderSn === this.currentOrderSn)
          if (updatedOrder) {
            this.currentOrderNotes = updatedOrder.notes || []
          }
        }
      }, 500)
    }

  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.demo-form-inline {
  margin-bottom: 20px;
}

.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}

/* 订单项展开区域样式 */
.order-items-section {
  background-color: #f8f9fa;
  border-radius: 6px;
}

.empty-table-placeholder {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

/* 快递单号样式 */
.express-no {
  color: #67C23A;
  font-weight: bold;
  background-color: #F0F9FF;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #67C23A;
}

.no-express {
  color: #909399;
  font-style: italic;
}

.clickable-express {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-express:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>
