package com.my.crossborder.controller.vo.sys_shift_attendance;

import java.time.LocalDate;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper=false)
public class MonthAttendanceFull 
					extends MonthAttendanceSummary {
	

	private Map<LocalDate, String> attendanceData;
	
}