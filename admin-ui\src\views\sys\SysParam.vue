<template>
  <div>
    <!-- breadcrumb -->
    <!-- <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>系统参数</el-breadcrumb-item>
    </el-breadcrumb> -->

    <el-tabs v-model="activeTab" class="margin-top">
      <el-tab-pane label="基础参数" name="basic">
        <el-form ref="form" :model="editForm" :rules="rules" label-width="180px">
          <!-- 检验周期参数设置 -->
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>AI 视觉参数</span>
              <el-button type="primary" size="small" style="margin-left: 90px;" @click="checkParams" :loading="checkLoading" icon="el-icon-refresh">检测所有参数</el-button>
            </div>
            <el-form-item label="AI 视觉模型" prop="aiModel">
              <div slot="label" style="display: inline-flex; align-items: center;">
                AI 视觉模型
                <el-tooltip content="查看模型广场" placement="top">
                  <el-link :underline="false" href="https://cloud.siliconflow.cn" target="_blank">
                    <i class="el-icon-question" style="cursor: pointer; margin-left: 5px;"></i>
                  </el-link>
                </el-tooltip>
              </div>
              <div class="form-item-container">
                <el-select v-model="editForm.aiModel" placeholder="请选择AI模型" class="half-width">
                  <el-option v-for="model in models" :key="model.name" :label="`[${model.price} 元 / M Tokens] ${model.name}`" :value="model.name"></el-option>
                </el-select>
                <div class="check-result" v-if="checkResults.aiModel">
                  <el-tag :type="checkResults.aiModel.valid ? 'success' : 'danger'">
                    {{ checkResults.aiModel.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="AI 密钥" prop="aiApiKey">
              <div class="form-item-container">
                <el-input v-model="editForm.aiApiKey" placeholder="请输入AI密钥" class="half-width" maxlength="64"></el-input>
                <div class="check-result" v-if="checkResults.aiApiKey">
                  <el-tag :type="checkResults.aiApiKey.valid ? 'success' : 'danger'">
                    {{ checkResults.aiApiKey.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="AI 用户提示词" prop="aiUserPrompt">
              <div class="form-item-container">
                <el-input type="textarea" :disabled="true" v-model="editForm.aiUserPrompt" placeholder="请输入deepseek用户提示词" :rows="3" class="half-width" show-word-limit></el-input>
                <div class="check-result" v-if="checkResults.aiUserPrompt">
                  <el-tag :type="checkResults.aiUserPrompt.valid ? 'success' : 'danger'">
                    {{ checkResults.aiUserPrompt.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
          </el-card>

          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>订单参数</span>
            </div>
            <el-form-item label="台湾提款手续费比例" prop="taiwanWithdrawFeeRatio">
              <div class="form-item-container">
                <el-input v-model="editForm.taiwanWithdrawFeeRatio" placeholder="台湾提款手续费比例" class="half-width" maxlength="512"></el-input>
                <div class="check-result" v-if="checkResults.taiwanWithdrawFeeRatio">
                  <el-tag :type="checkResults.taiwanWithdrawFeeRatio.valid ? 'success' : 'danger'">
                    {{ checkResults.taiwanWithdrawFeeRatio.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="订单同步天数" prop="orderSyncDays">
              <div class="form-item-container">
                <el-input v-model="editForm.orderSyncDays" placeholder="订单同步天数" class="half-width" maxlength="32"></el-input>
                <div class="check-result" v-if="checkResults.orderSyncDays">
                  <el-tag :type="checkResults.orderSyncDays.valid ? 'success' : 'danger'">
                    {{ checkResults.orderSyncDays.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
          </el-card>

          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>文件上传参数</span>
            </div>
            <el-form-item label="保存目录" prop="fileUploadPath">
              <div class="form-item-container">
                <el-input v-model="editForm.fileUploadPath" placeholder="请输入图片根目录" class="half-width" maxlength="512"></el-input>
                <div class="check-result" v-if="checkResults.fileUploadPath">
                  <el-tag :type="checkResults.fileUploadPath.valid ? 'success' : 'danger'">
                    {{ checkResults.fileUploadPath.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="文件最大限制 (M)" prop="fileMaxSize">
              <div class="form-item-container">
                <el-input type="number" v-model="editForm.fileMaxSize" placeholder="请输入文件大小限制 (M)" class="half-width" :max="1024"></el-input>
                <div class="check-result" v-if="checkResults.fileMaxSize">
                  <el-tag :type="checkResults.fileMaxSize.valid ? 'success' : 'danger'">
                    {{ checkResults.fileMaxSize.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="文件扩展名限制" prop="fileAllowExt">
              <div class="form-item-container">
                <el-input v-model="editForm.fileAllowExt" placeholder="请输入文件扩展名限制(jpg,png,jpeg,pdf,doc,docx,xls,xlsx,ppt,pptx)" class="half-width" maxlength="255"></el-input>
                <div class="check-result" v-if="checkResults.fileAllowExt">
                  <el-tag :type="checkResults.fileAllowExt.valid ? 'success' : 'danger'">
                    {{ checkResults.fileAllowExt.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button v-permission="['sys-param:update']" type="primary" @click="submitForm">保存全部参数</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-card>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="禾宸账号" name="account">
        <el-card class="box-card">
            <SysErpAccount />
          </el-card>
      </el-tab-pane>

      <el-tab-pane label="汇率" name="exchangeRate">
        <SysExchangeRateDayRange />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { sysParamUpdate, sysParamGet, sysParamCheck } from '../../api/SysParam'
import { loadToken } from '../../utils/util'
import SysErpAccount from './SysErpAccount.vue'
import SysExchangeRateDayRange from './SysExchangeRateDayRange.vue'
export default {
  data() {
    return {
      activeTab: 'basic',
      loading: false,
      checkLoading: false,
      editForm: {
        id: 1,
        fileUploadPath: '',
        fileMaxSize: '',
        fileAllowExt: '',
        aiModel: '',
        aiApiKey: '',
        aiUserPrompt: '',
        orderSyncDays:'',
      },
      models: [
        { "name": "Pro/Qwen/Qwen2.5-VL-7B-Instruct", "price": 0.35 },
        // { "name": "deepseek-ai/DeepSeek-R1-0528-Qwen-3-8B", "price": 0 },
        // { "name": "Qwen/Qwen3-8B", "price": 0 },
        // { "name": "THUDM/GLM-Z1-9B-0414", "price": 0 },
        // { "name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B", "price": 0 },
        // { "name": "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B", "price": 0.35 },
        // { "name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B", "price": 0.7 },
        // { "name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B", "price": 1.26 },
        // { "name": "Qwen/Qwen3-14B", "price": 2 },
        // { "name": "Qwen/Qwen3-30B-A3B", "price": 2.8 },
        // { "name": "Tongyi-Zhiwen/QwenLong-L1-32B", "price": 4 },
        // { "name": "Qwen/Qwen3-32B", "price": 4 },
        // { "name": "THUDM/GLM-Z1-32B-0414", "price": 4 },
        // { "name": "Qwen/QwQ-32B", "price": 4 },
        // { "name": "THUDM/GLM-Z1-Rumination-32B-0414", "price": 4 },
        // { "name": "Qwen/Qwen3-235B-A22B", "price": 10 },
        // { "name": "deepseek-ai/DeepSeek-R1", "price": 16 },
        // { "name": "MiniMaxAI/MiniMax-M1-80k", "price": 16 }
      ],
      checkResults: {}, // 检测结果
      userEntity: undefined,
      rules: {
        fileUploadPath: [
          { required: true, message: '请输入图片根目录', trigger: 'blur' },
        ],
        fileMaxSize: [
          { required: true, message: '请输入文件大小最大限制', trigger: 'blur' },
        ],
        fileAllowExt: [
          { required: true, message: '请输入文件扩展名限制', trigger: 'blur' },
        ],
        aiModel: [
          { required: true, message: '请选择AI模型', trigger: 'blur' },
        ],
        aiApiKey: [
          { required: true, message: '请输入AI密钥', trigger: 'blur' },
        ],
        aiUserPrompt: [
          { required: true, message: '请输入AI用户提示词', trigger: 'blur' },
        ],
        orderSyncDays: [
          { required: true, message: '必填', trigger: 'blur' },
          {
            validator: (_rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }
              const num = Number(value);
              if (isNaN(num)) {
                callback(new Error('请输入有效的数字'));
                return;
              }
              if (!Number.isInteger(num)) {
                callback(new Error('订单同步天数必须是整数'));
                return;
              }
              if (num < 30) {
                callback(new Error('订单同步天数不能少于30天'));
                return;
              }
              if (num > 90) {
                callback(new Error('订单同步天数不能超过90天'));
                return;
              }
              callback();
            },
            trigger: 'blur'
          },
        ],
        taiwanWithdrawFeeRatio: [
          { required: true, message: '必填', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === '' || value === null || value === undefined) {
                callback();
                return;
              }
              const num = parseFloat(value);
              if (isNaN(num)) {
                callback(new Error('必须是数字'));
              } else if (num <= 0) {
                callback(new Error('必须大于0'));
              } else if (num >= 1) {
                callback(new Error('必须小于1'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  components: {
    SysErpAccount,
    SysExchangeRateDayRange
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 加载系统参数配置
    this.getSysParamConfig();
  },
  methods: {
    // 获取系统参数配置
    getSysParamConfig() {
      this.loading = true;
      sysParamGet({}).then(res => {
        this.loading = false;
        this.editForm = {
          ...res.data,
          token: loadToken()
        };
      });
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          sysParamUpdate(this.editForm).then(res => {
            this.loading = false;
            if (res.success) {
              this.$message.success('保存成功');
              this.checkParams();
            } else {
              this.$message.error(res.message || '保存失败');
            }
          })
        } else {
          return false;
        }
      });
    },
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields();
      this.getSysParamConfig();
    },
    // 检测系统参数
    checkParams() {
      this.checkLoading = true;

      // 重置表单
      this.resetForm();

      // 正常应该使用实际的接口调用
      sysParamCheck(this.editForm).then(res => {
        this.checkLoading = false;
        if (res.success) {
          this.checkResults = res.data;
        } else {
          this.$message.error(res.message);
        }
      }).catch(err => {
        this.checkLoading = false;
        this.$message.error('检测失败');
      });

    },
  }
}
</script>

<style scoped>
.margin-top {
  /* margin-top: 20px; */
  margin-top: 0px;
}

.el-form-item__error {
  color: #F56C6C;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  left: 0;
  top: 100%;
}

.half-width {
  width: 40%;
}

.form-item-container {
  display: flex;
  align-items: center;
}

.check-result {
  margin-left: 10px;
}
</style>
