package com.my.crossborder.controller.dto.sys_dict_category;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_数据字典-类别表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictCategoryDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<String> idList;
	
}
