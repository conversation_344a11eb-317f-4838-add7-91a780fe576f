package com.my.crossborder.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchasePageDTO;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchaseUpdateDTO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchaseDetailVO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchaseItemWithExpressVO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchasePageVO;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;
import com.my.crossborder.mybatis.entity.OrdPurchase;
import com.my.crossborder.mybatis.mapper.OrdPurchaseMapper;
import com.my.crossborder.mybatis.mapper.WkbNoteMapper;
import com.my.crossborder.service.OrdPurchaseService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;

/**
 * 采购订单主表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@RequiredArgsConstructor
public class OrdPurchaseServiceImpl extends ServiceImpl<OrdPurchaseMapper, OrdPurchase> implements OrdPurchaseService {

	private final WkbNoteMapper wkbNoteMapper;


	@Transactional
	@Override
	public void update(OrdPurchaseUpdateDTO updateDTO) {
		OrdPurchase entity = BeanUtil.copyProperties(updateDTO, OrdPurchase.class);
		this.saveOrUpdate(entity);
	}

	@Override
	public OrdPurchaseDetailVO detail(String id) {
		OrdPurchase entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, OrdPurchaseDetailVO.class);
	}

	@Override
	public Page<OrdPurchasePageVO> page(OrdPurchasePageDTO pageDTO) {
		// 不再设置默认值，由前端控制
		Page<OrdPurchasePageVO> page = this.baseMapper.page(pageDTO);
		this.batchSetOrderItems(page);
		return page;
	}

//	@Transactional
//	@Override
//	public void delete(OrdPurchaseDeleteDTO deleteDTO) {
//		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
//	}

	/**
	 * 批量设置订单项和备注信息到分页结果中 
	 * @param orderPage 订单分页结果 
	 */
	private void batchSetOrderItems(Page<OrdPurchasePageVO> orderPage) {
		List<OrdPurchasePageVO> records = orderPage.getRecords();
		if (records == null || records.isEmpty()) {
			return;
		}

		// 收集所有订单号
		Set<String> orderIds = records.stream()
				.map(OrdPurchasePageVO::getOrderId)
				.collect(Collectors.toSet());
		Set<String> orderSnList = records.stream()
				.map(OrdPurchasePageVO::getOrderSn)
				.collect(Collectors.toSet());

		// 一次性查询所有订单项、所有备注
		List<OrdPurchaseItemWithExpressVO> allOrderItems = this.baseMapper.selectOrderItems(orderIds);
		List<WkbNoteDetailVO> allNotes = this.wkbNoteMapper.selectByOrderSnSet(orderSnList);

		// 按订单号分组(订单项、备注)
		Map<String, List<OrdPurchaseItemWithExpressVO>> orderItemMap = allOrderItems.stream()
				.collect(Collectors.groupingBy(OrdPurchaseItemWithExpressVO::getOrderId));
		Map<String, List<WkbNoteDetailVO>> noteMap = allNotes.stream()
				.collect(Collectors.groupingBy(WkbNoteDetailVO::getOrderSn));

		// 为每个订单设置对应的订单项和备注
		records.forEach(order -> {
			String orderId = order.getOrderId();
			String orderSn = order.getOrderSn();
			// 设置订单项
			order.setOrderItems(orderItemMap.getOrDefault(orderId, Lists.newLinkedList()));
			// 设置备注信息
			order.setNotes(noteMap.getOrDefault(orderSn, Lists.newLinkedList()));
		});
	}
}
