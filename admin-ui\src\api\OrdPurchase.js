import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 采购订单管理API
 */

// 分页查询采购订单
export function getOrdPurchasePage(params) {
  return reqGet('/ord-purchase/page', params)
}

// 新增采购订单
export function insertOrdPurchase(data) {
  return reqPost('/ord-purchase', data)
}

// 修改采购订单
export function updateOrdPurchase(data) {
  return reqPut('/ord-purchase', data)
}

// 查询采购订单详情
export function getOrdPurchaseDetail(id) {
  return reqGet(`/ord-purchase/${id}`)
}

// 删除采购订单
export function deleteOrdPurchase(data) {
  return reqDelete('/ord-purchase', data)
}

/**
 * 采购订单明细API
 */

// 分页查询采购订单明细
export function getOrdPurchaseItemPage(params) {
  return reqGet('/ord-purchase-item/page', params)
}

// 新增采购订单明细
export function insertOrdPurchaseItem(data) {
  return reqPost('/ord-purchase-item', data)
}

// 修改采购订单明细
export function updateOrdPurchaseItem(data) {
  return reqPut('/ord-purchase-item', data)
}

// 查询采购订单明细详情
export function getOrdPurchaseItemDetail(id) {
  return reqGet(`/ord-purchase-item/${id}`)
}

// 删除采购订单明细
export function deleteOrdPurchaseItem(data) {
  return reqDelete('/ord-purchase-item', data)
}
