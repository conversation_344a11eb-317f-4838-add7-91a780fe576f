<template>
  <el-container class="index-con">
    <!-- 顶部导航栏占据整行 -->
    <el-header class="index-header">
      <navcon></navcon>
    </el-header>

    <!-- 下面的容器：左侧菜单 + 右侧内容 -->
    <el-container class="content-container">
      <el-aside :class="['sidebar', { 'sidebar-collapsed': sidebarCollapsed }]">
        <leftnav></leftnav>
      </el-aside>
      <el-main class="index-main">
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
// 导入组件
import navcon from '../components/navcon.vue'
import leftnav from '../components/leftnav.vue'
export default {
  name: 'index',
  data() {
    return {
      sidebarCollapsed: false
    }
  },
  // 注册组件
  components: {
    navcon,
    leftnav
  },
  methods: {},
  created() {
    // 监听侧边栏折叠状态
    this.$root.Bus.$on('sidebarToggle', (collapsed) => {
      this.sidebarCollapsed = collapsed
    })
  },
  beforeUpdate() {},
  // 挂载前状态(里面是操作)
  beforeMount() {
    // 弹出登录成功
    this.$message({
      message: '登录成功',
      type: 'success'
    })
  }
}
</script>
<style >
.index-con {
  height: 100vh;
  width: 100vw;
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部导航栏样式 */
.index-header {
  width: 100%;
  height: 60px;
  padding: 0 30px 0 0;
  background-color: #334157;
  border-bottom: 2px solid #1f2d3d;
  flex-shrink: 0;
  box-sizing: border-box;
}

/* 内容容器 */
.content-container {
  flex: 1;
  /*height: calc(100vh - 135px);*/
  min-height: 560px;
  box-sizing: border-box;
  /*overflow: hidden;*/
}

/* 左侧菜单样式 */
.sidebar {
  width: 200px !important;
  height: 100%;
  background-color: #334157;
  margin: 0px;
  transition: width 0.3s ease;
  flex-shrink: 0;
  box-sizing: border-box;
  overflow: hidden;
}

.sidebar-collapsed {
  width: 64px !important;
}

/* 主体内容区 - 参考旧版本样式，保持原有效果 */
.index-main {
  padding: 20px;
  background-color: #f5f5f5;
  flex: 1;
  min-width: 0;
  max-width: calc(100vw - 200px);
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  border-left: 2px solid #333;
}

/* 侧边栏收起时，主体内容区宽度调整 */
.sidebar-collapsed + .index-main {
  max-width: calc(100vw - 64px);
}
</style>
