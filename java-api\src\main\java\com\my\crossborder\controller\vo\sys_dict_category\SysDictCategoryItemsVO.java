package com.my.crossborder.controller.vo.sys_dict_category;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 字典项
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictCategoryItemsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 值
     */
    private String value;

    /**
     * 文本
     */
    private String label;
    
    /**
     * 标签颜色
     */
    private String color;

}
