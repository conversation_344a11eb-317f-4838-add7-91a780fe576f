<template>
  <div class="todo-detail-container">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right" style="margin-bottom: 20px;">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">工作台</el-breadcrumb-item>
      <el-breadcrumb-item>待办事项</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="content-wrapper">
      <!-- 左侧待办事项列表 -->
      <div class="todo-list-panel">
        <div class="panel-header">
          <h3>待办事项</h3>
            <!-- <dict-select
              category-id="NOTIFICATION_TYPE"
              placeholder="公告类型"
              style="width: 120px">
            </dict-select> -->
        </div>

        <!-- tab切换 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="未读" name="unread"></el-tab-pane>
          <!-- <el-tab-pane label="已读" name="read"></el-tab-pane> -->
          <el-tab-pane label="全部" name="all"></el-tab-pane>
        </el-tabs>

        <!-- 待办事项列表 -->
        <div class="todo-items">
          <div
            v-for="todo in filteredTodos"
            :key="todo.id"
            :class="['todo-item', { 'active': selectedTodoId === todo.id, 'unread': !todo.read }]"
            @click="selectTodo(todo)">
            <div class="todo-item-header">
              <span class="todo-title">{{ todo.title }}</span>
              <el-tag type='warning' v-if="!todo.read"><i class="el-icon-s-check"> 未读</i></el-tag>
              <!-- <span class="todo-time">
                <el-tag :type="getTodoTagType(todo.type)" size="small">
                    {{ getTodoTypeName(todo.type) }}
                  </el-tag>
              </span> -->
            </div>
            <div class="todo-summary">{{ todo.summary }}</div>
            <div class="todo-meta">
              <el-tag :type="getTodoTagType(todo.type)" size="mini">{{ getTodoTypeName(todo.type) }}</el-tag>
              <span class="publish-time">{{ todo.publishTime }}</span>
            </div>
          </div>
          <div v-if="filteredTodos.length === 0" class="empty-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container" style="text-align: center;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageParams.current"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageParams.size"
            layout="total, prev, pager, next"
            :total="pageParams.total">
          </el-pagination>
        </div>
      </div>

      <!-- 右侧待办事项详情 -->
      <div class="todo-detail-panel">
        <div v-if="selectedTodo" class="detail-content">
          <div class="detail-header">
            <el-tag :type="getTodoTagType(selectedTodo.type)" size="medium">
              {{ getTodoTypeName(selectedTodo.type) }}
            </el-tag>
            <h2>{{ selectedTodo.title }}</h2>
            <div class="detail-actions">
              <el-dropdown @command="handleCommand">
                <el-button size="small">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="delete"><i class="el-icon-delete"></i> 删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>

          <div class="detail-info">
            <el-row :gutter="22">
              <!-- <el-col :span="22" style="text-align: center;">
                <span>{{ selectedTodo.publishTime }}</span>
                <span id="publishUserName">创建人：{{ selectedTodo.publishUserName }} </span>
              </el-col> -->
              <el-col :span="12">
                <div class="info-item">
                  <span class="label"><i class="el-icon-alarm-clock"></i> 待办截止时间：</span>
                  <span class="value">{{ selectedTodo.deadline }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">待办生成时间：</span>
                  <span class="value">{{ selectedTodo.publishTime }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="detail-content-body">
            <h4>待办内容：</h4>
            <div class="content-text" v-html="selectedTodo.content"></div>
          </div>

          <!-- <div class="detail-receivers" v-if="selectedTodo.receivers">
            <h4>接收对象</h4>
            <el-tag
              v-for="receiver in selectedTodo.receivers"
              :key="receiver"
              style="margin-right: 8px; margin-bottom: 8px;">
              {{ receiver }}
            </el-tag>
          </div> -->

          <!-- <div class="detail-attachments" v-if="selectedTodo.attachments">
            <h4>相关附件</h4>
            <div class="attachment-list">
              <div
                v-for="attachment in selectedTodo.attachments"
                :key="attachment.name"
                class="attachment-item">
                <i class="el-icon-document"></i>
                <span>{{ attachment.name }}</span>
                <el-button type="text" size="mini" @click="downloadAttachment(attachment)">下载</el-button>
              </div>
            </div>
          </div> -->
        </div>

        <div v-else class="empty-detail">
          <i class="el-icon-bell" style="font-size: 64px; color: #C0C4CC;"></i>
          <p>请选择一个待办事项查看详情</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { todoDetail } from '../../api/WkbTodo'
import { todoPage, todoDelete } from '../../api/WkbTodo'
import { dictCategoryItems } from '../../api/SysDictItem'
import DictSelect from '../../components/DictSelect.vue'

export default {
  name: 'TodoDetail',
  components: { DictSelect },
  data() {
    return {
      activeTab: 'all',
      selectedTodoId: null,
      selectedTodo: null,
      todos: [],
      todoTypes: [], // 待办类型字典
      loading: false,
      pageParams: {
        current: 1,
        size: 10,
        total: 0
      }
    }
  },
  created() {
    this.loadTodoTypes()
    this.loadTodos()
  },
  computed: {
    filteredTodos() {
      if (this.activeTab === 'unread') {
        return this.todos.filter(todo => !todo.read)
      }
      return this.todos
    }
  },
  methods: {
    // 加载待办类型字典
    loadTodoTypes() {
      dictCategoryItems({ categoryId: 'TODO_TYPE' })
        .then(res => {
          if (res.success && res.data) {
            this.todoTypes = res.data;
          }
        })
        .catch(err => {
          console.error('获取待办类型字典失败:', err);
        });
    },
    // 获取待办类型名称
    getTodoTypeName(type) {
      const item = this.todoTypes.find(t => t.value === type);
      return item ? item.label : type;
    },
    // 获取待办类型标签样式
    getTodoTagType(value) {
      if (!value) return '';
      const item = this.todoTypes.find(item => item.value === value);
      return item ? item.color : '';
    },
    // 加载待办列表
    async loadTodos() {
      this.loading = true
      try {
        const params = {
          current: this.pageParams.current,
          size: this.pageParams.size,
          read: this.activeTab === 'unread' ? false : undefined
        }
        const res = await todoPage(params)
        if (res.success) {
          this.todos = res.data.records
          this.pageParams.total = res.data.total

          // 如果有ID参数，选择对应的待办
          const todoId = this.$route.query.id
          if (todoId) {
            const todo = this.todos.find(n => n.id == todoId)
            if (todo) {
              this.selectTodo(todo)
            } else if (this.todos.length > 0) {
              // 如果找不到指定ID的待办，但列表不为空，则选择第一个
              this.selectTodo(this.todos[0])
            } else {
              // 列表为空，清空选择
              this.selectedTodo = null
              this.selectedTodoId = null
            }
          } else if (this.todos.length > 0) {
            // 默认选择第一个待办
            this.selectTodo(this.todos[0])
          } else {
            // 列表为空，清空选择
            this.selectedTodo = null
            this.selectedTodoId = null
          }
        }
      } catch (error) {
        this.$message.error('加载待办列表失败：' + (error.message || '未知错误'))
      }
      this.loading = false
    },
    // 选择待办
    async selectTodo(todo) {
      this.selectedTodoId = todo.id
      try {
        console.log('pre-detail', todo)
        // 获取待办详情
        const res = await todoDetail(todo.id)
        if (res.success) {
          this.selectedTodo = res.data
          // 如果未读，标记为已读并从缓存中移除
          if (!todo.read) {
            todo.read = true
            // 从store缓存中移除该待办
            this.$store.commit('removeTodoFromCache', todo.id)
          }
        }
      } catch (error) {
        console.log('error', error)
        this.$message.error('获取待办详情失败：' + (error.message || '未知错误'))
      }
    },
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.loadTodos()
    },
    handleAdd() {
      this.$router.push('/dashboard')
    },
    async handleCommand(command) {
      switch (command) {
        case 'edit':
          this.$message.info('编辑待办事项')
          break
        case 'delete':
          try {
            await this.$confirm('确认删除该待办事项?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            const deletedId = this.selectedTodoId
            await todoDelete({ idList: [deletedId] })
            this.$message.success('删除成功')
            // 删除成功后重新加载列表
            await this.loadTodos()
            
            // 如果加载后的列表为空，清空选择
            if (this.todos.length === 0) {
              this.selectedTodo = null
              this.selectedTodoId = null
            }
          } catch (error) {
            if (error !== 'cancel') {
              this.$message.error('删除失败：' + (error.message || '未知错误'))
            }
          }
          break
        case 'mark':
          try {
            await markTodoRead(this.selectedTodoId)
            this.selectedTodo.read = true
            this.$message.success('已标记为已读')
          } catch (error) {
            this.$message.error('标记失败：' + (error.message || '未知错误'))
          }
          break
      }
    },
    formatTime(timeStr) {
      if (!timeStr) return ''
      return timeStr.split(' ')[1].substring(0, 5)
    },
    downloadAttachment(attachment) {
      this.$message.success(`正在下载：${attachment.name}`)
    },
    handleSizeChange(size) {
      this.pageParams.size = size
      this.loadTodos()
    },
    handleCurrentChange(current) {
      this.pageParams.current = current
      this.loadTodos()
    }
  }
}
</script>

<style scoped>
.todo-detail-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

.content-wrapper {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.todo-list-panel {
  width: 400px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.todo-detail-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
}

.todo-items {
  flex: 1;
  overflow-y: auto;
}

.todo-item {
  padding: 15px;
  border-bottom: 1px solid #EBEEF5;
  cursor: pointer;
  transition: background-color 0.3s;
  border-left: 3px solid transparent;
}

.todo-item:hover {
  background-color: #f5f7fa;
}

.todo-item.active {
  background-color: #ecf5ff;
  border-left-color: #409EFF;
}

.todo-item.unread {
  background-color: #fef0f0;
}

.todo-item.unread .todo-title {
  font-weight: bold;
}

.todo-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.todo-title {
  font-size: 14px;
  color: #303133;
  flex: 1;
  margin-right: 10px;
  line-height: 1.4;
}

.todo-time {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.todo-summary {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.todo-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.publish-time {
  font-size: 12px;
  color: #909399;
}

.detail-content h2 {
  margin: 0 0 20px 0;
  color: #303133;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.detail-actions {
  display: flex;
  gap: 10px;
}

.detail-info {
  margin-bottom: 30px;
}

.info-item {
  margin-bottom: 15px;
}

.info-item .label {

  color: #606266;
  margin-right: 10px;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
}

.detail-content-body,
.detail-receivers,
.detail-attachments {
  margin-bottom: 30px;
}

.detail-content-body h4,
.detail-receivers h4,
.detail-attachments h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.content-text {
  color: #606266;
  line-height: 1.6;
}

.content-text p {
  margin-bottom: 10px;
}

.content-text h5 {
  color: #303133;
  margin: 15px 0 8px 0;
}

.content-text ul {
  margin: 10px 0;
  padding-left: 20px;
}

.attachment-list {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #EBEEF5;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-item i {
  margin-right: 10px;
  color: #909399;
}

.attachment-item span {
  flex: 1;
  color: #303133;
}

.empty-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.empty-detail p {
  margin-top: 20px;
  font-size: 16px;
}

.pagination-container {
  margin-top: 15px;
  padding: 10px 0;
}

#publishUserName {
  display: inline-block;
  margin-left: 20px;;
}
</style>
