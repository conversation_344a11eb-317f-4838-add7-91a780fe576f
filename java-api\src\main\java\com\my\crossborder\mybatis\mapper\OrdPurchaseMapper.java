package com.my.crossborder.mybatis.mapper;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchasePageDTO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchaseItemWithExpressVO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchasePageVO;
import com.my.crossborder.mybatis.entity.OrdPurchase;

/**
 * 采购订单主表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface OrdPurchaseMapper extends BaseMapper<OrdPurchase> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<OrdPurchasePageVO> page(OrdPurchasePageDTO pageDTO);

	/**
	 * 批量查询订单项
	 * @param orderIds 订单号集合
	 * @return 订单项列表
	 */
	List<OrdPurchaseItemWithExpressVO> selectOrderItems(@Param("orderIds") Set<String> orderIds);

}
