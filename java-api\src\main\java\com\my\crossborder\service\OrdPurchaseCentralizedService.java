package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.OrdPurchaseCentralized;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedInsertDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedPageDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedUpdateDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedDeleteDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedDetailVO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedPageVO;

/**
 * 集中采购 服务类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface OrdPurchaseCentralizedService extends IService<OrdPurchaseCentralized> {

	/**
	 * 新增
	 */
	void insert(OrdPurchaseCentralizedInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(OrdPurchaseCentralizedUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	OrdPurchaseCentralizedDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<OrdPurchaseCentralizedPageVO> page(OrdPurchaseCentralizedPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(OrdPurchaseCentralizedDeleteDTO deleteDTO);

	/**
	 * 确认状态
	 * @param id 采购记录ID
	 * @param status 确认状态
	 */
	void confirmStatus(Integer id, String status);

}
