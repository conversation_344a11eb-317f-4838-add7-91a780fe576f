package com.my.crossborder.controller.vo.erp_order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;

import java.io.Serializable;
import java.util.List;

/**
 * 分页_订单状态汇总
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpOrderStatusSummaryPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 采购登记
     */
    private String purchaseRegistration;

    /**
     * 集中采购登记
     */
    private String centralizedPurchase;

    /**
     * 未完整填写物流编号
     */
    private String incompleteLogistics;

    /**
     * 已填物流编号未入库
     */
    private String logisticsDoneNotWarehoused;

    /**
     * 货物已入库未出库
     */
    private String warehousedNotOutbound;

    /**
     * 已采购但出库前取消
     */
    private String purchaseDoneOrderCancelled;

    /**
     * 货物已入库重新采购
     */
    private String warehousedRepurchase;

    /**
     * 已出库售后
     */
    private String outboundAfterSale;

    /**
     * 物流理赔
     */
    private String logisticsClaim;

    /**
     * 台湾上架物品
     */
    private String taiwanListing;

    /**
     * 工作笔记列表
     */
    private List<WkbNoteDetailVO> notes;

}
