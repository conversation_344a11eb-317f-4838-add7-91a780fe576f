<template>
  <el-dialog :title="selectedDate + ' 采购详情'" :visible.sync="visible" top="8vh" width="1200px"
    :before-close="handleClose" @close="handleClose">

    <div class="purchase-detail-container">

      <!-- 采购数据标签页 -->
      <el-tabs v-model="activeTab" type="card">
        <!-- 分区采购登记 -->
        <el-tab-pane label="采购登记" name="purchase">
          <div class="purchase-table-container">
            <el-table :data="purchaseData" style="width: 100%" stripe border empty-text="暂无分区采购数据">
              <el-table-column prop="shopName" label="店铺名称" width="110" show-overflow-tooltip></el-table-column>
              <el-table-column prop="orderSn" label="订单号" width="150">
                <template slot-scope="scope">
                  <span class="order-number" @click="copyOrderNumber(scope.row.orderSn)">
                    {{ scope.row.orderSn }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="商品图片" width="80">
                <template slot-scope="scope">
                  <img :src="scope.row.itemImage" alt="商品图片" style="width: 50px; height: 50px; object-fit: cover;">
                </template>
              </el-table-column>
              <el-table-column prop="itemName" label="商品名称" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span class="item-name clickable-item-name"
                        @click="copyItemName(scope.row.itemName)"
                        :title="'点击复制商品名称: ' + scope.row.itemName">
                    {{ scope.row.itemName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="itemModelName" label="规格" width="150" show-overflow-tooltip></el-table-column>
              <!-- <el-table-column prop="itemPrice" label="商品价格" width="100">
                <template slot-scope="scope">
                  {{ scope.row.itemPrice.toFixed(2) }}
                </template>
              </el-table-column> -->
              <el-table-column prop="amount" label="数量" width="50" align="center"></el-table-column>
              <dict-table-column prop="purchaseChannel" label="采购渠道" width="120" category-id="PURCHASE_CHANNEL"
                align="center" />
              <el-table-column prop="purchaseAmount" label="采购金额" width="100" align="center">
                <template slot-scope="scope">
                  <span class="purchase-amount">¥{{ scope.row.purchaseAmount.toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="expressNo" label="快递单号" min-width="150" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.expressNo"
                        class="express-no clickable-express"
                        @click="copyExpressNo(scope.row.expressNo)"
                        :title="'点击复制快递号: ' + scope.row.expressNo">
                    {{ scope.row.expressNo }}
                  </span>
                  <span v-else class="no-express">未填写</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 集中采购登记 -->
        <el-tab-pane label="集中采购登记" name="centralized">
          <div class="purchase-table-container">
            <el-table :data="purchaseCentralizedData" style="width: 100%" stripe border empty-text="暂无集中采购数据">
              <el-table-column label="采购单号" width="150" align="center">
                <template slot-scope="scope">
                  <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
                    @click="copyToClipboard(scope.row.purchaseNumber, '采购单号', 'blue')"
                    :title="'点击复制采购单号: ' + scope.row.purchaseNumber">
                    {{ scope.row.purchaseNumber }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="关联订单" width="160" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.row.orderSnList && scope.row.orderSnList.length > 0">
                    <el-tag v-for="orderSn in scope.row.orderSnList" :key="orderSn" size="mini"
                      style="margin: 2px; cursor: pointer;" @click="copyOrderSn(orderSn)"
                      :title="'点击复制订单号: ' + orderSn">
                      {{ orderSn }}
                    </el-tag>
                  </div>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="品名" width="150" align="center">
                <template slot-scope="scope">
                  <span class="product-name clickable-item-name"
                        @click="copyItemName(scope.row.productName)"
                        :title="'点击复制品名: ' + scope.row.productName">
                    {{ scope.row.productName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="数量" width="90" align="center">
                <template slot-scope="scope">
                  {{ scope.row.quantity }}
                </template>
              </el-table-column>
              <el-table-column label="总金额" width="100" align="center">
                <template slot-scope="scope">
                  ¥ {{ scope.row.totalAmount }}
                </template>
              </el-table-column>
              <el-table-column label="单价" width="100" align="center">
                <template slot-scope="scope">
                  ¥ {{ scope.row.totalAmount / scope.row.quantity }}
                </template>
              </el-table-column>
              <dict-table-column prop="purchaseChannel" label="采购途径" category-id="PURCHASE_CENTRALIZED_CHANNEL"
                width="120"></dict-table-column>
              <!-- <el-table-column label="采购人" width="100" align="center">
                <template slot-scope="scope">
                  {{ scope.row.purchaseUserName }}
                </template>
              </el-table-column> -->
              <!-- <el-table-column label="采购日期" width="110" align="center">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.purchaseDate) }}
                </template>
              </el-table-column> -->
              <el-table-column prop="purchaseStatus" label="采购状态" min-width="140" align="center">
                <template slot-scope="scope">
                  <el-tooltip v-if="scope.row.purchaseStatus !== '0'"
                    :content="`${scope.row.confirmUserName || '未知'}, ${formatDateTime(scope.row.confirmTime) || '-'}`"
                    placement="top">
                    <dict-tag :value="scope.row.purchaseStatus" category-id="PURCHASE_CENTRALIZED_STATUS"
                      style="cursor: pointer;"></dict-tag>
                  </el-tooltip>
                  <dict-tag v-else :value="scope.row.purchaseStatus"
                    category-id="PURCHASE_CENTRALIZED_STATUS"></dict-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'StlPurchaseView',
  mixins: [copyMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    purchaseDetailData: {
      type: Object,
      default: () => ({
        purchaseData: [],
        purchaseCentralizedData: []
      })
    },
    selectedDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeTab: 'purchase'
    }
  },
  computed: {
    purchaseData() {
      return this.purchaseDetailData.purchaseData || []
    },
    purchaseCentralizedData() {
      return this.purchaseDetailData.purchaseCentralizedData || []
    }
  },
  methods: {
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    },

    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return ''
      const date = new Date(dateTimeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 复制订单号
    copyOrderNumber(orderSn) {
      if (!orderSn) return

      navigator.clipboard.writeText(orderSn).then(() => {
        this.$message.success(`订单号 ${orderSn} 已复制到剪贴板`)
      }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = orderSn
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success(`订单号 ${orderSn} 已复制到剪贴板`)
      })
    },

    // 复制快递单号
    copyExpressNumber(expressNo) {
      if (!expressNo) return

      navigator.clipboard.writeText(expressNo).then(() => {
        this.$message.success(`快递单号 ${expressNo} 已复制到剪贴板`)
      }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = expressNo
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success(`快递单号 ${expressNo} 已复制到剪贴板`)
      })
    },

    // 复制采购单号
    copyPurchaseNumber(purchaseNumber) {
      if (!purchaseNumber) return

      navigator.clipboard.writeText(purchaseNumber).then(() => {
        this.$message.success(`采购单号 ${purchaseNumber} 已复制到剪贴板`)
      }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = purchaseNumber
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success(`采购单号 ${purchaseNumber} 已复制到剪贴板`)
      })
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.purchase-detail-container {
  padding: 20px 0;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.purchase-table-container {
  margin-top: 20px;
}

/* 可点击的订单号样式 */
.order-number {
  color: #409EFF;
  cursor: pointer;
  text-decoration: underline;
  font-weight: 500;
}

.order-number:hover {
  color: #66b1ff;
}

/* 快递单号样式 */
.express-no {
  color: #67C23A;
  font-weight: bold;
  background-color: #F0F9FF;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #67C23A;
}

.clickable-express {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-express:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* 可点击的采购单号样式 */
.purchase-number {
  color: #67C23A;
  cursor: pointer;
  text-decoration: underline;
  font-weight: 500;
}

.purchase-number:hover {
  color: #85ce61;
}

/* 暂无快递单号样式 */
.no-express {
  color: #909399;
  font-style: italic;
}

/* 采购金额样式 */
.purchase-amount {
  color: #E6A23C;
  font-weight: bold;
}

/* 总金额样式 */
.total-amount {
  color: #67C23A;
  font-weight: bold;
}

/* 关联订单标签样式 */
.order-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.order-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.order-tag:hover {
  background-color: #409EFF;
  color: white;
}

/* 表格样式优化 */
.el-table {
  font-size: 13px;
}

.el-table th {
  background-color: #fafafa;
  color: #333;
  font-weight: 600;
}

.el-table td {
  padding: 8px 0;
}

/* 标签页样式 */
.el-tabs__header {
  margin-bottom: 20px;
}

.el-tabs__item {
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .purchase-table-container {
    overflow-x: auto;
  }
}
</style>
