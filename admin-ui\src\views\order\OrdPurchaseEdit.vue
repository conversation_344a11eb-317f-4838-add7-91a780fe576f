<template>
  <el-dialog :title="dialogTitle" top="8vh" :visible.sync="dialogVisible" width="650px" @close="handleDialogClose">
    <el-form :model="itemForm" :rules="itemRules" ref="itemForm" label-width="140px">
      <el-form-item label="店铺名称" prop="shopName">
        <el-input v-model="itemForm.shopName" placeholder="店铺名称" readonly></el-input>
      </el-form-item>

      <el-form-item label="订单号" prop="orderSn">
        <el-input v-model="itemForm.orderSn" placeholder="订单号" readonly></el-input>
      </el-form-item>

      <el-form-item label="下单时间" prop="createTime">
        <el-input v-model="formattedCreateTime" placeholder="下单时间" readonly></el-input>
      </el-form-item>

      <el-form-item label="订单总金额" prop="totalPrice">
        <el-input v-model="formattedTotalPrice" placeholder="订单总金额" readonly style="width: 200px;"></el-input>
        <span style="margin-left: 10px; color: #909399;">台币</span>
      </el-form-item>

      <hr style="margin: 20px 0; border: none; border-top: 1px solid #e4e7ed;" />

      <el-form-item label="订单入账金额" prop="incomeAmount">
        <el-input-number 
          v-model="itemForm.incomeAmount" 
          :precision="2" 
          :step="0.01" 
          :min="0" 
          controls-position="right"
          style="width: 200px;">
        </el-input-number>
        <span style="margin-left: 10px; color: #909399;">台币</span>
      </el-form-item>

      <el-form-item label="预计重量" prop="expectWeight">
        <el-input-number 
          v-model="itemForm.expectWeight" 
          :precision="2" 
          :step="0.01" 
          :min="0" 
          controls-position="right"
          style="width: 200px;">
        </el-input-number>
        <span style="margin-left: 10px; color: #909399;">kg</span>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { insertOrdPurchase, updateOrdPurchase } from '../../api/OrdPurchase'

export default {
  name: 'OrdPurchaseEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      itemForm: {
        orderSn: '',
        shopName: '',
        createTime: '',
        totalPrice: 0,
        incomeAmount: 0,
        expectWeight: 0
      },
      itemRules: {
        incomeAmount: [
          { required: true, message: '请输入订单入账金额', trigger: 'blur' },
          { validator: (_, value, callback) => {
            if (value <= 0) {
              callback(new Error('订单入账金额必须大于0'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ],
        expectWeight: [
          { required: true, message: '请输入预计重量', trigger: 'blur' },
          { validator: (_, value, callback) => {
            if (value <= 0) {
              callback(new Error('预计重量必须大于0'))
            } else {
              callback()
            }
          }, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return this.isEdit ? '编辑订单信息' : '添加订单信息'
    },
    formattedCreateTime() {
      if (!this.itemForm.createTime) return ''
      return this.formatDateTime(this.itemForm.createTime)
    },
    formattedTotalPrice() {
      if (!this.itemForm.totalPrice) return '0.00'
      return this.itemForm.totalPrice.toFixed(2)
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    },
    editData: {
      handler(newData) {
        if (this.visible && newData) {
          this.initForm()
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.editData) {
        // 编辑模式
        this.itemForm = {
          orderSn: this.editData.orderSn || '',
          shopName: this.editData.shopName || '',
          createTime: this.editData.createTime || '',
          totalPrice: this.editData.totalPrice || 0,
          incomeAmount: this.editData.incomeAmount || 0,
          expectWeight: this.editData.expectWeight || 0
        }
      } else {
        // 添加模式
        this.itemForm = {
          orderSn: '',
          shopName: '',
          createTime: '',
          totalPrice: 0,
          incomeAmount: 0,
          expectWeight: 0
        }
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.itemForm.validate((valid) => {
        if (valid) {
          // 准备提交的数据
          const submitData = {
            orderSn: this.itemForm.orderSn,
            incomeAmount: this.itemForm.incomeAmount,
            expectWeight: this.itemForm.expectWeight
          }

          console.log('提交的数据:', submitData)

          // 调用后端API
          const apiCall = this.isEdit ? updateOrdPurchase(submitData) : insertOrdPurchase(submitData)

          apiCall
            .then(response => {
              if (response.success) {
                this.$message.success('保存成功')
                this.$emit('submit', submitData)
                this.dialogVisible = false
              } else {
                this.$message.error('保存失败：' + response.message)
              }
            })
            .catch(error => {
              console.error('保存失败:', error)
              this.$message.error('保存失败，请稍后重试')
            })
        } else {
          this.$message.warning('请完善表单信息')
          return false
        }
      })
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    },

    // 对话框关闭时重置表单
    handleDialogClose() {
      this.$refs.itemForm.resetFields()
    }
  }
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 22px;
}

.el-input-number {
  width: 100%;
}

hr {
  margin: 20px 0;
  border: none;
  border-top: 1px solid #e4e7ed;
}
</style>
