package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderPageDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderPageVO;
import com.my.crossborder.mybatis.entity.OrdPurchaseCentralizedOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 集中采购订单 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface OrdPurchaseCentralizedOrderMapper extends BaseMapper<OrdPurchaseCentralizedOrder> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<OrdPurchaseCentralizedOrderPageVO> page(OrdPurchaseCentralizedOrderPageDTO pageDTO);
	
}
