package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.OrdTaiwan;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanInsertDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanPageDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanUpdateDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanDeleteDTO;
import com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanDetailVO;
import com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanPageVO;

/**
 * 台湾上架物品 服务类
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface OrdTaiwanService extends IService<OrdTaiwan> {

	/**
	 * 新增
	 */
	void insert(OrdTaiwanInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(OrdTaiwanUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	OrdTaiwanDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<OrdTaiwanPageVO> page(OrdTaiwanPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(OrdTaiwanDeleteDTO deleteDTO);	

}
