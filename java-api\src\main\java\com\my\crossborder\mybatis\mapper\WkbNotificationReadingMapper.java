package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingPageDTO;
import com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingPageVO;
import com.my.crossborder.mybatis.entity.WkbNotificationReading;

/**
 * 通知阅读表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface WkbNotificationReadingMapper extends BaseMapper<WkbNotificationReading> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<WkbNotificationReadingPageVO> page(WkbNotificationReadingPageDTO pageDTO);
	
}
