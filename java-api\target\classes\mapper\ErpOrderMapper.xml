<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.ErpOrderMapper">


    <!-- 物流未完成订单分页查询（包含订单项） -->
    <select id="incompleteLogisticsPageWithItems" resultType="com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO">
        SELECT
            order_id, order_sn, shop_id, shop_name, total_price, currency, 
            order_states, order_states_name, create_time, user_name
        FROM
            erp_order AS t1
        <where>
            1=1
            <!-- 物流未完成的条件：存在没有快递信息的订单项 -->
            AND EXISTS (
                SELECT 1 
                FROM erp_order_item t2 
                WHERE t2.order_id = t1.order_id
                  AND NOT EXISTS (
                    SELECT 1 
                    FROM erp_order_item_express t3 
                    WHERE t3.order_item_id = t2.id
                  )
            )
            <if test="shopIds != null and shopIds.size() > 0">
                AND t1.shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="orderSn != null and orderSn != ''">
                AND t1.order_sn = #{orderSn}
            </if>
            <if test="orderStates != null and orderStates != ''">
                AND t1.order_states = #{orderStates}
            </if>
        </where>
        ORDER BY t1.create_time ASC
    </select>

    <!-- 根据订单ID查询订单项（包含快递信息） -->
    <select id="selectOrderItemsWithExpress" resultType="com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemWithExpressVO">
		SELECT
			t1.id,
			t1.order_id AS orderId,
			t1.item_name AS itemName,
			t1.item_image AS itemImage,
			t1.item_price AS itemPrice,
			t1.amount,
			t1.item_model_name AS itemModelName,
			t1.item_model_sku AS itemModelSku,
			t2.express_no AS expressNo,
			t2.expressin_flag AS expressinFlag,
			t2.put_in_time AS putInTime 
		FROM
			erp_order_item t1
			LEFT JOIN erp_order_item_express t2 ON t2.order_item_id = t1.id
        WHERE t1.order_id = #{orderId}
        ORDER BY t1.product_idx ASC
    </select>

    <!-- 已填物流编号未入库订单分页查询（包含订单项） -->
    <select id="logisticsDoneNotWarehoused" resultType="com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO">
        SELECT
            order_id, order_sn, shop_id, shop_name, total_price, currency, 
            order_states, order_states_name, create_time, user_name, put_in_falg
        FROM
            erp_order AS t1
        <where>
            1=1
            <!-- 已填物流编号未入库的条件：所有订单项都有快递编号 -->
            AND NOT EXISTS (
                SELECT 1 
                FROM erp_order_item t2 
                WHERE t2.order_id = t1.order_id
                  AND NOT EXISTS (
                    SELECT 1 
                    FROM erp_order_item_express t3 
                    WHERE t3.order_item_id = t2.id 
                      AND t3.express_no IS NOT NULL 
                      AND t3.express_no != ''
                  )
            )
            <!-- 入库状态条件：100（未入库）或101（部分入库） -->
            <!-- AND t1.put_in_falg in ('100', '101') -->
             
            <if test="putInFalg != null and putInFalg != '' and putInFalg == '201'">
                AND t1.put_in_falg in ('100', '101')
            </if>
            <if test="putInFalg != null and putInFalg != '' and putInFalg != '201'">
                AND t1.put_in_falg = #{putInFalg}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                AND t1.shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="orderSn != null and orderSn != ''">
                AND t1.order_sn = #{orderSn}
            </if>
            <if test="orderStates != null and orderStates != ''">
                AND t1.order_states = #{orderStates}
            </if>
        </where>
        ORDER BY t1.create_time ASC
    </select>

    <!-- 已入库未出库订单分页查询（包含订单项） -->
    <select id="warehousedNotOutbound" resultType="com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO">
        SELECT
            order_id, order_sn, shop_id, shop_name, total_price, currency, 
            order_states, order_states_name, create_time, user_name
        FROM
            erp_order AS t1
        <where>
            1=1
            <!-- 已入库未出库的条件：入库状态是102（完全入库），出库状态是0（未出库） -->
            AND t1.put_in_falg = '102'
            AND t1.out_flag = '0'
            <!-- 实际出库标识 这个字段可能需要 
            AND t1.real_out_flag = 0
             -->
            <if test="shopIds != null and shopIds.size() > 0">
                AND t1.shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="orderSn != null and orderSn != ''">
                AND t1.order_sn = #{orderSn}
            </if>
            <if test="orderStates != null and orderStates != ''">
                AND t1.order_states = #{orderStates}
            </if>
        </where>
        ORDER BY t1.create_time ASC
    </select>

	<!--  2025.07.26 -->
    <!-- 已采购但出库前取消订单分页查询（包含订单项） -->
<!--     <select id="purchaseDoneOrderCancelled" resultType="com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO"> -->
<!--         SELECT -->
<!--             order_id, order_sn, shop_id, shop_name, total_price, currency,  -->
<!--             order_states, order_states_name, create_time, user_name -->
<!--         FROM -->
<!--             erp_order AS t1 -->
<!--         <where> -->
<!--             1=1 -->
<!--             已采购但出库前取消的条件：订单状态是003（不成立） -->
<!--             AND t1.order_states = '003' -->
<!--             <if test="shopIds != null and shopIds.size() > 0"> -->
<!--                 AND t1.shop_id IN -->
<!--                 <foreach collection="shopIds" item="shopId" open="(" separator="," close=")"> -->
<!--                     #{shopId} -->
<!--                 </foreach> -->
<!--             </if> -->
<!--             <if test="orderSn != null and orderSn != ''"> -->
<!--                 AND t1.order_sn = #{orderSn} -->
<!--             </if> -->
<!--             <if test="orderStates != null and orderStates != ''"> -->
<!--                 AND t1.order_states = #{orderStates} -->
<!--             </if> -->
<!--         </where> -->
<!--         ORDER BY t1.create_time ASC -->
<!--     </select> -->



    <!-- 添加批量查询订单项与快递信息的SQL -->
    <select id="selectOrderItemListWithExpress" resultType="com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemWithExpressVO">
        SELECT
			t1.id,
			t1.order_id AS orderId,
			t1.item_name AS itemName,
			t1.item_image AS itemImage,
			t1.item_price AS itemPrice,
			t1.amount,
			t1.item_model_name AS itemModelName,
			t1.item_model_sku AS itemModelSku,
			t2.express_no AS expressNo,
			t2.expressin_flag AS expressinFlag,
			t2.put_in_time AS putInTime 
		FROM
			erp_order_item t1
			LEFT JOIN erp_order_item_express t2 ON t2.order_item_id = t1.id
        WHERE t1.order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <!-- 重新采购订单分页查询（包含订单项） -->
    <select id="repurchasePageWithItems" resultType="com.my.crossborder.controller.vo.erp_order.RepurchaseErpOrderPageVO">
        SELECT
            t1.order_id,
            t1.order_sn,
            t1.shop_id,
            t1.shop_name,
            t1.total_price,
            t1.currency,
            t1.order_states,
            t1.order_states_name,
            t1.create_time,
            t1.user_name,
            t3.issue,
            t3.issue_time,
            t3.close_way,
            t3.close_status,
            t3.close_time,
            t3.id,
            t3.issue_user_id,
            t3.close_user_id,
            (select real_name from sys_user where user_id = t3.issue_user_id) as issue_user_name,
            (select real_name from sys_user where user_id = t3.close_user_id) as close_user_name,
            (select real_name from sys_user where user_id = t3.confirm_user_id) as confirm_user_name
        FROM
            erp_order AS t1 
            INNER JOIN ord_repurchase AS t3 ON t1.order_sn = t3.order_sn 
        <where>
            1=1
            <if test="shopIds != null and shopIds.size() > 0">
                AND t1.shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="orderSn != null and orderSn != ''">
                AND t1.order_sn LIKE CONCAT('%', #{orderSn}, '%')
            </if>
            <if test="orderStates != null and orderStates != ''">
                AND t1.order_states = #{orderStates}
            </if>
            <if test="closeWay != null and closeWay != ''">
                AND t3.close_way = #{closeWay}
            </if>
            <if test="closeStatus != null and closeStatus != ''">
                AND t3.close_status = #{closeStatus}
            </if>
        </where>
        ORDER BY t1.create_time ASC
    </select>

    <!-- 订单选择器专用分页查询（包含订单项） -->
    <select id="selectorPageWithItems" resultType="com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO">
        SELECT
            order_id, order_sn, shop_id, shop_name, shop_other_name, total_price, currency,
            order_states, order_states_name, create_time, user_name
        FROM
            erp_order AS t1
        <where>
            1=1
            <!-- 通用查询条件，不限制特定业务状态 -->
            <if test="shopIds != null and shopIds.size() > 0">
                AND t1.shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="shopName != null and shopName != ''">
                AND t1.shop_name LIKE CONCAT('%', #{shopName}, '%')
            </if>
            <if test="orderSn != null and orderSn != ''">
                AND t1.order_sn LIKE CONCAT('%', #{orderSn}, '%')
            </if>
            <if test="orderStates != null and orderStates != ''">
                AND t1.order_states = #{orderStates}
            </if>
            <if test="startTime != null">
                AND t1.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND t1.create_time &lt;= #{endTime}
            </if>
            <!-- 售后过滤条件：台湾商品特殊过滤 -->
            <if test="afterSaleFilter != null and afterSaleFilter == 'taiwan'">
                AND EXISTS (
                    SELECT 1 FROM ord_after_sale t_after
                    WHERE t_after.order_sn = t1.order_sn
                    AND t_after.issue_type IN ('1', '5')
                )
            </if>
            <!-- 售后过滤条件：理赔商品特殊过滤 -->
            <if test="afterSaleFilter != null and afterSaleFilter == 'claim'">
                AND EXISTS (
                    SELECT 1 FROM ord_after_sale t_after
                    WHERE t_after.order_sn = t1.order_sn
                    AND t_after.issue_type = '4'
                )
            </if>
        </where>
        ORDER BY t1.create_time DESC
    </select>

    <!-- 订单状态汇总分页查询 -->
    <select id="orderStatusSummaryPage" resultType="com.my.crossborder.controller.vo.erp_order.ErpOrderStatusSummaryPageVO">
        SELECT
            t1.order_sn AS orderSn,
            t1.shop_name AS shopName,
            CASE WHEN t_purchase.order_sn IS NOT NULL THEN '√' ELSE '' END AS purchaseRegistration,
            CASE WHEN t_centralized.order_sn IS NOT NULL THEN '√' ELSE '' END AS centralizedPurchase,
            CASE WHEN t_incomplete.order_sn IS NOT NULL THEN '√' ELSE '' END AS incompleteLogistics,
            CASE WHEN t_logistics_done.order_sn IS NOT NULL THEN '√' ELSE '' END AS logisticsDoneNotWarehoused,
            CASE WHEN t_warehoused.order_sn IS NOT NULL THEN '√' ELSE '' END AS warehousedNotOutbound,
            CASE WHEN t_cancelled.order_sn IS NOT NULL THEN '√' ELSE '' END AS purchaseDoneOrderCancelled,
            CASE WHEN t_repurchase.order_sn IS NOT NULL THEN '√' ELSE '' END AS warehousedRepurchase,
            CASE WHEN t_after_sale.order_sn IS NOT NULL THEN '√' ELSE '' END AS outboundAfterSale,
            CASE WHEN t_claim.order_sn IS NOT NULL THEN '√' ELSE '' END AS logisticsClaim,
            CASE WHEN t_taiwan.order_sn IS NOT NULL THEN '√' ELSE '' END AS taiwanListing
        FROM erp_order t1

        LEFT JOIN (
            SELECT DISTINCT order_sn FROM ord_purchase
        ) t_purchase ON t1.order_sn = t_purchase.order_sn

        LEFT JOIN (
            SELECT DISTINCT order_sn FROM ord_purchase_centralized_order
        ) t_centralized ON t1.order_sn = t_centralized.order_sn

        LEFT JOIN (
            SELECT DISTINCT t1.order_sn
            FROM erp_order t1
            WHERE EXISTS (
                SELECT 1
                FROM erp_order_item t2
                WHERE t2.order_id = t1.order_id
                  AND NOT EXISTS (
                    SELECT 1
                    FROM erp_order_item_express t3
                    WHERE t3.order_item_id = t2.id
                  )
            )
        ) t_incomplete ON t1.order_sn = t_incomplete.order_sn

        LEFT JOIN (
            SELECT DISTINCT t1.order_sn
            FROM erp_order t1
            WHERE NOT EXISTS (
                SELECT 1
                FROM erp_order_item t2
                WHERE t2.order_id = t1.order_id
                  AND NOT EXISTS (
                    SELECT 1
                    FROM erp_order_item_express t3
                    WHERE t3.order_item_id = t2.id
                      AND t3.express_no IS NOT NULL
                      AND t3.express_no != ''
                  )
            )
            AND t1.put_in_falg IN ('100', '101')
        ) t_logistics_done ON t1.order_sn = t_logistics_done.order_sn

        LEFT JOIN (
            SELECT DISTINCT order_sn
            FROM erp_order
            WHERE put_in_falg = '102' AND out_flag = '0'
        ) t_warehoused ON t1.order_sn = t_warehoused.order_sn

        LEFT JOIN (
            SELECT DISTINCT t2.order_sn
            FROM ord_refund t1
            RIGHT JOIN erp_order t2 ON t1.order_sn = t2.order_sn
            WHERE t2.order_states = '003'
            AND EXISTS (
        	 	SELECT 1 FROM erp_order_item t3
        	 	INNER JOIN ord_purchase_item t4 ON t3.id = t4.order_item_id
        	 	WHERE t3.order_id = t2.order_id
        	 )
        ) t_cancelled ON t1.order_sn = t_cancelled.order_sn

        LEFT JOIN (
            SELECT DISTINCT order_sn FROM ord_repurchase
        ) t_repurchase ON t1.order_sn = t_repurchase.order_sn

        LEFT JOIN (
            SELECT DISTINCT order_sn FROM ord_after_sale
        ) t_after_sale ON t1.order_sn = t_after_sale.order_sn

        LEFT JOIN (
            SELECT DISTINCT tOrder.order_sn
            FROM ord_claim t1
            LEFT JOIN erp_order_item tItem ON t1.order_item_id = tItem.id
            LEFT JOIN erp_order tOrder ON tItem.order_id = tOrder.order_id
            WHERE tOrder.order_sn IS NOT NULL
        ) t_claim ON t1.order_sn = t_claim.order_sn

        LEFT JOIN (
            SELECT DISTINCT t3.order_sn
            FROM ord_taiwan t1
            LEFT JOIN erp_order_item t2 ON t1.order_item_id = t2.id
            LEFT JOIN erp_order t3 ON t2.order_id = t3.order_id
            WHERE t3.order_sn IS NOT NULL
        ) t_taiwan ON t1.order_sn = t_taiwan.order_sn

        <where>
            <if test="orderSn != null and orderSn != ''">
                t1.order_sn = #{orderSn}
            </if>
            <if test="shopIds != null and shopIds.size() > 0">
                AND t1.shop_id IN
                <foreach collection="shopIds" item="shopId" open="(" close=")" separator=",">
                    #{shopId}
                </foreach>
            </if>
        </where>
        ORDER BY t1.create_time DESC
    </select>

</mapper>
