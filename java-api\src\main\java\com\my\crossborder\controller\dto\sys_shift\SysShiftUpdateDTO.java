package com.my.crossborder.controller.dto.sys_shift;

import java.io.Serializable;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_排班表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 排班日期
     */
	@NotNull(message="shiftDay不能为空")
    private LocalDate shiftDay;

    /**
     * 店铺ID
     */
	@NotNull(message="shopId不能为空")
    private Integer shopId;

    /**
     * 客服ID
     */
	@NotNull(message="serviceUserId不能为空")
    private Integer serviceUserId;

    /**
     * 主管ID
     */
	@NotNull(message="supervisorUserId不能为空")
    private Integer supervisorUserId;


}
