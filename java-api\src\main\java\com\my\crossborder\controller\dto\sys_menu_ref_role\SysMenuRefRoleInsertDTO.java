package com.my.crossborder.controller.dto.sys_menu_ref_role;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_菜单_角色_关联表
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenuRefRoleInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 菜单id
     */
	@NotNull(message="menuId不能为空")
    private String menuId;

}
