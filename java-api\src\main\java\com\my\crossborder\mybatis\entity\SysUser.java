package com.my.crossborder.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 系统用户
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_user")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Integer userId;

    /**
     * 岗位(角色id)
     */
    private Integer roleId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 人员状态
     */
    private String status;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 备用联系电话
     */
    private String phoneSecondary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除（1-已删除，0-未删除）
     */
    private Boolean disable;

    /**
     * 逻辑删除时间
     */
    private LocalDateTime disableTime;


}
