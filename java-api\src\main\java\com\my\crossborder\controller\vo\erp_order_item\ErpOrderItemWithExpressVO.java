package com.my.crossborder.controller.vo.erp_order_item;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 订单项信息（包含快递信息）
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpOrderItemWithExpressVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单ID，关联erp_order.order_id
     */
    private String orderId;

    /**
     * 商品索引，同一订单中商品的序号
     */
    private Integer productIdx;

    /**
     * 商品ID
     */
    private String itemId;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品图片URL
     */
    private String itemImage;

    /**
     * 商品价格
     */
    private BigDecimal itemPrice;

    /**
     * 商品数量
     */
    private Integer amount;

    /**
     * 商品规格名称
     */
    private String itemModelName;

    /**
     * 商品规格SKU
     */
    private String itemModelSku;

    /**
     * 快递编号
     */
    private String expressNo;

    /**
     * 快递入仓状态
     */
    private String expressinFlag;

    /**
     * 入仓时间
     */
    private LocalDateTime putInTime;

} 