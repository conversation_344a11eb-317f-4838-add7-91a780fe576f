package com.my.crossborder.controller.dto.ord_taiwan;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_台湾上架物品
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdTaiwanDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<String> idList;
	
}
