package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemPageDTO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemPageVO;
import com.my.crossborder.mybatis.entity.ErpOrderItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 订单项表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ErpOrderItemMapper extends BaseMapper<ErpOrderItem> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<ErpOrderItemPageVO> page(ErpOrderItemPageDTO pageDTO);
	
}
