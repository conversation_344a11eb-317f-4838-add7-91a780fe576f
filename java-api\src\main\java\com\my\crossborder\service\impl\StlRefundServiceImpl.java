package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.StlRefund;
import com.my.crossborder.mybatis.mapper.StlRefundMapper;
import com.my.crossborder.service.StlRefundService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.stl_refund.StlRefundInsertDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundPageDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundUpdateDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundDeleteDTO;
import com.my.crossborder.controller.vo.stl_refund.StlRefundDetailVO;
import com.my.crossborder.controller.vo.stl_refund.StlRefundPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import cn.dev33.satoken.stp.StpUtil;
import com.my.crossborder.service.OrdRefundService;
import com.my.crossborder.enums.RefundSettlementStatus;
import com.my.crossborder.exception.BusinessException;

import lombok.RequiredArgsConstructor;
import java.time.LocalDateTime;

/**
 * 结算_退款结算表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
@RequiredArgsConstructor
public class StlRefundServiceImpl extends ServiceImpl<StlRefundMapper, StlRefund> implements StlRefundService {

	private final OrdRefundService ordRefundService;


	@Transactional
	@Override
	public void insert(StlRefundInsertDTO insertDTO) {
		// 检查是否已经存在该周期的结算记录
		StlRefund existing = this.lambdaQuery()
				.eq(StlRefund::getRefundUserId, insertDTO.getRefundUserId())
				.eq(StlRefund::getRefundDateStart, insertDTO.getRefundDateStart())
				.eq(StlRefund::getRefundDateEnd, insertDTO.getRefundDateEnd())
				.one();
		BusinessException.when(existing != null, "该周期已存在结算记录");

		// 保存结算记录
		StlRefund entity = BeanUtil.copyProperties(insertDTO, StlRefund.class);
		entity.setCreateUserId(StpUtil.getLoginIdAsInt());
		entity.setSettlementDate(LocalDateTime.now());
		this.save(entity);

		// 更新ord_refund表中对应记录的settlement_status为已结算
		this.ordRefundService.settlementDone(insertDTO.getRefundUserId(),
											insertDTO.getRefundDateStart(),
											insertDTO.getRefundDateEnd());
	}

	@Transactional
	@Override
	public void update(StlRefundUpdateDTO updateDTO) {
		StlRefund entity = BeanUtil.copyProperties(updateDTO, StlRefund.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public StlRefundDetailVO detail(Integer id) {
		StlRefund entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, StlRefundDetailVO.class);
	}

	@Override
	public Page<StlRefundPageVO> page(StlRefundPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(StlRefundDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Override
	public StlRefundDetailVO getByWeek(Integer refundUserId, Integer refundYear, Integer refundMonth, Integer refundWeek) {
//		StlRefund entity = this.lambdaQuery()
//				.eq(StlRefund::getRefundUserId, refundUserId)
//				.eq(StlRefund::getRefundYear, refundYear)
//				.eq(StlRefund::getRefundMonth, refundMonth)
//				.eq(StlRefund::getRefundWeek, refundWeek)
//				.one();
//
//		if (entity == null) {
//			return null;
//		}
//
//		StlRefundDetailVO result = BeanUtil.copyProperties(entity, StlRefundDetailVO.class);
//		// TODO: 可以在这里添加创建用户名的查询逻辑
//		return result;
		return null;
	}
}
