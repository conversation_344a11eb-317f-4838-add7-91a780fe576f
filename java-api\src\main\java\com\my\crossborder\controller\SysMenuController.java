package com.my.crossborder.controller;


import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.auth.AuthConstant;
import com.my.crossborder.controller.dto.sys_menu.SysMenuDeleteDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuGrantDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuInsertDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuPageDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_menu.SysMenuDetailVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuNavVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuPageVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuTreeVO;
import com.my.crossborder.service.SysMenuRefRoleService;
import com.my.crossborder.service.SysMenuService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;

/**
 * 系统菜单表 
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/api/sys-menu")
@RequiredArgsConstructor
public class SysMenuController {

    private final SysMenuService sysMenuService;
    private final SysMenuRefRoleService sysMenuRefRoleService;
    

    /**
    * 新增
    */
    @SaCheckPermission("sys-menu:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysMenuInsertDTO insertDTO) {
    	this.sysMenuService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("sys-menu:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody SysMenuUpdateDTO updateDTO) {
    	this.sysMenuService.update(updateDTO);
    	return StdResp.success();
    }

    /**
    * 授权
    */
    @SaCheckPermission("sys-menu:grant")
    @PostMapping("grant")
    public StdResp<?> grant(@Valid @RequestBody SysMenuGrantDTO dto) {
    	this.sysMenuRefRoleService.grant(dto);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @SaCheckPermission("sys-menu:view")
    @GetMapping("/{id}")
    public StdResp<SysMenuDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.sysMenuService.detail(id));
    }
	
    /**
     * 分页
     */
    @SaCheckPermission("sys-menu:view")
    @GetMapping(value = "page")
    public StdResp<Page<SysMenuPageVO>> page(SysMenuPageDTO pageDTO) {
        Page<SysMenuPageVO> page = this.sysMenuService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
     * 菜单树结构
     * @return 所有菜单的树状结构，格式与前端menuData一致
     */
    @SaCheckPermission("sys-menu:view")
    @GetMapping(value = "tree-nodes")
    public StdResp<List<SysMenuTreeVO>> treeNodes() {
    	List<SysMenuTreeVO> treeNodes = this.sysMenuService.treeNodes();
    	return StdResp.success(treeNodes);
    }
    
    /**
     * 根据角色ID查询菜单ID列表
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    @SaCheckPermission("sys-menu:view")
    @GetMapping(value = "list-by-roleid")
    public StdResp<List<String>> listByRoleId(Integer roleId) {
        if (roleId == null) {
            return StdResp.success(new ArrayList<>());
        }
        List<String> menuIds = this.sysMenuRefRoleService.getMenuIds(roleId);
        return StdResp.success(menuIds);
    }
    
    /**
     * 查询我的所有权限
     */
    @GetMapping(value = "permissions")
    public StdResp<List<String>> permissions() {
    	List<String> permissions = StpUtil.getPermissionList();
    	return StdResp.success(permissions);
    }
    
    /**
     * 查询我的菜单
     * @return 当前用户有权限的菜单树结构，格式与leftnav.vue组件匹配
     */
    @SuppressWarnings("unchecked")
	@GetMapping(value = "tree")
    public StdResp<List<SysMenuNavVO>> tree() {
    	List<SysMenuNavVO> data = (List<SysMenuNavVO>) StpUtil.getTokenSession().get(AuthConstant.TOKEN_SESSION_MENU);
    	
    	return StdResp.success(data);
    }
    
	/**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("sys-menu:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysMenuDeleteDTO deleteDTO) {
    	this.sysMenuService.delete(deleteDTO);
		return StdResp.success();
    }
    
}