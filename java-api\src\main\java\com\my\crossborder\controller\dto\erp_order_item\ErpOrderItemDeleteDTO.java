package com.my.crossborder.controller.dto.erp_order_item;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_订单项表
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpOrderItemDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<String> idList;
	
}
