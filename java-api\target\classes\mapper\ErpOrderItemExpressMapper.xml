<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.ErpOrderItemExpressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.ErpOrderItemExpress">
        <id column="id" property="id" />
        <result column="express_no" property="expressNo" />
        <result column="expressIn_flag" property="expressinFlag" />
        <result column="put_in_time" property="putInTime" />
        <result column="put_create_time" property="putCreateTime" />
        <result column="order_item_id" property="orderItemId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, express_no, expressIn_flag, put_in_time, put_create_time, order_item_id
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.erp_order_item_express.ErpOrderItemExpressPageVO">
		SELECT
			id, express_no, expressIn_flag, put_in_time, put_create_time, order_item_id
		FROM
			erp_order_item_express AS t1
		<where>
        	1=1
	        <if test="expressNo != null and expressNo != ''">
	           	AND t1.express_no = #{expressNo}
            </if>
	        <if test="expressinFlag != null and expressinFlag != ''">
	           	AND t1.expressIn_flag = #{expressinFlag}
            </if>
	        <if test="putInTime != null and putInTime != ''">
	           	AND t1.put_in_time = #{putInTime}
            </if>
	        <if test="putCreateTime != null and putCreateTime != ''">
	           	AND t1.put_create_time = #{putCreateTime}
            </if>
	        <if test="orderItem != null and orderItemId != ''">
	           	AND t1.order_item_id = #{orderItemId}
            </if>
        </where>
    </select>

</mapper>
