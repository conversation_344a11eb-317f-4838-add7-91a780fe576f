package com.my.crossborder.controller.dto.ord_refund;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 修改_退款表
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdRefundUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
	@NotNull(message="orderSn不能为空")
    private String orderSn;

    /**
     * 申请状态 (0:待申请 1:已申请 2:不退采买改为入库)
     */
	@NotNull(message="applyStatus不能为空")
    private String applyStatus;

    /**
     * 申请退款金额
     */
	@NotNull(message="applyAmount不能为空")
    private BigDecimal applyAmount;

    /**
     * 申请人id
     */
	@NotNull(message="applyUserId不能为空")
    private Integer applyUserId;

    /**
     * 申请时间
     */
	@NotNull(message="applyTime不能为空")
    private LocalDateTime applyTime;

    /**
     * 结果状态 (1:退款成功 2:退款失败 3:确认已入库)
     */
	@NotNull(message="resultStatus不能为空")
    private String resultStatus;

    /**
     * 结果时间
     */
	@NotNull(message="resultTime不能为空")
    private LocalDateTime resultTime;

    /**
     * 退款失败备注
     */
	@NotNull(message="refundFailReason不能为空")
    private String refundFailReason;

    /**
     * 退款成功金额
     */
	@NotNull(message="refundSuccessAmount不能为空")
    private BigDecimal refundSuccessAmount;

    /**
     * 结果填写人
     */
	@NotNull(message="resultUserId不能为空")
    private Integer resultUserId;

}
