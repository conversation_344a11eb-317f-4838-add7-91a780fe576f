package com.my.crossborder.controller;


import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.erp_order.ErpOrderDeleteDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderInsertDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderPageDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderStatusSummaryPageDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderUpdateDTO;
import com.my.crossborder.controller.dto.erp_order.RepurchaseErpOrderPageDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.erp_order.ErpOrderDetailVO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderStatusSummaryPageVO;
import com.my.crossborder.controller.vo.erp_order.RepurchaseErpOrderPageVO;
import com.my.crossborder.forest.erp990.enums.OrderStatesEnum;
import com.my.crossborder.service.ErpOrderService;
import com.my.crossborder.service.SysShopService;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

/**
 * 订单主表(ERP990本地) 
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/api/erp-order")
@RequiredArgsConstructor
public class ErpOrderController {

    private final ErpOrderService erpOrderService;
    private final SysShopService sysShopService;

    /**
     * 物流未完成订单分页查询（包含订单项）
     */
    @GetMapping("/incomplete-logistics/page-with-items")
    public StdResp<Page<ErpOrderPageVO>> incompleteLogisticsPageWithItems(ErpOrderPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        return StdResp.success(erpOrderService.incompleteLogisticsPageWithItems(pageDTO));
    }

    /**
     * 已填物流编号未入库订单分页查询（包含订单项）
     */
    @GetMapping("/logistics-done-not-warehoused/page-with-items")
    public StdResp<Page<ErpOrderPageVO>> logisticsDoneNotWarehoused(ErpOrderPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        return StdResp.success(erpOrderService.logisticsDoneNotWarehoused(pageDTO));
    }

    /**
     * 已入库未出库订单分页查询（包含订单项）
     */
    @GetMapping("/warehoused-not-outbound/page-with-items")
    public StdResp<Page<ErpOrderPageVO>> warehousedNotOutbound(ErpOrderPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        return StdResp.success(erpOrderService.warehousedNotOutbound(pageDTO));
    }

//     2025.07.26 这块逻辑是错的， 删除
//    /**
//     * 已采购但出库前取消订单分页查询（包含订单项）
//     */
//    @GetMapping("/purchase-done-order-cancelled/page-with-items")
//    public StdResp<Page<ErpOrderPageVO>> purchaseDoneOrderCancelled(ErpOrderPageDTO pageDTO) {
//        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
//        List<Integer> myShopIds = this.sysShopService.myShopIds();
//        if (myShopIds != null && !myShopIds.isEmpty()) {
//            pageDTO.setShopIds(myShopIds.stream().map(String::valueOf).collect(Collectors.toList()));
//        }
//
//        return StdResp.success(erpOrderService.purchaseDoneOrderCancelled(pageDTO));
//    }

    /**
     * 重新采购订单分页查询（包含订单项）
     */
    @GetMapping("/repurchase/page-with-items")
    public StdResp<Page<RepurchaseErpOrderPageVO>> repurchasePageWithItems(RepurchaseErpOrderPageDTO pageDTO) {
        return StdResp.success(erpOrderService.repurchasePageWithItems(pageDTO));
    }

    /**
     * 订单选择器专用分页查询（包含订单项）
     * 用于OrderSelector组件的通用订单选择功能
     */
    @GetMapping("/selector/page-with-items")
    public StdResp<Page<ErpOrderPageVO>> selectorPageWithItems(ErpOrderPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        return StdResp.success(erpOrderService.selectorPageWithItems(pageDTO));
    }

    /**
     * 根据ID查询订单详情
     */
    @GetMapping("/{orderId}")
    public StdResp<ErpOrderDetailVO> detail(@PathVariable String orderId) {
        return StdResp.success(erpOrderService.detail(orderId));
    }

    /**
     * 新增订单
     */
    @PostMapping
    public StdResp<String> insert(@Valid @RequestBody ErpOrderInsertDTO insertDTO) {
        erpOrderService.insert(insertDTO);
        return StdResp.success("新增成功");
    }

    /**
     * 修改订单
     */
    @PutMapping
    public StdResp<String> update(@Valid @RequestBody ErpOrderUpdateDTO updateDTO) {
        erpOrderService.update(updateDTO);
        return StdResp.success("修改成功");
    }

    /**
     * 删除订单
     */
    @DeleteMapping
    public StdResp<String> delete(@Valid @RequestBody ErpOrderDeleteDTO deleteDTO) {
        erpOrderService.delete(deleteDTO);
        return StdResp.success("删除成功");
    }

    /**
     * 订单状态汇总分页查询
     */
    @GetMapping("/status-summary/page")
    public StdResp<Page<ErpOrderStatusSummaryPageVO>> orderStatusSummaryPage(ErpOrderStatusSummaryPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds);
        }

        Page<ErpOrderStatusSummaryPageVO> page = erpOrderService.orderStatusSummaryPage(pageDTO);
        return StdResp.success(page);
    }

    /**
     * 获取订单状态枚举列表
     */
    @GetMapping("/order-states")
    public StdResp<List<OrderStateVO>> getOrderStates() {
        List<OrderStateVO> orderStates = Arrays.stream(OrderStatesEnum.values())
            .map(state -> new OrderStateVO(state.getCode(), state.getDescription()))
            .collect(Collectors.toList());
        return StdResp.success(orderStates);
    }

    /**
     * 订单状态VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderStateVO {
        
        private String value;

        private String label;

    }

}
