package com.my.crossborder.controller.dto.ord_taiwan;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 新增_台湾上架物品
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdTaiwanInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单项id
     */
	@NotNull(message="订单项id不能为空")
    private String orderItemId;

    /**
     * 数量
     */
	@NotNull(message="数量不能为空")
    private Integer amount;

    /**
     * 退货单号
     */
    private String waybillNumber;

    /**
     * 状态字典：TAIWAN_GOODS_STATUS
     */
	@NotNull(message="状态不能为空")
    private String status;

    /**
     * 台湾仓柜号
     */
    private String warehouseNumber;

    /**
     * 上架时间
     */
    private LocalDate onlineTime;

    /**
     * 下架时间
     */
    private LocalDate offlineTime;

}
