<template>
  <div class="calendar-container">
    <!-- 日历图例 -->
    <div class="calendar-legend">
      <div class="legend-item">
        <span class="legend-circle fully-scheduled-count"></span>
        <span class="legend-text">排满</span>
      </div>
      <div class="legend-item">
        <span class="legend-circle partially-scheduled-count"></span>
        <span class="legend-text">未排满</span>
      </div>
    </div>

    <el-calendar v-model="selectedDate" @pick="handleDatePick">
      <!-- 自定义日历单元格 -->
      <template slot="dateCell" slot-scope="{date, data}">
        <div class="calendar-day" :class="{
          'has-schedule': hasSchedule(data.day),
          'is-today': isToday(data.day),
          'is-selected': isSelectedDay(data.day),
          'past-day': isPastDay(data.day),
          'future-day': isFutureDay(data.day)
        }">
          <span class="day-number">{{ data.day.split('-').slice(2).join('') }}</span>
          <div class="schedule-count" v-if="hasSchedule(data.day)" :class="{
            'fully-scheduled-count': isFullyScheduled(data.day) && !needSubstitute(data.day),
            'partially-scheduled-count': isPartiallyScheduled(data.day) && !needSubstitute(data.day),
            'need-substitute-count': needSubstitute(data.day)
          }">
            {{ getScheduleCount(data.day) }}/{{ getShopCount(data.day) }}
          </div>
        </div>
      </template>
    </el-calendar>
  </div>
</template>

<script>
export default {
  name: 'SysShiftCalendar',
  props: {
    // 选中的日期
    value: {
      type: Date,
      required: true
    },
    // 排班数据
    scheduleData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    selectedDate: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 计算当前月份，用于监听月份变化
    currentMonth() {
      const date = this.selectedDate
      return `${date.getFullYear()}-${date.getMonth()}`
    }
  },
  watch: {
    // 监听月份变化
    currentMonth(newMonth, oldMonth) {
      if (oldMonth && newMonth !== oldMonth) {
        console.log('日历月份变化:', oldMonth, '->', newMonth)
        this.$emit('month-change', this.selectedDate)
      }
    }
  },
  methods: {
    // 格式化日期
    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 处理日期选择
    handleDatePick(date) {
      this.selectedDate = date;
      this.$emit('date-pick', date);
    },

    // 检查指定日期是否有排班
    hasSchedule(dateStr) {
      // 检查该日期是否至少有一个店铺有排班
      return this.getScheduledShopCount(dateStr) > 0;
    },

    // 获取指定日期已排班的店铺数量
    getScheduledShopCount(dateStr) {
      // 获取当前日期的排班数据
      const scheduledData = this.scheduleData.filter(item => item.date === dateStr);

      // 统计有效排班的店铺数
      return scheduledData.filter(item => item.serviceUserId).length;
    },

    // 获取指定日期的排班数量
    getScheduleCount(dateStr) {
      // 返回有效排班的店铺数量
      return this.getScheduledShopCount(dateStr);
    },

    // 获取指定日期的店铺总数
    getShopCount(dateStr) {
      // 从当天的排班数据中获取shopCount
      const dayData = this.scheduleData.find(item => item.date === dateStr);
      return dayData ? dayData.shopCount : 0;
    },

    // 检查指定日期是否排满班（所有店铺都已排班）
    isFullyScheduled(dateStr) {
      const count = this.getScheduledShopCount(dateStr);
      const totalShops = this.getShopCount(dateStr);
      return count === totalShops && count > 0;
    },

    // 检查指定日期是否部分排班（有店铺排班但未排满）
    isPartiallyScheduled(dateStr) {
      const count = this.getScheduledShopCount(dateStr);
      const totalShops = this.getShopCount(dateStr);
      return count > 0 && count < totalShops;
    },

    // 检查是否为今天
    isToday(dateStr) {
      return dateStr === this.formatDate(new Date());
    },

    // 检查是否为过去的日期
    isPastDay(dateStr) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const checkDate = new Date(dateStr);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate < today;
    },

    // 检查是否为未来的日期
    isFutureDay(dateStr) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const checkDate = new Date(dateStr);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate > today;
    },

    // 检查是否为选中的日期
    isSelectedDay(dateStr) {
      return dateStr === this.formatDate(this.selectedDate);
    },

    // 检查是否需要代班（已简化，不再需要替岗）
    needSubstitute(dateStr) {
      return false;
    }
  }
}
</script>

<style scoped>
.calendar-container {
  width: 40%;
  padding: 20px;
  border-right: 1px solid #ebeef5;
}

.calendar-legend {
  display: flex;
  justify-content: space-around;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 5px;
}

.legend-text {
  font-size: 14px;
  color: #303133;
}

.calendar-day {
  height: 40px;
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.day-number {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
}

.past-day {
  color: #909399;
  background-color: #f5f7fa;
  opacity: 0.8;
}

.future-day {
  color: #303133;
}

.calendar-day.is-today {
  border: 1px solid #409EFF;
}

.calendar-day.is-selected {
  border: 2px solid #409EFF;
}

.schedule-count {
  font-size: 12px;
  color: white;
  border-radius: 10px;
  padding: 0 6px;
  position: absolute;
  bottom: 2px;
}

.fully-scheduled-count {
  background-color: #67c23a;
  /* 绿色 */
}

.partially-scheduled-count {
  background-color: #909399;
  /* 灰色 */
}

.need-substitute-count {
  background-color: #E6A23C;
  /* 橙色 */
}
</style>
