package com.my.crossborder.controller.dto.sys_dict_category;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.my.crossborder.controller.dto.AttachmentIdListDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_数据字典-类别表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictCategoryInsertDTO extends AttachmentIdListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类别id
     */
    private String categoryId;

    /**
     * 类别名称
     */
	@NotNull(message="categoryName不能为空")
    private String categoryName;

    /**
     * 描述
     */
    private String description;

}
