package com.my.crossborder.controller.dto.sys_param;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import com.my.crossborder.controller.dto.AttachmentIdListDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysParamUpdateDTO extends AttachmentIdListDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 文件上传保存路径
     */
	@NotBlank(message="fileUploadPath不能为空")
    private String fileUploadPath;

    /**
     * 文件大小最大限制 (M)
     */
	@NotBlank(message="fileMaxSize不能为空")
    private String fileMaxSize;

    /**
     * 允许的文件后缀
     */
	@NotBlank(message="fileAllowExt不能为空")
    private String fileAllowExt;
	
    /**
     * AI模型
     */
    private String aiModel;

    /**
     * AI系统提示词
     */
	private String aiSystemPrompt;
    
    /**
     * AI用户提示词
     */
	private String aiUserPrompt;

    /**
     * AI接口秘钥
     */
	private String aiApiKey;
	
	/**
	 * erp990调试tokeon
	 */
	private String debugErp990Token;
	
	/**
	 * 台湾提取手续费比例
	 */
	private String taiwanWithdrawFeeRatio;
	
    /**
     *  订单同步天数
     */
    public String orderSyncDays;

}
