package com.my.crossborder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryDeleteDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryInsertDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryPageDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryUpdateDTO;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryDetailVO;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryPageVO;
import com.my.crossborder.mybatis.entity.SysDictCategory;

/**
 * 数据字典-类别表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface SysDictCategoryService extends IService<SysDictCategory> {

	/**
	 * 新增
	 */
	void insert(SysDictCategoryInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysDictCategoryUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysDictCategoryDetailVO detail(String id);

	/**
	 * 查询所有数据
	 */
	List<SysDictCategoryPageVO> listAll();

	/**
	 * 分页
	 */
	Page<SysDictCategoryPageVO> page(SysDictCategoryPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysDictCategoryDeleteDTO deleteDTO);	

}
