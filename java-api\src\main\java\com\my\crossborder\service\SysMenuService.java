package com.my.crossborder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_menu.SysMenuDeleteDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuInsertDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuPageDTO;
import com.my.crossborder.controller.dto.sys_menu.SysMenuUpdateDTO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuDetailVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuNavVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuPageVO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuTreeVO;
import com.my.crossborder.mybatis.entity.SysMenu;

/**
 * 系统菜单表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface SysMenuService extends IService<SysMenu> {

	/**
	 * 新增
	 */
	void insert(SysMenuInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysMenuUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysMenuDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<SysMenuPageVO> page(SysMenuPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysMenuDeleteDTO deleteDTO);	

	/**
	 * 获取菜单树结构
	 * @return 菜单树结构
	 */
	List<SysMenuTreeVO> treeNodes();

	/**
	 * 查询菜单
	 * @param menuIds
	 * @return
	 */
	List<SysMenu> listByMenuIds(List<String> menuIds);

	/**
	 * 左侧导航树
	 * @return
	 */
	List<SysMenuNavVO> tree();

	/**
	 * 工作台级子菜单id
	 * @return
	 */
	List<String> dashboardMenuIds();
}
