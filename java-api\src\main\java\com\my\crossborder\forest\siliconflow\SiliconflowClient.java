package com.my.crossborder.forest.siliconflow;

import java.math.BigDecimal;

import com.dtflys.forest.annotation.Address;
import com.dtflys.forest.annotation.BodyType;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Headers;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;
import com.dtflys.forest.callback.OnError;
import com.my.crossborder.forest.siliconflow.address.SiliconflowAddress;
import com.my.crossborder.forest.siliconflow.convert.GetBalanceConverter;
import com.my.crossborder.forest.siliconflow.dto.ChatCompletionsDTO;
import com.my.crossborder.forest.siliconflow.vo.ChatCompletionsVO;

/**
 * Siliconflow接口
 * 
 * <AUTHOR>
 */
@Address(source = SiliconflowAddress.class)
public interface SiliconflowClient {

    
    /**
     * 会话补全
     * 
     * @param apiKey DeepSeek API密钥
     * @return API响应内容
     */
    @Post(url = "/v1/chat/completions", readTimeout = 30000, connectTimeout = 30000)
    @Headers({
        "Authorization: Bearer ${apiKey}",
    	"Content-Type: application/json"
    })
    @BodyType("json")
    ChatCompletionsVO chatCompletions(@JSONBody ChatCompletionsDTO chatCompletionsDTO,
								        @Var("apiKey") String apiKey,
								        OnError onError);

    /**
     * 查询余额
     * @param apiKey DeepSeek API密钥
     * @return 余额信息
     */
    @Get(url = "/v1/user/info", decoder = GetBalanceConverter.class, readTimeout = 5000, connectTimeout = 5000)
    @Headers({
        "Authorization: Bearer ${apiKey}"
    })
    BigDecimal getBalance(@Var("apiKey") String apiKey, OnError onError);
    
} 