package com.my.crossborder.controller.dto.sys_shop;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_shop.SysShopPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_店铺管理表
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShopPageDTO 
						extends PageDTO<SysShopPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺号
     */
    private String shopNo;

    /**
     * 起店时间
     */
    private LocalDate openingDate;

    /**
     * 合作人ID（用于筛选）
     */
    private Integer partnerId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除（1-已删除，0-未删除）
     */
    private Boolean disable;

    /**
     * 店铺ID列表（用于权限过滤）
     */
    private List<Integer> shopIds;

    /**
     * 逻辑删除时间
     */
    private LocalDateTime disableTime;

}
