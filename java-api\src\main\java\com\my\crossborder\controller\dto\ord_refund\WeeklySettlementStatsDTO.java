package com.my.crossborder.controller.dto.ord_refund;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;

/**
 * 员工周结算统计查询DTO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WeeklySettlementStatsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工用户ID
     */
    @NotNull(message = "员工ID不能为空")
    private Integer userId;

    /**
     * 查询年份
     */
    @NotNull(message = "年份不能为空")
    @Min(value = 2020, message = "年份不能小于2020")
    @Max(value = 2030, message = "年份不能大于2030")
    private Integer year;

    /**
     * 查询月份
     */
    @NotNull(message = "月份不能为空")
    @Min(value = 1, message = "月份不能小于1")
    @Max(value = 12, message = "月份不能大于12")
    private Integer month;

}
