package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.sys_shift.SysShiftPageDTO;
import com.my.crossborder.controller.vo.sys_shift.SysShiftPageVO;
import com.my.crossborder.mybatis.entity.SysShift;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 排班表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface SysShiftMapper extends BaseMapper<SysShift> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysShiftPageVO> page(SysShiftPageDTO pageDTO);

	/**
	 * 根据日期查询排班数据
	 * @param shiftDay 排班日期（格式：YYYY-MM-DD）
	 * @return 排班数据列表
	 */
	List<SysShiftPageVO> listByShiftDay(@Param("shiftDay") String shiftDay);

	/**
	 * 根据日期范围查询排班数据
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 排班数据列表
	 */
	List<SysShiftPageVO> listByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

	// ========== 打卡考勤相关查询方法 ==========

	/**
	 * 根据用户和日期查询排班记录
	 * @param userId 用户ID
	 * @param date 日期
	 * @return 排班记录列表
	 */
	List<SysShift> selectByDateAndUser(@Param("userId") Integer userId, @Param("date") String date);


	/**
	 * 获取用户考勤统计
	 * @param userId 用户ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 考勤统计
	 */
	Map<String, Object> getUserAttendanceStats(@Param("userId") Integer userId, @Param("startDate") String startDate, @Param("endDate") String endDate);

	/**
	 * 更新指定日期的缺勤状态
	 * @param date 日期
	 * @return 更新记录数
	 */
	int updateAbsentStatusForDate(@Param("date") String date);
}
