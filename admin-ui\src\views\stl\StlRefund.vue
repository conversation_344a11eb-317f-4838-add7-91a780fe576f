<template>
  <div class="stl-refund-container">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">结算管理</el-breadcrumb-item>
      <el-breadcrumb-item>退款结算</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="main-content">
      <!-- 左侧员工列表 -->
      <div class="left-panel">
        <div class="employee-section">
          <div class="section-header">
            <h3>{{ canEditSettlement ? '人员列表' : '我的结算' }}</h3>
            <el-select
              v-if="canEditSettlement"
              v-model="roleFilter"
              placeholder="全部角色"
              size="small"
              clearable
              style="width: 120px;">
              <el-option label="全部角色" value=""></el-option>
              <el-option label="客服" value="客服"></el-option>
              <el-option label="客服主管" value="客服主管"></el-option>
            </el-select>
          </div>
          <!-- 添加筛选框 -->
          <div class="filter-box">
            <el-input
              v-model="employeeFilter"
              placeholder="搜索用户..."
              prefix-icon="el-icon-search"
              size="small"
              clearable
              style="margin-bottom: 15px;">
            </el-input>
          </div>
          <div class="employee-list">
            <div
              v-for="employee in filteredEmployeeList"
              :key="employee.id"
              :class="['employee-item', { active: isEmployeeSelected(employee) }]"
              @click="selectEmployee(employee)">
              <div class="employee-avatar">
                <i class="el-icon-user"></i>
              </div>
              <div class="employee-info">
                <div class="employee-name">{{ employee.name }}</div>
                <div class="employee-dept">{{ employee.roleName }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间结算记录列表 -->
      <div class="middle-panel">
        <div class="panel-content">
          <div v-if="selectedEmployee" class="settlement-records-section">
            <h3 style="text-align: center;">{{ selectedEmployee.name }} {{ getCurrentMonthName() }}退款结算</h3>

            <div class="month-stats">
                <div class="stat-card settled">
                  <div class="stat-number">{{ monthlyStats.settledCount }}</div>
                  <div class="stat-label">已结算</div>
                  <div class="stat-amount">￥{{ monthlyStats.settledAmount.toFixed(2) }}</div>
                </div>
                <div class="stat-card unsettled">
                  <div class="stat-number">{{ monthlyStats.unsettledCount }}</div>
                  <div class="stat-label">未结算</div>
                  <div class="stat-amount">￥{{ monthlyStats.unsettledAmount.toFixed(2) }}</div>
                </div>
              </div>

            <!-- 每周退款结算记录列表 -->
            <div class="weekly-records" style="margin-top: 10px;">
              <!-- <h4 style="text-align: center; margin-bottom: 15px;">{{ getCurrentMonthName() }} 结算记录</h4> -->
              <div class="weekly-records-list">
                <div
                  v-for="record in getAllWeeklyRecords()"
                  :key="record.week"
                  :class="['record-row', record.status, { 'current-week': record.isCurrentWeek }]"
                  @click="openSettlementConfirmDialog(record)">

                  <!-- 左侧：周次和状态 -->
                  <div class="record-left">
                    <div class="record-week">第{{ record.week }}周</div>
                    <div class="record-status">
                      <span v-if="record.status === 'settled'" class="status-settled">
                        <i class="el-icon-circle-check"></i>
                        <span class="status-text">已结算</span>
                      </span>
                      <span v-else-if="record.status === 'unsettled'" class="status-unsettled">
                        <i class="el-icon-warning"></i>
                        <span class="status-text">待结算</span>
                      </span>
                      <span v-else class="status-none">
                        <i class="el-icon-minus"></i>
                        <span class="status-text">无需结算</span>
                      </span>
                    </div>
                  </div>

                  <!-- 右侧：退款金额和日期信息 -->
                  <div class="record-right">
                    <div class="refund-amount">
                      <div v-if="record.amount > 0" class="amount-info">
                        <div class="amount-row">
                          <span class="amount-label">退款金额：</span>
                          <span class="amount-value">¥{{ record.amount.toFixed(2) }}</span>
                        </div>
                        <!-- 已结算项显示结算金额 -->
                        <div v-if="record.status === 'settled'" class="amount-row">
                          <span class="amount-label">结算金额：</span>
                          <span class="settled-amount-value">¥{{ record.settledAmount.toFixed(2) }}</span>
                        </div>
                        <div class="week-dates">{{ record.dateRange }}</div>
                      </div>
                      <div v-else class="no-refund">{{ record.dateRange }} - 无退款记录</div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="getAllWeeklyRecords().length === 0" class="no-records">
                <i class="el-icon-document"></i>
                <p>该月暂无结算记录</p>
              </div>
            </div>

          </div>

          <div v-else class="no-selection">
            <i class="el-icon-user"></i>
            <p>请选择员工查看退款结算记录</p>
          </div>
        </div>


      </div>

      <!-- 右侧详情区域 -->
      <div class="right-panel">
        <div v-if="selectedEmployee" class="detail-section">
        <!-- 状态说明图例 -->
        <div class="status-legend">
            <div class="legend-item">
              <span class="legend-icon status-none">
                <i class="el-icon-minus"></i>
              </span>
              <span class="legend-text">无需结算</span>
            </div>
            <div class="legend-item">
              <span class="legend-icon status-unsettled">
                <i class="el-icon-warning"></i>
              </span>
              <span class="legend-text">未结算</span>
            </div>
            <div class="legend-item">
              <span class="legend-icon status-settled">
                <i class="el-icon-circle-check"></i>
              </span>
              <span class="legend-text">已结算</span>
            </div>
          </div>

          <!-- 按自然周显示的日历 -->
          <div class="calendar-section">
            <div class="calendar-header">
              <el-button
                type="text"
                icon="el-icon-arrow-left"
                @click="goToPreviousMonth"
                :disabled="isPreviousMonthDisabled">
                上个月
              </el-button>
              <h4>{{ getCurrentMonthName() }} 退款结算日历</h4>
              <el-button
                type="text"
                icon="el-icon-arrow-right"
                @click="goToNextMonth"
                :disabled="isNextMonthDisabled">
                下个月
              </el-button>
            </div>
            <div class="week-calendar">
              <div
                v-for="week in getNaturalWeeksInMonth()"
                :key="week.weekNumber"
                :class="['week-card', getWeekSettlementStatus(week.weekNumber)]"
                @click="openRefundDetailDialog(week)">
                <div class="week-number">第{{ week.weekNumber }}周</div>
                <div class="week-dates">{{ week.dateRange }}</div>
                <div class="week-status">
                  <span v-if="getWeekSettlementStatus(week.weekNumber) === 'settled'" class="status-settled">
                    <i class="el-icon-circle-check"></i>
                  </span>
                  <span v-else-if="getWeekSettlementStatus(week.weekNumber) === 'unsettled'" class="status-unsettled">
                    <i class="el-icon-warning"></i>
                  </span>
                  <span v-else class="status-none">
                    <i class="el-icon-minus"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-selection">
          <i class="el-icon-user"></i>
          <p>请选择员工查看退款结算详情</p>
        </div>
      </div>
    </div>

    <!-- 结算确认对话框 -->
    <el-dialog
      title="退款结算确认"
      :visible.sync="settlementConfirmVisible"
      width="450px"
      :before-close="closeSettlementConfirmDialog">
      <div class="settlement-confirm-content" v-if="selectedSettlementRecord">
        <div class="confirm-item">
          <label>结算周期：</label>
          <span>第{{ selectedSettlementRecord.week }}周</span>
        </div>
        <div class="confirm-item">
          <label>结算金额：</label>
          <span class="amount-highlight">￥{{ selectedSettlementRecord.amount.toFixed(2) }}</span>
        </div>
        <div class="confirm-item">
          <label>当前状态：</label>
          <el-tag :type="selectedSettlementRecord.status === 'settled' ? 'success' : 'warning'">
            {{ selectedSettlementRecord.status === 'settled' ? '已结算' : '未结算' }}
          </el-tag>
        </div>
        <div class="confirm-item confirm-item-note" v-if="selectedSettlementRecord.status === 'unsettled'">
          <label>结算备注：<span style="color: red;">*</span></label>
          <el-input
            v-model="settlementNote"
            placeholder="请输入结算备注（必填）"
            type="textarea"
            :rows="3"
            maxlength="200"
            show-word-limit>
          </el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer" v-if="selectedSettlementRecord">
        <!-- 未结算状态显示的按钮 -->
        <template v-if="selectedSettlementRecord.status === 'unsettled'">
          <el-button @click="closeSettlementConfirmDialog">取消</el-button>
          <el-button
            type="primary"
            @click="confirmSettlement"
            :disabled="!settlementNote || settlementNote.trim() === ''">
            确认已结算
          </el-button>
        </template>
        <!-- 已结算状态不显示任何按钮，或者只显示关闭按钮 -->
        <template v-else>
          <el-button type="primary" @click="closeSettlementConfirmDialog">关闭</el-button>
        </template>
      </span>
    </el-dialog>



    <!-- 结算编辑弹窗 -->
    <StlRefundEdit
      :visible.sync="editDialogVisible"
      :refund-data="editRefundData"
      @close="closeEditDialog"
      @success="handleSettlementSuccess" />

    <!-- 结算查看弹窗 -->
    <StlRefundView
      :visible.sync="viewDialogVisible"
      :refund-data="viewRefundData"
      @close="closeViewDialog" />

  </div>
</template>

<script>
import { getAllStaffUsers, getAllSupervisorUsers } from '@/api/SysUser'
import { getWeeklySettlementStats } from '../../api/OrdRefund'
import StlRefundEdit from './StlRefundEdit.vue'
import StlRefundView from './StlRefundView.vue'

export default {
  name: 'StlRefund',
  components: {
    StlRefundEdit,
    StlRefundView
  },
  data() {
    return {
      selectedEmployee: null,
      employeeList: [],
      roleFilter: '', // 角色过滤器
      // 模拟数据保留作为备用
      mockEmployeeList: [
        {
          id: 1,
          name: '张三',
          employeeNo: 'EMP001',
          avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          department: '客服',
          settlementRate: 80,
          monthlyStats: {
            settledCount: 3,
            unsettledCount: 1,
            settledAmount: 8500.50,
            unsettledAmount: 1200.20
          }
        },
        {
          id: 2,
          name: '李四',
          employeeNo: 'EMP002',
          avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          department: '客服',
          settlementRate: 90,
          monthlyStats: {
            settledCount: 4,
            unsettledCount: 0,
            settledAmount: 11800.75,
            unsettledAmount: 0
          }
        },
        {
          id: 3,
          name: '王五',
          employeeNo: 'EMP003',
          avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          department: '客服',
          settlementRate: 70,
          monthlyStats: {
            settledCount: 2,
            unsettledCount: 2,
            settledAmount: 6800.45,
            unsettledAmount: 3250.80
          }
        },
        {
          id: 4,
          name: '赵六',
          employeeNo: 'EMP004',
          avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          department: '客服主管',
          settlementRate: 85,
          monthlyStats: {
            settledCount: 3,
            unsettledCount: 1,
            settledAmount: 13750.90,
            unsettledAmount: 2420.60
          }
        },
        {
          id: 5,
          name: '孙七',
          employeeNo: 'EMP005',
          avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          department: '客服',
          settlementRate: 75,
          monthlyStats: {
            settledCount: 2,
            unsettledCount: 2,
            settledAmount: 7200.35,
            unsettledAmount: 2850.70
          }
        },
        {
          id: 6,
          name: '钱八',
          employeeNo: 'EMP006',
          avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          department: '客服主管',
          settlementRate: 95,
          monthlyStats: {
            settledCount: 4,
            unsettledCount: 0,
            settledAmount: 16300.15,
            unsettledAmount: 0
          }
        }
      ],
      employeeWeeklyData: {},
      employeeFilter: '',
      // 当前查看的月份
      currentViewYear: new Date().getFullYear(),
      currentViewMonth: new Date().getMonth() + 1,
      // 对话框相关
      settlementConfirmVisible: false,
      selectedSettlementRecord: null,
      settlementNote: '',

      // 真实的周结算数据
      weeklyStatsData: [],
      // 结算编辑弹窗相关
      editDialogVisible: false,
      editRefundData: {
        week: 1,
        dateRange: '',
        amount: 0,
        userId: null,
        year: null,
        month: null
      },
      // 结算查看弹窗相关
      viewDialogVisible: false,
      viewRefundData: {
        week: 1,
        dateRange: '',
        amount: 0,
        userId: null,
        year: null,
        month: null
      }

    }
  },
  computed: {
    // 检查当前用户角色权限
    currentUserRole() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.roleId
    },

    // 获取当前用户ID
    currentUserId() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.userId || userEntity.id
    },

    // 检查是否可以编辑结算（roleId=11时可用）
    canEditSettlement() {
      return this.currentUserRole === 11
    },

    filteredEmployeeList() {
      let filtered = this.employeeList

      // 如果不是roleId=11的用户，只能查看自己的结算
      if (this.currentUserRole !== 11) {
        filtered = filtered.filter(employee => {
          const userId = employee.userId || employee.id
          return userId === this.currentUserId
        })
      }

      // 按角色过滤
      if (this.roleFilter) {
        filtered = filtered.filter(employee => employee.roleName === this.roleFilter)
      }

      // 按搜索关键字过滤
      if (this.employeeFilter) {
        filtered = filtered.filter(employee =>
          employee.name.toLowerCase().includes(this.employeeFilter.toLowerCase()) ||
          employee.roleName.toLowerCase().includes(this.employeeFilter.toLowerCase())
        )
      }

      return filtered
    },
    isPreviousMonthDisabled() {
      // 限制查看范围，例如不能查看一年前的数据
      const limitDate = new Date()
      limitDate.setFullYear(limitDate.getFullYear() - 1)
      const currentViewDate = new Date(this.currentViewYear, this.currentViewMonth - 1, 1)
      return currentViewDate <= limitDate
    },
    isNextMonthDisabled() {
      // 不能查看未来的月份
      const now = new Date()
      const currentViewDate = new Date(this.currentViewYear, this.currentViewMonth - 1, 1)
      const currentDate = new Date(now.getFullYear(), now.getMonth(), 1)
      return currentViewDate >= currentDate
    },


    // 月度统计数据
    monthlyStats() {
      if (!this.selectedEmployee) {
        return {
          settledCount: 0,
          unsettledCount: 0,
          settledAmount: 0,
          unsettledAmount: 0
        }
      }

      // 如果有真实的周数据，从中计算月度统计
      if (this.weeklyStatsData.length > 0) {
        let settledCount = 0
        let unsettledCount = 0
        let settledAmount = 0
        let unsettledAmount = 0

        this.weeklyStatsData.forEach(week => {
          settledCount += week.settledCount || 0
          unsettledCount += week.unsettledCount || 0
          settledAmount += week.settledAmount || 0
          unsettledAmount += week.unsettledAmount || 0
        })

        return {
          settledCount,
          unsettledCount,
          settledAmount,
          unsettledAmount
        }
      }

      // 兜底返回模拟数据或默认值
      return this.selectedEmployee.monthlyStats || {
        settledCount: 0,
        unsettledCount: 0,
        settledAmount: 0,
        unsettledAmount: 0
      }
    }
  },
  created() {
    this.loadEmployeeList()
    this.generateMonthlyWeeklyData()
  },
  watch: {
    roleFilter() {
      // 当角色过滤器变化时，检查当前选中的员工是否仍在过滤结果中
      if (this.selectedEmployee) {
        const isStillVisible = this.filteredEmployeeList.some(emp =>
          this.isEmployeeSelected(emp)
        )

        // 如果当前选中的员工不在过滤结果中，清除选中状态
        if (!isStillVisible) {
          this.selectedEmployee = null
        }
      }
    }
  },
  methods: {
    // 加载员工列表
    async loadEmployeeList() {
      try {
        // 如果不是roleId=11的用户，只加载当前用户信息
        if (!this.canEditSettlement) {
          const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
          const currentUser = {
            id: userEntity.userId || userEntity.id,
            userId: userEntity.userId || userEntity.id,
            name: userEntity.realName || userEntity.name || userEntity.username || '当前用户',
            roleName: userEntity.roleId === 21 ? '客服' : userEntity.roleId === 22 ? '客服主管' : '其他'
          }
          this.employeeList = [currentUser]
          console.log('当前用户信息:', currentUser)

          // 自动选择当前用户
          if (this.employeeList.length > 0) {
            this.selectEmployee(this.employeeList[0])
          }
          return
        }

        // roleId=11的用户可以查看所有员工
        const allEmployees = []

        // 获取客服用户 - 分页结果在data.records中
        const staffRes = await getAllStaffUsers({ pageNum: 1, pageSize: 1000 })
        if (staffRes.success && staffRes.data && staffRes.data.records) {
          const staffUsers = staffRes.data.records.map(user => ({
            ...user,
            name: user.name || user.userName || user.realName || user.nickName || user.username || `用户${user.id || user.userId}`,
            roleName: '客服'
          }))
          allEmployees.push(...staffUsers)
          console.log('客服用户数据结构:', staffUsers.length > 0 ? staffUsers[0] : 'empty')
        }

        // 获取客服主管用户
        const supervisorRes = await getAllSupervisorUsers({ pageNum: 1, pageSize: 1000 })
        if (supervisorRes.success && supervisorRes.data && supervisorRes.data.records) {
          const supervisorUsers = supervisorRes.data.records.map(user => ({
            ...user,
            name: user.name || user.userName || user.realName || user.nickName || user.username || `用户${user.id || user.userId}`,
            roleName: '客服主管'
          }))
          allEmployees.push(...supervisorUsers)
          console.log('客服主管用户数据结构:', supervisorUsers.length > 0 ? supervisorUsers[0] : 'empty')
        }

        this.employeeList = allEmployees
        console.log('所有员工列表:', allEmployees)

        // 不自动选择第一个员工，让用户手动选择
      } catch (error) {
        console.error('加载员工列表失败:', error)
        this.$message.error('加载员工列表失败')
      }
    },

    // 检查员工是否被选中
    isEmployeeSelected(employee) {
      if (!this.selectedEmployee) return false
      const selectedId = this.selectedEmployee.id || this.selectedEmployee.userId
      const employeeId = employee.id || employee.userId
      return selectedId === employeeId
    },

    generateMonthlyWeeklyData() {
      this.employeeList.forEach(employee => {
        this.employeeWeeklyData[employee.id] = {}
      })
      // 为当前月份生成初始数据
      this.generateMonthlyWeeklyDataForCurrentView()
    },

    getWeeksInMonth() {
      return [
        { weekNumber: 1, dateRange: '6月1日-6月7日', isCurrentWeek: false },
        { weekNumber: 2, dateRange: '6月8日-6月14日', isCurrentWeek: false },
        { weekNumber: 3, dateRange: '6月15日-6月21日', isCurrentWeek: true },
        { weekNumber: 4, dateRange: '6月22日-6月30日', isCurrentWeek: false }
      ]
    },

        // 按自然周计算，允许跨月
    getNaturalWeeksInMonth() {
      // 如果有真实的周数据，直接使用
      if (this.weeklyStatsData.length > 0) {
        return this.weeklyStatsData.map(week => ({
          weekNumber: week.weekNumber,
          dateRange: week.dateRange,
          isCurrentWeek: week.isCurrentWeek,
          startDate: new Date(week.startDate),
          endDate: new Date(week.endDate)
        }))
      }

      // 否则使用计算逻辑（兜底方案）
      const now = new Date()
      const viewYear = this.currentViewYear
      const viewMonth = this.currentViewMonth
      const weeks = []

      // 获取查看月份第一天是星期几
      const firstDay = new Date(viewYear, viewMonth - 1, 1)
      const firstDayOfWeek = firstDay.getDay() // 0是周日，1是周一

      // 计算第一周的开始日期（从周一开始）
      const firstMondayDate = new Date(firstDay)
      if (firstDayOfWeek === 0) {
        // 如果第一天是周日，则往前推6天到周一
        firstMondayDate.setDate(firstDay.getDate() - 6)
      } else if (firstDayOfWeek !== 1) {
        // 如果第一天不是周一，则往前推到周一
        firstMondayDate.setDate(firstDay.getDate() - (firstDayOfWeek - 1))
      }

      // 获取查看月份最后一天
      const lastDay = new Date(viewYear, viewMonth, 0)

      let weekNumber = 1
      let currentWeekStart = new Date(firstMondayDate)

      while (currentWeekStart <= lastDay) {
        const currentWeekEnd = new Date(currentWeekStart)
        currentWeekEnd.setDate(currentWeekStart.getDate() + 6)

        // 判断是否为当前周（只有在查看当前月份时才判断）
        const isCurrentWeek = (viewYear === now.getFullYear() && viewMonth === now.getMonth() + 1)
          && (now >= currentWeekStart && now <= currentWeekEnd)

        weeks.push({
          weekNumber: weekNumber,
          dateRange: `${this.formatDateShort(currentWeekStart)} - ${this.formatDateShort(currentWeekEnd)}`,
          isCurrentWeek: isCurrentWeek,
          startDate: new Date(currentWeekStart),
          endDate: new Date(currentWeekEnd)
        })

        weekNumber++
        currentWeekStart.setDate(currentWeekStart.getDate() + 7)
      }

      return weeks
    },

    formatDateShort(date) {
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    },

    async selectEmployee(employee) {
      console.log('选中的员工对象:', employee)

      // 尝试不同的ID字段，并在employee对象上标准化为realUserId
      const userId = employee.id || employee.userId || employee.sysUserId || employee.employeeId
      console.log('实际使用的userId:', userId)

      if (userId) {
        // 设置选中员工
        this.selectedEmployee = { ...employee, realUserId: userId }

        // 加载该员工的真实结算数据
        await this.loadEmployeeWeeklyData()
      } else {
        console.error('无法找到用户ID字段')
        this.$message.error('无法获取用户ID')
      }
    },

    // 加载员工周结算数据
    async loadEmployeeWeeklyData() {
      if (!this.selectedEmployee || !this.selectedEmployee.realUserId) {
        return
      }

      try {
        const params = {
          userId: this.selectedEmployee.realUserId,
          year: this.currentViewYear,
          month: this.currentViewMonth
        }

        console.log('请求周结算数据参数:', params)
        const response = await getWeeklySettlementStats(params)

        if (response.success && response.data) {
          // 将API返回的数据转换为前端需要的格式
          this.weeklyStatsData = response.data
          console.log('获取到的周结算数据:', this.weeklyStatsData)
        } else {
          console.error('获取周结算数据失败:', response.message)
          // 如果API失败，使用模拟数据进行测试
          this.generateMockWeeklyData()
        }
      } catch (error) {
        console.error('加载员工周结算数据失败:', error)
        // 如果API失败，使用模拟数据进行测试
        this.generateMockWeeklyData()
      }
    },

    // 生成模拟周数据（用于测试）
    generateMockWeeklyData() {
      const mockData = [
        {
          weekNumber: 1,
          startDate: '2025-06-30',
          endDate: '2025-07-06',
          dateRange: '6月30日 - 7月6日',
          settlementStatus: '1', // 已结算
          isCurrentWeek: false,
          totalAmount: 1500.00,
          settledAmount: 1500.00,
          unsettledAmount: 0.00,
          totalCount: 3,
          settledCount: 3,
          unsettledCount: 0
        },
        {
          weekNumber: 2,
          startDate: '2025-07-07',
          endDate: '2025-07-13',
          dateRange: '7月7日 - 7月13日',
          settlementStatus: '1', // 已结算
          isCurrentWeek: false,
          totalAmount: 2200.00,
          settledAmount: 2200.00,
          unsettledAmount: 0.00,
          totalCount: 4,
          settledCount: 4,
          unsettledCount: 0
        },
        {
          weekNumber: 3,
          startDate: '2025-07-14',
          endDate: '2025-07-20',
          dateRange: '7月14日 - 7月20日',
          settlementStatus: '-1', // 无需结算
          isCurrentWeek: false,
          totalAmount: 0.00,
          settledAmount: 0.00,
          unsettledAmount: 0.00,
          totalCount: 0,
          settledCount: 0,
          unsettledCount: 0
        },
        {
          weekNumber: 4,
          startDate: '2025-07-21',
          endDate: '2025-07-27',
          dateRange: '7月21日 - 7月27日',
          settlementStatus: '1', // 已结算
          isCurrentWeek: false,
          totalAmount: 3200.00,
          settledAmount: 3200.00,
          unsettledAmount: 0.00,
          totalCount: 5,
          settledCount: 5,
          unsettledCount: 0
        },
        {
          weekNumber: 5,
          startDate: '2025-07-28',
          endDate: '2025-08-03',
          dateRange: '7月28日 - 8月3日',
          settlementStatus: '0', // 待结算
          isCurrentWeek: true,
          totalAmount: 1200.00,
          settledAmount: 0.00,
          unsettledAmount: 1200.00,
          totalCount: 2,
          settledCount: 0,
          unsettledCount: 2
        }
      ]

      this.weeklyStatsData = mockData
      console.log('使用模拟周结算数据:', this.weeklyStatsData)
    },

    getWeekSettlementStatus(weekNumber) {
      if (!this.selectedEmployee || !this.weeklyStatsData.length) return 'none'

      const weekData = this.weeklyStatsData.find(week => week.weekNumber === weekNumber)
      if (!weekData) return 'none'

      // 将后端的数字状态转换为前端需要的字符串状态
      switch (weekData.settlementStatus) {
        case '-1': return 'none'      // 无需结算
        case '0': return 'unsettled'  // 待结算
        case '1': return 'settled'    // 已结算
        default: return 'none'
      }
    },

    getWeeklySettlementRecords() {
      if (!this.selectedEmployee) return []
      const weeks = this.getNaturalWeeksInMonth()
      const records = []

      for (let i = weeks.length - 1; i >= 0; i--) {
        const week = weeks[i]
        const settlementData = this.employeeWeeklyData[this.selectedEmployee.id]
        const status = settlementData ? settlementData[week.weekNumber] || 'none' : 'none'
        const amount = Math.floor(Math.random() * 4000) + 1000

        records.push({
          week: week.weekNumber,
          dateRange: week.dateRange,
          status: status,
          isCurrentWeek: week.isCurrentWeek,
          amount: amount
        })
      }

      return records
    },

        // 获取所有周记录（已结算和未结算）
    getAllWeeklyRecords() {
      if (!this.selectedEmployee) return []

      // 如果有真实数据，使用真实数据
      if (this.weeklyStatsData.length > 0) {
        return this.weeklyStatsData.map(week => {
          // 将后端的数字状态转换为前端需要的字符串状态
          let status = 'none'
          switch (week.settlementStatus) {
            case '-1': status = 'none'; break      // 无需结算
            case '0': status = 'unsettled'; break  // 待结算
            case '1': status = 'settled'; break    // 已结算
          }

          return {
            week: week.weekNumber,
            dateRange: week.dateRange,
            status: status,
            isCurrentWeek: week.isCurrentWeek,
            amount: week.totalAmount || 0,
            settledAmount: week.settledAmount || 0
          }
        }).sort((a, b) => a.week - b.week)
      }

      // 否则使用模拟数据（兼容性保留）
      const weeks = this.getNaturalWeeksInMonth()
      const records = []

      for (let i = 0; i < weeks.length; i++) {
        const week = weeks[i]
        const monthKey = `${this.currentViewYear}-${this.currentViewMonth}`
        const settlementData = this.employeeWeeklyData[this.selectedEmployee.id]

        // 生成基于月份和周的唯一key来获取状态
        const weekKey = `${monthKey}-W${week.weekNumber}`
        const status = settlementData ? settlementData[weekKey] || 'unsettled' : 'unsettled'

        const amount = Math.floor(Math.random() * 4000) + 1000

        records.push({
          week: week.weekNumber,
          dateRange: week.dateRange,
          status: status,
          isCurrentWeek: week.isCurrentWeek,
          amount: amount,
          settledAmount: status === 'settled' ? amount : 0
        })
      }

      return records.sort((a, b) => a.week - b.week)
    },



    // 获取当前查看月份的名称
    getCurrentMonthName() {
      // return `${this.currentViewYear}年${this.currentViewMonth}月`
      return `${this.currentViewMonth}月`
    },

    // 切换到上个月
    async goToPreviousMonth() {
      if (this.currentViewMonth === 1) {
        this.currentViewYear--
        this.currentViewMonth = 12
      } else {
        this.currentViewMonth--
      }
      // 重新加载该月份的数据
      if (this.selectedEmployee) {
        await this.loadEmployeeWeeklyData()
      }
      this.generateMonthlyWeeklyDataForCurrentView()
    },

    // 切换到下个月
    async goToNextMonth() {
      if (this.currentViewMonth === 12) {
        this.currentViewYear++
        this.currentViewMonth = 1
      } else {
        this.currentViewMonth++
      }
      // 重新加载该月份的数据
      if (this.selectedEmployee) {
        await this.loadEmployeeWeeklyData()
      }
      this.generateMonthlyWeeklyDataForCurrentView()
    },

    // 为当前查看的月份生成数据
    generateMonthlyWeeklyDataForCurrentView() {
      this.employeeList.forEach(employee => {
        if (!this.employeeWeeklyData[employee.id]) {
          this.employeeWeeklyData[employee.id] = {}
        }

        const weeks = this.getNaturalWeeksInMonth()
        const monthKey = `${this.currentViewYear}-${this.currentViewMonth}`

        weeks.forEach(week => {
          const weekKey = `${monthKey}-W${week.weekNumber}`

          // 如果该周的数据还不存在，则生成
          if (!this.employeeWeeklyData[employee.id][weekKey]) {
            const baseRate = employee.settlementRate / 100
            const random = Math.random()
            const status = random < baseRate ? 'settled' : 'unsettled'
            this.employeeWeeklyData[employee.id][weekKey] = status
          }
        })
      })
    },

    openSettlementConfirmDialog(record) {
      if (record.status === 'unsettled') {
        // 检查是否为本周，本周不允许结算
        if (record.isCurrentWeek) {
          this.$message.warning('本周数据不允许结算，请等待本周结束后再进行结算操作')
          return
        }

        // 待结算：打开结算编辑弹窗
        this.editRefundData = {
          week: record.week,
          dateRange: record.dateRange,
          amount: record.amount,
          userId: this.selectedEmployee.realUserId,
          year: this.currentViewYear,
          month: this.currentViewMonth
        }
        this.editDialogVisible = true
      } else if (record.status === 'settled') {
        // 已结算：打开结算查看弹窗
        this.viewRefundData = {
          week: record.week,
          dateRange: record.dateRange,
          amount: record.amount,
          userId: this.selectedEmployee.realUserId,
          year: this.currentViewYear,
          month: this.currentViewMonth
        }
        this.viewDialogVisible = true
      } else {
        // 无需结算或其他状态：显示提示
        this.$message.info('该周无需结算')
      }
    },

    closeSettlementConfirmDialog() {
      this.settlementConfirmVisible = false
      this.selectedSettlementRecord = null
      this.settlementNote = ''
    },

    // 关闭结算编辑弹窗
    closeEditDialog() {
      this.editDialogVisible = false
    },

    // 关闭结算查看弹窗
    closeViewDialog() {
      this.viewDialogVisible = false
    },

    // 结算成功处理
    async handleSettlementSuccess() {
      this.$message.success('结算成功！')
      this.editDialogVisible = false
      // 重新加载数据
      if (this.selectedEmployee) {
        await this.loadEmployeeWeeklyData()
      }
    },

    confirmSettlement() {
      // 处理结算确认逻辑
      if (this.selectedSettlementRecord) {
        this.selectedSettlementRecord.status = 'settled'

        // 更新员工周数据
        const monthKey = `${this.currentViewYear}-${this.currentViewMonth}`
        const weekKey = `${monthKey}-W${this.selectedSettlementRecord.week}`
        this.employeeWeeklyData[this.selectedEmployee.id][weekKey] = 'settled'


        this.$message.success('退款结算确认成功！')
      }
      this.closeSettlementConfirmDialog()
    },

    openRefundDetailDialog(week) {
      // 检查周的结算状态
      const status = this.getWeekSettlementStatus(week.weekNumber)

      if (status === 'none') {
        this.$message.info('该周无需结算')
        return
      }

      // 检查是否选择了员工
      if (!this.selectedEmployee) {
        this.$message.warning('请先选择员工')
        return
      }

      // 获取该周的退款金额
      const weekData = this.weeklyStatsData.find(item => item.week === week.weekNumber)
      const amount = weekData ? weekData.amount : 0

      // 使用和中间面板相同的方式打开StlRefundView
      this.viewRefundData = {
        week: week.weekNumber,
        dateRange: week.dateRange,
        amount: amount,
        userId: this.selectedEmployee.realUserId,
        year: this.currentViewYear,
        month: this.currentViewMonth
      }
      this.viewDialogVisible = true
    },









  }
}
</script>

<style scoped>
.stl-refund-container {
  background: #f5f5f5;
  min-height: 100vh;
}



.main-content {
  margin-top: 20px;
  padding-top: 20px;
  display: flex;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
  height: calc(100vh - 160px);
}

.left-panel {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.middle-panel {
  flex: 0 0 400px;
  background: white;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
  display: flex;
  flex-direction: column;
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  transition: margin-bottom 0.3s ease;
}



.right-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.employee-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  height: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.employee-section h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.employee-list {
  max-height: calc(100% - 50px);
  overflow-y: auto;
}

.employee-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.employee-item:hover {
  background: #f8f9fa;
  border-color: #409EFF;
}

.employee-item.active {
  background: #e8f4ff;
  border-color: #409EFF;
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.employee-dept {
  color: #666;
  font-size: 12px;
}

.month-stats {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.stat-card {
  text-align: center;
  padding: 15px 20px;
  border-radius: 8px;
  min-width: 80px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-card.settled {
  background: #f0f9ff;
  border: 2px solid #67C23A;
}

.stat-card.unsettled {
  background: #fef3e2;
  border: 2px solid #E6A23C;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-card.settled .stat-number {
  color: #67C23A;
}

.stat-card.unsettled .stat-number {
  color: #E6A23C;
}

.stat-label {
  color: #666;
  font-size: 12px;
}

.stat-amount {
  font-size: 14px;
  font-weight: bold;
  margin-top: 4px;
  color: #333;
}

.status-legend {
  display: flex;
  justify-content: space-around;
  margin: 15px 0 20px 0;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.legend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
}

.legend-icon {
  margin-bottom: 4px;
  font-size: 16px;
}

.legend-text {
  color: #666;
  white-space: nowrap;
}

.calendar-section h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.week-calendar {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.week-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  border: 1px solid #eee;
  border-radius: 6px;
  transition: all 0.3s;
  background: #fff;
  text-align: center;
  cursor: pointer;
}

.week-card:hover {
  background: #f8f9fa;
  border-color: #409EFF;
}

.week-card.settled {
  border-left: 4px solid #67C23A;
}

.week-card.unsettled {
  border-left: 4px solid #E6A23C;
}

.week-card.none {
  border-left: 4px solid #C0C4CC;
  opacity: 0.7;
}

.week-number {
  font-weight: bold;
  color: #333;
  font-size: 14px;
  margin-bottom: 4px;
}

.week-dates {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
}

.week-status i {
  font-size: 20px;
}

.no-selection {
  text-align: center;
  color: #999;
  padding: 80px 0;
}

.no-selection i {
  font-size: 48px;
  margin-bottom: 20px;
}

.settlement-records-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
}

.weekly-records {
  max-height: calc(100% - 120px);
  overflow-y: auto;
}

.weekly-records-list {
  max-height: calc(100% - 120px);
  overflow-y: auto;
}

.record-row {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 8px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #eee;
  border-left: 4px solid #ddd;
  transition: all 0.3s;
  cursor: pointer;
}

.record-row:hover {
  background: #f8f9fa;
  border-color: #c6e2ff;
}

.record-row.settled {
  border-color: #67C23A;
  border-left-color: #67C23A;
}

.record-row.unsettled {
  border-left-color: #E6A23C;
}

.record-row.none {
  border-left-color: #C0C4CC;
}



.record-left {
  flex: 0 0 110px;
  display: flex;
  flex-direction: column;
}

.record-week {
  font-weight: bold;
  color: #333;
  font-size: 14px;
  margin-bottom: 4px;
}

.record-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.record-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.refund-amount {
  text-align: right;
}

.amount-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-label {
  color: #666;
  font-size: 13px;
}

.amount-value {
  color: #409EFF;
  font-weight: bold;
  font-size: 14px;
}

.settled-amount-value {
  color: #67C23A;
  font-weight: bold;
  font-size: 14px;
}

.week-dates {
  color: #999;
  font-size: 12px;
}

.no-refund {
  color: #999;
  font-size: 13px;
}

.record-status i {
  font-size: 16px;
}

.status-text {
  font-size: 13px;
  font-weight: 500;
}

.status-settled {
  color: #67C23A;
}

.status-unsettled {
  color: #E6A23C;
}

.status-none {
  color: #C0C4CC;
}





.week-detail-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.week-detail-layer > div {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  max-height: 500px;
  width: 90%;
  overflow: hidden;
}

.week-detail-header {
  background: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.week-detail-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.week-detail-content {
  padding: 30px 20px;
  overflow: auto;
  max-height: 400px;
}

.week-detail-placeholder {
  text-align: center;
  padding: 40px 20px;
}

.week-detail-placeholder i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #ccc;
}

.week-detail-placeholder p {
  color: #666;
  margin-bottom: 25px;
  font-size: 14px;
  line-height: 1.5;
}

.week-detail-placeholder .el-button {
  margin-top: 10px;
}

.no-unsettled, .no-records {
  text-align: center;
  padding: 40px 20px;
  color: #67C23A;
}

.no-unsettled i, .no-records i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.no-unsettled p, .no-records p {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.no-records {
  color: #999;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 10px;
}

.calendar-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.calendar-header .el-button {
  padding: 8px 12px;
  font-size: 12px;
  color: #409EFF;
}

.calendar-header .el-button:hover:not(.is-disabled) {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
}

.calendar-header .el-button.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}



/* 对话框样式 */
.settlement-confirm-content {
  padding: 10px 0;
}

.confirm-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  font-size: 14px;
}

.confirm-item label {
  width: 80px;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
}

.confirm-item span {
  flex: 1;
  color: #333;
}

.amount-highlight {
  color: #F56C6C !important;
  font-weight: bold !important;
  font-size: 16px !important;
}

.confirm-item-note {
  align-items: flex-start;
}

.confirm-item-note label {
  margin-top: 8px;
}

/* 退款详情弹框样式 */
.refund-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.refund-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.total-amount-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.total-label {
  color: #666;
  font-size: 14px;
}

.total-amount {
  color: #F56C6C;
  font-weight: bold;
  font-size: 18px;
}
</style>
