package com.my.crossborder.aoplog;

import java.time.LocalDateTime;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import com.alibaba.fastjson.JSON;
import com.my.crossborder.controller.dto.sys_log.SysLogInsertDTO;
import com.my.crossborder.mybatis.entity.SysDictItem;
import com.my.crossborder.service.SysDictItemService;
import com.my.crossborder.service.SysLogService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 接口日志切面 拦截所有controller的insert和update方法，记录操作日志
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class InterfaceLogAspect {

	private static final String CONTROLLER_MODULE = "CONTROLLER_MODULE";
	private final SysLogService sysLogService;
	private final SysDictItemService sysDictItemService;

	/**
	 * 拦截所有controller的insert和update方法
	 */
	@Around("execution(* com.my.crossborder.controller.*.insert(..)) "
			+ "|| execution(* com.my.crossborder.controller.*.update(..))"
			+ "|| execution(* com.my.crossborder.controller.*.delete(..))"
	)
	public Object interceptControllerMethods(ProceedingJoinPoint joinPoint) throws Throwable {
		String controllerName = joinPoint.getTarget().getClass().getSimpleName();
		String methodName = joinPoint.getSignature().getName();

		// 执行原方法
		try {
			Object result = joinPoint.proceed();
			this.saveLog(joinPoint, controllerName, methodName, Boolean.TRUE, null);
			return result;
		} catch(Exception e) {
			this.saveLog(joinPoint, controllerName, methodName, Boolean.FALSE, e.getMessage());
			throw e;
		}
	}

	/**
	 * 记录日志
	 * @param joinPoint
	 * @param controllerName
	 * @param methodName
	 * @param success
	 * @param errMsg
	 */
	private void saveLog(ProceedingJoinPoint joinPoint, String controllerName, String methodName, Boolean success, String errMsg) {
		try {
			this.recordLog(joinPoint, controllerName, methodName, success, errMsg);
		} catch (Exception e) {
			log.error("接口日志切面执行时发生错误, 控制器: {}, 方法: {}", controllerName, methodName, e);
		}
	}
	

	/**
	 * 记录操作日志
	 */
	private void recordLog(ProceedingJoinPoint joinPoint, String controllerName, String methodName, Boolean success, String errMsg) {
		// 获取RequestMapping注解信息
		RequestMapping requestMapping = joinPoint.getTarget().getClass().getAnnotation(RequestMapping.class);
		if (requestMapping == null || requestMapping.value().length == 0) {
			log.warn("控制器 {} 没有 @RequestMapping 注解，跳过日志记录", controllerName);
			return;
		}

		String mappingPath = requestMapping.value()[0];
		if (StrUtil.isBlank(mappingPath)) {
			log.warn("controller {} 的 @RequestMapping 值为空，跳过SysLog记录", controllerName);
			return;
		}

		// 提取路径中的关键部分，例如从 "/api/rsk-check-daily" 提取 "rsk-check-daily"
		String pathKey = extractPathKey(mappingPath);
		if (StrUtil.isBlank(pathKey)) {
			log.warn("无法从controller {} 提取模块名，跳过SysLog记录", mappingPath);
			return;
		}

		// 在sys_dict_item表中查找对应的记录
		SysDictItem dictItem = findDictItemByValue(pathKey);
		if (dictItem == null) {
			log.warn("在字典表中未找到controller关键字 {} 对应的记录，跳过SysLog记录", pathKey);
			return;
		}

		// 构建日志记录
		SysLogInsertDTO logDTO = SysLogInsertDTO.builder().moduleName(dictItem.getColor()) // 使用color字段作为module_name
				.menuName(dictItem.getItemName()) // 使用item_name作为menu_name
				.operationName(this.getOperationName(methodName)) // 根据方法名判断操作类型
				.operationDetail(this.getOperationDetail(joinPoint)) // 请求参数转JSON
				.createUserId(StpUtil.getLoginIdAsInt()) // 当前登录用户ID
				.createTime(LocalDateTime.now()) // 当前时间
				.success(success)
				.errMsg(errMsg)
				.build();

		// 保存日志
		this.sysLogService.insert(logDTO);
		log.debug("成功记录操作日志: 控制器={}, 方法={}, 模块={}, 菜单={}, 操作={}", controllerName, methodName, dictItem.getColor(),
					dictItem.getItemName(), getOperationName(methodName));
	}

	/**
	 * 从RequestMapping路径中提取关键字 例如："/api/rsk-check-daily" -> "rsk-check-daily"
	 */
	private String extractPathKey(String mappingPath) {
		if (StrUtil.isBlank(mappingPath)) {
			return null;
		}

		// 去掉前缀 "/api/"
		if (mappingPath.startsWith("/api/")) {
			return mappingPath.substring(5);
		}

		// 去掉开头的 "/"
		if (mappingPath.startsWith("/")) {
			return mappingPath.substring(1);
		}

		return mappingPath;
	}

	/**
	 * 在字典表中查找对应的项目
	 */
	private SysDictItem findDictItemByValue(String itemValue) {
		try {
			return this.sysDictItemService.get(CONTROLLER_MODULE, itemValue);
		} catch (Exception e) {
			log.error("查询字典项失败: itemValue={}", itemValue, e);
			return null;
		}
	}

	/**
	 * 根据方法名获取操作名称
	 */
	private String getOperationName(String methodName) {
		if ("insert".equals(methodName)) {
			return "新增";
		} else if ("update".equals(methodName)) {
			return "修改";
		} else if ("delete".equals(methodName)) {
			return "删除";
		} else {
			return methodName;
		}
	}

	/**
	 * 获取操作详情（请求参数转JSON）
	 */
	private String getOperationDetail(ProceedingJoinPoint joinPoint) {
		try {
			Object[] args = joinPoint.getArgs();
			if (args == null || args.length == 0) {
				return "{}";
			}

			// 取第一个参数作为操作详情
			Object firstArg = args[0];
			if (firstArg == null) {
				return "{}";
			}

			return JSON.toJSONString(firstArg);
		} catch (Exception e) {
			log.warn("获取操作详情失败", e);
			return "{\"error\":\"参数序列化失败\"}";
		}
	}
}