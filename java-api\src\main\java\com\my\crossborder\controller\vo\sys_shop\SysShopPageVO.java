package com.my.crossborder.controller.vo.sys_shop;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_店铺管理表
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShopPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺号
     */
    private String shopNo;

    /**
     * 起店时间
     */
    private LocalDate openingDate;

    /**
     * 合作人姓名列表，逗号分隔
     */
    private String partnerNames;

    /**
     * 合作人ID数组
     */
    private List<Integer> partnerIds;

    /**
     * 合作人ID字符串，逗号分隔（用于SQL查询结果映射）
     */
    private String partnerIdsStr;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 逻辑删除（1-已删除，0-未删除）
     */
    private Boolean disable;

    /**
     * 逻辑删除时间
     */
    private LocalDateTime disableTime;

}
