<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysErpClientLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysErpClientLog">
        <id column="log_id" property="logId" />
        <result column="username" property="username" />
        <result column="operation_name" property="operationName" />
        <result column="operation_content" property="operationContent" />
        <result column="success" property="success" />
        <result column="err_msg" property="errMsg" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        log_id, username, operation_name, operation_content, success, err_msg, create_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_erp_client_log.SysErpClientLogPageVO">
		SELECT
			log_id, username, operation_name, operation_content, success, err_msg, create_time
		FROM
			sys_erp_client_log AS t1
		<where>
        	1=1
	        <if test="logId != null and logId != ''">
	           	AND t1.log_id = #{logId}
            </if>
	        <if test="username != null and username != ''">
	           	AND t1.username = #{username}
            </if>
	        <if test="operationName != null and operationName != ''">
	           	AND t1.operation_name = #{operationName}
            </if>
	        <if test="operationContent != null and operationContent != ''">
	           	AND t1.operation_content = #{operationContent}
            </if>
	        <if test="success != null and success != ''">
	           	AND t1.success = #{success}
            </if>
	        <if test="errMsg != null and errMsg != ''">
	           	AND t1.err_msg = #{errMsg}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
        </where>
    </select>

</mapper>
