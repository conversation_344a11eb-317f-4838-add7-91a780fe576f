package com.my.crossborder.controller.dto.stl_refund;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 修改_结算_退款结算表
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class StlRefundUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
	@NotNull(message="id不能为空")
    private Integer id;

    /**
     * 退款员工ID
     */
	@NotNull(message="refundUserId不能为空")
    private Integer refundUserId;

    /**
     * 退款日期_开始
     */
	@NotNull(message="refundDateStart不能为空")
    private LocalDate refundDateStart;

    /**
     * 退款日期_结束
     */
	@NotNull(message="refundDateEnd不能为空")
    private LocalDate refundDateEnd;

    /**
     * 结算金额
     */
	@NotNull(message="settlementAmount不能为空")
    private BigDecimal settlementAmount;

    /**
     * 结算日期
     */
	@NotNull(message="settlementDate不能为空")
    private LocalDateTime settlementDate;

    /**
     * 备注( 使用常见标签快速填写）
     */
	@NotNull(message="remark不能为空")
    private String remark;

    /**
     * 结算操作员用户id
     */
	@NotNull(message="createUserId不能为空")
    private Integer createUserId;

    /**
     * 创建时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

}
