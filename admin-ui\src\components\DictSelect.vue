<template>
  <el-select
    :value="value"
    @input="handleInput"
    :placeholder="placeholder"
    :clearable="clearable"
    :disabled="disabled"
    :size="size"
    :style="selectStyle">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value">
    </el-option>
  </el-select>
</template>

<script>
import { dictCategoryItems } from '../api/SysDictItem'

export default {
  name: 'DictSelect',
  props: {
    // 字典分类ID
    categoryId: {
      type: String,
      required: true
    },
    // 当前值
    value: {
      type: [String, Number],
      default: ''
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 组件大小
    size: {
      type: String,
      default: ''
    },
    // 自定义样式
    selectStyle: {
      type: [String, Object],
      default: () => ({})
    }
  },
  data() {
    return {
      options: [],
      loading: false
    }
  },
  watch: {
    categoryId: {
      handler(newVal) {
        if (newVal) {
          this.loadOptions()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 处理选择值变化
    handleInput(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    },

    // 加载字典选项
    loadOptions() {
      if (!this.categoryId) {
        this.options = []
        return
      }

      this.loading = true
      dictCategoryItems({ categoryId: this.categoryId })
        .then(res => {
          this.loading = false
          // 根据接口返回格式调整数据结构
          if (res.data && Array.isArray(res.data)) {
            this.options = res.data.map(item => ({
              label: item.label || item.itemLabel || item.name,
              value: item.value || item.itemValue || item.code,
              ...item
            }))
          } else {
            this.options = []
          }
        })
        .catch(err => {
          this.loading = false
          console.error('加载字典数据失败:', err)
          this.$message.error('加载字典数据失败')
          this.options = []
        })
    }
  }
}
</script>
