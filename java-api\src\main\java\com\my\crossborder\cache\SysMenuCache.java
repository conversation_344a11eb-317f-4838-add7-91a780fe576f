package com.my.crossborder.cache;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.my.crossborder.mybatis.entity.SysMenu;
import com.my.crossborder.mybatis.mapper.SysMenuMapper;

import lombok.RequiredArgsConstructor;

/**
 * 菜单缓存
 * <AUTHOR>
 * @date 2023年4月22日
 */
@Component
@RequiredArgsConstructor
public class SysMenuCache {
	
	private final SysMenuMapper sysMenuMapper;

	private List<SysMenu> entityList;
	
	
	/**
	 * 初始缓存
	 */
	@PostConstruct
	public void reloadCache() {
		Wrapper<SysMenu> wrapper = Wrappers.lambdaQuery();
		this.entityList = this.sysMenuMapper.selectList(wrapper);
	}
	
    /**
     * 按类别查询
     * @param categoryId 
     * @return
     */
	public List<SysMenu> listByIds(List<String> menuIdList) {
		return this.entityList.stream()
				.filter(t -> menuIdList.contains(t.getId()))
				.collect(Collectors.toList());
	}
	
	/**
	 * 按主键查询
	 * @param menuId
	 * @return
	 */
	public SysMenu get(String menuId) {
		return this.entityList.stream()
				.filter(t -> t.getId().equals(menuId))
				.findFirst()
				.orElse(null);
	}
	
}
