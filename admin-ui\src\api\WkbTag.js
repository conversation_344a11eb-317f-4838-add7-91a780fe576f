import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 工作台_订单标签 API
 */

/**
 * 新增标签
 * @param {Object} data 标签数据
 */
export function insertWkbTag(data) {
  return reqPost('/wkb-tag', data)
}

/**
 * 修改标签
 * @param {Object} data 标签数据
 */
export function updateWkbTag(data) {
  return reqPut('/wkb-tag', data)
}

/**
 * 根据主键查询标签详情
 * @param {Number} id 主键
 */
export function getWkbTagDetail(id) {
  return reqGet(`/wkb-tag/${id}`)
}

/**
 * 分页查询标签
 * @param {Object} params 查询参数
 */
export function getWkbTagPage(params) {
  return reqGet('/wkb-tag/page', params)
}

/**
 * 批量删除标签
 * @param {Object} data 删除数据
 */
export function deleteWkbTag(data) {
  return reqDelete('/wkb-tag', data)
}

/**
 * 为订单添加标签
 * @param {String} orderSn 订单号
 * @param {String} scene 场景
 * @param {String} tag 标签值
 */
export function addOrderTag(orderSn, scene, tag) {
  return insertWkbTag({
    orderSn,
    scene,
    tag
  })
}

/**
 * 删除订单标签
 * @param {Number} tagId 标签ID
 */
export function removeOrderTag(tagId) {
  return deleteWkbTag({
    idList: [tagId]
  })
}
