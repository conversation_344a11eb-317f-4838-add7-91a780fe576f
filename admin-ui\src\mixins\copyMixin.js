/**
 * 复制功能混入
 * 提供点击复制订单号和快递号的功能
 */
export default {
  methods: {
    /**
     * 复制文本到剪贴板
     * @param {string} text 要复制的文本
     * @param {string} type 复制的类型，用于提示信息
     * @param {string} color 提示信息的颜色，可选值：'blue', 'green', 默认为绿色
     */
    async copyToClipboard(text, type = '内容', color = 'green') {
      if (!text || text.trim() === '') {
        this.$message.warning(`${type}为空，无法复制`)
        return
      }

      try {
        // 优先使用现代的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(text)
          this.showCopySuccessMessage(`${type}已复制: ${text}`, color)
        } else {
          // 降级到传统方法
          this.fallbackCopyTextToClipboard(text, type, color)
        }
      } catch (err) {
        console.error('复制失败:', err)
        // 如果现代API失败，尝试降级方法
        this.fallbackCopyTextToClipboard(text, type, color)
      }
    },

    /**
     * 显示复制成功消息
     * @param {string} message 消息内容
     * @param {string} color 颜色类型
     */
    showCopySuccessMessage(message, color) {
      if (color === 'blue') {
        // 使用蓝色样式的成功提示
        this.$message({
          message: message,
          type: 'success',
          customClass: 'copy-success-blue',
          duration: 2000
        })
      } else if (color === 'orange') {
        // 使用橙色样式的成功提示
        this.$message({
          message: message,
          type: 'success',
          customClass: 'copy-success-orange',
          duration: 2000
        })
      } else {
        // 默认绿色成功提示
        this.$message.success(message)
      }
    },

    /**
     * 降级复制方法（兼容旧浏览器）
     * @param {string} text 要复制的文本
     * @param {string} type 复制的类型
     * @param {string} color 提示颜色
     */
    fallbackCopyTextToClipboard(text, type, color = 'green') {
      const textArea = document.createElement('textarea')
      textArea.value = text

      // 避免滚动到底部
      textArea.style.top = '0'
      textArea.style.left = '0'
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'

      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.showCopySuccessMessage(`${type}已复制: ${text}`, color)
        } else {
          this.$message.error(`${type}复制失败`)
        }
      } catch (err) {
        console.error('降级复制方法失败:', err)
        this.$message.error(`${type}复制失败`)
      }

      document.body.removeChild(textArea)
    },

    /**
     * 复制订单号（使用蓝色提示）
     * @param {string} orderSn 订单号
     */
    copyOrderSn(orderSn) {
      this.copyToClipboard(orderSn, '订单号', 'blue')
    },

    /**
     * 复制快递号（仅当快递号有值时，使用绿色提示）
     * @param {string} expressNo 快递号
     */
    copyExpressNo(expressNo) {
      if (!expressNo || expressNo.trim() === '') {
        this.$message.warning('快递号为空，无法复制')
        return
      }
      this.copyToClipboard(expressNo, '快递号', 'green')
    },

    /**
     * 复制商品名称（使用橙色提示）
     * @param {string} itemName 商品名称
     */
    copyItemName(itemName) {
      if (!itemName || itemName.trim() === '') {
        this.$message.warning('商品名称为空，无法复制')
        return
      }
      this.copyToClipboard(itemName, '商品名称', 'orange')
    },


  }
}
