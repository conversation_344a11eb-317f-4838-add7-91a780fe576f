package com.my.crossborder.controller.vo.sys_shift_day;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_排班日期表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftDayDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排班日期
     */
    private LocalDate shiftDay;

    /**
     * 当日应排店铺总数（固化值，避免店铺变化影响历史数据）
     */
    private Integer shopCount;

    /**
     * 创建人ID（关联用户表）
     */
    private Integer createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
