package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_menu.SysMenuPageDTO;
import com.my.crossborder.controller.vo.sys_menu.SysMenuPageVO;
import com.my.crossborder.mybatis.entity.SysMenu;

/**
 * 系统菜单表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysMenuPageVO> page(SysMenuPageDTO pageDTO);
	
}
