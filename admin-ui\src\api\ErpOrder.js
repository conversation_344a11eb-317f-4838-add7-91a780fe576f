import { reqDelete, reqGet, reqPost, reqPut } from './axiosFun'

/**
 * 分页查询订单
 * @param {Object} params 查询参数
 */
export function erpOrderPage(params) {
  return reqGet('/erp-order/page', params)
}

/**
 * 物流未完成订单分页查询（包含订单项）
 * @param {Object} params 查询参数
 */
export function incompleteLogisticsPageWithItems(params) {
  return reqGet('/erp-order/incomplete-logistics/page-with-items', params)
}

/**
 * 已填物流编号未入库订单分页查询（包含订单项）
 * @param {Object} params 查询参数
 */
export function logisticsDoneNotWarehoused(params) {
  return reqGet('/erp-order/logistics-done-not-warehoused/page-with-items', params)
}

/**
 * 已入库未出库订单分页查询（包含订单项）
 * @param {Object} params 查询参数
 */
export function warehousedNotOutbound(params) {
  return reqGet('/erp-order/warehoused-not-outbound/page-with-items', params)
}



/**
 * 重新采购订单分页查询（包含订单项）
 * @param {Object} params 查询参数
 */
export function repurchasePageWithItems(params) {
  return reqGet('/erp-order/repurchase/page-with-items', params)
}

/**
 * 根据ID查询订单详情
 * @param {String} orderId 订单ID
 */
export function erpOrderDetail(orderId) {
  return reqGet(`/erp-order/${orderId}`)
}

/**
 * 新增订单
 * @param {Object} data 订单数据
 */
export function insertErpOrder(data) {
  return reqPost('/erp-order', data)
}

/**
 * 修改订单
 * @param {Object} data 订单数据
 */
export function updateErpOrder(data) {
  return reqPut('/erp-order', data)
}

/**
 * 删除订单
 * @param {Object} data 删除参数
 */
export function deleteErpOrder(data) {
  return reqDelete('/erp-order', data)
}

/**
 * 获取订单状态枚举列表
 */
export function getOrderStates() {
  return reqGet('/erp-order/order-states')
}



/**
 * 售后服务分页查询
 * @param {Object} params 查询参数
 */
export function getOrdAfterSalePage(params) {
  return reqGet('/ord-after-sale/page', params)
}

/**
 * 新增售后服务
 * @param {Object} data 售后数据
 */
export function insertOrdAfterSale(data) {
  return reqPost('/ord-after-sale', data)
}

/**
 * 修改售后服务
 * @param {Object} data 售后数据
 */
export function updateOrdAfterSale(data) {
  return reqPut('/ord-after-sale', data)
}

/**
 * 删除售后服务
 * @param {Object} data 删除参数
 */
export function deleteOrdAfterSale(data) {
  return reqDelete('/ord-after-sale', data)
}

/**
 * 根据ID查询售后详情
 * @param {String} id 售后ID
 */
export function getOrdAfterSaleDetail(id) {
  return reqGet(`/ord-after-sale/${id}`)
}

/**
 * 确认完成售后
 * @param {String} id 售后ID
 */
export function confirmCompleteOrdAfterSale(id) {
  return reqPut(`/ord-after-sale/confirm-complete/${id}`)
}



/**
 * 删除退款记录
 * @param {Object} data 删除参数
 */
export function deleteOrdRefund(data) {
  return reqDelete('/ord-refund', data)
}

/**
 * 订单选择器专用分页查询（包含订单项）
 * @param {Object} params 查询参数
 */
export function selectorPageWithItems(params) {
  return reqGet('/erp-order/selector/page-with-items', params)
}
