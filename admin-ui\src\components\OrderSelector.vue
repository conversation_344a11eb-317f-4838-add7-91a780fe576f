<template>
  <div class="order-selector">
    <el-input v-model="displayText" :placeholder="placeholder" readonly @focus="openOrderDialog" :disabled="disabled">
      <el-button slot="append" icon="el-icon-search" @click="openOrderDialog" :disabled="disabled"></el-button>
    </el-input>

    <!-- 订单选择对话框 -->
    <el-dialog title="选择订单" top="5vh" :visible="showOrderSelectDialog" width="1200px" append-to-body @close="closeOrderDialog" :close-on-click-modal="false">
      <div>
        <!-- 订单搜索区域 -->
        <el-form :inline="true" :model="orderSearchForm" class="demo-form-inline">
          <el-form-item label="店铺">
            <el-select v-model="orderSearchForm.shopId" placeholder="请选择店铺" clearable style="width: 140px;">
              <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="orderSearchForm.orderSn" style="width:180px" placeholder="订单号" clearable></el-input>
          </el-form-item>
          <el-form-item label="订单状态">
            <dict-select v-model="orderSearchForm.orderStates" category-id="ERP_ORDER_STATUS" placeholder="订单状态" style="width:120px" clearable></dict-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchOrders">查询</el-button>
            <el-button @click="resetOrderSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 订单表格 -->
        <el-table ref="orderTable" v-loading="orderLoading" :data="orderTableData" style="width: 100%" height="530px"
          border @row-click="handleRowSelect" :expand-row-keys="expandedRows" :row-key="getRowKey" @expand-change="handleExpandChange"
          :highlight-current-row="true" @selection-change="handleSelectionChange"
          :row-class-name="getRowClassName" :row-style="getRowStyle" class="order-table-custom">
          <!-- 多选模式下的复选框列 -->
          <el-table-column v-if="multiple" type="selection" width="55"></el-table-column>
          <!-- 展开列 -->
          <el-table-column type="expand">
            <template slot-scope="scope">
              <div class="order-items-section">
                <div v-if="scope.row.itemsLoading" style="text-align: center; padding: 20px;">
                  <i class="el-icon-loading"></i> 加载中...
                </div>
                <el-table v-else-if="scope.row.orderItems && scope.row.orderItems.length > 0" :data="scope.row.orderItems" border style="width: 100%;" header-cell-class-name="dark-header">
                  <el-table-column label="产品图片" width="150" align="center">
                    <template slot-scope="item">
                      <img :src="item.row.itemImage || '/static/img/default-product.png'"
                        style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;"
                        @error="handleImageError" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="itemName" label="产品名称" min-width="200" show-overflow-tooltip>
                    <template slot-scope="item">
                      <span class="item-name clickable-item-name"
                            @click="copyItemName(item.row.itemName)"
                            :title="'点击复制商品名称: ' + item.row.itemName">
                        {{ item.row.itemName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="itemModelName" label="规格" min-width="150" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="amount" label="数量" width="90" align="center"></el-table-column>
                  <el-table-column prop="itemPrice" label="单价" width="100" align="center"></el-table-column>
                  <el-table-column prop="expressNo" label="快递编号" min-width="150" align="center">
                    <template slot-scope="item">
                      <span v-if="item.row.expressNo"
                            class="reissue-waybill-no"
                            style="color: #67C23A; font-weight: bold; cursor: pointer;"
                            @click="copyExpressNo(item.row.expressNo)"
                            :title="'点击复制快递号: ' + item.row.expressNo">
                        {{ item.row.expressNo }}
                      </span>
                      <span v-else class="no-express">-</span>
                    </template>
                  </el-table-column>
                </el-table>
                <div v-else style="text-align: center; padding: 20px; color: #999;">
                  暂无订单子项数据
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="shopName" label="店铺名称" min-width="130" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="orderSn" label="订单号" min-width="180" align="center">
            <template slot-scope="scope">
              <span class="order-number"
                    @click.stop="copyOrderSn(scope.row.orderSn)"
                    :title="'点击复制订单号'">
                {{ scope.row.orderSn }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="下单时间" min-width="140" align="center">
            <!-- <template slot-scope="scope">
              {{ formatDate(scope.row.createTime) }}
            </template> -->
          </el-table-column>
          <el-table-column prop="orderStatesName" label="订单状态" min-width="100" align="center"></el-table-column>
          <el-table-column prop="totalPrice" label="订单总价" min-width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.totalPrice }}
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty-table-placeholder">
              <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
              <p>暂无数据</p>
            </div>
          </template>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination @size-change="handleOrderSizeChange" @current-change="handleOrderCurrentChange"
            :current-page="orderSearchForm.current" :page-sizes="[10, 20, 30, 50]" :page-size="orderSearchForm.size"
            layout="total, sizes, prev, pager, next, jumper" :total="orderTotal">
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmOrderSelection"
          :disabled="multiple ? selectedOrders.length === 0 : !selectedOrder">
          确认选择{{ multiple && selectedOrders.length > 0 ? `(${selectedOrders.length})` : '' }}
        </el-button>
        <el-button @click="closeOrderDialog">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { selectorPageWithItems, erpOrderDetail } from '../api/ErpOrder'
import { getAllEnabledShops } from '../api/SysShop'
import DictSelect from './DictSelect'
import copyMixin from '../mixins/copyMixin'

export default {
  name: 'OrderSelector',
  mixins: [copyMixin],
  components: {
    DictSelect
  },
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择订单'
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedOrderSn: this.multiple ? [] : (this.value || ''),
      selectedOrderInfo: null,
      showOrderSelectDialog: false,
      orderSearchForm: {
        shopId: '',
        orderSn: '',
        orderStates: '',
        current: 1,
        size: 10
      },
      shopList: [],
      orderTableData: [],
      orderTotal: 0,
      orderLoading: false,
      expandedRows: [],
      selectedOrder: null,
      selectedOrders: [] // 多选模式下的选中订单列表
    }
  },
  computed: {
    displayText() {
      if (this.multiple) {
        if (this.selectedOrderSn && this.selectedOrderSn.length > 0) {
          return `已选择 ${this.selectedOrderSn.length} 个订单`;
        }
        return '';
      } else {
        if (this.selectedOrderSn) {
          return this.selectedOrderInfo ?
            `${this.selectedOrderSn} (${this.selectedOrderInfo.shopName})` :
            this.selectedOrderSn;
        }
        return '';
      }
    }
  },
  watch: {
    value(newVal) {
      if (this.multiple) {
        this.selectedOrderSn = Array.isArray(newVal) ? newVal : [];
      } else {
        this.selectedOrderSn = newVal || '';
        if (newVal && !this.selectedOrderInfo) {
          // 如果有值但没有订单信息，可以考虑查询订单详情
          this.loadOrderInfo(newVal);
        }
      }
    },
    selectedOrderSn(newVal) {
      this.$emit('input', newVal);
    }
  },
  methods: {
    // 获取行的key
    getRowKey(row) {
      return row.orderId;
    },

    // 获取行的类名（用于自定义选中行样式）
    getRowClassName({ row }) {
      // 单选模式下，如果是选中的行，添加自定义类名
      if (!this.multiple && this.selectedOrder && this.selectedOrder.orderId === row.orderId) {
        console.log('单选模式选中行:', row.orderSn);
        return 'selected-row';
      }
      // 多选模式下，如果行在选中列表中，添加自定义类名
      if (this.multiple && this.selectedOrders.some(order => order.orderId === row.orderId)) {
        console.log('多选模式选中行:', row.orderSn);
        return 'selected-row';
      }
      return '';
    },

    // 获取行的内联样式（直接设置选中行背景色）
    getRowStyle({ row }) {
      // 单选模式下，如果是选中的行，直接设置背景色
      if (!this.multiple && this.selectedOrder && this.selectedOrder.orderId === row.orderId) {
        return {
          'background-color': '#40a9ff !important',
          'background': '#40a9ff !important',
          'border-left': '3px solid #1890ff',
          'color': '#fff'
        };
      }
      // 多选模式下，如果行在选中列表中，直接设置背景色
      if (this.multiple && this.selectedOrders.some(order => order.orderId === row.orderId)) {
        return {
          'background-color': '#40a9ff !important',
          'background': '#40a9ff !important',
          'border-left': '3px solid #1890ff',
          'color': '#fff'
        };
      }
      return {};
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '';
      return new Date(dateTime).toLocaleString('zh-CN');
    },

    // 格式化日期（只显示日期部分）
    formatDate(dateTime) {
      if (!dateTime) return '';
      return new Date(dateTime).toLocaleDateString('zh-CN');
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png';
    },



    // 复制快递号
    copyExpressNo(expressNo) {
      this.copyToClipboard(expressNo, '快递号');
    },

    // 处理表格展开事件
    handleExpandChange(row, expandedRows) {
      // 如果行被展开且还没有加载订单子项
      if (expandedRows.includes(row) && !row.orderItems && !row.itemsLoading) {
        this.loadOrderItems(row);
      }
    },

    // 加载订单子项
    loadOrderItems(row) {
      this.$set(row, 'itemsLoading', true);
      erpOrderDetail(row.orderId)
        .then(res => {
          this.$set(row, 'itemsLoading', false);
          if (res.success && res.data) {
            this.$set(row, 'orderItems', res.data.orderItems || []);
          } else {
            this.$set(row, 'orderItems', []);
            this.$message.error(res.message || '加载订单子项失败');
          }
        })
        .catch(error => {
          this.$set(row, 'itemsLoading', false);
          this.$set(row, 'orderItems', []);
          this.$message.error('加载订单子项失败: ' + error.message);
        });
    },

    // 搜索订单
    searchOrders() {
      this.orderSearchForm.current = 1;
      this.loadOrderData();
    },

    // 重置订单搜索
    resetOrderSearch() {
      this.orderSearchForm = {
        shopId: '',
        orderSn: '',
        orderStates: '',
        current: 1,
        size: 10
      };
      this.loadOrderData();
    },

    // 加载店铺列表
    loadShopList() {
      getAllEnabledShops()
        .then(res => {
          if (res.success) {
            this.shopList = res.data.records || [];
          } else {
            this.$message.error(res.message || '加载店铺列表失败');
          }
        })
        .catch(error => {
          this.$message.error('加载店铺列表失败: ' + error.message);
        });
    },

    // 加载订单数据
    loadOrderData() {
      this.orderLoading = true;
      selectorPageWithItems(this.orderSearchForm)
        .then(res => {
          this.orderLoading = false;
          if (res.success) {
            this.orderTableData = res.data.records || [];
            this.orderTotal = res.data.total || 0;

            // 数据加载完成后更新行样式
            this.$nextTick(() => {
              this.updateRowStyles();
            });
          } else {
            this.$message.error(res.message || '加载订单数据失败');
          }
        })
        .catch(error => {
          this.orderLoading = false;
          this.$message.error('加载订单数据失败: ' + error.message);
        });
    },

    // 加载订单信息（用于显示已选订单的详情）
    loadOrderInfo(orderSn) {
      // 这里可以根据需要实现订单详情查询
      // 暂时不实现，只显示订单号
    },

    // 订单分页大小变化
    handleOrderSizeChange(val) {
      this.orderSearchForm.size = val;
      this.loadOrderData();
    },

    // 订单分页页码变化
    handleOrderCurrentChange(val) {
      this.orderSearchForm.current = val;
      this.loadOrderData();
    },

    // 打开订单选择对话框
    openOrderDialog() {
      if (this.disabled) return;
      this.showOrderSelectDialog = true;
      this.expandedRows = [];
      // 打开对话框时加载数据
      this.loadOrderData();
    },

    // 行点击事件（选择订单）
    handleRowSelect(row) {
      console.log('行被点击:', row);

      if (!this.multiple) {
        // 单选模式：选择订单但不关闭对话框
        this.selectedOrder = row;
        // 高亮选中的行
        this.$refs.orderTable.setCurrentRow(row);
        console.log('单选模式 - 选中订单:', this.selectedOrder);
      } else {
        // 多选模式：切换行的选中状态
        this.$refs.orderTable.toggleRowSelection(row);
        console.log('多选模式 - 切换行选择状态');
      }

      // 强制更新表格显示并直接操作DOM设置样式
      this.$nextTick(() => {
        this.updateRowStyles();
        if (this.$refs.orderTable) {
          this.$refs.orderTable.doLayout();
        }
      });
    },

    // 直接操作DOM更新行样式
    updateRowStyles() {
      // 兼容旧版本语法，不使用可选链操作符
      if (!this.$refs.orderTable || !this.$refs.orderTable.$el) return;

      const tableBody = this.$refs.orderTable.$el.querySelector('.el-table__body tbody');
      if (!tableBody) return;

      const rows = tableBody.querySelectorAll('tr');
      rows.forEach((row, index) => {
        const rowData = this.orderTableData[index];
        if (!rowData) return;

        let isSelected = false;
        if (this.multiple) {
          isSelected = this.selectedOrders.some(order => order.orderId === rowData.orderId);
        } else {
          isSelected = this.selectedOrder && this.selectedOrder.orderId === rowData.orderId;
        }

        if (isSelected) {
          // 为选中行设置样式
          const cells = row.querySelectorAll('td');
          cells.forEach(cell => {
            cell.style.backgroundColor = '#40a9ff';
            cell.style.background = '#40a9ff';
            cell.style.color = '#fff';

            // 特别处理订单号的颜色 - 使用金黄色确保在蓝色背景上可见
            const orderNumbers = cell.querySelectorAll('.order-number');
            orderNumbers.forEach(orderNumber => {
              orderNumber.style.setProperty('color', '#FFD700', 'important');
            });

            // 处理所有链接和span元素
            const allSpans = cell.querySelectorAll('span');
            allSpans.forEach(span => {
              span.style.color = '#fff';
            });

            // 特别处理商品名称 - 但不影响展开行内的商品名称
            const itemNames = cell.querySelectorAll('.clickable-item-name');
            itemNames.forEach(itemName => {
              // 如果不在展开行内，则设为白色
              if (!itemName.closest('.order-items-section')) {
                itemName.style.color = '#fff';
              }
            });
          });
          row.style.backgroundColor = '#40a9ff';
          row.style.background = '#40a9ff';
          console.log('设置选中行样式:', rowData.orderSn);
        } else {
          // 清除非选中行的样式
          const cells = row.querySelectorAll('td');
          cells.forEach(cell => {
            cell.style.backgroundColor = '';
            cell.style.background = '';
            cell.style.color = '';

            // 恢复订单号的原始颜色
            const orderNumbers = cell.querySelectorAll('.order-number');
            orderNumbers.forEach(orderNumber => {
              orderNumber.style.color = '';
            });

            // 恢复其他span元素的颜色
            const otherSpans = cell.querySelectorAll('span:not(.order-number)');
            otherSpans.forEach(span => {
              span.style.color = '';
            });

            // 恢复商品名称的原始颜色
            const itemNames = cell.querySelectorAll('.clickable-item-name');
            itemNames.forEach(itemName => {
              itemName.style.color = '';
            });
          });
          row.style.backgroundColor = '';
          row.style.background = '';
        }
      });
    },

    // 多选模式下的选择变化事件
    handleSelectionChange(selection) {
      if (this.multiple) {
        this.selectedOrders = selection;
        console.log('多选模式 - 当前选中订单:', this.selectedOrders);

        // 更新行样式
        this.$nextTick(() => {
          this.updateRowStyles();
        });
      }
    },

    // 确认选择订单
    confirmOrderSelection() {
      if (this.multiple) {
        // 多选模式
        if (this.selectedOrders.length === 0) {
          this.$message.warning('请先选择订单');
          return;
        }

        // 设置选中的订单号列表
        this.selectedOrderSn = this.selectedOrders.map(order => order.orderSn);

        // 关闭对话框
        this.closeOrderDialog();

        // 触发事件，通知父组件选择了订单
        this.$emit('select', this.selectedOrders);
        this.$emit('input', this.selectedOrderSn);
      } else {
        // 单选模式
        if (!this.selectedOrder) {
          this.$message.warning('请先选择一个订单');
          return;
        }

        // 设置选中的订单信息
        this.selectedOrderSn = this.selectedOrder.orderSn;
        this.selectedOrderInfo = this.selectedOrder;

        // 关闭对话框
        this.closeOrderDialog();

        // 触发事件，通知父组件选择了订单
        this.$emit('select', this.selectedOrder);
        this.$emit('input', this.selectedOrderSn);
      }
    },

    // 关闭订单对话框
    closeOrderDialog() {
      this.showOrderSelectDialog = false;
      this.expandedRows = [];
      this.selectedOrder = null;
      this.selectedOrders = [];
      // 清除表格选中状态
      if (this.$refs.orderTable) {
        this.$refs.orderTable.setCurrentRow();
        this.$refs.orderTable.clearSelection();
      }
    }
  },
  mounted() {
    // 组件挂载时加载店铺列表
    this.loadShopList();
  }
}
</script>

<style scoped>
.order-selector {
  width: 100%;
}

.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
}

.order-items-section {
  background-color: #f8f9fa;
  border-radius: 6px;
  /* padding: 15px; */
  /* margin: 10px; */
}

.order-items-section .el-table {
  background-color: white;
  border-radius: 4px;
}

/* 快递单号样式 */
.reissue-waybill-no {
  color: #67C23A !important;
  font-weight: bold !important;
  background-color: #F0F9FF !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
  border: 1px solid #67C23A !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  display: inline-block !important;
}

.reissue-waybill-no:hover {
  background-color: #E8F5E8 !important;
  border-color: #5CB85C !important;
}

.no-express {
  color: #C0C4CC;
  font-style: italic;
}

.empty-table-placeholder {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.empty-table-placeholder p {
  margin: 0;
  font-size: 14px;
}

/* 表格行悬停效果 */
.order-selector .el-table tbody tr:hover > td {
  background-color: #f5f7fa !important;
}

/* 选中行背景色 - 使用最高优先级选择器 */
.order-selector .order-table-custom .el-table__body-wrapper .el-table__body tbody tr.current-row td,
.order-selector .order-table-custom .el-table__body-wrapper .el-table__body tbody tr.selected-row td,
.order-selector .order-table-custom tbody tr.current-row > td,
.order-selector .order-table-custom tbody tr.selected-row > td,
.order-selector .order-table-custom .el-table__body tr.current-row > td,
.order-selector .order-table-custom .el-table__body tr.selected-row > td,
.order-selector .el-table .el-table__body-wrapper .el-table__body tbody tr.current-row td,
.order-selector .el-table .el-table__body-wrapper .el-table__body tbody tr.selected-row td,
.order-selector .el-table tbody tr.current-row > td,
.order-selector .el-table tbody tr.selected-row > td,
.order-selector .el-table .el-table__body tr.current-row > td,
.order-selector .el-table .el-table__body tr.selected-row > td {
  background-color: #40a9ff !important;
  background: #40a9ff !important;
}

/* 选中行悬停时的背景色 - 更深的蓝色 */
.order-selector .order-table-custom .el-table__body-wrapper .el-table__body tbody tr.current-row:hover td,
.order-selector .order-table-custom .el-table__body-wrapper .el-table__body tbody tr.selected-row:hover td,
.order-selector .order-table-custom tbody tr.current-row:hover > td,
.order-selector .order-table-custom tbody tr.selected-row:hover > td,
.order-selector .el-table .el-table__body-wrapper .el-table__body tbody tr.current-row:hover td,
.order-selector .el-table .el-table__body-wrapper .el-table__body tbody tr.selected-row:hover td,
.order-selector .el-table tbody tr.current-row:hover > td,
.order-selector .el-table tbody tr.selected-row:hover > td {
  background-color: #1890ff !important;
  background: #1890ff !important;
}

/* 多选模式下选中行的背景色 */
.order-selector .order-table-custom .el-table__body-wrapper .el-table__body tbody tr.el-table__row--selected td,
.order-selector .order-table-custom tbody tr.el-table__row--selected > td,
.order-selector .el-table .el-table__body-wrapper .el-table__body tbody tr.el-table__row--selected td,
.order-selector .el-table tbody tr.el-table__row--selected > td {
  background-color: #40a9ff !important;
  background: #40a9ff !important;
}

/* 为选中行添加左侧蓝色边框指示器 */
.order-selector .el-table tbody tr.current-row > td:first-child,
.order-selector .el-table tbody tr.selected-row > td:first-child {
  border-left: 3px solid #1890ff !important;
}

/* 确保选中行文字颜色为白色，在深蓝背景上清晰显示 */
.order-selector .el-table tbody tr.current-row > td .cell,
.order-selector .el-table tbody tr.selected-row > td .cell {
  color: #fff !important;
}

/* 选中行中的链接文字也设为白色 */
.order-selector .el-table tbody tr.current-row > td .cell a,
.order-selector .el-table tbody tr.selected-row > td .cell a,
.order-selector .el-table tbody tr.current-row > td .cell span,
.order-selector .el-table tbody tr.selected-row > td .cell span {
  color: #fff !important;
}

/* 选中行背景色和文字颜色 */
.order-selector .el-table tr[class*="current-row"] td,
.order-selector .el-table tr[class*="selected-row"] td {
  background-color: #40a9ff !important;
  color: #fff !important;
}

/* 订单号基础样式 */
.order-selector .order-number {
  color: #409EFF;
  font-weight: bold;
  cursor: pointer;
}

/* 选中行中的订单号使用金黄色，确保在蓝色背景上可见 */
.order-selector .el-table tr[class*="current-row"] td .order-number,
.order-selector .el-table tr[class*="selected-row"] td .order-number {
  color: #FFD700 !important;
}

/* 选中行中的商品名称也要变为白色 */
.order-selector .el-table tr[class*="current-row"] td .clickable-item-name,
.order-selector .el-table tr[class*="selected-row"] td .clickable-item-name {
  color: #fff !important;
}

/* 展开行内的所有文字都使用默认颜色，不受选中行样式影响 */
.order-selector .el-table tr[class*="current-row"] td .order-items-section *,
.order-selector .el-table tr[class*="selected-row"] td .order-items-section * {
  color: #333 !important;
}

/* 商品名称复制样式 - 使用默认文字颜色 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
  color: #333;
  font-weight: normal;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
  color: #409EFF;
}

/* 订单号样式 */
.el-table .cell {
  word-break: break-all;
}

/* 展开行样式 */
.el-table .el-table__expanded-cell {
  padding: 0;
  background-color: #fafafa;
}

/* 深色表头样式 */
.dark-header {
  background-color: #2c3e50 !important;
  color: #ffffff !important;
  font-weight: bold;
}

.dark-header .cell {
  color: #ffffff !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .demo-form-inline .el-form-item {
    display: block;
    margin-right: 0;
  }

  .order-items-section {
    padding: 10px;
  }
}
</style>
