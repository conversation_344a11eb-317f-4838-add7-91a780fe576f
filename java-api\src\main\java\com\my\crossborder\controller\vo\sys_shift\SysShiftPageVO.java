package com.my.crossborder.controller.vo.sys_shift;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_排班表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排班日期（格式：YYYY-MM-DD）
     */
    private LocalDate date;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 应排班店铺数量
     */
    private Integer shopCount;

    /**
     * 客服ID
     */
    private Integer serviceUserId;

    /**
     *  客服姓名
     */
    private String serviceUserName;

    /**
     * 主管ID
     */
    private Integer supervisorUserId;
    
    /**
     * 主管姓名
     */
    private String supervisorUserName;

    /**
     * 客服状态：0=待打卡/1=已打卡/2=缺勤
     */
    private Integer serviceShiftStatus;

    /**
     * 主管状态：0=待打卡/1=已打卡/2=缺勤
     */
    private Integer supervisorShiftStatus;

    /**
     * 客服打卡时间
     */
    private LocalDateTime serviceClockTime;

    /**
     * 主管打卡时间
     */
    private LocalDateTime supervisorClockTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
