<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.WkbNotificationReadingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.WkbNotificationReading">
        <id column="id" property="id" />
        <result column="notification_id" property="notificationId" />
        <result column="user_id" property="userId" />
        <result column="is_read" property="read" />
        <result column="read_time" property="readTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, notification_id, user_id, is_read, read_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingPageVO">
		SELECT	
			tRead.user_id,
			tRead.id,
			tRead.is_read as 'read',
			tRead.notification_id,
			tNotice.type,
			tNotice.title,
			tNotice.content,
			tNotice.publish_time,
			tNotice.publish_user_id,
			tUser.real_name as publishUserName
		FROM
			wkb_notification_reading AS tRead 
			LEFT JOIN wkb_notification AS tNotice ON tRead.notification_id = tNotice.id
			LEFT JOIN sys_user AS tUser ON tNotice.publish_user_id = tUser.user_id
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND tRead.id = #{id}
            </if>
	        <if test="notificationId != null and notificationId != ''">
	           	AND tRead.notification_id = #{notificationId}
            </if>
	        <if test="userId != null and userId != ''">
	           	AND tRead.user_id = #{userId}
            </if>
	        <if test="read != null">
	           	AND tRead.is_read = #{read}
            </if>
	        <if test="type != null">
	           	AND tNotice.type = #{type}
            </if>
        </where>
    </select>

</mapper>
