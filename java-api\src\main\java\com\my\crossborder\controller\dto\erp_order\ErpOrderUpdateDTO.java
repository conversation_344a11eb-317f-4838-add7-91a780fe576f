package com.my.crossborder.controller.dto.erp_order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_订单主表
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpOrderUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台订单ID
     */
	@NotNull(message="orderId不能为空")
    private String orderId;

    /**
     * 订单ID1
     */
	@NotNull(message="orderId1不能为空")
    private String orderId1;

    /**
     * 订单编号
     */
	@NotNull(message="orderSn不能为空")
    private String orderSn;

    /**
     * 店铺ID
     */
	@NotNull(message="shopId不能为空")
    private String shopId;

    /**
     * 店铺名称
     */
	@NotNull(message="shopName不能为空")
    private String shopName;

    /**
     * 店铺其他名称
     */
	@NotNull(message="shopOtherName不能为空")
    private String shopOtherName;

    /**
     * 店铺登录类型
     */
	@NotNull(message="shopLoginType不能为空")
    private String shopLoginType;

    /**
     * 订单总价
     */
	@NotNull(message="totalPrice不能为空")
    private BigDecimal totalPrice;

    /**
     * 第三方托管价格
     */
	@NotNull(message="escrowPrice不能为空")
    private BigDecimal escrowPrice;

    /**
     * 订单状态码
     */
	@NotNull(message="orderStates不能为空")
    private String orderStates;

    /**
     * 订单状态名称
     */
	@NotNull(message="orderStatesName不能为空")
    private String orderStatesName;

    /**
     * 订单类型
     */
	@NotNull(message="orderType不能为空")
    private String orderType;

    /**
     * 付款方式
     */
	@NotNull(message="paymentMethod不能为空")
    private String paymentMethod;

    /**
     * 货币类型
     */
	@NotNull(message="currency不能为空")
    private String currency;

    /**
     * 地区
     */
	@NotNull(message="region不能为空")
    private String region;

    /**
     * 买家地址姓名
     */
	@NotNull(message="buyerAddressName不能为空")
    private String buyerAddressName;

    /**
     * 买家地址电话
     */
	@NotNull(message="buyerAddressPhone不能为空")
    private String buyerAddressPhone;

    /**
     * 买家取消原因
     */
	@NotNull(message="buyerCancelReason不能为空")
    private String buyerCancelReason;

    /**
     * 买家是否已评价
     */
	@NotNull(message="buyerIsRated不能为空")
    private String buyerIsRated;

    /**
     * 买家最后修改地址时间
     */
	@NotNull(message="buyerLastChangeAddressTime不能为空")
    private LocalDateTime buyerLastChangeAddressTime;

    /**
     * 买家交易手续费
     */
	@NotNull(message="buyerTxnFee不能为空")
    private BigDecimal buyerTxnFee;

    /**
     * 收货地址
     */
	@NotNull(message="shippingAddress不能为空")
    private String shippingAddress;

    /**
     * 实际承运商
     */
	@NotNull(message="actualCarrier不能为空")
    private String actualCarrier;

    /**
     * 配送方式
     */
	@NotNull(message="shippingMethod不能为空")
    private String shippingMethod;

    /**
     * 平台配送方式
     */
	@NotNull(message="platShipping不能为空")
    private String platShipping;

    /**
     * 平台配送方法
     */
	@NotNull(message="platShippingMethod不能为空")
    private String platShippingMethod;

    /**
     * 包裹编号
     */
	@NotNull(message="packageNumber不能为空")
    private String packageNumber;

    /**
     * 第三方单号
     */
	@NotNull(message="thirdNo不能为空")
    private String thirdNo;

    /**
     * 第三方交易号
     */
	@NotNull(message="thirdPartyTn不能为空")
    private String thirdPartyTn;

    /**
     * 安排取件截止日期
     */
	@NotNull(message="arrangePickupByDate不能为空")
    private LocalDateTime arrangePickupByDate;

    /**
     * 自动取消3PL确认日期
     */
	@NotNull(message="autoCancel3plAckDate不能为空")
    private Long autoCancel3plAckDate;

    /**
     * 自动取消安排发货日期
     */
	@NotNull(message="autoCancelArrangeShipDate不能为空")
    private LocalDateTime autoCancelArrangeShipDate;

    /**
     * 付款截止日期
     */
	@NotNull(message="paybyDate不能为空")
    private LocalDateTime paybyDate;

    /**
     * 评价截止日期
     */
	@NotNull(message="rateByDate不能为空")
    private LocalDateTime rateByDate;

    /**
     * 取件时间
     */
	@NotNull(message="pickupTime不能为空")
    private LocalDateTime pickupTime;

    /**
     * 取件截止时间
     */
	@NotNull(message="pickupCutoffTime不能为空")
    private LocalDateTime pickupCutoffTime;

    /**
     * 配送时间
     */
	@NotNull(message="deliveryTime不能为空")
    private LocalDateTime deliveryTime;

    /**
     * 完成时间
     */
	@NotNull(message="completeTime不能为空")
    private LocalDateTime completeTime;

    /**
     * 发货确认时间
     */
	@NotNull(message="shippingConfirmTime不能为空")
    private LocalDateTime shippingConfirmTime;

    /**
     * 出库时间
     */
	@NotNull(message="outTime不能为空")
    private LocalDateTime outTime;

    /**
     * 实际出库时间
     */
	@NotNull(message="realOutTime不能为空")
    private LocalDateTime realOutTime;

    /**
     * 清理时间
     */
	@NotNull(message="cleanTime不能为空")
    private LocalDateTime cleanTime;

    /**
     * Shopee申请邮寄标识
     */
	@NotNull(message="shopeeApplyMailFlag不能为空")
    private String shopeeApplyMailFlag;

    /**
     * Shopee申请邮寄时间
     */
	@NotNull(message="shopeeApplyMailTime不能为空")
    private LocalDateTime shopeeApplyMailTime;

    /**
     * Shopee入库标识
     */
	@NotNull(message="shopeeInShopeFlag不能为空")
    private String shopeeInShopeFlag;

    /**
     * Shopee入库时间
     */
	@NotNull(message="shopeeInShopeTime不能为空")
    private LocalDateTime shopeeInShopeTime;

    /**
     * Shopee备注
     */
	@NotNull(message="shopeeRemark不能为空")
    private String shopeeRemark;

    /**
     * 平台申请邮寄日期
     */
	@NotNull(message="platApplyMailDate不能为空")
    private LocalDate platApplyMailDate;

    /**
     * 平台申请邮寄时间
     */
	@NotNull(message="platApplyMailTime不能为空")
    private LocalDateTime platApplyMailTime;

    /**
     * 申请邮寄标识
     */
	@NotNull(message="applyMailFlag不能为空")
    private String applyMailFlag;

    /**
     * 发货标识
     */
	@NotNull(message="deliverFlag不能为空")
    private String deliverFlag;

    /**
     * 配送标识
     */
	@NotNull(message="deliveryFlag不能为空")
    private String deliveryFlag;

    /**
     * 打包标识
     */
	@NotNull(message="packFlag不能为空")
    private Integer packFlag;

    /**
     * 拣货标识
     */
	@NotNull(message="pickFlag不能为空")
    private String pickFlag;

    /**
     * 出库标识
     */
	@NotNull(message="outFlag不能为空")
    private String outFlag;

    /**
     * 实际出库标识
     */
	@NotNull(message="realOutFlag不能为空")
    private String realOutFlag;

    /**
     * 清理标识
     */
	@NotNull(message="cleanFlag不能为空")
    private String cleanFlag;

    /**
     * 隔离标识
     */
	@NotNull(message="isolateFlag不能为空")
    private String isolateFlag;

    /**
     * 关联标识
     */
	@NotNull(message="correlationFlag不能为空")
    private String correlationFlag;

    /**
     * 保险标识
     */
	@NotNull(message="baoFlag不能为空")
    private String baoFlag;

    /**
     * 预售标识
     */
	@NotNull(message="preFlag不能为空")
    private String preFlag;

    /**
     * 部分发货标识
     */
	@NotNull(message="partFlag不能为空")
    private String partFlag;

    /**
     * 一对多标识
     */
	@NotNull(message="oneManyFlag不能为空")
    private String oneManyFlag;

    /**
     * 删除标识
     */
	@NotNull(message="deleteFlag不能为空")
    private String deleteFlag;

    /**
     * 快递100打印标识
     */
	@NotNull(message="kuaidi100PrintFlag不能为空")
    private String kuaidi100PrintFlag;

    /**
     * 快递100打印时间
     */
	@NotNull(message="kuaidi100PrintTime不能为空")
    private LocalDateTime kuaidi100PrintTime;

    /**
     * 入库标识
     */
	@NotNull(message="putInFalg不能为空")
    private String putInFalg;

    /**
     * 验货状态
     */
	@NotNull(message="examineGoodsStatus不能为空")
    private Integer examineGoodsStatus;

    /**
     * 超额金额
     */
	@NotNull(message="overAmount不能为空")
    private BigDecimal overAmount;

    /**
     * 平台替代标识
     */
	@NotNull(message="palt不能为空")
    private String palt;

    /**
     * 平台标识
     */
	@NotNull(message="pf不能为空")
    private Integer pf;

    /**
     * Go信标识
     */
	@NotNull(message="goXin不能为空")
    private Integer goXin;

    /**
     * 包装箱尺寸
     */
	@NotNull(message="cartonSize不能为空")
    private String cartonSize;

    /**
     * 硬币抵扣
     */
	@NotNull(message="coinOffset不能为空")
    private BigDecimal coinOffset;

    /**
     * 取件尝试次数
     */
	@NotNull(message="pickupAttempts不能为空")
    private Integer pickupAttempts;

    /**
     * 销售渠道
     */
	@NotNull(message="channel不能为空")
    private String channel;

    /**
     * 列表类型
     */
	@NotNull(message="listType不能为空")
    private String listType;

    /**
     * 物流状态
     */
	@NotNull(message="logisticsStatus不能为空")
    private String logisticsStatus;

    /**
     * 状态
     */
	@NotNull(message="status不能为空")
    private String status;

    /**
     * 状态扩展
     */
	@NotNull(message="statusExt不能为空")
    private String statusExt;

    /**
     * 操作类型
     */
	@NotNull(message="operationType不能为空")
    private String operationType;

    /**
     * 手工订单标识
     */
	@NotNull(message="handOrder不能为空")
    private String handOrder;

    /**
     * 换货ID
     */
	@NotNull(message="exchangeId不能为空")
    private String exchangeId;

    /**
     * 附加交易ID
     */
	@NotNull(message="addOnDealId不能为空")
    private String addOnDealId;

    /**
     * 促销活动ID
     */
	@NotNull(message="promotionId不能为空")
    private String promotionId;

    /**
     * 快照ID
     */
	@NotNull(message="snapshotId不能为空")
    private String snapshotId;

    /**
     * 消息
     */
	@NotNull(message="message不能为空")
    private String message;

    /**
     * 用户消息
     */
	@NotNull(message="userMessage不能为空")
    private String userMessage;

    /**
     * 订单详情
     */
	@NotNull(message="orderDetail不能为空")
    private String orderDetail;

    /**
     * 订单表单
     */
	@NotNull(message="orderForm不能为空")
    private String orderForm;

    /**
     * 备注
     */
	@NotNull(message="remark不能为空")
    private String remark;

    /**
     * 取消原因扩展
     */
	@NotNull(message="cancelReasonExt不能为空")
    private String cancelReasonExt;

    /**
     * 取消用户ID
     */
	@NotNull(message="cancelUserid不能为空")
    private String cancelUserid;

    /**
     * 部门ID
     */
	@NotNull(message="deptId不能为空")
    private Long deptId;

    /**
     * 部门名称
     */
	@NotNull(message="deptName不能为空")
    private String deptName;

    /**
     * 系统用户ID
     */
	@NotNull(message="sysUserId不能为空")
    private Long sysUserId;

    /**
     * 用户ID
     */
	@NotNull(message="userId不能为空")
    private String userId;

    /**
     * 用户名
     */
	@NotNull(message="userName不能为空")
    private String userName;

    /**
     * 用户昵称
     */
	@NotNull(message="nickName不能为空")
    private String nickName;

    /**
     * 平台用户ID
     */
	@NotNull(message="platUserId不能为空")
    private String platUserId;

    /**
     * 父级ID
     */
	@NotNull(message="parentId不能为空")
    private Long parentId;

    /**
     * 角色
     */
	@NotNull(message="role不能为空")
    private String role;

    /**
     * 创建人
     */
	@NotNull(message="createBy不能为空")
    private String createBy;

    /**
     * 创建时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
	@NotNull(message="updateBy不能为空")
    private String updateBy;

    /**
     * 更新时间
     */
	@NotNull(message="updateTime不能为空")
    private LocalDateTime updateTime;

}
