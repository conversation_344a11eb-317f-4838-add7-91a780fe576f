package com.my.crossborder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采购途径枚举
 * 对应字典：PURCHASE_CHANNEL
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Getter
@AllArgsConstructor
public enum PurchaseChannelEnum {

    /**
     * 拼多多
     */
    PINDUODUO("1", "拼多多", 1),

    /**
     * 1688
     */
    ALIBABA_1688("2", "1688", 2),

    /**
     * 淘宝及其他
     */
    TAOBAO_OTHER("3", "淘宝及其他", 3),

    /**
     * 调用库存
     */
    INVENTORY_CALL("4", "调用库存", 4),

    /**
     * 缺货打包
     */
    OUT_OF_STOCK_PACK("5", "缺货打包", 5);

    /**
     * 字典值
     */
    private final String value;

    /**
     * 字典名称
     */
    private final String name;

    /**
     * 排序号
     */
    private final Integer sortNum;

    /**
     * 根据值获取枚举
     *
     * @param value 字典值
     * @return 枚举对象
     */
    public static PurchaseChannelEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (PurchaseChannelEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据值获取名称
     *
     * @param value 字典值
     * @return 字典名称
     */
    public static String getNameByValue(String value) {
        PurchaseChannelEnum item = getByValue(value);
        return item != null ? item.getName() : null;
    }

    /**
     * 验证值是否有效
     *
     * @param value 字典值
     * @return 是否有效
     */
    public static boolean isValid(String value) {
        return getByValue(value) != null;
    }
}
