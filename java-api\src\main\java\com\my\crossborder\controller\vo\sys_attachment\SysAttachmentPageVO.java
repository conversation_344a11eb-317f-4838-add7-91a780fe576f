package com.my.crossborder.controller.vo.sys_attachment;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_附件表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysAttachmentPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer attachmentId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 数据源id
     */
    private String dataSourceId;

    /**
     * 文件原始名称
     */
    private String fileOriginalName;

    private LocalDateTime createTime;

}
