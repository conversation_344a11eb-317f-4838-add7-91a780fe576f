package com.my.crossborder.mybatis.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 售后表
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ord_after_sale")
public class OrdAfterSale implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 问题类别
     */
    private String issueType;

    /**
     * 处理办法 字典参数aftersale_close_way
     */
    private String closeWay;

    /**
     * 处理状态 字典参数close_status
     */
    private String closeStatus;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 新订单编号
     */
    private String newOrderSn;

    /**
     * 问题录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理完成时间
     */
    private LocalDateTime closeTime;

    /**
     * 录入人id
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer issueUserId;

    /**
     * 处理人id
     */
    private Integer closeUserId;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;


}
