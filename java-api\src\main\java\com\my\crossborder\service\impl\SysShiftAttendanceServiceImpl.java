package com.my.crossborder.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.my.crossborder.controller.vo.sys_shift.SysShiftPageVO;
import com.my.crossborder.controller.vo.sys_shift_attendance.MonthAttendanceFull;
import com.my.crossborder.controller.vo.sys_shift_attendance.MonthAttendanceSummary;
import com.my.crossborder.controller.vo.sys_shift_attendance.TodayAttendanceStatus;
import com.my.crossborder.enums.ClockStatusEnum;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.mybatis.entity.SysShift;
import com.my.crossborder.mybatis.entity.SysShiftAttendance;
import com.my.crossborder.mybatis.mapper.SysShiftAttendanceMapper;
import com.my.crossborder.service.SysShiftAttendanceService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * 考勤表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class SysShiftAttendanceServiceImpl extends ServiceImpl<SysShiftAttendanceMapper, SysShiftAttendance> implements SysShiftAttendanceService {

	@Transactional
	@Override
	public void createIfAbsense(Set<SysShiftAttendance> attendanceSet) {
		attendanceSet.forEach(t -> {
			this.createIfAbsense(t);
		});
	}
	
	@Transactional
	@Override
	public void createIfAbsense(SysShiftAttendance dto) {
		Wrapper<SysShiftAttendance> wrapper = new LambdaQueryWrapper<SysShiftAttendance>()
				.eq(SysShiftAttendance::getShiftDay, dto.getShiftDay())
				.eq(SysShiftAttendance::getUserId, dto.getUserId());
		SysShiftAttendance entity = this.baseMapper.selectOne(wrapper);
		if (entity == null) {
			this.save(dto);
		}
	}

	@Transactional
	@Override
	public void removeByUserId(LocalDate shiftDay, Integer userId) {
		this.remove(new LambdaQueryWrapper<SysShiftAttendance>()
				.eq(SysShiftAttendance::getShiftDay, shiftDay)
				.eq(SysShiftAttendance::getUserId, userId));
	}

	@Override
	public TodayAttendanceStatus todayAttendanceStatus(Integer userId, LocalDate shiftDay) {
		// 查询该用户在指定日期的排班记录
		SysShiftAttendance entity = this.get(shiftDay, userId);

		if (entity == null) {
			return TodayAttendanceStatus.builder()
				.needAttendance(Boolean.FALSE)
				.build();
		} else {
			return TodayAttendanceStatus.builder()
				.needAttendance(Boolean.TRUE)
				.clockTime(entity.getClockTime())
				.clockStatus(entity.getClockStatus())
				.build();
		}
	}

	@Transactional
	@Override
	public void punchIn() {
		Integer userId = StpUtil.getLoginIdAsInt();
		LocalDate shiftDay = LocalDate.now();
		// 查询该用户在指定日期的排班记录
		SysShiftAttendance entity = this.get(shiftDay, userId);
		BusinessException.when(entity == null,  "今日无排班，无需打卡");

		LambdaUpdateWrapper<SysShiftAttendance> wrapper = new LambdaUpdateWrapper<SysShiftAttendance>()
			.set(SysShiftAttendance::getClockTime, LocalDateTime.now())
			.set(SysShiftAttendance::getClockStatus, ClockStatusEnum.CHECKED.getCode())
			.eq(SysShiftAttendance::getShiftDay, shiftDay)
			.eq(SysShiftAttendance::getUserId, userId)
			;
		this.update(wrapper);
	}

	@Override
	public MonthAttendanceSummary monthAttendanceSummary(Integer userId, Integer year, Integer month) {
		// 构建查询日期范围
		String startDate = String.format("%d-%02d-01", year, month);
		LocalDate start = LocalDate.parse(startDate);
		LocalDate end = start.withDayOfMonth(start.lengthOfMonth());
		Integer scheduledDays = this.count(new LambdaQueryWrapper<SysShiftAttendance>()
				.eq(SysShiftAttendance::getUserId, userId)
				.ge(SysShiftAttendance::getShiftDay, start)
				.le(SysShiftAttendance::getShiftDay, end));
		Integer checkedDays = this.count(new LambdaQueryWrapper<SysShiftAttendance>()
				.eq(SysShiftAttendance::getUserId, userId)
				.ge(SysShiftAttendance::getShiftDay, start)
				.le(SysShiftAttendance::getShiftDay, end)
				.eq(SysShiftAttendance::getClockStatus, ClockStatusEnum.CHECKED.getCode()));
		Integer absentDays = this.count(new LambdaQueryWrapper<SysShiftAttendance>()
				.eq(SysShiftAttendance::getUserId, userId)
				.ge(SysShiftAttendance::getShiftDay, start)
				.le(SysShiftAttendance::getShiftDay, end)
				.eq(SysShiftAttendance::getClockStatus, ClockStatusEnum.ABSENT.getCode()));
		MonthAttendanceSummary summary = MonthAttendanceSummary.builder()
			.scheduledDays(scheduledDays)
			.checkedDays(checkedDays)
			.absentDays(absentDays)
			.build();
		return summary;
	}

	@Override
	public MonthAttendanceFull monthlyAttendanceFull(Integer userId, Integer year, Integer month) {
		MonthAttendanceSummary summary = this.monthAttendanceSummary(userId, year, month);
		MonthAttendanceFull result = BeanUtil.copyProperties(summary, MonthAttendanceFull.class);

		// 查询月度考勤详细数据
		String startDate = String.format("%d-%02d-01", year, month);
		LocalDate start = LocalDate.parse(startDate);
		LocalDate end = start.withDayOfMonth(start.lengthOfMonth());
		List<SysShiftAttendance> entityList = this.baseMapper.selectList(new LambdaQueryWrapper<SysShiftAttendance>()
			.eq(SysShiftAttendance::getUserId, userId)
			.ge(SysShiftAttendance::getShiftDay, start)
			.le(SysShiftAttendance::getShiftDay, end));
		// 每日状态数据
		Map<LocalDate, String> attendanceData = Maps.newTreeMap();
		for (SysShiftAttendance entity : entityList) {
			attendanceData.put(entity.getShiftDay(), ClockStatusEnum.getByCode(entity.getClockStatus()).name());
		}
		result.setAttendanceData(attendanceData);
		return result;
	}

	@Transactional
	@Override
	public void autoAbsentStatus() {
		// 查询前一天所有未打卡改为缺勤
		LambdaUpdateWrapper<SysShiftAttendance> updateWrapper = new LambdaUpdateWrapper<SysShiftAttendance>()
				.set(SysShiftAttendance::getClockStatus, ClockStatusEnum.ABSENT.getCode())
				.lt(SysShiftAttendance::getShiftDay, LocalDate.now())
				;
		this.update(updateWrapper);
	}
	
	@Deprecated
	@Override
	public Map<String, Object> dateShift(Integer userId, String date) {
		List<Map<String, Object>> schedules = null; // this.baseMapper.getDateScheduleDetail(userId, date);
		if (schedules.isEmpty()) {
			return null;
		}

		Map<String, Object> schedule = schedules.get(0);
		Map<String, Object> result = new HashMap<>();

		result.put("date", date);
		result.put("shopName", schedule.get("shopName"));

		// 根据用户角色返回相应信息
		Integer serviceUserId = (Integer) schedule.get("serviceUserId");
		Integer supervisorUserId = (Integer) schedule.get("supervisorUserId");

		if (userId.equals(serviceUserId)) {
			result.put("role", "客服");
			result.put("status", schedule.get("serviceShiftStatus"));
			result.put("clockTime", schedule.get("serviceClockTime"));
		} else if (userId.equals(supervisorUserId)) {
			result.put("role", "客服主管");
			result.put("status", schedule.get("supervisorShiftStatus"));
			result.put("clockTime", schedule.get("supervisorClockTime"));
		}
		

		return result;
	}

	private SysShiftAttendance get(LocalDate shiftDay, Integer userId) {
		SysShiftAttendance entity = this.baseMapper.selectOne(new LambdaQueryWrapper<SysShiftAttendance>()
				.eq(SysShiftAttendance::getShiftDay, shiftDay)
				.eq(SysShiftAttendance::getUserId, userId));
		return entity;
	}
	

}
