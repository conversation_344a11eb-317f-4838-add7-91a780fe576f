package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.OrdClaim;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimInsertDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimPageDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimUpdateDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimDeleteDTO;
import com.my.crossborder.controller.vo.ord_claim.OrdClaimDetailVO;
import com.my.crossborder.controller.vo.ord_claim.OrdClaimPageVO;

/**
 * 物流理赔 服务类
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface OrdClaimService extends IService<OrdClaim> {

	/**
	 * 新增
	 */
	void insert(OrdClaimInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(OrdClaimUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	OrdClaimDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<OrdClaimPageVO> page(OrdClaimPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(OrdClaimDeleteDTO deleteDTO);

	/**
	 * 确认完成
	 * @param id 理赔记录ID
	 */
	void confirmComplete(Integer id);

}
