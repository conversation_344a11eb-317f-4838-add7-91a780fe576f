package com.my.crossborder.controller.dto.wkb_notification_reading;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_通知阅读表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationReadingPageDTO 
						extends PageDTO<WkbNotificationReadingPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 通知id
     */
    private Integer notificationId;

    /**
     * 公告的接收用户id
     */
    private Integer userId;

    /**
     * 是否已读
     */
    private Boolean read;

    /**
     * 公告类型
     */
    private String type;

    
    /**
     * 构造函数
     * @param current
     * @param size
     */
    public WkbNotificationReadingPageDTO(long current, long size) {
    	super(current, size);
    }
}
