import { reqDelete, reqGet, reqPost, reqPut } from './axiosFun'

/**
 * 退款管理分页查询
 * @param {Object} params 查询参数
 */
export function getOrdRefundPage(params) {
  return reqGet('/ord-refund/page', params)
}

/**
 * 申请退款
 * @param {Object} data 申请退款数据
 */
export function applyRefund(data) {
  return reqPost('/ord-refund/apply-refund', data)
}

/**
 * 申请入库
 * @param {Object} data 申请入库数据
 */
export function applyPutIn(data) {
  return reqPost('/ord-refund/apply-put-in', data)
}

/**
 * 退款结果
 * @param {Object} data 退款结果数据
 */
export function refundResult(data) {
  return reqPost('/ord-refund/refund-result', data)
}

/**
 * 确认已入库
 * @param {Object} data 确认入库数据
 */
export function confirmPutIn(data) {
  return reqPost('/ord-refund/confirm-put-in', data)
}

/**
 * 员工周结算统计
 * @param {Object} params 查询参数 {userId, year, month}
 */
export function getWeeklySettlementStats(params) {
  return reqGet('/ord-refund/weekly-settlement-stats', params)
}
