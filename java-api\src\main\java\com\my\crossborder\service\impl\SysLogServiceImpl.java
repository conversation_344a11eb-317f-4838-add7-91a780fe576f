package com.my.crossborder.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.sys_log.SysLogDeleteDTO;
import com.my.crossborder.controller.dto.sys_log.SysLogInsertDTO;
import com.my.crossborder.controller.dto.sys_log.SysLogPageDTO;
import com.my.crossborder.controller.vo.sys_log.SysLogDetailVO;
import com.my.crossborder.controller.vo.sys_log.SysLogPageVO;
import com.my.crossborder.mybatis.entity.SysLog;
import com.my.crossborder.mybatis.mapper.SysLogMapper;
import com.my.crossborder.service.SysLogService;
import com.my.crossborder.util.ColumnLambda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

/**
 * 操作日志表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {


	@Override
	public void insert(SysLogInsertDTO insertDTO) {
		SysLog entity = BeanUtil.copyProperties(insertDTO, SysLog.class);
		this.save(entity);
	}

	@Override
	public SysLogDetailVO detail(Long id) {
		SysLog entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SysLogDetailVO.class);
	}

	@Override
	public Page<SysLogPageVO> page(SysLogPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<SysLog>().columnsToString(SysLog::getLogId)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysLogDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
