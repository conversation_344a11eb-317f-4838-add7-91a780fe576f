<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>系统日志</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="操作时间">
          <el-date-picker v-model="formInline.createTimeRange" type="daterange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="操作成功">
          <el-select v-model="formInline.success" placeholder="成功" clearable style="width: 80px">
            <el-option label="成功" :value="true"></el-option>
            <el-option label="失败" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作人">
          <user-selector v-model="selectedUsers" :multiple="true" @select="handleUsersSelected" style="width: 120px"></user-selector>
        </el-form-item>
        <!-- <el-form-item label="所属模块">
          <el-select v-model="formInline.moduleName" placeholder="模块" clearable style="width: 120px">
            <el-option label="工作台" value="工作台"></el-option>
            <el-option label="系统管理" value="系统管理"></el-option>
            <el-option label="人员管理" value="人员管理"></el-option>
            <el-option label="管理制度" value="管理制度"></el-option>
            <el-option label="设备管理" value="设备管理"></el-option>
            <el-option label="检验检测" value="检验检测"></el-option>
            <el-option label="风险管控" value="风险管控"></el-option>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="菜单">
          <el-input v-model="formInline.menuName" placeholder="菜单" style="width: 100px"></el-input>
        </el-form-item> -->
        <el-form-item label="类型">
          <el-select v-model="formInline.operationName" placeholder="类型" clearable style="width: 80px">
            <el-option label="新增" value="新增"></el-option>
            <el-option label="修改" value="修改"></el-option>
            <el-option label="删除" value="删除"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" border style="width: 100%;" v-loading="loading"
        @sort-change="handleSortChange">
        <!-- <el-table-column type="index" label="序号" width="60" align="center">
        </el-table-column> -->
        <el-table-column prop="logId" label="ID" width="60" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="createTime" label="操作时间" width="160" align="center" sortable="custom">
        </el-table-column>
        <el-table-column prop="success" label="操作成功" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.success ? 'success' : 'danger'" v-if="scope.row.success">
              {{ scope.row.success ? '成功' : '失败' }}
            </el-tag>
            <el-tooltip :content="scope.row.errMsg" placement="top" v-else>
              <el-tag :type="'danger'">
                {{ scope.row.success ? '成功' : '失败' }}
              </el-tag>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createUserId" label="操作人" width="130" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ getUserNameById(scope.row.createUserId) }}
          </template>
        </el-table-column>
        <el-table-column prop="moduleName" label="所属模块" width="140" align="center" show-overflow-tooltip
          sortable="custom">
        </el-table-column>
        <el-table-column prop="menuName" label="操作菜单" width="200" align="center" sortable="custom" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="operationName" label="类型" align="center" sortable="custom" width="100">
        </el-table-column>
        <el-table-column prop="operationDetail" label="操作详情" min-width="120" align="center" show-overflow-tooltip>
        </el-table-column>

        <!-- <el-table-column prop="errMsg" label="错误信息" min-width="200" align="center" show-overflow-tooltip>
        </el-table-column> -->
        <!-- <el-table-column label="操作" min-width="80" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" style="color: #F56C6C" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column> -->
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>
  </div>
</template>

<script>
import { sysLogPage, deleteSysLog } from '../../api/SysLog'
import { sysUserList } from '../../api/SysUser'
import { loadToken } from '../../utils/util'
import Pagination from '../../components/Pagination'
import DictSelect from '../../components/DictSelect'
import UserSelector from '../../components/UserSelector'
import qs from 'qs'
import { mapState } from 'vuex'

export default {
  name: 'SysLog',
  components: {
    Pagination,
    DictSelect,
    UserSelector
  },
  computed: {
    ...mapState(['userEntity'])
  },
  data() {
    return {
      selectedUsers: [],
      userMap: {}, // 用户ID到用户信息的映射
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        logId: '',
        moduleName: '',
        menuName: '',
        operationName: '',
        success: null,
        createUserId: '',
        createTimeStart: '',
        createTimeEnd: '',
        createTimeRange: [],
        token: loadToken(),
        orders: []
      },
      // 表格数据
      tableData: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false
    }
  },
  created() {
    // 获取分页数据
    this.getPageData()
  },
  methods: {
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = this.formInline
      }

      // 处理日期范围
      if (parameter.createTimeRange && parameter.createTimeRange.length === 2) {
        parameter.createTimeStart = parameter.createTimeRange[0]
        parameter.createTimeEnd = parameter.createTimeRange[1]
      } else {
        parameter.createTimeStart = undefined
        parameter.createTimeEnd = undefined
      }

      sysLogPage(parameter)
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
          this.pageParam.currentPage = res.data.current
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total

          // 批量查询用户信息
          this.loadUserInfo()
        })
        .catch(err => {
          this.loading = false
          this.$message.error('获取数据失败：' + err.message)
        })
    },

    // 批量查询用户信息
    loadUserInfo() {
      // 提取当前页所有记录中的userId
      const userIds = new Set()
      this.tableData.forEach(row => {
        if (row.createUserId) {
          userIds.add(row.createUserId)
        }
      })

      if (userIds.size > 0) {
        const userIdList = Array.from(userIds)
        sysUserList({ idList: userIdList })
          .then(res => {
            if (res.success && res.data) {
              // 构建用户ID到用户信息的映射
              this.userMap = {}
              res.data.forEach(user => {
                this.userMap[user.userId] = user
              })
            }
          })
          .catch(err => {
            console.error('批量查询用户信息失败:', err)
          })
      }
    },

    // 分页回调
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },

    // 排序处理
    handleSortChange(column) {
      if (column.order != null) {
        let sortProp = column.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        let orderBy = { "column": sortProp, "asc": column.order === 'ascending' }
        this.formInline.orders[0] = orderBy
      } else {
        this.formInline.orders = []
      }
      this.getPageData()
    },

    // 搜索
    onSearch() {
      this.formInline.current = 1
      this.getPageData()
    },

    // 重置
    onReset() {
      this.selectedUsers = []
      this.formInline = {
        current: 1,
        size: 10,
        logId: '',
        moduleName: '',
        menuName: '',
        operationName: '',
        success: null,
        createUserId: '',
        createTimeStart: '',
        createTimeEnd: '',
        createTimeRange: [],
        token: loadToken(),
        orders: []
      }
      this.getPageData()
    },

    // 处理用户选择
    handleUsersSelected(selectedUsers) {
      if (selectedUsers && selectedUsers.length > 0) {
        this.formInline.createUserId = selectedUsers[0].userId
      } else {
        this.formInline.createUserId = ''
      }
    },

    // 获取用户姓名
    getUserNameById(userId) {
      if (!userId) return ''
      const user = this.userMap[userId]
      return user ? user.realName : '未知用户'
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该条日志记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSysLog({ idList: [row.logId] })
          .then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getPageData()
          })
          .catch(err => {
            this.$message.error('删除失败：' + err.message)
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: left;
}

.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}
</style>
