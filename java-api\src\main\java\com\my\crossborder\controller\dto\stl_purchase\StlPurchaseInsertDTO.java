package com.my.crossborder.controller.dto.stl_purchase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_结算_采购结算表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class StlPurchaseInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 采购员工ID
     */
	@NotNull(message="purchaseUserId不能为空")
    private Integer purchaseUserId;

    /**
     * 采购日期
     */
	@NotNull(message="purchaseDate不能为空")
    private LocalDate purchaseDate;

    /**
     * 结算金额
     */
	@NotNull(message="settlementAmount不能为空")
    private BigDecimal settlementAmount;

    /**
     * 备注( 使用常见标签快速填写）
     */
	@NotNull(message="remark不能为空")
    private String remark;


}
