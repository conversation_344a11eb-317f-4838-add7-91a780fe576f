package com.my.crossborder.service.impl;

import java.lang.reflect.Field;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.cache.SysParamCache;
import com.my.crossborder.controller.dto.sys_param.SysParamUpdateDTO;
import com.my.crossborder.controller.vo.sys_param.SysParamVO;
import com.my.crossborder.enums.SysParamFieldEnum;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.mybatis.entity.SysParam;
import com.my.crossborder.mybatis.mapper.SysParamMapper;
import com.my.crossborder.service.SysParamService;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 *  系统参数配置表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SysParamServiceImpl extends ServiceImpl<SysParamMapper, SysParam> implements SysParamService {

	private final SysParamCache sysParamCache;
	

	@Transactional
	@Override
	public void update(SysParamUpdateDTO updateDTO) {
		// 通过反射获取DTO中的非null字段值
		Class<?> dtoClass = updateDTO.getClass();
		
		// 遍历所有的参数枚举（排除id和batchNumber字段）
		for (SysParamFieldEnum fieldEnum : SysParamFieldEnum.values()) {
			String fieldName = fieldEnum.name();
			
			// 通过反射获取字段值
			Field field = null;
			try {
				field = dtoClass.getDeclaredField(fieldName);
			} catch(NoSuchFieldException e) {
				log.warn("SysParamUpdateDTO不存在该字段 => SysParamFieldEnum[{}]", fieldName);
				continue;
			}
			field.setAccessible(true);
			String fieldValue = null;
			try {
				fieldValue = (String) field.get(updateDTO);
			} catch (Exception e) {
				BusinessException.by("从SysParamUpdateDTO取值失败:{}", fieldName);
			}
			
			// 如果字段值不为null，则加入更新列表
			if (StrUtil.isNotBlank(fieldValue)) {
				// 检查数据库中是否已存在该键
				LambdaQueryWrapper<SysParam> queryWrapper = new LambdaQueryWrapper<SysParam>()
						.eq(SysParam::getK, fieldName);
				SysParam entity = this.baseMapper.selectOne(queryWrapper);
				if (entity == null) {
					log.error("SysParam表缺少k: {}", fieldName);
					BusinessException.by("SysParam表缺少k: {}", fieldName);
				}
				
				// 执行更新
				LambdaUpdateWrapper<SysParam> updateWrapper = new LambdaUpdateWrapper<SysParam>()
						.eq(SysParam::getK, fieldName)
						.set(SysParam::getV, fieldValue.toString());
				this.baseMapper.update(null, updateWrapper);
				log.info("更新系统参数: {} = {}", fieldName, fieldValue);
			}
		}
		log.info("系统参数批量更新完成");
		
		// 重置缓存
		this.sysParamCache.reloadCache();
	}

	@Override
	public SysParamVO get() {
		return this.sysParamCache.get();
	}

}
