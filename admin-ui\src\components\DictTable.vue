<template>
  <el-table
    ref="table"
    :data="processedData"
    v-bind="$attrs"
    v-on="$listeners">
    <slot></slot>
  </el-table>
</template>

<script>
import dictService from '../utils/dictService'

export default {
  name: 'DictTable',
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 字典映射配置
    // 格式：{ 字段名: '字典分类ID' }
    dictMapping: {
      type: Object,
      default: () => ({})
    },
    // 是否自动转换字典字段
    autoConvert: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      processedData: [],
      loading: false
    }
  },
  watch: {
    data: {
      handler() {
        this.processData()
      },
      immediate: true
    },
    dictMapping: {
      handler() {
        this.processData()
      },
      deep: true
    }
  },
  methods: {
    // 处理数据，转换字典字段
    async processData() {
      if (!this.autoConvert || !this.data || !Object.keys(this.dictMapping).length) {
        this.processedData = this.data || []
        return
      }

      try {
        this.loading = true
        this.processedData = await dictService.convertDictFields(this.data, this.dictMapping)
      } catch (error) {
        console.error('处理字典数据失败:', error)
        this.processedData = this.data || []
      } finally {
        this.loading = false
      }
    },

    // 暴露表格的方法
    clearSelection() {
      this.$refs.table.clearSelection()
    },

    toggleRowSelection(row, selected) {
      this.$refs.table.toggleRowSelection(row, selected)
    },

    toggleAllSelection() {
      this.$refs.table.toggleAllSelection()
    },

    toggleRowExpansion(row, expanded) {
      this.$refs.table.toggleRowExpansion(row, expanded)
    },

    setCurrentRow(row) {
      this.$refs.table.setCurrentRow(row)
    },

    clearSort() {
      this.$refs.table.clearSort()
    },

    clearFilter(columnKey) {
      this.$refs.table.clearFilter(columnKey)
    },

    doLayout() {
      this.$refs.table.doLayout()
    },

    sort(prop, order) {
      this.$refs.table.sort(prop, order)
    }
  }
}
</script>
