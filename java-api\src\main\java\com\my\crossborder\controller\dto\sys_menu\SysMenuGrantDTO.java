package com.my.crossborder.controller.dto.sys_menu;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenuGrantDTO {

	/**
	 * 角色id
	 */
	@NotNull(message = "roleId不能为空")
	Integer roleId;
	
	/**
	 * 菜单id
	 */
	@NotEmpty(message = "menuId不能为空")
	List<String> menuIdList;
}
