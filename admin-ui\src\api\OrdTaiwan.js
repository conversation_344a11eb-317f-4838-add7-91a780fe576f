import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 台湾上架物品分页查询
 * @param {Object} params 查询参数
 */
export function getOrdTaiwanPage(params) {
  return reqGet('/ord-taiwan/page', params)
}

/**
 * 新增台湾上架物品
 * @param {Object} data 台湾上架物品数据
 */
export function insertOrdTaiwan(data) {
  return reqPost('/ord-taiwan', data)
}

/**
 * 修改台湾上架物品
 * @param {Object} data 台湾上架物品数据
 */
export function updateOrdTaiwan(data) {
  return reqPut('/ord-taiwan', data)
}

/**
 * 删除台湾上架物品
 * @param {Object} data 删除参数
 */
export function deleteOrdTaiwan(data) {
  return reqDelete('/ord-taiwan', data)
}

/**
 * 根据ID查询台湾上架物品详情
 * @param {String} id 台湾上架物品ID
 */
export function getOrdTaiwanDetail(id) {
  return reqGet(`/ord-taiwan/${id}`)
}

/**
 * 批量导入台湾上架物品
 * @param {Object} data 导入数据
 */
export function batchImportOrdTaiwan(data) {
  return reqPost('/ord-taiwan/batch-import', data)
}

/**
 * 导出台湾上架物品数据
 * @param {Object} params 导出参数
 */
export function exportOrdTaiwan(params) {
  return reqGet('/ord-taiwan/export', params)
}

/**
 * 更新台湾上架物品状态
 * @param {Number} id 台湾上架物品ID
 * @param {Number} status 状态值
 */
export function updateOrdTaiwanStatus(id, status) {
  return reqPut(`/ord-taiwan/${id}/status`, { status })
}
