2025-08-01 13:02:52.125 INFO [ main] com.my.crossborder.App:84 - Tomcat Port 20000 is available. Starting application >> 
2025-08-01 13:02:52.130 INFO [ main] com.my.crossborder.App:655 - The following profiles are active: local,local-sa,local-db,local-forest,local-job
2025-08-01 13:02:54.087 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990AuthClient' and Proxy of 'com.my.crossborder.forest.erp990.Erp990AuthClient' client interface
2025-08-01 13:02:54.088 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990Client' and Proxy of 'com.my.crossborder.forest.erp990.Erp990Client' client interface
2025-08-01 13:02:54.088 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.crossborder.forest.siliconflow.SiliconflowClient' client interface
2025-08-01 13:04:17.782 INFO [ main] com.my.crossborder.App:84 - Tomcat Port 20000 is available. Starting application >> 
2025-08-01 13:04:17.789 INFO [ main] com.my.crossborder.App:655 - The following profiles are active: local,local-sa,local-db,local-forest,local-job
2025-08-01 13:04:19.444 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990AuthClient' and Proxy of 'com.my.crossborder.forest.erp990.Erp990AuthClient' client interface
2025-08-01 13:04:19.444 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990Client' and Proxy of 'com.my.crossborder.forest.erp990.Erp990Client' client interface
2025-08-01 13:04:19.444 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.crossborder.forest.siliconflow.SiliconflowClient' client interface
2025-08-01 13:04:20.984 WARN [ main] c.b.m.core.metadata.TableInfoHelper:335 - Can not find table primary key in Class: "com.my.crossborder.mybatis.entity.SysDictItem".
2025-08-01 13:04:22.584 INFO [ main] com.my.crossborder.cache.SysParamCache:70 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Pro/Qwen/Qwen2.5-VL-7B-Instruct","aiSystemPrompt":"-","aiUserPrompt":"本图片是网页登录页的验证码，它是一个数学公式，通常是加减乘除的计算。格式是【数值1 符号 数值2 = ?】，请帮我计算结果，仅返回计算结果即可。(如果识别失败返回0)","debugErp990Token":"eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE0N2YyYTRmLTIwYzMtNDNkNS1iZmM2LWVjYzRkMDE5NGQzYSJ9.ky28FBGqrWui4TSz9ELtJ3CHseB0IQ1-vlIsqhm5eRPtwiR8f28RDL14WAPOnZhnLITUyx9XT17Ui1QeVngNaQ","fileAllowExt":".jpg,.png,.jpeg","fileMaxSize":"3","fileUploadPath":"/home/<USER>/test/upload-images","orderSyncDays":"32","taiwanWithdrawFeeRatio":"0.9"}
2025-08-01 13:04:24.886 INFO [ main] com.my.crossborder.job.AttendanceJob:37 - 开始执行缺勤状态处理任务
2025-08-01 13:04:25.011 INFO [ main] com.my.crossborder.job.AttendanceJob:41 - 缺勤状态处理任务执行成功
2025-08-01 13:04:26.519 INFO [ main] com.my.crossborder.App:61 - Started App in 10.044 seconds (JVM running for 17.187)
2025-08-01 13:04:26.522 INFO [ main] com.my.crossborder.App:49 -  >> Web Server Started on port: 20000
2025-08-01 13:13:47.711 ERROR [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 562691
2025-08-01 13:15:36.655 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101479
2025-08-01 13:15:36.654 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101509
2025-08-01 13:15:36.657 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101576
2025-08-01 13:19:02.965 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 177145
2025-08-01 13:19:02.966 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 177214
2025-08-01 13:20:09.459 WARN [http-nio-20000-exec-4] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-category 对应的记录，跳过SysLog记录
2025-08-01 13:20:45.274 WARN [http-nio-20000-exec-6] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-item 对应的记录，跳过SysLog记录
2025-08-01 13:21:40.676 WARN [http-nio-20000-exec-9] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-item 对应的记录，跳过SysLog记录
2025-08-01 13:21:47.980 WARN [http-nio-20000-exec-7] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-item 对应的记录，跳过SysLog记录
2025-08-01 13:30:00.081 INFO [scheduling-1] c.d.forest.config.ForestConfiguration:22 - [Forest] Http Backend: okhttp3
2025-08-01 13:34:58.577 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 777157
2025-08-01 13:37:23.643 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 131390
2025-08-01 13:37:23.645 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 132728
2025-08-01 13:37:23.651 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 144677
2025-08-01 13:43:07.716 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 343953
2025-08-01 13:43:07.718 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 343987
