2025-08-01 13:02:52.125 INFO [ main] com.my.crossborder.App:84 - Tomcat Port 20000 is available. Starting application >> 
2025-08-01 13:02:52.130 INFO [ main] com.my.crossborder.App:655 - The following profiles are active: local,local-sa,local-db,local-forest,local-job
2025-08-01 13:02:54.087 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990AuthClient' and Proxy of 'com.my.crossborder.forest.erp990.Erp990AuthClient' client interface
2025-08-01 13:02:54.088 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990Client' and Proxy of 'com.my.crossborder.forest.erp990.Erp990Client' client interface
2025-08-01 13:02:54.088 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.crossborder.forest.siliconflow.SiliconflowClient' client interface
2025-08-01 13:04:17.782 INFO [ main] com.my.crossborder.App:84 - Tomcat Port 20000 is available. Starting application >> 
2025-08-01 13:04:17.789 INFO [ main] com.my.crossborder.App:655 - The following profiles are active: local,local-sa,local-db,local-forest,local-job
2025-08-01 13:04:19.444 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990AuthClient' and Proxy of 'com.my.crossborder.forest.erp990.Erp990AuthClient' client interface
2025-08-01 13:04:19.444 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990Client' and Proxy of 'com.my.crossborder.forest.erp990.Erp990Client' client interface
2025-08-01 13:04:19.444 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.crossborder.forest.siliconflow.SiliconflowClient' client interface
2025-08-01 13:04:20.984 WARN [ main] c.b.m.core.metadata.TableInfoHelper:335 - Can not find table primary key in Class: "com.my.crossborder.mybatis.entity.SysDictItem".
2025-08-01 13:04:22.584 INFO [ main] com.my.crossborder.cache.SysParamCache:70 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Pro/Qwen/Qwen2.5-VL-7B-Instruct","aiSystemPrompt":"-","aiUserPrompt":"本图片是网页登录页的验证码，它是一个数学公式，通常是加减乘除的计算。格式是【数值1 符号 数值2 = ?】，请帮我计算结果，仅返回计算结果即可。(如果识别失败返回0)","debugErp990Token":"eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE0N2YyYTRmLTIwYzMtNDNkNS1iZmM2LWVjYzRkMDE5NGQzYSJ9.ky28FBGqrWui4TSz9ELtJ3CHseB0IQ1-vlIsqhm5eRPtwiR8f28RDL14WAPOnZhnLITUyx9XT17Ui1QeVngNaQ","fileAllowExt":".jpg,.png,.jpeg","fileMaxSize":"3","fileUploadPath":"/home/<USER>/test/upload-images","orderSyncDays":"32","taiwanWithdrawFeeRatio":"0.9"}
2025-08-01 13:04:24.886 INFO [ main] com.my.crossborder.job.AttendanceJob:37 - 开始执行缺勤状态处理任务
2025-08-01 13:04:25.011 INFO [ main] com.my.crossborder.job.AttendanceJob:41 - 缺勤状态处理任务执行成功
2025-08-01 13:04:26.519 INFO [ main] com.my.crossborder.App:61 - Started App in 10.044 seconds (JVM running for 17.187)
2025-08-01 13:04:26.522 INFO [ main] com.my.crossborder.App:49 -  >> Web Server Started on port: 20000
2025-08-01 13:13:47.711 ERROR [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 562691
2025-08-01 13:15:36.655 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101479
2025-08-01 13:15:36.654 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101509
2025-08-01 13:15:36.657 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101576
2025-08-01 13:19:02.965 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 177145
2025-08-01 13:19:02.966 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 177214
2025-08-01 13:20:09.459 WARN [http-nio-20000-exec-4] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-category 对应的记录，跳过SysLog记录
2025-08-01 13:20:45.274 WARN [http-nio-20000-exec-6] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-item 对应的记录，跳过SysLog记录
2025-08-01 13:21:40.676 WARN [http-nio-20000-exec-9] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-item 对应的记录，跳过SysLog记录
2025-08-01 13:21:47.980 WARN [http-nio-20000-exec-7] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-item 对应的记录，跳过SysLog记录
2025-08-01 13:30:00.081 INFO [scheduling-1] c.d.forest.config.ForestConfiguration:22 - [Forest] Http Backend: okhttp3
2025-08-01 13:34:58.577 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 777157
2025-08-01 13:37:23.643 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 131390
2025-08-01 13:37:23.645 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 132728
2025-08-01 13:37:23.651 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 144677
2025-08-01 13:43:07.716 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 343953
2025-08-01 13:43:07.718 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 343987
2025-08-01 13:45:49.214 INFO [ main] com.my.crossborder.App:84 - Tomcat Port 20000 is available. Starting application >> 
2025-08-01 13:45:49.221 INFO [ main] com.my.crossborder.App:655 - The following profiles are active: local,local-sa,local-db,local-forest,local-job
2025-08-01 13:45:50.736 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990AuthClient' and Proxy of 'com.my.crossborder.forest.erp990.Erp990AuthClient' client interface
2025-08-01 13:45:50.737 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990Client' and Proxy of 'com.my.crossborder.forest.erp990.Erp990Client' client interface
2025-08-01 13:45:50.737 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.crossborder.forest.siliconflow.SiliconflowClient' client interface
2025-08-01 13:45:52.267 WARN [ main] c.b.m.core.metadata.TableInfoHelper:335 - Can not find table primary key in Class: "com.my.crossborder.mybatis.entity.SysDictItem".
2025-08-01 13:45:53.477 INFO [ main] com.my.crossborder.cache.SysParamCache:70 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Pro/Qwen/Qwen2.5-VL-7B-Instruct","aiSystemPrompt":"-","aiUserPrompt":"本图片是网页登录页的验证码，它是一个数学公式，通常是加减乘除的计算。格式是【数值1 符号 数值2 = ?】，请帮我计算结果，仅返回计算结果即可。(如果识别失败返回0)","debugErp990Token":"eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE0N2YyYTRmLTIwYzMtNDNkNS1iZmM2LWVjYzRkMDE5NGQzYSJ9.ky28FBGqrWui4TSz9ELtJ3CHseB0IQ1-vlIsqhm5eRPtwiR8f28RDL14WAPOnZhnLITUyx9XT17Ui1QeVngNaQ","fileAllowExt":".jpg,.png,.jpeg","fileMaxSize":"3","fileUploadPath":"/home/<USER>/test/upload-images","orderSyncDays":"32","taiwanWithdrawFeeRatio":"0.9"}
2025-08-01 13:45:55.564 INFO [ main] com.my.crossborder.job.AttendanceJob:37 - 开始执行缺勤状态处理任务
2025-08-01 13:45:55.902 INFO [ main] com.my.crossborder.job.AttendanceJob:41 - 缺勤状态处理任务执行成功
2025-08-01 13:45:57.341 INFO [ main] com.my.crossborder.App:61 - Started App in 9.207 seconds (JVM running for 10.312)
2025-08-01 13:45:57.343 INFO [ main] com.my.crossborder.App:49 -  >> Web Server Started on port: 20000
2025-08-01 13:53:22.442 ERROR [http-nio-20000-exec-4] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-shop/page, Err-detail:接口401
2025-08-01 13:53:22.442 ERROR [http-nio-20000-exec-6] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/ord-purchase/page, Err-detail:接口401
2025-08-01 13:53:22.442 ERROR [http-nio-20000-exec-1] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/erp-order/order-states, Err-detail:接口401
2025-08-01 13:53:22.442 ERROR [http-nio-20000-exec-3] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-dict-category/items, Err-detail:接口401
2025-08-01 13:53:22.442 ERROR [http-nio-20000-exec-2] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-08-01 13:53:22.844 ERROR [http-nio-20000-exec-5] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-dict-category/items, Err-detail:接口401
2025-08-01 13:53:25.256 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 449345
2025-08-01 14:00:00.067 INFO [scheduling-1] c.d.forest.config.ForestConfiguration:22 - [Forest] Http Backend: okhttp3
2025-08-01 14:02:38.843 INFO [ main] com.my.crossborder.App:84 - Tomcat Port 20000 is available. Starting application >> 
2025-08-01 14:02:38.849 INFO [ main] com.my.crossborder.App:655 - The following profiles are active: local,local-sa,local-db,local-forest,local-job
2025-08-01 14:02:41.034 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990AuthClient' and Proxy of 'com.my.crossborder.forest.erp990.Erp990AuthClient' client interface
2025-08-01 14:02:41.035 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'erp990Client' and Proxy of 'com.my.crossborder.forest.erp990.Erp990Client' client interface
2025-08-01 14:02:41.036 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.crossborder.forest.siliconflow.SiliconflowClient' client interface
2025-08-01 14:02:43.471 WARN [ main] c.b.m.core.metadata.TableInfoHelper:335 - Can not find table primary key in Class: "com.my.crossborder.mybatis.entity.SysDictItem".
2025-08-01 14:02:44.686 INFO [ main] com.my.crossborder.cache.SysParamCache:70 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Pro/Qwen/Qwen2.5-VL-7B-Instruct","aiSystemPrompt":"-","aiUserPrompt":"本图片是网页登录页的验证码，它是一个数学公式，通常是加减乘除的计算。格式是【数值1 符号 数值2 = ?】，请帮我计算结果，仅返回计算结果即可。(如果识别失败返回0)","debugErp990Token":"eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImE0N2YyYTRmLTIwYzMtNDNkNS1iZmM2LWVjYzRkMDE5NGQzYSJ9.ky28FBGqrWui4TSz9ELtJ3CHseB0IQ1-vlIsqhm5eRPtwiR8f28RDL14WAPOnZhnLITUyx9XT17Ui1QeVngNaQ","fileAllowExt":".jpg,.png,.jpeg","fileMaxSize":"3","fileUploadPath":"/home/<USER>/test/upload-images","orderSyncDays":"32","taiwanWithdrawFeeRatio":"0.9"}
2025-08-01 14:02:46.993 INFO [ main] com.my.crossborder.job.AttendanceJob:37 - 开始执行缺勤状态处理任务
2025-08-01 14:02:47.067 INFO [ main] com.my.crossborder.job.AttendanceJob:41 - 缺勤状态处理任务执行成功
2025-08-01 14:02:48.907 INFO [ main] com.my.crossborder.App:61 - Started App in 11.42 seconds (JVM running for 12.576)
2025-08-01 14:02:48.909 INFO [ main] com.my.crossborder.App:49 -  >> Web Server Started on port: 20000
2025-08-01 14:05:47.193 ERROR [http-nio-20000-exec-2] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-shop/page, Err-detail:接口401
2025-08-01 14:05:47.193 ERROR [http-nio-20000-exec-1] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-menu/tree, Err-detail:接口401
2025-08-01 14:05:47.306 ERROR [http-nio-20000-exec-4] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-08-01 14:05:47.306 ERROR [http-nio-20000-exec-3] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/erp-order/order-states, Err-detail:接口401
2025-08-01 14:05:47.308 ERROR [http-nio-20000-exec-5] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-dict-category/items, Err-detail:接口401
2025-08-01 14:05:47.311 ERROR [http-nio-20000-exec-6] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/ord-purchase/page, Err-detail:接口401
2025-08-01 14:05:47.590 ERROR [http-nio-20000-exec-8] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-dict-category/items, Err-detail:接口401
2025-08-01 14:05:47.590 ERROR [http-nio-20000-exec-7] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/sys-dict-category/items, Err-detail:接口401
2025-08-01 14:05:47.624 ERROR [http-nio-20000-exec-9] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/wkb-notification-reading/all-unread, Err-detail:接口401
2025-08-01 14:05:47.640 ERROR [http-nio-20000-exec-10] c.m.c.exception.ExceptionAspect:53 - Err-uri:http://localhost:20000/api/wkb-todo/all-unread, Err-detail:接口401
2025-08-01 14:05:50.609 ERROR [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 183532
2025-08-01 14:10:05.363 WARN [http-nio-20000-exec-10] c.m.c.aoplog.InterfaceLogAspect:101 - 在字典表中未找到controller关键字 sys-dict-category 对应的记录，跳过SysLog记录
2025-08-01 14:10:41.374 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ******************************************************************************************************************************************************************************************, jdbcUrl : ******************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 286369
