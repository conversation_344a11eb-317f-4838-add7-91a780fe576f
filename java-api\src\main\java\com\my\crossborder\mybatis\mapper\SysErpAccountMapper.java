package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountPageDTO;
import com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountPageVO;
import com.my.crossborder.mybatis.entity.SysErpAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 禾宸物流接口账号 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface SysErpAccountMapper extends BaseMapper<SysErpAccount> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysErpAccountPageVO> page(SysErpAccountPageDTO pageDTO);
	
}
