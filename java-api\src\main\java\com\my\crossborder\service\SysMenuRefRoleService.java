package com.my.crossborder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_menu.SysMenuGrantDTO;
import com.my.crossborder.mybatis.entity.SysMenuRefRole;

/**
 * 菜单_角色_关联表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface SysMenuRefRoleService extends IService<SysMenuRefRole> {


	/**
	 * 授权
	 * @param dto
	 */
	void grant(SysMenuGrantDTO dto);	

	/**
	 * 根据角色ID查询菜单ID列表
	 * @param roleId 角色ID
	 * @return 菜单ID列表
	 */
	List<String> getMenuIds(Integer roleId);

	/**
	 * 返回权限集合
	 * @return
	 */
	List<String> getPermissionList();

}
