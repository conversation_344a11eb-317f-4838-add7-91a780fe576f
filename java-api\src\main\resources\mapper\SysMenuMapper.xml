<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysMenu">
        <id column="id" property="id" />
        <result column="menu_name" property="menuName" />
        <result column="parent_id" property="parentId" />
        <result column="is_menu" property="menu" />
        <result column="route_path" property="routePath" />
        <result column="permission" property="permission" />
        <result column="sort_num" property="sortNum" />
        <result column="enable" property="enable" />
        <result column="icon" property="icon" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, menu_name, parent_id, is_menu, route_path, permission, sort_num, enable, icon, create_time, update_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_menu.SysMenuPageVO">
		SELECT
			id, menu_name, parent_id, is_menu, route_path, permission, sort_num, enable, icon, create_time, update_time
		FROM
			sys_menu AS t1
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="menuName != null and menuName != ''">
	           	AND t1.menu_name = #{menuName}
            </if>
	        <if test="parentId != null and parentId != ''">
	           	AND t1.parent_id = #{parentId}
            </if>
	        <if test="menu != null and menu != ''">
	           	AND t1.is_menu = #{menu}
            </if>
	        <if test="routePath != null and routePath != ''">
	           	AND t1.route_path = #{routePath}
            </if>
	        <if test="permission != null and permission != ''">
	           	AND t1.permission = #{permission}
            </if>
	        <if test="sortNum != null and sortNum != ''">
	           	AND t1.sort_num = #{sortNum}
            </if>
	        <if test="enable != null and enable != ''">
	           	AND t1.enable = #{enable}
            </if>
	        <if test="icon != null and icon != ''">
	           	AND t1.icon = #{icon}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
	        <if test="updateTime != null and updateTime != ''">
	           	AND t1.update_time = #{updateTime}
            </if>
        </where>
    </select>

</mapper>
