package com.my.crossborder.aopattachment;

import java.lang.reflect.Method;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.AttachmentIdListDTO;    
import com.my.crossborder.service.SysAttachmentService;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 附件ID service切面
 * 拦截指定service的insert 在实体新增成功后获取实体id
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class AttachmentServiceInsertAspect {
	
	private final SysAttachmentService sysAttachmentService;
	

    /**
     * 拦截多个service的insert方法
     */
    @Around("   "
		+ "  execution(* com.my.crossborder.service.OrdClaimService.insert(..)) "
    )
    public Object interceptServiceMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        String serviceName = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        AttachmentIdListDTO insertDTO = null;
        if (args.length > 0 && args[0] instanceof AttachmentIdListDTO) {
            insertDTO = (AttachmentIdListDTO) args[0];
        }
        
        // 执行原方法
        Object result = joinPoint.proceed();
        
        // 如果有附件ID列表，则处理附件关联
        if (insertDTO != null && CollectionUtil.isNotEmpty(insertDTO.getAttachmentIdList())) {
            try {
                // 获取最新插入记录的主键ID
                String primaryKeyId = getLatestPrimaryKeyId(joinPoint.getTarget(), serviceName);
                
                if (primaryKeyId != null) {
                    // 获取对应实体类的表名
                    String tableName = getEntityTableName(joinPoint.getTarget());
                    
                    // 调用附件服务更新数据源ID和表名
                    this.sysAttachmentService.fillDataSourceId(primaryKeyId, insertDTO.getAttachmentIdList(), tableName);
                    //log.info("成功为{}条附件设置数据源ID: {}", insertDTO.getAttachmentIdList().size(), primaryKeyId);
                }
            } catch (Exception e) {
                log.error("处理附件关联失败, 服务: {}, 方法: {}", serviceName, methodName, e);
            }
        }
        
        return result;
    }

    /**
     * 获取最新插入记录的主键ID
     */
    private String getLatestPrimaryKeyId(Object serviceInstance, String serviceName) {
        try {
            if (serviceInstance instanceof IService) {
                @SuppressWarnings("unchecked")
                IService<Object> service = (IService<Object>) serviceInstance;
                
                QueryWrapper<Object> queryWrapper = new QueryWrapper<>();
                
                // 根据不同的服务确定主键字段名和排序字段
                String primaryKeyField = getPrimaryKeyField(serviceName);
                String orderByField = getOrderByField(serviceName);
                
                queryWrapper.orderByDesc(orderByField).last("LIMIT 1");
                Object latestEntity = service.getOne(queryWrapper);
                
                if (latestEntity != null) {
                    // 使用反射获取主键值
                    Method getter = getGetterMethod(latestEntity.getClass(), primaryKeyField);
                    if (getter != null) {
                        Object primaryKeyValue = getter.invoke(latestEntity);
                        return primaryKeyValue != null ? primaryKeyValue.toString() : null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取最新主键ID失败: {}", serviceName, e);
        }
        return null;
    }

    /**
     * 根据服务名获取主键字段名
     */
    private String getPrimaryKeyField(String serviceName) {
        return "id";
    }

    /**
     * 根据服务名获取排序字段名（用于获取最新记录）
     */
    private String getOrderByField(String serviceName) {
        return "id";
    }

    /**
     * 获取getter方法
     */
    private Method getGetterMethod(Class<?> clazz, String fieldName) {
        try {
            String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            return clazz.getMethod(methodName);
        } catch (Exception e) {
            log.debug("获取getter方法失败: {}.{}", clazz.getSimpleName(), fieldName);
            return null;
        }
    }

    /**
     * 获取实体类的表名
     */
    private String getEntityTableName(Object serviceInstance) {
        try {
            if (serviceInstance instanceof IService) {
                // 获取泛型类型
                Class<?> entityClass = getEntityClass(serviceInstance);
                if (entityClass != null) {
                    // 从TableName注解获取表名
                    TableName tableNameAnnotation = entityClass.getAnnotation(TableName.class);
                    if (tableNameAnnotation != null) {
                        return tableNameAnnotation.value();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取表名失败", e);
        }
        return null;
    }
    
    /**
     * 获取Service的实体类类型
     */
    private Class<?> getEntityClass(Object serviceInstance) {
        try {
            // 获取IService接口的第一个泛型参数，即实体类
            Class<?> clazz = serviceInstance.getClass();
            // 遍历实现的接口，找到IService接口
            for (Class<?> itf : clazz.getInterfaces()) {
                if (IService.class.isAssignableFrom(itf)) {
                    // 获取第一个泛型参数
                    java.lang.reflect.Type[] genericInterfaces = clazz.getGenericInterfaces();
                    for (java.lang.reflect.Type type : genericInterfaces) {
                        if (type instanceof java.lang.reflect.ParameterizedType) {
                            java.lang.reflect.ParameterizedType parameterizedType = (java.lang.reflect.ParameterizedType) type;
                            java.lang.reflect.Type[] typeArguments = parameterizedType.getActualTypeArguments();
                            if (typeArguments.length > 0) {
                                // 第一个泛型参数就是实体类型
                                return (Class<?>) typeArguments[0];
                            }
                        }
                    }
                    break;
                }
            }
            
            // 如果直接接口没有找到，检查父类的泛型参数（处理ServiceImpl的情况）
            java.lang.reflect.Type genericSuperclass = clazz.getGenericSuperclass();
            if (genericSuperclass instanceof java.lang.reflect.ParameterizedType) {
                java.lang.reflect.ParameterizedType parameterizedType = (java.lang.reflect.ParameterizedType) genericSuperclass;
                java.lang.reflect.Type[] typeArguments = parameterizedType.getActualTypeArguments();
                if (typeArguments.length > 1) {
                    // ServiceImpl的第二个泛型参数是实体类型
                    return (Class<?>) typeArguments[1];
                }
            }
            
        } catch (Exception e) {
            log.error("获取实体类类型失败", e);
        }
        return null;
    }

} 