package com.my.crossborder.forest.siliconflow.dto.component;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class TextMessage 
					extends Message {

	private String role;
	
	private String content;
	
	
	private TextMessage() {
	}

	private TextMessage(String role, String content) {
		super();
		this.role = role;
		this.content = content;
	}
	
	/**
	 * 系统消息
	 * @param content
	 * @return
	 */
	public static TextMessage system(String content) {
		return new TextMessage("system", content);
	}
	
	/**
	 * 用户消息
	 * @param content
	 * @return
	 */
	public static TextMessage user(String content) {
		return new TextMessage("user", content);
	}
}
