package com.my.crossborder.service.impl;

import java.util.List;

import javax.validation.Valid;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.cache.SysUserCache;
import com.my.crossborder.controller.dto.sys_user.SysUserChangeMyPasswordDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserDeleteDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserInsertDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserPageDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserResetPwdDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserUpdateDTO;
import com.my.crossborder.controller.vo.sys_user.SysUserDetailVO;
import com.my.crossborder.controller.vo.sys_user.SysUserPageVO;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.mybatis.mapper.SysUserMapper;
import com.my.crossborder.service.SysUserService;
import com.my.crossborder.util.ColumnLambda;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.RequiredArgsConstructor;

/**
 * system user 服务实现类
 *
 * @date 2024-05-20
 */
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
	
	private static final String DEFAULT_PASSWORD = "123456";
	private final SysUserCache sysUserCache;


	@Override
	public SysUserDetailVO detail(Integer userId) {
		SysUser entity = this.sysUserCache.getById(userId);
		return BeanUtil.copyProperties(entity, SysUserDetailVO.class);
	}

	@Override
	public SysUser getByUsernameAndPassword(String username, String password) {
		// 使用MD5加密密码进行比较
		String encryptedPassword = DigestUtil.md5Hex(password);
		return this.baseMapper.getByUsernameAndPassword(username, encryptedPassword);
	}

	@Override
	public SysUser getById(Integer userId) {
		return this.sysUserCache.getById(userId);
	}

	@Override
	public void changeMyPassword(SysUserChangeMyPasswordDTO dto) {
		dto.validate();
		Integer userId = StpUtil.getLoginIdAsInt();
		String pwd = dto.getPassword();
		// 使用MD5加密密码
		String encryptedPwd = DigestUtil.md5Hex(pwd);
		this.baseMapper.updatePassword(userId, encryptedPwd);
	}
	
	@Transactional
	@Override
	public void insert(SysUserInsertDTO insertDTO) {
		String username = insertDTO.getUsername();
		Boolean exists = this.baseMapper.exists(username);
		BusinessException.when(exists, "用户名【{}】已存在", username);
		SysUser entity = BeanUtil.copyProperties(insertDTO, SysUser.class);
		entity.setDisable(Boolean.FALSE);
		entity.setStatus("1"); // 状态正常
		// 使用MD5加密密码
		String encryptedPwd = DigestUtil.md5Hex(insertDTO.getPassword());
		entity.setPassword(encryptedPwd);
		this.save(entity);
		
		// 重置缓存
		this.sysUserCache.reloadCache();
	}

	@Transactional
	@Override
	public void update(SysUserUpdateDTO updateDTO) {
		SysUser entity = BeanUtil.copyProperties(updateDTO, SysUser.class);
		this.baseMapper.updateById(entity);
		// 重置缓存
		this.sysUserCache.reloadCache();
	}

	@Override
	public Page<SysUserPageVO> page(SysUserPageDTO pageDTO) {
		// 仅显示未逻辑删除的
		pageDTO.setDisable(false); 
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<SysUser>().columnsToString(SysUser::getUserId)));
		}
		Page<SysUserPageVO> pageVO = this.baseMapper.page(pageDTO);
		return pageVO;
	}

	@Transactional
	@Override
	public void delete(SysUserDeleteDTO deleteDTO) {
		List<Integer> idList = deleteDTO.getIdList();
		Integer userId = idList.get(0);
		this.baseMapper.logicDelete(userId);
		// 重置缓存
		this.sysUserCache.reloadCache();
	}

	@Override
	public List<SysUser> listByRoleIdList(List<Integer> roleIdList) {
		return this.sysUserCache.listByRoleIdList(roleIdList);
	}

	@Override
	public List<SysUser> list(List<Integer> userIdList) {
		return this.sysUserCache.list(userIdList);
	}

	@Override
	public void resetPwd(@Valid SysUserResetPwdDTO dto) {
		// 使用MD5加密密码
		String encryptedPwd = DigestUtil.md5Hex(DEFAULT_PASSWORD);
		this.baseMapper.updatePassword(dto.getUserId(), encryptedPwd);
	}

}
