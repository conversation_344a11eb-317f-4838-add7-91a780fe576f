<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="handleDialogClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="120px">
      <el-form-item label="订单号" prop="orderSn">
        <order-selector v-model="form.orderSn" placeholder="请选择订单" @select="handleOrderSelect" style="width: 100%;"></order-selector>
      </el-form-item>

      <el-form-item label="问题类别" prop="issueType">
        <dict-select v-model="form.issueType" category-id="AFTERSALE_ISSUE_TYPE" placeholder="请选择问题类别"
          style="width: 100%;">
        </dict-select>
      </el-form-item>

      <el-form-item label="处理办法" prop="closeWay">
        <dict-select v-model="form.closeWay" category-id="AFTERSALE_CLOSE_WAY" placeholder="请选择处理办法"
          style="width: 100%;" @change="handleCloseWayChange">
        </dict-select>
      </el-form-item>

      <el-form-item label="新订单号" prop="newOrderSn" v-if="isNewOrderSnRequired">
        <order-selector v-model="form.newOrderSn" placeholder="请选择新订单" @select="handleNewOrderSelect" style="width: 100%;"></order-selector>
      </el-form-item>

      <el-form-item label="退款金额" prop="refundAmount" v-if="form.closeWay && form.closeWay === '2'">
        <el-input-number v-model="form.refundAmount" :precision="2" :step="0.01" :min="0.01" controls-position="right" style="width: 215px;" placeholder="请输入退款金额"></el-input-number>
        <span style="margin-left: 10px; color: #909399;">台币</span>
      </el-form-item>

      <el-form-item label="结算金额" prop="settlementAmount" v-if="form.closeWay && form.closeWay === '2'">
        <el-input-number v-model="form.settlementAmount" :precision="2" :step="0.01" :min="0.01" controls-position="right" style="width: 215px;" placeholder="请输入结算金额"></el-input-number>
        <span style="margin-left: 10px; color: #909399;">人民币</span>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import DictSelect from '../../components/DictSelect'
import OrderSelector from '../../components/OrderSelector'

export default {
  name: 'OrdAfterSaleEdit',
  components: {
    DictSelect,
    OrderSelector
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        id: null,
        orderSn: '',
        issueType: '',
        closeWay: '',
        refundAmount: '',
        settlementAmount: '',
        newOrderSn: '',
        closed: false
      },
      baseRules: {
        orderSn: [
          { required: true, message: '请输入订单号', trigger: 'blur' }
        ],
        issueType: [
          { required: true, message: '请选择问题类别', trigger: 'change' }
        ],
        closeWay: [
          { required: true, message: '请选择处理办法', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return this.isEdit ? '编辑售后服务' : '添加售后服务'
    },
    // 当处理方法选择"1"（补寄）时，退款金额禁用
    isRefundDisabled() {
      return this.form.closeWay === '1'
    },
    // 当处理方法选择"1"（补寄）时，新订单号必填
    isNewOrderSnRequired() {
      return this.form.closeWay === '1'
    },

    // 动态生成验证规则
    rules() {
      const rules = { ...this.baseRules }

      // 根据处理办法动态设置新订单号验证规则
      if (this.form.closeWay === '1') {
        // 补寄时，新订单号必填
        rules.newOrderSn = [
          { required: true, message: '请选择新订单号', trigger: 'blur' }
        ]
      }

      // 根据处理办法动态设置退款金额验证规则
      if (this.form.closeWay === '2') {
        // 退款时，退款金额必填且必须大于0
        rules.refundAmount = [
          { required: true, message: '请输入退款金额', trigger: 'blur' },
          { type: 'number', message: '退款金额必须为数字', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== undefined && value !== null && value !== '') {
                if (Number(value) <= 0) {
                  callback(new Error('退款金额必须大于0'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      } else {
        // 补寄或其他情况，退款金额不是必填项，但如果填写了必须大于0
        rules.refundAmount = [
          { type: 'number', message: '退款金额必须为数字', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== undefined && value !== null && value !== '') {
                if (Number(value) <= 0) {
                  callback(new Error('退款金额必须大于0'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }

      return rules
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    },
    editData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.initForm()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    initForm() {
      if (this.isEdit && this.editData) {
        this.form = { ...this.editData }
      } else {
        this.form = {
          id: null,
          orderSn: '',
          issueType: '',
          closeWay: '',
          refundAmount: '',
          settlementAmount: '',
          newOrderSn: '',
          closed: false
        }
      }
      // 清除验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    handleCloseWayChange(value) {
      // 当处理方法选择"1"（补寄）时，清空退款金额和结算金额
      if (value === '1') {
        this.form.refundAmount = ''
        this.form.settlementAmount = ''
      } else if (value === '2') {
        // 当处理方法选择"2"（退款）时，清空新订单号
        this.form.newOrderSn = ''
      } else {
        // 其他情况，清空所有相关字段
        this.form.refundAmount = ''
        this.form.settlementAmount = ''
        this.form.newOrderSn = ''
      }

      // 清除相关字段的验证状态，让新的验证规则生效
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['refundAmount', 'settlementAmount', 'newOrderSn'])
        }
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 格式化时间为后端期望的格式 (YYYY-MM-DD HH:mm:ss)
          const now = new Date()
          const issueTime = now.getFullYear() + '-' +
            String(now.getMonth() + 1).padStart(2, '0') + '-' +
            String(now.getDate()).padStart(2, '0') + ' ' +
            String(now.getHours()).padStart(2, '0') + ':' +
            String(now.getMinutes()).padStart(2, '0') + ':' +
            String(now.getSeconds()).padStart(2, '0')

          const formData = {
            ...this.form,
            issueTime: issueTime
          }

          // 如果是补寄且退款金额为空，设置为null
          if (this.form.closeWay === '1' && (!this.form.refundAmount || this.form.refundAmount === '')) {
            formData.refundAmount = null
          }

          this.$emit('submit', formData)
        } else {
          this.$message.warning('请完成必填项')
          return false
        }
      })
    },
    handleCancel() {
      this.dialogVisible = false
    },
    handleDialogClose() {
      this.$refs.form.resetFields()
    },

    // 处理订单选择
    handleOrderSelect(order) {
      // 订单选择后的处理逻辑，可以在这里添加额外的业务逻辑
      console.log('选择的订单:', order)
    },

    // 处理新订单选择
    handleNewOrderSelect(order) {
      // 新订单选择后的处理逻辑
      console.log('选择的新订单:', order)
    }
  }
}
</script>

<style scoped>
</style>
