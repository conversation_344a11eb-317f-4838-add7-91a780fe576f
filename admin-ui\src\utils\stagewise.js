/**
 * 简化版Stagewise工具 - 仅开发环境使用
 * 功能：标记元素，添加备注，复制备注，跨页面标记
 */

// 配置选项
const STAGEWISE_CONFIG = {
    // 是否启用Stagewise组件 - true启用（显示悬浮图标），false停用（不显示）
    enabled: false,
    // 是否显示标记元素的边框 - true显示边框和背景，false只显示标号
    showBorder: false,
    // 其他配置可以在这里添加
    onlyInDevelopment: true,
    position: { x: '75%', y: '20px' }
};

class SimpleStagewise {
    constructor() {
        this.isActive = false;
        this.markers = [];
        this.currentMarkerId = 0;
        this.selectedElement = null;
        this.currentModal = null;
        this.isToolbarCollapsed = false; // 默认展开以显示标记列表
        this.isDragging = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.toolbarStartX = 0;
        this.toolbarStartY = 0;
        this.init();
    }

    init() {
        // 检查是否启用Stagewise组件
        if (!STAGEWISE_CONFIG.enabled) {
            console.log('🎯 SimpleStagewise 已被禁用');
            return;
        }

        // 只在开发环境启用（如果配置了的话）
        if (STAGEWISE_CONFIG.onlyInDevelopment && process.env.NODE_ENV !== 'development') {
            return;
        }

        // 加载已保存的标记数据
        this.loadMarkersFromStorage();
        this.createToolbar();
        this.bindEvents();
        console.log('🎯 SimpleStagewise 已启动 - 按 Ctrl+Shift+S 激活/关闭，按 Ctrl+B 快速开启/关闭标记模式');
    }

    loadMarkersFromStorage() {
        try {
            const savedMarkers = localStorage.getItem('stagewise-markers');
            if (savedMarkers) {
                const markersData = JSON.parse(savedMarkers);
                // 加载标记数据，但不包含DOM引用
                this.markers = markersData.markers || [];
                this.currentMarkerId = markersData.currentMarkerId || 0;

                console.log(`SimpleStagewise: 从存储加载了 ${this.markers.length} 个标记`);

                // 重新创建当前页面的标记显示
                this.markers.forEach(marker => {
                    // 初始化为null，避免undefined引用
                    marker.element = null;
                    marker.markerElement = null;

                    // 只为当前页面的标记重建DOM元素
                    if (marker.pageUrl === window.location.href && marker.selector) {
                        this.recreateMarkerDisplay(marker);
                    }
                });
            }
        } catch (error) {
            console.warn('SimpleStagewise: 加载标记数据失败', error);
            this.markers = [];
            this.currentMarkerId = 0;
        }
    }

    saveMarkersToStorage() {
        try {
            // 创建不包含DOM引用的副本
            const safeMarkers = this.markers.map(marker => ({
                id: marker.id,
                note: marker.note,
                selector: marker.selector,
                cssClass: marker.cssClass,
                pageUrl: marker.pageUrl,
                timestamp: marker.timestamp
                // 不保存DOM引用
            }));

            const markersData = {
                markers: safeMarkers,
                currentMarkerId: this.currentMarkerId,
                lastSaved: new Date().toISOString()
            };
            localStorage.setItem('stagewise-markers', JSON.stringify(markersData));
        } catch (error) {
            console.warn('SimpleStagewise: 保存标记数据失败', error);
        }
    }

    recreateMarkerDisplay(marker) {
        try {
            // 尝试根据XPath找到元素
            const element = this.findElementByXPath(marker.selector);
            if (element) {
                marker.element = element;
                this.createMarker(marker);
            } else {
                // 如果找不到元素，确保marker没有无效的DOM引用
                marker.element = null;
                marker.markerElement = null;
            }
        } catch (error) {
            console.warn('SimpleStagewise: 重新创建标记显示失败', error);
            // 确保不会有无效引用
            marker.element = null;
            marker.markerElement = null;
        }
    }

    findElementByXPath(xpath) {
        try {
            const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            return result.singleNodeValue;
        } catch (error) {
            console.warn('SimpleStagewise: XPath查找失败', xpath, error);
            return null;
        }
    }

    createToolbar() {
        // 创建工具栏容器
        const toolbar = document.createElement('div');
        toolbar.id = 'stagewise-toolbar';
        toolbar.innerHTML = `
            <div class="stagewise-toolbar-header">
                <span class="stagewise-title">🎯 SimpleStagewise</span>
                <button id="stagewise-collapse" class="stagewise-collapse-btn">
                    <span class="collapse-icon">−</span>
                </button>
            </div>
            <div class="stagewise-toolbar-content" id="stagewise-toolbar-content">
                <div class="stagewise-toolbar-controls">
                    <button id="stagewise-toggle" class="stagewise-btn">
                        <span class="icon">🎯</span>
                        <span class="text">标记模式</span>
                    </button>
                    <button id="stagewise-export" class="stagewise-btn" disabled>
                        <span class="icon">📋</span>
                        <span class="text">复制备注</span>
                    </button>
                    <button id="stagewise-clear" class="stagewise-btn" disabled>
                        <span class="icon">🗑️</span>
                        <span class="text">清空</span>
                    </button>
                    <div class="stagewise-counter">
                        <span id="stagewise-count">0</span> 个标记
                    </div>
                </div>
                <div class="stagewise-markers-list" id="stagewise-markers-list">
                    <!-- 标记列表将在这里动态生成 -->
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #stagewise-toolbar {
                position: fixed;
                top: 20px;
                left: 75%;
                transform: translateX(-50%);
                z-index: 10000;
                background: #fff;
                border: 2px solid #409eff;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                display: none;
                min-width: 320px;
                max-width: 400px;
                transition: all 0.3s ease;
                user-select: none;
            }

            #stagewise-toolbar.dragging {
                transition: none;
                box-shadow: 0 8px 24px rgba(0,0,0,0.25);
                filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
            }

            .stagewise-toolbar-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                background: #409eff;
                color: white;
                border-radius: 6px 6px 0 0;
                cursor: move;
                position: relative;
            }

            .stagewise-toolbar-header:hover {
                background: #337ecc;
            }

            .stagewise-toolbar-header::before {
                content: '⋮⋮';
                position: absolute;
                left: 6px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 12px;
                opacity: 0.7;
                letter-spacing: -2px;
            }

            .stagewise-title {
                font-weight: bold;
                font-size: 13px;
                margin-left: 16px;
            }

            .stagewise-collapse-btn {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 16px;
                line-height: 1;
                transition: background 0.2s;
            }

            .stagewise-collapse-btn:hover {
                background: rgba(255,255,255,0.2);
            }

            .stagewise-toolbar-content {
                transition: all 0.3s ease;
                overflow: hidden;
            }

            .stagewise-toolbar-content.collapsed {
                height: 0;
                padding: 0;
                opacity: 0;
            }

            .stagewise-toolbar-controls {
                display: flex;
                align-items: center;
                padding: 8px;
                gap: 8px;
                flex-wrap: wrap;
                border-bottom: 1px solid #eee;
            }

            .stagewise-btn {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 6px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: #fff;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.2s;
                white-space: nowrap;
            }

            .stagewise-btn:hover:not(:disabled) {
                background: #f5f5f5;
                border-color: #409eff;
            }

            .stagewise-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .stagewise-btn.active {
                background: #409eff;
                color: white;
                border-color: #409eff;
            }

            .stagewise-counter {
                font-size: 12px;
                color: #666;
                padding: 0 8px;
                white-space: nowrap;
            }

            .stagewise-markers-list {
                max-height: 300px;
                overflow-y: auto;
                padding: 8px;
            }

            .stagewise-marker-item {
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                padding: 6px 8px;
                margin-bottom: 4px;
                background: #f8f9fa;
                border-radius: 4px;
                font-size: 12px;
                line-height: 1.4;
                border-left: 3px solid #409eff;
            }

            .stagewise-marker-item.current-page {
                background: #e8f4fd;
                border-left-color: #67c23a;
            }

            .stagewise-marker-content {
                flex: 1;
                margin-right: 8px;
            }

            .stagewise-marker-id {
                font-weight: bold;
                color: #409eff;
            }

            .stagewise-marker-note {
                color: #333;
                margin-top: 2px;
                word-break: break-word;
            }

            .stagewise-marker-url {
                color: #999;
                font-size: 10px;
                margin-top: 2px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .stagewise-marker-delete {
                background: #f56c6c;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 2px 6px;
                cursor: pointer;
                font-size: 10px;
                flex-shrink: 0;
                transition: background 0.2s;
            }

            .stagewise-marker-delete:hover {
                background: #e55353;
            }

            .stagewise-marker {
                position: absolute;
                background: rgba(64, 158, 255, 0.2);
                border: 2px solid #409eff;
                border-radius: 4px;
                pointer-events: none;
                z-index: 9999;
            }

            .stagewise-marker-no-border {
                background: transparent !important;
                border: none !important;
            }

            .stagewise-marker-label {
                position: absolute;
                top: -25px;
                left: 0;
                background: #409eff;
                color: white;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 11px;
                font-weight: bold;
                white-space: nowrap;
            }

            .stagewise-highlight {
                outline: 2px solid #409eff !important;
                outline-offset: 2px;
                cursor: crosshair !important;
            }

            .stagewise-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .stagewise-modal-content {
                background: white;
                border-radius: 8px;
                padding: 20px;
                width: 400px;
                max-width: 90vw;
            }

            .stagewise-modal h3 {
                margin: 0 0 15px 0;
                color: #333;
            }

            .stagewise-modal textarea {
                width: 100%;
                height: 100px;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                resize: vertical;
                box-sizing: border-box;
                font-family: inherit;
            }

            .stagewise-modal textarea:focus {
                outline: none;
                border-color: #409eff;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }

            .stagewise-modal-buttons {
                display: flex;
                gap: 10px;
                justify-content: flex-end;
                margin-top: 15px;
            }

            .stagewise-modal-btn {
                padding: 8px 16px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.2s;
            }

            .stagewise-modal-btn:hover {
                background: #f5f5f5;
            }

            .stagewise-modal-btn.primary {
                background: #409eff;
                color: white;
                border-color: #409eff;
            }

            .stagewise-modal-btn.primary:hover {
                background: #337ecc;
                border-color: #337ecc;
            }

            .stagewise-toast {
                position: fixed;
                top: 80px;
                right: 20px;
                background: #67c23a;
                color: white;
                padding: 12px 16px;
                border-radius: 4px;
                z-index: 10002;
                font-size: 14px;
                animation: slideIn 0.3s ease;
            }

            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(toolbar);

        // 显示工具栏
        setTimeout(() => {
            toolbar.style.display = 'block';
            // 更新标记列表显示
            this.updateMarkersDisplay();
            this.updateCounter();
        }, 100);
    }

    bindEvents() {
        // 快捷键 Ctrl+Shift+S 和 Ctrl+B
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                this.toggleMarkingMode();
            } else if (e.ctrlKey && e.key === 'b') {
                e.preventDefault();
                this.toggleMarkingMode();
            }
        });

        // 工具栏按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('#stagewise-toggle')) {
                this.toggleMarkingMode();
            } else if (e.target.closest('#stagewise-export')) {
                this.exportNotes();
            } else if (e.target.closest('#stagewise-clear')) {
                this.clearAllMarkers();
            } else if (e.target.closest('#stagewise-collapse')) {
                this.toggleToolbar();
            }
        });

        // 拖动功能事件
        this.bindDragEvents();

        // 元素点击事件
        document.addEventListener('click', (e) => {
            if (this.isActive &&
                !e.target.closest('#stagewise-toolbar') &&
                !e.target.closest('.stagewise-modal') &&
                !e.target.closest('.stagewise-marker')) {
                e.preventDefault();
                e.stopPropagation();
                this.selectElement(e.target);
            }
        }, true);

        // 鼠标悬停高亮
        document.addEventListener('mouseover', (e) => {
            if (this.isActive &&
                !e.target.closest('#stagewise-toolbar') &&
                !e.target.closest('.stagewise-modal') &&
                !e.target.closest('.stagewise-marker')) {
                this.highlightElement(e.target);
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (this.isActive &&
                !e.target.closest('#stagewise-toolbar') &&
                !e.target.closest('.stagewise-modal') &&
                !e.target.closest('.stagewise-marker')) {
                this.removeHighlight(e.target);
            }
        });
    }

    toggleMarkingMode() {
        this.isActive = !this.isActive;
        const toggleBtn = document.getElementById('stagewise-toggle');

        if (this.isActive) {
            toggleBtn.classList.add('active');
            toggleBtn.querySelector('.text').textContent = '退出标记';
            document.body.style.cursor = 'crosshair';
            this.showToast('标记模式已激活，点击元素添加备注');
        } else {
            toggleBtn.classList.remove('active');
            toggleBtn.querySelector('.text').textContent = '标记模式';
            document.body.style.cursor = '';
            this.removeAllHighlights();
            this.showToast('标记模式已关闭');
        }
    }

    highlightElement(element) {
        if (element.classList.contains('stagewise-highlight')) return;
        element.classList.add('stagewise-highlight');
    }

    removeHighlight(element) {
        element.classList.remove('stagewise-highlight');
    }

    removeAllHighlights() {
        document.querySelectorAll('.stagewise-highlight').forEach(el => {
            el.classList.remove('stagewise-highlight');
        });
    }

    selectElement(element) {
        this.selectedElement = element;
        this.showNoteModal();
    }

    showNoteModal() {
        const cssClass = this.selectedElement.className || '(无CSS类)';
        const modal = document.createElement('div');
        modal.className = 'stagewise-modal';
        modal.innerHTML = `
            <div class="stagewise-modal-content">
                <h3>添加备注</h3>
                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
                    元素XPath: <code style="background: #f5f5f5; padding: 2px 4px; border-radius: 2px;">${this.getElementSelector(this.selectedElement)}</code>
                </div>
                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
                    CSS类: <code style="background: #f0f9ff; padding: 2px 4px; border-radius: 2px; color: #1d4ed8;">${cssClass}</code>
                </div>
                <textarea id="stagewise-note-input" placeholder="请输入备注内容..."></textarea>
                <div class="stagewise-modal-buttons">
                    <button class="stagewise-modal-btn" id="stagewise-cancel-btn">取消</button>
                    <button class="stagewise-modal-btn primary" id="stagewise-save-btn">保存</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 聚焦到输入框
        const noteInput = document.getElementById('stagewise-note-input');
        noteInput.focus();

        // 绑定按钮事件
        const cancelBtn = document.getElementById('stagewise-cancel-btn');
        const saveBtn = document.getElementById('stagewise-save-btn');

        cancelBtn.addEventListener('click', () => {
            this.closeModal();
        });

        saveBtn.addEventListener('click', () => {
            this.saveNote();
        });

        // 点击模态框背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });

        // ESC键关闭
        const closeModal = (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                document.removeEventListener('keydown', closeModal);
            } else if (e.key === 'Enter' && e.ctrlKey) {
                // Ctrl+Enter 快速保存
                e.preventDefault();
                this.saveNote();
            }
        };
        document.addEventListener('keydown', closeModal);

        // 存储模态框引用，用于后续操作
        this.currentModal = modal;
    }

    saveNote() {
        const noteInput = document.getElementById('stagewise-note-input');
        if (!noteInput) {
            this.showToast('输入框未找到', 'error');
            return;
        }

        const note = noteInput.value.trim();

        if (!note) {
            this.showToast('请输入备注内容', 'warning');
            noteInput.focus();
            return;
        }

        const markerId = ++this.currentMarkerId;
        const marker = {
            id: markerId,
            element: this.selectedElement,
            note: note,
            selector: this.getElementSelector(this.selectedElement),
            cssClass: this.selectedElement.className || '',
            pageUrl: window.location.href,
            timestamp: new Date().toLocaleString()
        };

        // 先关闭模态框，避免后续操作导致问题
        this.closeModal();

        this.markers.push(marker);
        this.createMarker(marker);

        // 保存到localStorage
        this.saveMarkersToStorage();

        // 更新显示
        this.updateMarkersDisplay();
        this.updateCounter();

        this.showToast(`备注已添加 (#${markerId})`);
    }

    createMarker(marker) {
        // 只为当前页面的标记创建视觉标记
        if (marker.pageUrl !== window.location.href || !marker.element) {
            return;
        }

        try {
            const rect = marker.element.getBoundingClientRect();
            const markerEl = document.createElement('div');

            // 根据配置决定样式类
            if (STAGEWISE_CONFIG.showBorder) {
                markerEl.className = 'stagewise-marker';
            } else {
                markerEl.className = 'stagewise-marker stagewise-marker-no-border';
            }

            markerEl.style.left = (rect.left + window.scrollX) + 'px';
            markerEl.style.top = (rect.top + window.scrollY) + 'px';
            markerEl.style.width = rect.width + 'px';
            markerEl.style.height = rect.height + 'px';

            const label = document.createElement('div');
            label.className = 'stagewise-marker-label';
            label.textContent = `#${marker.id}`;
            markerEl.appendChild(label);

            document.body.appendChild(markerEl);
            marker.markerElement = markerEl;
        } catch (error) {
            console.warn(`SimpleStagewise: 创建标记 #${marker.id} 的视觉元素失败`, error);
            // 确保无效引用
            marker.markerElement = null;
        }
    }

    updateCounter() {
        const totalCount = this.markers.length;
        const currentPageCount = this.markers.filter(m => m.pageUrl === window.location.href).length;

        document.getElementById('stagewise-count').textContent =
            `${totalCount} (当前页面: ${currentPageCount})`;

        const exportBtn = document.getElementById('stagewise-export');
        const clearBtn = document.getElementById('stagewise-clear');

        if (this.markers.length > 0) {
            exportBtn.disabled = false;
            clearBtn.disabled = false;
        } else {
            exportBtn.disabled = true;
            clearBtn.disabled = true;
        }
    }

    exportNotes() {
        if (this.markers.length === 0) {
            this.showToast('没有备注可以导出', 'warning');
            return;
        }

        try {
            // 直接使用增强格式导出，不显示选择对话框
            this.doExport('enhanced');
        } catch (error) {
            console.error('导出备注失败:', error);
            this.showToast('导出备注失败，请查看控制台', 'error');
        }
    }

    doExport(format) {
        // 构建JSON格式的导出数据
        const exportData = {
            // pageUrl: window.location.href,
            exportTime: new Date().toISOString(),
            totalMarkers: this.markers.length,
            markers: this.markers.map(marker => ({
                id: marker.id,
                xpath: marker.selector,
                cssClass: marker.cssClass || '',
                content: marker.note,
                pageUrl: marker.pageUrl,
                timestamp: marker.timestamp,
                elementTag: marker.element ? marker.element.tagName.toLowerCase() : 'unknown',
                elementText: marker.element && marker.element.textContent ?
                    marker.element.textContent.trim().substring(0, 50) : ''
            }))
        };

        // 直接使用增强格式
        const exportText = JSON.stringify(exportData, null, 2);
        const exportContent = `请按照粘贴的内容进行修改，这是一个SimpleStagewise工具导出的页面元素标记数据。

作为Cursor AI编程助手，请帮我根据以下JSON数据中的每个标记点进行精确的代码修改：

${exportText}

🎯 Cursor执行指南：
=================

📋 任务概述：
这是从前端页面收集的${this.markers.length}个UI元素修改标记，每个标记都指向具体的DOM元素，包含精确的定位信息和修改要求。

🔍 标记字段详解：
- **id**: 标记编号（便于跟踪）
- **xpath**: 元素XPath路径（DOM精确定位）
- **cssClass**: 元素CSS类名（辅助定位和样式定位）
- **content**: 具体修改要求 ⭐（最重要：这是你需要执行的具体指令）
- **pageUrl**: 页面URL（确定文件位置和路由）
- **elementTag**: HTML标签类型（如div、button、span等）
- **elementText**: 元素文本内容（辅助确认定位准确性）
- **timestamp**: 标记创建时间

🚀 执行流程：
1. **分析pageUrl** → 根据URL路径确定对应的页面组件文件
2. **使用xpath和cssClass** → 在组件文件中精确定位到目标元素
3. **解析content内容** → 理解具体的修改需求（文本修改、样式调整、功能变更等）
4. **执行代码修改** → 在找到的元素位置进行精确修改
5. **验证修改** → 确保修改符合Vue/React组件规范

💡 定位策略：
- 优先使用**cssClass**结合**elementTag**在组件中搜索对应元素
- 使用**xpath**信息理解元素在DOM树中的层级关系
- 使用**elementText**内容确认定位到正确的元素
- 对于动态内容，重点关注**cssClass**和组件结构

⚠️ 重要提醒：
- **content字段是核心**：这里包含了具体要执行的修改指令
- **保持代码结构**：只修改必要部分，保持组件完整性
- **注意样式一致性**：如果涉及样式修改，确保与整体UI风格协调
- **测试兼容性**：修改后的代码应该能正常运行

📊 本次导出统计：
- 导出时间: ${new Date().toLocaleString()}
- 标记总数: ${this.markers.length}个
- 涉及页面: ${[...new Set(this.markers.map(m => m.pageUrl))].length}个
- 工具版本: SimpleStagewise v1.1 Enhanced

🔥 开始执行：
请逐个处理markers数组中的每个标记，优先处理当前工作目录中能找到的页面组件文件。如有疑问请详细说明具体的定位或修改困难。`;

        // 复制到剪贴板
        navigator.clipboard.writeText(exportContent).then(() => {
            this.showToast(`已复制 ${this.markers.length} 个备注到剪贴板 (增强格式)`);
        }).catch(() => {
            // 降级方案
            const textarea = document.createElement('textarea');
            textarea.value = exportContent;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            this.showToast(`已复制 ${this.markers.length} 个备注到剪贴板 (增强格式)`);
        });
    }

    clearAllMarkers() {
        if (this.markers.length === 0) return;

        try {
            if (confirm(`确定要清空所有 ${this.markers.length} 个备注吗？`)) {
                // 安全地删除所有视觉标记
                this.markers.forEach(marker => {
                    // 确保markerElement存在且是DOM元素
                    if (marker.markerElement && typeof marker.markerElement.remove === 'function') {
                        marker.markerElement.remove();
                    } else if (marker.markerElement && marker.markerElement.parentNode) {
                        // 备用方法：使用parentNode.removeChild
                        marker.markerElement.parentNode.removeChild(marker.markerElement);
                    }
                    // 如果上述方法都不可行，只是忽略视觉删除，但仍然从数组中移除
                });

                // 移除所有DOM上的标记元素（以防万一）
                document.querySelectorAll('.stagewise-marker').forEach(el => {
                    el.parentNode.removeChild(el);
                });

                this.markers = [];
                this.currentMarkerId = 0;

                // 清空localStorage
                this.saveMarkersToStorage();

                // 更新显示
                this.updateMarkersDisplay();
                this.updateCounter();

                this.showToast('所有备注已清空');
            }
        } catch (error) {
            console.error('清空备注失败:', error);
            this.showToast('清空备注失败，请查看控制台', 'error');
        }
    }

    getElementXPath(element) {
        // 生成元素的XPath
        if (element === document.body) {
            return '/html/body';
        }

        let path = '';
        let current = element;

        while (current && current !== document.documentElement) {
            let tagName = current.tagName.toLowerCase();

            if (current.id) {
                // 如果有ID，直接使用ID定位
                path = `//${tagName}[@id='${current.id}']` + path;
                break;
            } else {
                // 计算同级元素中的位置
                let siblings = Array.from(current.parentNode.children);
                let sameTagSiblings = siblings.filter(sibling =>
                    sibling.tagName.toLowerCase() === tagName
                );

                if (sameTagSiblings.length === 1) {
                    path = `/${tagName}` + path;
                } else {
                    let index = sameTagSiblings.indexOf(current) + 1;
                    path = `/${tagName}[${index}]` + path;
                }
            }

            current = current.parentNode;
        }

        return '/html' + path;
    }

    getElementSelector(element) {
        // 保留原有方法作为备用，但主要使用XPath
        return this.getElementXPath(element);
    }

    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = 'stagewise-toast';
        toast.textContent = message;

        if (type === 'warning') {
            toast.style.background = '#e6a23c';
        } else if (type === 'error') {
            toast.style.background = '#f56c6c';
        }

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    closeModal() {
        if (this.currentModal) {
            this.currentModal.remove();
            this.currentModal = null;
        }
    }

    toggleToolbar() {
        this.isToolbarCollapsed = !this.isToolbarCollapsed;
        const content = document.getElementById('stagewise-toolbar-content');
        const collapseBtn = document.getElementById('stagewise-collapse');
        const collapseIcon = collapseBtn.querySelector('.collapse-icon');

        if (this.isToolbarCollapsed) {
            content.classList.add('collapsed');
            collapseIcon.textContent = '+';
        } else {
            content.classList.remove('collapsed');
            collapseIcon.textContent = '−';
        }
    }

    bindDragEvents() {
        const toolbar = document.getElementById('stagewise-toolbar');
        const header = toolbar.querySelector('.stagewise-toolbar-header');

        let isDragging = false;
        let startX, startY, initialLeft, initialTop;

        header.addEventListener('mousedown', (e) => {
            if (e.target.closest('.stagewise-collapse-btn')) {
                return; // 不要在折叠按钮上启动拖动
            }

            isDragging = true;
            this.isDragging = true;

            startX = e.clientX;
            startY = e.clientY;

            const rect = toolbar.getBoundingClientRect();
            initialLeft = rect.left;
            initialTop = rect.top;

            toolbar.classList.add('dragging');

            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            e.preventDefault();

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            let newLeft = initialLeft + deltaX;
            let newTop = initialTop + deltaY;

            // 边界检查，确保工具栏不会移出屏幕
            const maxLeft = window.innerWidth - toolbar.offsetWidth;
            const maxTop = window.innerHeight - toolbar.offsetHeight;

            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));

            // 移除transform，使用left和top定位
            toolbar.style.transform = '';
            toolbar.style.left = newLeft + 'px';
            toolbar.style.top = newTop + 'px';
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                this.isDragging = false;
                toolbar.classList.remove('dragging');
            }
        });

        // 防止拖动时选中文本
        header.addEventListener('selectstart', (e) => {
            e.preventDefault();
        });
    }

    // 添加回误删的方法：更新标记列表显示
    updateMarkersDisplay() {
        const markersList = document.getElementById('stagewise-markers-list');
        if (!markersList) return;

        markersList.innerHTML = '';

        this.markers.forEach(marker => {
            const item = document.createElement('div');
            item.className = 'stagewise-marker-item';

            // 截取备注内容，避免太长
            const noteText = marker.note.length > 30 ?
                marker.note.substring(0, 30) + '...' : marker.note;

            // 显示页面URL（简化）
            const urlDisplay = marker.pageUrl === window.location.href ?
                '当前页面' : new URL(marker.pageUrl).pathname;

            item.innerHTML = `
                <div class="stagewise-marker-content">
                    <div><span class="stagewise-marker-id">#${marker.id}</span> ${noteText}</div>
                    <div class="stagewise-marker-url">${urlDisplay}</div>
                </div>
                <button class="stagewise-marker-delete" data-marker-id="${marker.id}">×</button>
            `;

            if (marker.pageUrl === window.location.href) {
                item.classList.add('current-page');
            }

            // 绑定删除事件
            const deleteBtn = item.querySelector('.stagewise-marker-delete');
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.removeMarker(marker.id);
            });

            markersList.appendChild(item);
        });
    }

    // 添加回误删的方法：删除单个标记
    removeMarker(markerId) {
        const marker = this.markers.find(m => m.id === markerId);
        if (!marker) return;
        try {
            // 安全地删除视觉标记
            if (marker.markerElement && typeof marker.markerElement.remove === 'function') {
                marker.markerElement.remove();
            } else if (marker.markerElement && marker.markerElement.parentNode) {
                marker.markerElement.parentNode.removeChild(marker.markerElement);
            }

            // 从数组中移除
            this.markers = this.markers.filter(m => m.id !== markerId);

            // 保存到localStorage
            this.saveMarkersToStorage();

            // 更新显示
            this.updateMarkersDisplay();
            this.updateCounter();

            this.showToast(`标记 #${markerId} 已删除`);
        } catch (error) {
            console.error(`删除标记 #${markerId} 失败:`, error);
            this.showToast(`删除标记失败，请查看控制台`, 'error');
        }
    }
}

// 初始化
if (typeof window !== 'undefined') {
    window.stagewise = new SimpleStagewise();
}

export default SimpleStagewise;

