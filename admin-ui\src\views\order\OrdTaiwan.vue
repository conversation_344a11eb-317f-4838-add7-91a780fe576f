<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>台湾上架管理</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="店铺">
          <el-select v-model="searchForm.shopIds" placeholder="请选择店铺" clearable multiple style="width: 180px;">
            <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="原订单号">
          <el-input v-model="searchForm.originalOrderNumber" placeholder="请输入原订单号" clearable style="width: 180px;"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
            <el-option label="待上架" :value="1"></el-option>
            <el-option label="已上架" :value="2"></el-option>
            <el-option label="已转单" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button v-permission="['ord-taiwan:insert']" type="primary" icon="el-icon-plus" @click="handleAddItem">添加</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="shelfItemsList" style="width: 100%" v-loading="loading" border>
        <el-table-column prop="originalShopName" label="原店铺名称" width="110" align="center" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column prop="originalShopId" label="原订单店铺号" width="120" align="center" show-overflow-tooltip></el-table-column> -->
        <el-table-column prop="originalOrderNumber" label="原订单号" width="160" align="center">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
                  @click="copyOrderSn(scope.row.originalOrderNumber)"
                  :title="'点击复制原订单号: ' + scope.row.originalOrderNumber">
              {{ scope.row.originalOrderNumber }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" width="210" align="center">
          <template slot-scope="scope">
            <el-popover
              placement="top"
              width="300"
              trigger="hover"
              :disabled="!scope.row.productName">
              <div slot="reference" class="product-name-cell">
                <span class="product-name-text clickable-item-name"
                      @click="copyItemName(scope.row.productName)"
                      :title="'点击复制商品名称: ' + scope.row.productName">{{ scope.row.productName }}</span>
              </div>
              <!-- Popover内容 -->
              <div class="product-preview-content">
                <div class="product-image-section">
                  <img :src="scope.row.productImage || '/static/img/default-product.png'"
                       style="width: 80px; height: 80px; object-fit: cover; border-radius: 4px; border: 1px solid #e4e7ed;"
                       @error="handleImageError" />
                </div>
                <div class="product-info-section">
                  <div class="product-detail-row">
                    <span class="product-label">商品名称：</span>
                    <span class="product-value">{{ scope.row.productName || '-' }}</span>
                  </div>
                  <div class="product-detail-row">
                    <span class="product-label">商品规格：</span>
                    <span class="product-value">{{ scope.row.productSpec || '-' }}</span>
                  </div>
                  <div class="product-detail-row">
                    <span class="product-label">商品价格：</span>
                    <span class="product-value product-price">{{ scope.row.productPrice ? '¥ ' + scope.row.productPrice : '-' }}</span>
                  </div>
                  <div class="product-detail-row">
                    <span class="product-label">商品数量：</span>
                    <span class="product-value">{{ scope.row.amount || '-' }}</span>
                  </div>
                </div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="productSpec" label="商品规格" width="120" align="center" show-overflow-tooltip></el-table-column> -->
        <el-table-column prop="amount" label="商品数量" width="80" align="center"></el-table-column>
        <el-table-column prop="waybillNumber" label="退货单号" width="140" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.waybillNumber"
                  class="waybill-no clickable-waybill"
                  @click="copyWaybillNo(scope.row.waybillNumber)"
                  :title="'点击复制退货单号: ' + scope.row.waybillNumber">
              {{ scope.row.waybillNumber }}
            </span>
            <span v-else class="no-waybill">未填写</span>
          </template>
        </el-table-column>
        <dict-table-column prop="status" label="状态" category-id="TAIWAN_GOODS_STATUS" width="90" show-overflow-tooltip></dict-table-column>
        <el-table-column prop="warehouseNumber" label="台湾仓柜号" width="120" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="onlineTime" label="上架时间" width="100" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="offlineTime" label="下架时间" width="100" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" min-width="150" align="center" >
          <template slot-scope="scope">
            <el-button v-permission="['ord-taiwan:update']" type="text" size="mini" @click="handleEdit(scope.row)" icon="el-icon-edit">编辑</el-button>
            <el-button v-permission="['ord-taiwan:delete']" type="text" size="mini" @click="handleDelete(scope.row)" icon="el-icon-delete" style="color: #F56C6C">删除</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-table-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

    <!-- 添加/编辑物品对话框 -->
    <OrdTaiwanEdit
      :visible.sync="itemDialogVisible"
      :editData="currentItemData"
      :isEdit="isEditItem"
      @submit="handleItemSubmit"
    />
  </div>
</template>

<script>
import Pagination from '../../components/Pagination'
import OrdTaiwanEdit from './OrdTaiwanEdit'
import { getAllEnabledShops } from '../../api/SysShop'
import {
  getOrdTaiwanPage,
  deleteOrdTaiwan
} from '@/api/OrdTaiwan'
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'OrdTaiwan',
  mixins: [copyMixin],
  components: {
    Pagination,
    OrdTaiwanEdit
  },
  data() {
    return {
      loading: false,
      // 搜索表单数据
      searchForm: {
        current: 1,
        size: 10,
        shopIds: [],
        originalOrderNumber: '',
        status: ''
      },
      // 表格数据
      shelfItemsList: [],
      // 店铺列表
      shopList: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      allShelfItemsData: [],
      orderNumbersList: [], // 订单号列表，用于自动补全

      // 编辑对话框相关
      itemDialogVisible: false,
      currentItemData: {},
      isEditItem: false
    }
  },
  created() {
    // 只在页面初始化时加载主要数据
    this.loadData()
    this.loadShops()
    // 订单号列表延迟加载，只在需要时才加载
  },
  methods: {
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = {
          current: this.searchForm.current,
          size: this.searchForm.size,
          shopIds: this.searchForm.shopIds && this.searchForm.shopIds.length > 0 ? this.searchForm.shopIds.map(id => String(id)) : undefined,
          originalOrderNumber: this.searchForm.originalOrderNumber || undefined,
          status: this.searchForm.status || undefined
        }
      }

      getOrdTaiwanPage(parameter)
        .then(res => {
          this.loading = false
          if (res.success && res.data) {
            this.shelfItemsList = res.data.records
            this.pageParam.currentPage = res.data.current
            this.pageParam.pageSize = res.data.size
            this.pageParam.total = res.data.total
          }
        })
        .catch(err => {
          this.loading = false
          this.$message.error('获取数据失败：' + err.message)
        })
    },

    // 加载数据（保持向后兼容）
    loadData() {
      this.getPageData()
    },

    // 加载店铺数据
    loadShops() {
      getAllEnabledShops()
        .then(res => {
          if (res.success && res.data && res.data.records) {
            this.shopList = res.data.records
          }
        })
        .catch(err => {
          console.error('加载店铺数据失败：', err)
        })
    },

    // 分页回调
    callback_getPageData(parm) {
      this.searchForm.current = parm.currentPage
      this.searchForm.size = parm.pageSize
      this.getPageData()
    },

    // 懒加载订单号列表（只在第一次使用时加载）
    loadOrderNumbers() {
      if (this.orderNumbersList.length > 0) {
        return // 如果已经加载过，直接返回
      }

      console.log('开始加载订单号列表...')
      // 减少模拟数据数量，提高性能
      this.orderNumbersList = []
      for (let i = 1; i <= 10; i++) { // 从30减少到10
        this.orderNumbersList.push({
          value: `ORD${String(i).padStart(6, '0')}`,
          shopName: `测试店铺${i}`,
          shopId: `SHOP${String(i).padStart(4, '0')}`
        })
      }
      console.log('订单号列表加载完成，数量:', this.orderNumbersList.length)
    },

    // 订单号自动补全查询（懒加载）
    queryOrderNumbers(queryString, cb) {
      // 第一次使用时才加载订单号列表
      this.loadOrderNumbers()

      const results = queryString
        ? this.orderNumbersList.filter(item =>
            item.value.toLowerCase().indexOf(queryString.toLowerCase()) > -1)
        : this.orderNumbersList

      cb(results)
    },



    // 生成测试数据
    generateTestData() {
      if (this.allShelfItemsData.length === 0) {
        const shopNames = ['亚马逊台湾', '蝦皮', '露天', 'momo购物']

        for (let i = 1; i <= 20; i++) {
          const shopName = shopNames[Math.floor(Math.random() * shopNames.length)]
          const shopId = `SHOP${String(Math.floor(Math.random() * 1000)).padStart(4, '0')}`

          // 生成随机状态：1-待上架，2-已上架，3-已转单
          const shelfStatus = Math.floor(Math.random() * 3) + 1
          const isOnShelf = shelfStatus === 2

          let shelfTime = null
          let offShelfTime = null
          let taiwanWarehouseShelfId = ''

          if (isOnShelf) {
            // 生成上架时间（过去1-30天内随机时间）
            const shelfDate = new Date()
            shelfDate.setDate(shelfDate.getDate() - Math.floor(Math.random() * 30) - 1)
            shelfTime = shelfDate.toISOString()

            // 部分记录有下架时间
            if (i % 5 === 0) {
              const offShelfDate = new Date(shelfDate)
              offShelfDate.setDate(offShelfDate.getDate() + Math.floor(Math.random() * 15) + 1)
              offShelfTime = offShelfDate.toISOString()
            }

            taiwanWarehouseShelfId = `A${Math.floor(Math.random() * 10)}-${Math.floor(Math.random() * 100)}`
          }

          this.allShelfItemsData.push({
            id: i,
            originalShopName: shopName,
            originalShopId: shopId,
            originalOrderNumber: `ORD${String(i).padStart(6, '0')}`,
            productName: `测试商品${i}`,
            productQuantity: Math.floor(Math.random() * 10) + 1,
            returnOrderNumber: `RTN${String(i).padStart(6, '0')}`,
            isOnShelf: isOnShelf,
            shelfStatus: shelfStatus, // 添加状态字段：1-待上架，2-已上架，3-已转单
            taiwanWarehouseShelfId: taiwanWarehouseShelfId,
            shelfTime: shelfTime,
            offShelfTime: offShelfTime
          })
        }
      }
    },

    // 过滤数据
    filterData() {
      let filteredData = [...this.allShelfItemsData]

      if (this.searchForm.shopName) {
        filteredData = filteredData.filter(item =>
          item.originalShopName.includes(this.searchForm.shopName)
        )
      }

      if (this.searchForm.originalOrderNumber) {
        filteredData = filteredData.filter(item =>
          item.originalOrderNumber.includes(this.searchForm.originalOrderNumber)
        )
      }

      if (this.searchForm.productName) {
        filteredData = filteredData.filter(item =>
          item.productName.includes(this.searchForm.productName)
        )
      }

      if (this.searchForm.shelfStatus !== '') {
        filteredData = filteredData.filter(item =>
          item.shelfStatus === this.searchForm.shelfStatus
        )
      }

      this.total = filteredData.length
      const start = (this.currentPage - 1) * this.pageSize
      this.shelfItemsList = filteredData.slice(start, start + this.pageSize)
    },

    // 搜索
    handleSearch() {
      this.searchForm.current = 1
      this.getPageData()
    },

    // 重置搜索条件
    handleReset() {
      this.searchForm = {
        current: 1,
        size: 10,
        shopIds: [],
        originalOrderNumber: '',
        status: ''
      }
      this.getPageData()
    },

    // 添加物品
    handleAddItem() {
      this.currentItemData = {}
      this.isEditItem = false
      this.itemDialogVisible = true
    },

    // 编辑物品
    handleEdit(row) {
      this.currentItemData = { ...row }
      this.isEditItem = true
      this.itemDialogVisible = true
    },

    // 删除物品
    handleDelete(row) {
      this.$confirm('确定要删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('准备删除的记录:', row)
        console.log('订单项ID:', row.orderItemId)

        // 后端期望的是idList数组，主键是orderItemId
        const deleteData = {
          idList: [row.orderItemId]
        }

        console.log('删除请求数据:', deleteData)

        deleteOrdTaiwan(deleteData)
          .then(response => {
            console.log('删除响应:', response)
            if (response && response.success) {
              this.$message.success('删除成功')
              this.loadData() // 重新加载数据
            } else {
              this.$message.error('删除失败：' + (response.message || '未知错误'))
            }
          })
          .catch(error => {
            console.error('删除失败:', error)
            if (error.response && error.response.data) {
              this.$message.error('删除失败：' + error.response.data.message)
            } else {
              this.$message.error('删除失败，请稍后重试')
            }
          })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 处理子组件提交事件
    handleItemSubmit(formData) {
      this.loadData() // 重新加载数据
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png'
    },

    // 复制退货单号
    copyWaybillNo(waybillNo) {
      this.copyToClipboard(waybillNo, '退货单号', 'green')
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.empty-table-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

/* 退货单号样式 */
.waybill-no {
  color: #67C23A;
  font-weight: bold;
}

.clickable-waybill {
  cursor: pointer;
  transition: color 0.2s;
}

.clickable-waybill:hover {
  color: #529b2e;
  text-decoration: underline;
}

.no-waybill {
  color: #C0C4CC;
  font-style: italic;
}

/* 商品名称单元格样式 */
.product-name-cell {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.product-name-cell:hover {
  background-color: #f5f7fa;
}

.product-name-text {
  display: inline-block;
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #606266;
}

/* 商品预览弹窗内容样式 */
.product-preview-content {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.product-image-section {
  flex-shrink: 0;
}

.product-info-section {
  flex: 1;
  min-width: 0;
}

.product-detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.product-detail-row:last-child {
  margin-bottom: 0;
}

.product-label {
  color: #909399;
  font-weight: 500;
  min-width: 70px;
  flex-shrink: 0;
}

.product-value {
  color: #606266;
  word-break: break-all;
  flex: 1;
}

.product-price {
  color: #E6A23C;
  font-weight: bold;
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>
