package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogPageDTO;
import com.my.crossborder.controller.vo.sys_erp_client_log.SysErpClientLogPageVO;
import com.my.crossborder.mybatis.entity.SysErpClientLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * erp接口日志表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface SysErpClientLogMapper extends BaseMapper<SysErpClientLog> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysErpClientLogPageVO> page(SysErpClientLogPageDTO pageDTO);
	
}
