package com.my.crossborder.controller.vo.ord_purchase_centralized;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_集中采购
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseCentralizedPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 采购单号
     */
    private String purchaseNumber;

    /**
     * 品名
     */
    private String productName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 采购途径
     */
    private String purchaseChannel;

    /**
     * 采购人id
     */
    private Integer purchaseUserId;

    /**
     * 采购人姓名
     */
    private String purchaseUserName;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购状态
     */
    private String purchaseStatus;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认人姓名
     */
    private String confirmUserName;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 关联的订单号列表
     */
    private List<String> orderSnList;

    /**
     * 是否已结算
     */
    private Boolean settlementFlag;
}
