<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>物流理赔管理</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="店铺">
          <el-select v-model="searchForm.shopIds" placeholder="请选择店铺" clearable multiple style="width: 150px;">
            <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="searchForm.orderSn" placeholder="请输入订单号" maxlength="16" clearable style="width: 180px;"></el-input>
        </el-form-item>
        <el-form-item label="快递单号">
          <el-input v-model="searchForm.expressNo" placeholder="请输入快递单号" maxlength="16" clearable style="width: 180px;"></el-input>
        </el-form-item>
        <el-form-item label="问题类别">
          <dict-select v-model="searchForm.issueType" placeholder="类别" clearable category-id="CLAIM_ISSUE_TYPE" style="width: 80px;">
          </dict-select>
        </el-form-item>
        <el-form-item label="处理办法">
          <dict-select v-model="searchForm.closeWay" placeholder="办法" clearable category-id="CLAIM_CLOSE_WAY" style="width: 80px;">
          </dict-select>
        </el-form-item>
        <el-form-item label="理赔进度">
          <dict-select v-model="searchForm.claimStatus" placeholder="进度" clearable category-id="CLAIM_STATUS" style="width: 100px;">
          </dict-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button v-permission="['ord-claim:insert']" type="primary" icon="el-icon-plus" @click="handleAddItem">添加</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="claimList" style="width: 100%" v-loading="loading" border>
        <!-- <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column> -->
        <el-table-column prop="shopName" label="店铺" width="100" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderSn" label="订单号" width="150" align="center">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
                  @click="copyOrderSn(scope.row.orderSn)"
                  :title="'点击复制订单号: ' + scope.row.orderSn">
              {{ scope.row.orderSn }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="expressNo" label="快递单号" width="160" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.expressNo"
                  class="reissue-waybill-no"
                  style="color: #67C23A; font-weight: bold; cursor: pointer;"
                  @click="copyExpressNumber(scope.row.expressNo)"
                  :title="'点击复制快递单号: ' + scope.row.expressNo">
              {{ scope.row.expressNo }}
            </span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="itemName" label="商品名称" width="120" align="center">
          <template slot-scope="scope">
            <el-popover
              placement="top"
              width="300"
              trigger="hover"
              :disabled="!scope.row.itemName">
              <div slot="reference" class="item-name-cell">
                <span class="item-name-text clickable-item-name"
                      @click="copyItemName(scope.row.itemName)"
                      :title="'点击复制商品名称: ' + scope.row.itemName">{{ scope.row.itemName }}</span>
              </div>
              <!-- Popover内容 -->
              <div class="item-preview-content">
                <div class="item-image-section">
                  <img :src="scope.row.itemImage || '/static/img/default-product.png'"
                       style="width: 80px; height: 80px; object-fit: cover; border-radius: 4px; border: 1px solid #e4e7ed;"
                       @error="handleImageError" />
                </div>
                <div class="item-info-section">
                  <div class="item-detail-row">
                    <span class="item-label">商品名称：</span>
                    <span class="item-value">{{ scope.row.itemName || '-' }}</span>
                  </div>
                  <div class="item-detail-row">
                    <span class="item-label">商品规格：</span>
                    <span class="item-value">{{ scope.row.itemModelName || '-' }}</span>
                  </div>
                  <div class="item-detail-row">
                    <span class="item-label">商品售价：</span>
                    <span class="item-value item-price">{{ scope.row.itemPrice ? '¥ ' + scope.row.itemPrice : '-' }}</span>
                  </div>
                </div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="itemModelName" label="商品规格" width="120" align="center" show-overflow-tooltip></el-table-column>         -->
        <el-table-column prop="itemPrice" label="商品售价" width="80" align="center">
          <template slot-scope="scope">
            <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.itemPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="purchaseCost" label="采购成本" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.purchaseCost ? '¥ ' + scope.row.purchaseCost : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="issue" label="问题描述" width="120" align="center" show-overflow-tooltip></el-table-column>
        <dict-table-column prop="issueType" label="问题类别" category-id="CLAIM_ISSUE_TYPE" width="80" show-overflow-tooltip></dict-table-column>
        <dict-table-column prop="closeWay" label="处理办法" category-id="CLAIM_CLOSE_WAY" width="80" show-overflow-tooltip></dict-table-column>
        <el-table-column prop="processResult" label="处理结果" width="160" align="center">
          <template slot-scope="scope">
            <!-- 补寄处理：显示补寄单号 -->
            <span v-if="scope.row.closeWay === '1' && scope.row.waybillNumber"
                  class="reissue-waybill-no"
                  @click="copyText(scope.row.waybillNumber, '补寄单号')"
                  :title="'点击复制补寄单号: ' + scope.row.waybillNumber">
              {{ scope.row.waybillNumber }}
            </span>
            <!-- 退款处理：显示退款截图 -->
            <span v-else-if="scope.row.closeWay === '2' && scope.row.attachmentId"
                  style="color: #E6A23C; font-weight: bold; cursor: pointer;"
                  @click="viewAttachment(scope.row.attachmentId)"
                  :title="'点击查看退款截图: ' + (scope.row.fileOriginalName || '截图文件')">
              <el-popover
                placement="top"
                trigger="hover"
                popper-class="refund-image-popover">
                <img :src='`/api/sys-attachment/view/${scope.row.attachmentId}`' class="refund-image-preview" />
                <img slot="reference" :src='`/api/sys-attachment/view/${scope.row.attachmentId}`' class="refund-image" title="退款截图"/>
              </el-popover>
            </span>
            <!-- 其他情况或无数据 -->
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="claimStatus" label="理赔进度" width="100" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.claimStatus === '1'"
              :content="`确认人：${scope.row.closeUserName || '未知'}, ${formatDateTime(scope.row.closeTime) || '-'}`"
              placement="top">
              <dict-tag :value="scope.row.claimStatus" category-id="CLAIM_STATUS"
                style="cursor: pointer;"></dict-tag>
            </el-tooltip>
            <dict-tag v-else :value="scope.row.claimStatus" category-id="CLAIM_STATUS"></dict-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="issueTime" label="录入时间" width="150" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.issueTime ? formatDateTime(scope.row.issueTime) : '-' }}</span>
          </template>
        </el-table-column> -->
        <!-- 使用NoteColumn组件 -->
        <!-- <NoteColumn :current-scene="currentScene" /> -->
        <el-table-column label="操作" min-width="280" align="center" >
          <template slot-scope="scope">
            <template v-if="scope.row.claimStatus === '0'">
              <el-button v-permission="['ord-claim:update']" type="text" size="mini" @click="handleEdit(scope.row)" icon="el-icon-edit">编辑</el-button>
              <el-button v-permission="['ord-claim:delete']" type="text" size="mini" @click="handleDelete(scope.row)" icon="el-icon-delete" style="color: #F56C6C">删除</el-button>
              <el-button v-permission="['ord-claim:confirm']" size="mini" type="text" @click="handleConfirmComplete(scope.row)"
                :disabled="!canConfirmComplete(scope.row)">
                <i class="el-icon-check"></i> 确认完成
              </el-button>
            </template>
            <!-- <el-button size="mini" type="text" @click="openNotesDrawer(scope.row)" icon="el-icon-tickets">备注</el-button> -->
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-table-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

    <!-- 添加/编辑理赔对话框 -->
    <OrdClaimEdit
      :visible.sync="itemDialogVisible"
      :editData="currentItemData"
      :isEdit="isEditItem"
      @submit="handleItemSubmit"
    />

    <!-- 使用EditNoteDialog组件 -->
    <EditNoteDialog
      :visible.sync="noteDialogVisible"
      :orderSn="currentOrderSn"
      :scene="currentScene"
      sceneName="物流理赔"
      @saved="handleNoteSaved" />

    <!-- 备注抽屉 -->
    <el-drawer
      title="订单备注"
      :visible.sync="notesDrawerVisible"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose">
      <div style="padding: 20px;">
        <OrderNotesDrawer
          :notes="currentOrderNotes"
          :orderSn="currentOrderSn"
          :currentScene="currentScene"
          :showDebug="false"
          @note-updated="handleNoteUpdated" />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Pagination from '../../components/Pagination'
import OrdClaimEdit from './OrdClaimEdit'
import NoteColumn from '../../components/NoteColumn'
import { getAllEnabledShops } from '../../api/SysShop'
import {
  getOrdClaimPage,
  deleteOrdClaim,
  confirmCompleteOrdClaim
} from '@/api/OrdClaim'
import copyMixin from '../../mixins/copyMixin'
import noteDrawerMixin from '../../mixins/noteDrawerMixin'

export default {
  name: 'OrdClaim',
  mixins: [copyMixin, noteDrawerMixin],
  components: {
    Pagination,
    OrdClaimEdit,
    NoteColumn
  },
  data() {
    return {
      loading: false,
      // 搜索表单数据
      searchForm: {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        expressNo: '',
        issueType: '',
        closeWay: '',
        claimStatus: ''
      },
      // 表格数据
      claimList: [],
      // 店铺列表
      shopList: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 编辑对话框相关
      itemDialogVisible: false,
      currentItemData: {},
      isEditItem: false,

      // 备注相关
      currentScene: '08'  // 物流理赔场景
    }
  },
  computed: {

    // 获取当前登录用户ID
    currentUserId() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.id || userEntity.userId
    }
  },
  created() {
    // 检查URL参数中是否有orderSn
    if (this.$route.query.orderSn) {
      this.searchForm.orderSn = this.$route.query.orderSn;
      console.log('物流理赔页面接收到订单号:', this.searchForm.orderSn);
    }

    // 加载数据
    this.loadData()
    this.loadShops()
  },
  methods: {
    // 检查是否可以确认完成（需要有补寄单号或退款截图）
    canConfirmComplete(row) {
      // 检查角色权限
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      if (userEntity.roleId !== 22) {
        return false
      }

      // 检查是否有补寄单号（处理办法为补寄且有运单号）
      const hasReissueWaybill = row.closeWay === '1' && row.waybillNumber

      // 检查是否有退款截图（处理办法为退款且有附件）
      const hasRefundScreenshot = row.closeWay === '2' && row.attachmentId

      // 有补寄单号或退款截图时才能确认完成
      return hasReissueWaybill || hasRefundScreenshot
    },

    // 获取分页数据
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = {
          current: this.searchForm.current,
          size: this.searchForm.size,
          shopIds: this.searchForm.shopIds && this.searchForm.shopIds.length > 0 ? this.searchForm.shopIds.map(id => String(id)) : undefined,
          orderSn: this.searchForm.orderSn || undefined,
          expressNo: this.searchForm.expressNo || undefined,
          issueType: this.searchForm.issueType || undefined,
          closeWay: this.searchForm.closeWay || undefined,
          claimStatus: this.searchForm.claimStatus || undefined
        }
      }

      getOrdClaimPage(parameter)
        .then(res => {
          this.loading = false
          if (res.success && res.data) {
            this.claimList = res.data.records
            this.pageParam.currentPage = res.data.current
            this.pageParam.pageSize = res.data.size
            this.pageParam.total = res.data.total
          }
        })
        .catch(err => {
          this.loading = false
          this.$message.error('获取数据失败：' + err.message)
        })
    },

    // 加载数据（保持向后兼容）
    loadData() {
      this.getPageData()
    },

    // 加载店铺数据
    loadShops() {
      getAllEnabledShops()
        .then(res => {
          if (res.success && res.data && res.data.records) {
            this.shopList = res.data.records
          }
        })
        .catch(err => {
          console.error('加载店铺数据失败：', err)
        })
    },

    // 分页回调
    callback_getPageData(parm) {
      this.searchForm.current = parm.currentPage
      this.searchForm.size = parm.pageSize
      this.getPageData()
    },

    // 搜索
    handleSearch() {
      this.searchForm.current = 1
      this.getPageData()
    },

    // 重置搜索条件
    handleReset() {
      this.searchForm = {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        expressNo: '',
        issueType: '',
        closeWay: '',
        claimStatus: ''
      }
      this.getPageData()
    },

    // 添加理赔
    handleAddItem() {
      this.currentItemData = {}
      this.isEditItem = false
      this.itemDialogVisible = true
    },

    // 编辑理赔
    handleEdit(row) {
      this.currentItemData = { ...row }
      this.isEditItem = true
      this.itemDialogVisible = true
    },

    // 删除理赔
    handleDelete(row) {
      this.$confirm('确定要删除这条理赔记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const deleteData = {
          idList: [row.id]
        }

        deleteOrdClaim(deleteData)
          .then(response => {
            if (response && response.success) {
              this.$message.success('删除成功')
              this.loadData() // 重新加载数据
            } else {
              this.$message.error('删除失败：' + (response.message || '未知错误'))
            }
          })
          .catch(error => {
            console.error('删除失败:', error)
            if (error.response && error.response.data) {
              this.$message.error('删除失败：' + error.response.data.message)
            } else {
              this.$message.error('删除失败，请稍后重试')
            }
          })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 处理子组件提交事件
    handleItemSubmit(formData) {
      this.loadData() // 重新加载数据
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png'
    },

    // 确认完成
    handleConfirmComplete(row) {
      this.$confirm(`确定要将订单号为 ${row.orderSn} 的理赔记录标记为完成吗？`, '确认完成', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用确认完成API
        confirmCompleteOrdClaim(row.id)
          .then(res => {
            if (res.success) {
              this.$message.success('确认完成成功')
              this.loadData()
            } else {
              this.$message.error('确认完成失败：' + (res.message || '未知错误'))
            }
          })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    // 查看附件
    viewAttachment(attachmentId) {
      if (!attachmentId) {
        this.$message.warning('附件ID无效')
        return
      }
      // 在新窗口中打开附件预览
      const viewUrl = `/api/sys-attachment/view/${attachmentId}`
      window.open(viewUrl, '_blank')
    },

    // 复制文本
    copyText(text, type) {
      if (!text) {
        this.$message.warning(`${type}为空，无法复制`)
        return
      }

      // 创建临时文本域
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()

      try {
        document.execCommand('copy')
        this.$message.success(`${type}已复制到剪贴板`)
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }

      document.body.removeChild(textArea)
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + ' ' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0')
    },

    // 格式化日期（不包含时间）
    formatDate(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0')
    },

    // 复制快递单号
    copyExpressNumber(expressNumber) {
      this.copyToClipboard(expressNumber, '快递单号', 'green')
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.empty-table-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

/* 商品名称单元格样式 */
.item-name-cell {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.item-name-cell:hover {
  background-color: #f5f7fa;
}

.item-name-text {
  display: inline-block;
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #606266;
}

/* 商品预览弹窗内容样式 */
.item-preview-content {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.item-image-section {
  flex-shrink: 0;
}

.item-info-section {
  flex: 1;
  min-width: 0;
}

.item-detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.item-detail-row:last-child {
  margin-bottom: 0;
}

.item-label {
  color: #909399;
  font-weight: 500;
  min-width: 70px;
  flex-shrink: 0;
}

.item-value {
  color: #606266;
  word-break: break-all;
  flex: 1;
}

.item-price {
  color: #E6A23C;
  font-weight: bold;
}

/* 退款截图样式 */
.refund-image {
  width: 30px;
  height: 30px;
  object-fit: cover;
  border-radius: 4px;
  margin-left: 8px;
  border: 1px solid #DCDFE6;
  cursor: pointer;
  transition: all 0.3s;
}

.refund-image:hover {
  border-color: #E6A23C;
  transform: scale(1.05);
}

.refund-image-preview {
  width: 200px;
  height: 200px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #DCDFE6;
}

/* 补寄单号样式 */
.reissue-waybill-no {
  color: #67C23A;
  font-weight: bold;
  background-color: #F0F9FF;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #67C23A;
  cursor: pointer;
  transition: all 0.3s;
}

.reissue-waybill-no:hover {
  background-color: #E8F5E8;
  border-color: #5CB85C;
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>

<style>
/* 全局样式：退款截图预览弹窗 */
.refund-image-popover {
  padding: 8px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
</style>
