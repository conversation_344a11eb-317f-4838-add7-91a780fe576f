package com.my.crossborder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款申请状态枚举
 * 对应字典：REFUND_APPLY_STATUS
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Getter
@AllArgsConstructor
public enum RefundApplyStatus {

    /**
     * 待处理
     */
    PENDING("0", "待处理"),

    /**
     * 已申请退款
     */
    APPLIED("1", "已申请退款"),

    /**
     * 不退采买做入库
     */
    PUT_IN_STORAGE("2", "不退采买做入库");

    /**
     * 字典值
     */
    private final String value;

    /**
     * 字典名称
     */
    private final String name;

    /**
     * 根据值获取枚举
     * @param value 字典值
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static RefundApplyStatus getByValue(String value) {
        for (RefundApplyStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据值获取名称
     * @param value 字典值
     * @return 对应的名称，如果找不到则返回null
     */
    public static String getNameByValue(String value) {
        RefundApplyStatus status = getByValue(value);
        return status != null ? status.getName() : null;
    }
}
