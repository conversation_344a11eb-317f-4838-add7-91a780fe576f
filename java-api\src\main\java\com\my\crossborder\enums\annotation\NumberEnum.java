package com.my.crossborder.enums.annotation;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.util.Assert;

import com.my.crossborder.exception.BusinessException;

/**
 * 数字作为key的枚举
 * <AUTHOR>
 * @date 2019年7月29日
 */
public interface NumberEnum {

	/**
	 * 数值
	 * @return
	 */
    Number getNumber();

    /**
     * 标签
     * @return
     * <AUTHOR> 2022年4月5日
     */
    String getLabel();
    
    
    /**	使用label创建对象 */
    @SuppressWarnings("unchecked")
	static <T> T labelOf(Class<T> cls, String label) {
    	Assert.notNull(label, "label不能为空");
        Method method;
		try {
			// 入参是否为子类
			if (!NumberEnum.class.isAssignableFrom(cls)) {
				BusinessException.by(String.format("%s不是NumberEnum的子类，无法判断", cls.getSimpleName()));
			}
			method = cls.getMethod("values");
			NumberEnum[] enums = (NumberEnum[]) method.invoke(null);
			for (NumberEnum tEnum : enums) {
				if (tEnum.getLabel().equals(label)) {
					return (T) tEnum;
				}
			}
			List<String> labels = Arrays.asList(enums).stream().map(e -> e.getLabel()).collect(Collectors.toList());
			String errMsg = String.format("枚举【%s】不包含此label:%s! 仅包含:%s", cls.getSimpleName(), label, labels);
			BusinessException.by(errMsg);
		} catch (BusinessException e) {
			throw e;
		} catch (Exception e) {
			e.printStackTrace();
		}
        
        return null;
	}
    
    /**	使用number创建对象 */
    @SuppressWarnings("unchecked")
	static <T> T numberOf(Class<T> cls, Number number) {
    	Assert.notNull(number, "number不能为空");  
        Method method;
		try {
			// 入参是否为子类
			if (!NumberEnum.class.isAssignableFrom(cls)) {
				BusinessException.by(String.format("%s不是NumberEnum的子类，无法判断", cls.getSimpleName()));
			}
			method = cls.getMethod("values");
			NumberEnum[] enums = (NumberEnum[]) method.invoke(null);
			for (NumberEnum tEnum : enums) {
				if (tEnum.getNumber().equals(number)) {
					return (T) tEnum;
				}
			}
			List<Number> numbers = Arrays.asList(enums).stream().map(e -> e.getNumber()).collect(Collectors.toList());
			String errMsg = String.format("枚举【%s】不包含此number:%s! 仅包含:%s", cls.getSimpleName(), number, numbers);
			BusinessException.by(errMsg);
		} catch (BusinessException e) {
			throw e;
		} catch (Exception e) {
			e.printStackTrace();
		}
        
        return null;
	}
}
