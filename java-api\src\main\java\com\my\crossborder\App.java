package com.my.crossborder;

import java.net.ServerSocket;
import java.util.HashMap;
import java.util.Map;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.my.crossborder.util.BrowseUtil;

import lombok.extern.slf4j.Slf4j;

@EnableScheduling
@EnableCaching
@MapperScan("com.my.crossborder.mybatis.mapper")
@SpringBootApplication(scanBasePackages = { "com.my.crossborder"})
@EnableAspectJAutoProxy
@Slf4j
public class App
				implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    private static final String SERVER_PORT = "server.port";

	private static Integer currentPort = 0;

	@Autowired
	private ApplicationContext applicationContext;


	public static void main(String[] args) {
        SpringApplication app = new SpringApplication(App.class);
        app.addInitializers(new App());
        app.run(args);
        log.info(" >> Web Server Started on port: {}", currentPort);
	}

	// 自动打开浏览器
	@EventListener(ApplicationReadyEvent.class)
	public void openBrowser() throws Exception {
		Environment environment = applicationContext.getEnvironment();
		Boolean autoOpenBrowser = Boolean.parseBoolean(environment.getProperty("project.auto-open-browser"));
		if (autoOpenBrowser) {
			try {
				BrowseUtil.browse("http://localhost:" + currentPort);
			} catch(Exception e) {
				log.error("打开浏览器失败!");
			}
		}
	}

	// 计算可用端口
	@Override
	public void initialize(ConfigurableApplicationContext applicationContext) {
		ConfigurableEnvironment environment = applicationContext.getEnvironment();
		currentPort = Integer.parseInt(environment.getProperty(SERVER_PORT));
		portAutoIncrement();
		MutablePropertySources propertySources = environment.getPropertySources();
		Map<String, Object> appMap = new HashMap<>();
		appMap.put(SERVER_PORT, currentPort); // 设置端口
		propertySources.addFirst(new MapPropertySource("DynamicPort", appMap));
	}

	/**
	 * 端口号自增
	 */
	private static void portAutoIncrement() {
		while (true) {
			try (ServerSocket socket = new ServerSocket(currentPort)) {
				log.info("Tomcat Port {} is available. Starting application >> ", currentPort);
				break;
			} catch (Exception e) {
				// 端口被占用，尝试下一个端口
				currentPort++;
				log.info("Tomcat Port {} is occupied. Trying {}", (currentPort - 1) , currentPort);
				if (currentPort > 65535) { // 防止端口溢出
					throw new RuntimeException("No available ports found for tomcat !");
				}
			}
		}
	}

	/**
	 * 返回server port(SocketIO的端口也需要用到)
	 * @return
	 */
	public static Integer getCurrentPort() {
		return currentPort;
	}
}
