## 跨境电商运营管理系统

## 项目简介
本项目是一个跨境电商运营管理系统，采用前后端分离架构。

## 技术栈
- **前端**: Vue.js 2 + ElementUI + Vuex + Vue Router + Webpack + ES6/7 + Axios
- **后端**: SpringBoot + MyBatis + MySQL
- **推荐Node.js版本**: 14.21.3以上

## 项目结构

### � java-api (后端服务)
后端SpringBoot项目，提供API接口服务
```
java-api/
├── src/
│   ├── main/
│   │   ├── java/com/my/pipesystem/
│   │   │   ├── aopattachment/     # AOP附件处理
│   │   │   ├── aoplog/            # AOP日志切面
│   │   │   ├── auth/              # 认证授权模块
│   │   │   ├── cache/             # 缓存管理
│   │   │   ├── config/            # 配置类
│   │   │   ├── controller/        # 控制器层
│   │   │   ├── enums/             # 枚举类
│   │   │   ├── exception/         # 异常处理
│   │   │   ├── job/               # 定时任务
│   │   │   ├── mybatis/           # MyBatis配置
│   │   │   ├── service/           # 业务服务层
│   │   │   ├── tmp/               # 临时文件处理
│   │   │   ├── util/              # 工具类
│   │   │   └── App.java           # 主启动类
│   │   └── resources/
│   │       ├── config-dev/        # 开发环境配置
│   │       ├── config-test/       # 测试环境配置
│   │       ├── config-prod/       # 生产环境配置
│   │       ├── config-aliyun/     # 阿里云环境配置
│   │       ├── mapper/            # MyBatis映射文件
│   │       ├── static/            # 静态资源目录(前端打包后放置)
│   │       ├── application.yml    # 主配置文件
│   │       ├── bootstrap.yml      # 启动配置
│   │       └── logback.xml        # 日志配置
│   └── test/                      # 测试代码
├── target/                        # 编译输出目录
├── logs/                          # 日志文件目录
├── pom.xml                        # Maven依赖配置
├── smart-doc.json                 # API文档配置
├── smart-doc.bat                  # API文档生成脚本
└── README.md                      # 项目说明
```

### � admin-ui (前端管理界面)
基于Vue.js的管理后台界面

```
admin-ui/
├── build/                         # Webpack构建配置
│   ├── build.js                   # 生产环境构建脚本
│   ├── check-versions.js          # 版本检查
│   ├── utils.js                   # 构建工具函数
│   ├── vue-loader.conf.js         # Vue加载器配置
│   ├── webpack.base.conf.js       # Webpack基础配置
│   ├── webpack.dev.conf.js        # 开发环境Webpack配置
│   └── webpack.prod.conf.js       # 生产环境Webpack配置
├── config/                        # 项目配置
│   ├── dev.env.js                 # 开发环境变量
│   ├── index.js                   # 主配置文件
│   └── prod.env.js                # 生产环境变量
├── src/                           # 源码目录
│   ├── api/                       # API接口定义
│   │   ├── Auth.js                # 认证接口
│   │   ├── SysUser.js             # 系统用户接口
│   │   ├── SysMenu.js             # 系统菜单接口
│   │   ├── SysDictItem.js         # 数据字典接口
│   │   ├── SysLog.js              # 系统日志接口
│   │   ├── SysAttachment.js       # 系统附件接口
│   │   ├── RskResourceStaff.js    # 风险资源人员接口
│   │   ├── RskResourceTraining.js # 风险资源培训接口
│   │   ├── RskDrillRecord.js      # 风险演练记录接口
│   │   ├── RskEstimate.js         # 风险评估接口
│   │   ├── RskDrillPlan.js        # 风险演练计划接口
│   │   ├── RskCheck*.js           # 风险检查相关接口
│   │   ├── PolPolicy.js           # 政策管理接口
│   │   ├── Ins*.js                # 检测相关接口
│   │   ├── Eqm*.js                # 设备管理相关接口
│   │   ├── Wkb*.js                # 工作台相关接口
│   │   └── axiosFun.js            # Axios封装
│   ├── assets/                    # 静态资源(图片等)
│   ├── components/                # 公共组件
│   │   ├── ColorTagSelector.vue   # 颜色标签选择器
│   │   ├── DictTag.vue            # 字典标签组件
│   │   ├── DictSelect.vue         # 字典选择组件
│   │   ├── DictTable.vue          # 字典表格组件
│   │   ├── FileUpload.vue         # 文件上传组件
│   │   ├── HelpHint.vue           # 帮助提示组件
│   │   ├── NotificationDialog.vue # 通知对话框组件
│   │   ├── Pagination.vue         # 分页组件
│   │   ├── PipelineSelector.vue   # 管道选择器
│   │   ├── SafetyComponentSelector.vue # 安全组件选择器
│   │   ├── UserSelector.vue       # 用户选择器
│   │   ├── leftnav.vue            # 左侧导航组件
│   │   ├── navcon.vue             # 导航控制组件
│   │   └── template.vue           # 组件模板
│   ├── directives/                # Vue指令
│   ├── router/                    # 路由配置
│   ├── utils/                     # 工具函数
│   │   ├── dictPlugin.js          # 字典插件
│   │   ├── dictService.js         # 字典服务
│   │   ├── stagewise.js           # 阶段性处理工具
│   │   ├── unreadCacheMixin.js    # 未读缓存混入
│   │   └── util.js                # 通用工具函数
│   ├── views/                     # 页面组件
│   │   ├── eqm/                   # 设备管理模块页面
│   │   ├── ins/                   # 检测管理模块页面
│   │   ├── pol/                   # 政策管理模块页面
│   │   ├── rsk/                   # 风险管理模块页面
│   │   ├── sys/                   # 系统管理模块页面
│   │   ├── wkb/                   # 工作台模块页面
│   │   ├── index.vue              # 首页
│   │   └── login.vue              # 登录页
│   ├── vuex/                      # Vuex状态管理
│   ├── App.vue                    # 根组件
│   └── main.js                    # 应用入口
├── static/                        # 静态资源目录
├── dist/                          # 构建输出目录
├── node_modules/                  # 依赖包目录
├── test-build/                    # 测试构建目录
├── .babelrc                       # Babel配置
├── .editorconfig                  # 编辑器配置
├── .eslintrc.js                   # ESLint配置
├── .gitignore                     # Git忽略文件
├── .postcssrc.js                  # PostCSS配置
├── favicon.ico                    # 网站图标
├── index.html                     # HTML模板
├── package.json                   # 项目依赖配置
├── package-lock.json              # 依赖锁定文件
├── sync_config.jsonc              # 同步配置
├── DICT_USAGE.md                  # 字典使用说明
└── README.md                      # 项目说明
```

### � 运维页面
所有组件的快捷访问页面

```
ops-html/
├── logos/ 						# 图标目录
└── index.html
```

## 核心功能模块

### � 系统管理模块 (sys)
- **用户管理**: 用户账号和权限管理
- **菜单管理**: 系统菜单和权限配置
- **字典管理**: 系统数据字典维护
- **日志管理**: 系统操作日志记录和查询
- **参数配置**: 系统参数设置和管理

### � 工作台模块 (wkb)
- **待办事项**: 个人和部门待办任务管理
- **通知管理**: 系统通知的发送和接收
- **阅读记录**: 通知和文档的阅读状态跟踪

## 快速开始

### 后端开发
```bash
# 进入后端目录
cd java-api

# 1. 使用SQL脚本建表
# 2. 修改config-dev/application-dev-db.yml的JDBC配置
# 3. 启动SpringBoot项目
```

### 前端开发
```bash
# 进入前端目录
cd admin-ui

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 项目打包
npm run build
```

### 项目部署
```bash
# 1. 前端打包
cd admin-ui
npm run build

# 2. 将打包文件复制到后端静态资源目录
cp -r dist/* ../java-api/src/main/resources/static/

# 3. 后端打包
cd ../java-api
mvn clean package

# 4. 服务器部署
java -jar target/*.jar
```

## 开发规范

### 前端规范
- 使用ESLint进行代码质量检查
- 组件命名采用PascalCase
- 文件命名采用kebab-case
- API接口统一放在`/src/api/`目录下
- 公共组件放在`/src/components/`目录下

### 后端规范
- 遵循Spring Boot最佳实践
- 使用分层架构（Controller-Service-Mapper）
- 统一异常处理和返回格式
- 使用AOP进行日志记录和权限控制

## 环境要求
- **Java**: JDK 8+
- **Node.js**: 14.21.3+ (推荐)
- **MySQL**: 5.7+
- **Maven**: 3.6+

## 许可证
本项目采用私有许可证，仅供内部使用。 