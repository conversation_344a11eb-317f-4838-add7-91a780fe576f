package com.my.crossborder.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagInsertDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagPageDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagUpdateDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagDeleteDTO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagDetailVO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagPageVO;
import com.my.crossborder.controller.vo.StdResp;

import com.my.crossborder.service.WkbTagService;

import org.springframework.web.bind.annotation.RestController;

/**
 * 工作台_订单标签 
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@RequestMapping("/api/wkb-tag")
@RequiredArgsConstructor
public class WkbTagController {

    private final WkbTagService wkbTagService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody WkbTagInsertDTO insertDTO) {
    	this.wkbTagService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody WkbTagUpdateDTO updateDTO) {
    	this.wkbTagService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<WkbTagDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.wkbTagService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<WkbTagPageVO>> page(WkbTagPageDTO pageDTO) {
        Page<WkbTagPageVO> page = this.wkbTagService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody WkbTagDeleteDTO deleteDTO) {
    	this.wkbTagService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
