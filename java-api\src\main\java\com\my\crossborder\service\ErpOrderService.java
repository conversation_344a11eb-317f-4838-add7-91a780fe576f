package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.ErpOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.erp_order.ErpOrderInsertDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderPageDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderStatusSummaryPageDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderUpdateDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderDeleteDTO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderDetailVO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderStatusSummaryPageVO;
import com.my.crossborder.controller.vo.erp_order.RepurchaseErpOrderPageVO;

/**
 * 订单主表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ErpOrderService extends IService<ErpOrder> {

	/**
	 * 新增
	 */
	void insert(ErpOrderInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(ErpOrderUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param orderId 订单ID
	 */
	ErpOrderDetailVO detail(String orderId);

	/**
	 * 物流未完成订单分页查询（包含订单项）
	 */
	Page<ErpOrderPageVO> incompleteLogisticsPageWithItems(ErpOrderPageDTO pageDTO);

	/**
	 * 已填物流编号未入库订单分页查询（包含订单项）
	 */
	Page<ErpOrderPageVO> logisticsDoneNotWarehoused(ErpOrderPageDTO pageDTO);

	/**
	 * 已入库未出库订单分页查询（包含订单项）
	 */
	Page<ErpOrderPageVO> warehousedNotOutbound(ErpOrderPageDTO pageDTO);

//	/**
//	 * 已采购但出库前取消订单分页查询（包含订单项）
//	 */
//	Page<ErpOrderPageVO> purchaseDoneOrderCancelled(ErpOrderPageDTO pageDTO);

	/**
	 * 重新采购订单分页查询（包含订单项）
	 */
	Page<RepurchaseErpOrderPageVO> repurchasePageWithItems(ErpOrderPageDTO pageDTO);

	/**
	 * 订单选择器专用分页查询（包含订单项）
	 * 用于OrderSelector组件的通用订单选择功能
	 */
	Page<ErpOrderPageVO> selectorPageWithItems(ErpOrderPageDTO pageDTO);

	/**
	 * 批量删除(物理删除)
	 */
	void delete(ErpOrderDeleteDTO deleteDTO);

	<T extends ErpOrderPageVO> void batchSetOrderItemsAndNotes(Page<T> orderPage);

	/**
	 * 订单状态汇总分页查询
	 */
	Page<ErpOrderStatusSummaryPageVO> orderStatusSummaryPage(ErpOrderStatusSummaryPageDTO pageDTO);

}
