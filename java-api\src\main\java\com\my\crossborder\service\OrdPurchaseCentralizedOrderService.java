package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.OrdPurchaseCentralizedOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderInsertDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderPageDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderUpdateDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderDeleteDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderDetailVO;
import com.my.crossborder.controller.vo.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderPageVO;

/**
 * 集中采购订单 服务类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface OrdPurchaseCentralizedOrderService extends IService<OrdPurchaseCentralizedOrder> {

	/**
	 * 新增
	 */
	void insert(OrdPurchaseCentralizedOrderInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(OrdPurchaseCentralizedOrderUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	OrdPurchaseCentralizedOrderDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<OrdPurchaseCentralizedOrderPageVO> page(OrdPurchaseCentralizedOrderPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(OrdPurchaseCentralizedOrderDeleteDTO deleteDTO);	

}
