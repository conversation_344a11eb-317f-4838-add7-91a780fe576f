package com.my.crossborder.controller.dto.erp_order_item;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_订单项表
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpOrderItemInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private String id;

    /**
     * 订单ID，关联erp_order.order_id
     */
	@NotNull(message="orderId不能为空")
    private String orderId;

    /**
     * 商品索引，同一订单中商品的序号
     */
	@NotNull(message="productIdx不能为空")
    private Integer productIdx;

    /**
     * 商品ID
     */
	@NotNull(message="itemId不能为空")
    private String itemId;

    /**
     * 商品名称
     */
	@NotNull(message="itemName不能为空")
    private String itemName;

    /**
     * 商品图片URL
     */
	@NotNull(message="itemImage不能为空")
    private String itemImage;

    /**
     * 商品价格
     */
	@NotNull(message="itemPrice不能为空")
    private BigDecimal itemPrice;

    /**
     * 订单中该商品价格
     */
	@NotNull(message="orderPrice不能为空")
    private BigDecimal orderPrice;

    /**
     * 商品数量
     */
	@NotNull(message="amount不能为空")
    private Integer amount;

    /**
     * 商品描述
     */
	@NotNull(message="description不能为空")
    private String description;

    /**
     * 规格ID
     */
	@NotNull(message="modelId不能为空")
    private String modelId;

    /**
     * 商品规格名称
     */
	@NotNull(message="itemModelName不能为空")
    private String itemModelName;

    /**
     * 商品规格SKU
     */
	@NotNull(message="itemModelSku不能为空")
    private String itemModelSku;

    /**
     * 商品SKU
     */
	@NotNull(message="productSku不能为空")
    private String productSku;

    /**
     * 优惠前价格
     */
	@NotNull(message="priceBeforeBundle不能为空")
    private BigDecimal priceBeforeBundle;

    /**
     * 折扣前价格
     */
	@NotNull(message="priceBeforeDiscount不能为空")
    private BigDecimal priceBeforeDiscount;

    /**
     * 首个商品数量
     */
	@NotNull(message="firstItemCount不能为空")
    private Integer firstItemCount;

    /**
     * 首个商品是否批发
     */
	@NotNull(message="firstItemIsWholesale不能为空")
    private String firstItemIsWholesale;

    /**
     * 首个商品型号
     */
	@NotNull(message="firstItemModel不能为空")
    private String firstItemModel;

    /**
     * 首个商品名称
     */
	@NotNull(message="firstItemName不能为空")
    private String firstItemName;

    /**
     * 首个商品退货状态
     */
	@NotNull(message="firstItemReturn不能为空")
    private String firstItemReturn;

    /**
     * 商品状态
     */
	@NotNull(message="goodsStates不能为空")
    private String goodsStates;

    /**
     * 商品类型
     */
	@NotNull(message="goodsType不能为空")
    private String goodsType;

    /**
     * 即时买家取消发货
     */
	@NotNull(message="instantBuyercancelToship不能为空")
    private String instantBuyercancelToship;

    /**
     * 是否买家取消发货
     */
	@NotNull(message="isBuyercancelToship不能为空")
    private String isBuyercancelToship;

    /**
     * 创建人
     */
	@NotNull(message="createBy不能为空")
    private String createBy;

    /**
     * 创建时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
	@NotNull(message="updateBy不能为空")
    private String updateBy;

    /**
     * 更新时间
     */
	@NotNull(message="updateTime不能为空")
    private LocalDateTime updateTime;

}
