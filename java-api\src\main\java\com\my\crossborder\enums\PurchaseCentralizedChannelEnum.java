package com.my.crossborder.enums;

/**
 * 集中采购途径枚举
 * PURCHASE_CENTRALIZED_CHANNEL
 */
public enum PurchaseCentralizedChannelEnum {
    
    /**
     * pdd
     */
    PDD("1", "pdd"),
    
    /**
     * 1688
     */
    ALIBABA_1688("2", "1688"),
    
    /**
     * 淘宝或其他
     */
    TAOBAO_OR_OTHER("3", "淘宝或其他");
    
    private final String code;
    private final String desc;
    
    PurchaseCentralizedChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static PurchaseCentralizedChannelEnum getByCode(String code) {
        for (PurchaseCentralizedChannelEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     */
    public static String getDescByCode(String code) {
        PurchaseCentralizedChannelEnum channel = getByCode(code);
        return channel != null ? channel.getDesc() : null;
    }
}
