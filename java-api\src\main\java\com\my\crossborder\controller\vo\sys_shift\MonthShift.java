package com.my.crossborder.controller.vo.sys_shift;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import com.my.crossborder.controller.vo.sys_shift_attendance.MonthAttendanceFull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 月度排班
 * <AUTHOR>
 *
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper=false)
public class MonthShift {

	private Map<LocalDate, List<ShiftObj>> scheduleData;
	
	
	
	@NoArgsConstructor
	@AllArgsConstructor
	@Data
	@Builder
	@EqualsAndHashCode(callSuper=false)
	public static class ShiftObj {
		
		Integer shopId;
		
		String shopName;
	}
}
