package com.my.crossborder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款申请结果枚举
 * 对应字典：REFUND_RESULT_STATUS
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Getter
@AllArgsConstructor
public enum RefundResultStatus {

    /**
     * 退款成功
     */
    REFUND_SUCCESS("1", "退款成功"),

    /**
     * 退款失败
     */
    REFUND_FAILED("2", "退款失败"),

    /**
     * 已入库
     */
    CONFIRMED_IN_STORAGE("3", "已入库");

    /**
     * 字典值
     */
    private final String value;

    /**
     * 字典名称
     */
    private final String name;

    /**
     * 根据值获取枚举
     * @param value 字典值
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static RefundResultStatus getByValue(String value) {
        for (RefundResultStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据值获取名称
     * @param value 字典值
     * @return 对应的名称，如果找不到则返回null
     */
    public static String getNameByValue(String value) {
        RefundResultStatus status = getByValue(value);
        return status != null ? status.getName() : null;
    }
}
