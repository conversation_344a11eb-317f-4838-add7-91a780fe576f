package com.my.crossborder.controller;

import java.util.List;
import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimInsertDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimPageDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimUpdateDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimDeleteDTO;
import com.my.crossborder.controller.vo.ord_claim.OrdClaimDetailVO;
import com.my.crossborder.controller.vo.ord_claim.OrdClaimPageVO;
import com.my.crossborder.controller.vo.StdResp;

import com.my.crossborder.service.OrdClaimService;
import com.my.crossborder.service.SysShopService;

import cn.dev33.satoken.annotation.SaCheckPermission;

import org.springframework.web.bind.annotation.RestController;

/**
 * 物流理赔 
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/api/ord-claim")
@RequiredArgsConstructor
public class OrdClaimController {

    private final OrdClaimService ordClaimService;
    private final SysShopService sysShopService;

    /**
    * 新增
    */
    @SaCheckPermission("ord-claim:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody OrdClaimInsertDTO insertDTO) {
    	this.ordClaimService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("ord-claim:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody OrdClaimUpdateDTO updateDTO) {
    	this.ordClaimService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<OrdClaimDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.ordClaimService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<OrdClaimPageVO>> page(OrdClaimPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds.stream().map(String::valueOf).collect(java.util.stream.Collectors.toList()));
        }

        Page<OrdClaimPageVO> page = this.ordClaimService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("ord-claim:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody OrdClaimDeleteDTO deleteDTO) {
    	this.ordClaimService.delete(deleteDTO);
		return StdResp.success();
    }

    /**
     * 确认完成
     * @param id 理赔记录ID
     */
    @SaCheckPermission("ord-claim:confirm")
    @PutMapping("/confirm-complete/{id}")
    public StdResp<?> confirmComplete(@PathVariable Integer id) {
        this.ordClaimService.confirmComplete(id);
        return StdResp.success();
    }

}
