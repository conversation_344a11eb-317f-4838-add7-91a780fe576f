<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdPurchaseCentralizedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.OrdPurchaseCentralized">
        <id column="id" property="id" />
        <result column="purchase_number" property="purchaseNumber" />
        <result column="product_name" property="productName" />
        <result column="quantity" property="quantity" />
        <result column="total_amount" property="totalAmount" />
        <result column="purchase_channel" property="purchaseChannel" />
        <result column="purchase_user_id" property="purchaseUserId" />
        <result column="purchase_date" property="purchaseDate" />
        <result column="purchase_status" property="purchaseStatus" />
        <result column="confirm_user_id" property="confirmUserId" />
        <result column="confirm_time" property="confirmTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, purchase_number, product_name, quantity, total_amount, purchase_channel, purchase_user_id, purchase_date, purchase_status, confirm_user_id, confirm_time, create_time, update_time
    </sql>

	<!-- 分页 -->
    <select id="page" resultType="com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedPageVO">
		SELECT
			t1.id,
			t1.purchase_number,
			t1.product_name,
			t1.quantity,
			t1.total_amount,
			t1.purchase_channel,
			t1.purchase_user_id,
			t1.purchase_date,
			t1.purchase_status,
			t1.confirm_user_id,
			t1.confirm_time,
			t1.create_time,
			t1.update_time,
			tPurchaseUser.real_name AS purchase_user_name,
			tConfirmUser.real_name AS confirm_user_name,
			CASE
				WHEN t1.purchase_channel NOT IN (2, 3) THEN NULL
				WHEN tSet.id IS NULL THEN NULL
				ELSE 1
			END AS settlement_flag
		FROM
			ord_purchase_centralized AS t1
		LEFT JOIN sys_user AS tPurchaseUser ON t1.purchase_user_id = tPurchaseUser.user_id
		LEFT JOIN sys_user AS tConfirmUser ON t1.confirm_user_id = tConfirmUser.user_id
		LEFT JOIN stl_purchase tSet ON t1.purchase_date = tSet.purchase_date AND tSet.purchase_user_id = t1.purchase_user_id
		<where>
        	1=1
	        <if test="id != null">
	           	AND t1.id = #{id}
            </if>
	        <if test="purchaseNumber != null and purchaseNumber != ''">
	           	AND t1.purchase_number = #{purchaseNumber}
            </if>
	        <if test="productName != null and productName != ''">
	           	AND t1.product_name LIKE CONCAT('%', #{productName}, '%')
            </if>
	        <if test="purchaseUserId != null and purchaseUserId != ''">
	           	AND t1.purchase_user_id = #{purchaseUserId}
            </if>
	        <if test="purchaseUserIdList != null and purchaseUserIdList.size() > 0">
	           	AND t1.purchase_user_id IN
	           	<foreach collection="purchaseUserIdList" item="userId" open="(" separator="," close=")">
	           		#{userId}
	           	</foreach>
            </if>
	        <if test="orderSnList != null and orderSnList.size() > 0">
	           	AND EXISTS (
	           		SELECT 1 FROM ord_purchase_centralized_order opco
	           		WHERE opco.purchase_id = t1.id
	           		AND opco.order_sn IN
	           		<foreach collection="orderSnList" item="orderSn" open="(" separator="," close=")">
	           			#{orderSn}
	           		</foreach>
	           	)
            </if>
	        <if test="purchaseDateStart != null">
	           	AND DATE(t1.purchase_date) >= #{purchaseDateStart}
            </if>
	        <if test="purchaseDateEnd != null">
	           	AND DATE(t1.purchase_date) &lt;= #{purchaseDateEnd}
            </if>
	        <if test="purchaseStatus != null and purchaseStatus != ''">
	           	AND t1.purchase_status = #{purchaseStatus}
            </if>
	        <if test="purchaseChannel != null and purchaseChannel != ''">
	           	AND t1.purchase_channel = #{purchaseChannel}
            </if>
	        <if test="shopIds != null and shopIds.size() > 0">
	           	AND EXISTS (
	           		SELECT 1 FROM ord_purchase_centralized_order opco
	           		INNER JOIN erp_order eo ON opco.order_sn = eo.order_sn
	           		WHERE opco.purchase_id = t1.id
	           		AND eo.shop_id IN
	           		<foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
	           			#{shopId}
	           		</foreach>
	           	)
            </if>
        </where>
        ORDER BY t1.id DESC
    </select>

</mapper>
