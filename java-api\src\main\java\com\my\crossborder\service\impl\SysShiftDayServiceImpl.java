package com.my.crossborder.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.mybatis.entity.SysShiftDay;
import com.my.crossborder.mybatis.mapper.SysShiftDayMapper;
import com.my.crossborder.service.SysShiftDayService;
import com.my.crossborder.service.SysShiftService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 排班日期表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysShiftDayServiceImpl extends ServiceImpl<SysShiftDayMapper, SysShiftDay> implements SysShiftDayService {


	@Transactional
	@Override
	public void createIfAbsense(LocalDate shiftDay) {
		// 查找是否已存在
		SysShiftDay existingShift = this.getById(shiftDay);
		if (existingShift != null) {
			return;
		}

		// 如果不存在，创建新的排班记录
		SysShiftDay newShift = SysShiftDay.builder()
				.shiftDay(shiftDay)
				.shopCount(0)
				.shiftShopCount(0)
				.createUserId(StpUtil.getLoginIdAsInt())
				.createTime(LocalDateTime.now())
				.build();
		this.save(newShift);
	}

	@Transactional
	@Override
	public void update(LocalDate shiftDay, Integer shopCount, Integer shiftCount) {
		SysShiftDay shift = SysShiftDay.builder()
				.shiftDay(shiftDay)
				.shopCount(shopCount)
				.shiftShopCount(shiftCount)
				.build();
		this.updateById(shift);
	}


//	@Transactional
//	@Override
//	public void checkAndCleanEmptyShift(LocalDate shiftDay) {
//		if (shiftDay == null) return;
//
//		// 查询该排班日期
//		SysShiftDay shift = this.getById(shiftDay);
//		if (shift == null) return;
//
//		// 统计该日期下的排班详情数量
//		int count = SpringUtil.getBean(SysShiftService.class).countShift(shiftDay);
//
//		if (count == 0) {
//			// 如果没有排班详情，删除主表记录
//			this.removeById(shiftDay);
//		} else {
//			// 更新统计数量
//			shift.setShopCount((int) count);
//			this.updateById(shift);
//		}
//	}

}
