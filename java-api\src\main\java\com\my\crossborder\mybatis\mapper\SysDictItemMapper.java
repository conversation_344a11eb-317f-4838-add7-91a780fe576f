package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemPageDTO;
import com.my.crossborder.controller.vo.sys_dict_item.SysDictItemPageVO;
import com.my.crossborder.mybatis.entity.SysDictItem;

/**
 * 数据字典-字典项表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface SysDictItemMapper extends BaseMapper<SysDictItem> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysDictItemPageVO> page(SysDictItemPageDTO pageDTO);
	
}
