package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.OrdAfterSale;
import com.my.crossborder.mybatis.mapper.OrdAfterSaleMapper;
import com.my.crossborder.service.ErpOrderService;
import com.my.crossborder.service.OrdAfterSaleService;
import java.time.LocalDateTime;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleInsertDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSalePageDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleUpdateDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleDeleteDTO;
import com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSaleDetailVO;
import com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSalePageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;

import org.springframework.transaction.annotation.Transactional;

/**
 * 售后表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
@RequiredArgsConstructor
public class OrdAfterSaleServiceImpl extends ServiceImpl<OrdAfterSaleMapper, OrdAfterSale> implements OrdAfterSaleService {
	
	private final ErpOrderService erpOrderService;
	

	@Transactional
	@Override
	public void insert(OrdAfterSaleInsertDTO insertDTO) {
		OrdAfterSale entity = BeanUtil.copyProperties(insertDTO, OrdAfterSale.class);
		entity.setIssueUserId(StpUtil.getLoginIdAsInt());
		entity.setIssueTime(LocalDateTime.now());
		entity.setCloseStatus("0");
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(OrdAfterSaleUpdateDTO updateDTO) {
		OrdAfterSale entity = BeanUtil.copyProperties(updateDTO, OrdAfterSale.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public OrdAfterSaleDetailVO detail(Integer id) {
		OrdAfterSale entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, OrdAfterSaleDetailVO.class);
	}

	@Override
	public Page<OrdAfterSalePageVO> page(OrdAfterSalePageDTO pageDTO) {
		Page<OrdAfterSalePageVO> page = this.baseMapper.page(pageDTO);
		this.erpOrderService.batchSetOrderItemsAndNotes(page); 
		return page;
	}

	@Transactional
	@Override
	public void delete(OrdAfterSaleDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Transactional
	@Override
	public void confirmComplete(Integer id) {
		OrdAfterSale entity = new OrdAfterSale();
		entity.setId(id);
		entity.setCloseStatus("1");
		entity.setCloseTime(LocalDateTime.now()); // 设置处理时间
		entity.setCloseUserId(StpUtil.getLoginIdAsInt()); // 设置处理人
		this.baseMapper.updateById(entity);
	}
}
