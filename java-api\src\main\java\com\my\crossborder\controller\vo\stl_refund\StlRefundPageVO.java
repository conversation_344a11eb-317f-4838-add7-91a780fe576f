package com.my.crossborder.controller.vo.stl_refund;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_结算_退款结算表
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class StlRefundPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 退款员工ID
     */
    private Integer refundUserId;

    /**
     * 退款日期_开始
     */
    private LocalDate refundDateStart;

    /**
     * 退款日期_结束
     */
    private LocalDate refundDateEnd;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 结算日期
     */
    private LocalDateTime settlementDate;

    /**
     * 备注( 使用常见标签快速填写）
     */
    private String remark;

    /**
     * 结算操作员用户id
     */
    private Integer createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
