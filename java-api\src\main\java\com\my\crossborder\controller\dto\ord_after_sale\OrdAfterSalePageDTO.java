package com.my.crossborder.controller.dto.ord_after_sale;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSalePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_售后表
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdAfterSalePageDTO 
						extends PageDTO<OrdAfterSalePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 问题类别
     */
    private String issueType;

    /**
     * 处理办法 字典aftersale_close_way
     */
    private String closeWay;

    /**
     * 处理状态 字典close_status
     */
    private String closeStatus;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 新订单编号
     */
    private String newOrderSn;

    /**
     * 问题录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理完成时间
     */
    private LocalDateTime closeTime;

    /**
     * 录入人id
     */
    private Integer issueUserId;

    /**
     * 处理人id
     */
    private Integer closeUserId;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 店铺ID（用于查询过滤）
     */
    private Integer shopId;

}
