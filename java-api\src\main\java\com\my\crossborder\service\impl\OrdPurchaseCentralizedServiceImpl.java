package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.OrdPurchaseCentralized;
import com.my.crossborder.mybatis.entity.OrdPurchaseCentralizedOrder;
import com.my.crossborder.mybatis.mapper.OrdPurchaseCentralizedMapper;
import com.my.crossborder.mybatis.mapper.OrdPurchaseCentralizedOrderMapper;
import com.my.crossborder.service.OrdPurchaseCentralizedService;
import com.my.crossborder.enums.PurchaseCentralizedStatusEnum;
import com.my.crossborder.exception.BusinessException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedInsertDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedPageDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedUpdateDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedDeleteDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedDetailVO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 集中采购 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
@RequiredArgsConstructor
public class OrdPurchaseCentralizedServiceImpl extends ServiceImpl<OrdPurchaseCentralizedMapper, OrdPurchaseCentralized> implements OrdPurchaseCentralizedService {

	private final OrdPurchaseCentralizedOrderMapper ordPurchaseCentralizedOrderMapper;

	@Transactional
	@Override
	public void insert(OrdPurchaseCentralizedInsertDTO insertDTO) {
		// 检查采购单号是否已存在
		checkPurchaseNumberExists(insertDTO.getPurchaseNumber(), null);

		OrdPurchaseCentralized entity = BeanUtil.copyProperties(insertDTO, OrdPurchaseCentralized.class);
		// 设置默认值
		entity.setPurchaseUserId(StpUtil.getLoginIdAsInt());
		entity.setPurchaseDate(LocalDate.now());
		entity.setPurchaseStatus(PurchaseCentralizedStatusEnum.PENDING_WAREHOUSE.getCode());
		entity.setCreateTime(LocalDateTime.now());
		entity.setUpdateTime(LocalDateTime.now());

		this.save(entity);

		// 保存关联的订单
		if (CollUtil.isNotEmpty(insertDTO.getOrderSnList())) {
			List<OrdPurchaseCentralizedOrder> orderList = insertDTO.getOrderSnList().stream()
				.map(orderSn -> OrdPurchaseCentralizedOrder.builder()
					.purchaseId(entity.getId())
					.orderSn(orderSn)
					.build())
				.collect(Collectors.toList());
			orderList.forEach(ordPurchaseCentralizedOrderMapper::insert);
		}
	}

	@Transactional
	@Override
	public void update(OrdPurchaseCentralizedUpdateDTO updateDTO) {
		// 检查采购单号是否已存在（排除当前记录）
		checkPurchaseNumberExists(updateDTO.getPurchaseNumber(), updateDTO.getId());

		OrdPurchaseCentralized entity = BeanUtil.copyProperties(updateDTO, OrdPurchaseCentralized.class);
		entity.setUpdateTime(LocalDateTime.now());
		this.baseMapper.updateById(entity);

		// 更新关联的订单
		// 先删除原有关联
		LambdaQueryWrapper<OrdPurchaseCentralizedOrder> deleteWrapper = new LambdaQueryWrapper<>();
		deleteWrapper.eq(OrdPurchaseCentralizedOrder::getPurchaseId, updateDTO.getId());
		ordPurchaseCentralizedOrderMapper.delete(deleteWrapper);

		// 如果有新的订单列表，重新插入
		if (CollUtil.isNotEmpty(updateDTO.getOrderSnList())) {
			List<OrdPurchaseCentralizedOrder> orderList = updateDTO.getOrderSnList().stream()
				.map(orderSn -> OrdPurchaseCentralizedOrder.builder()
					.purchaseId(updateDTO.getId())
					.orderSn(orderSn)
					.build())
				.collect(Collectors.toList());
			orderList.forEach(ordPurchaseCentralizedOrderMapper::insert);
		}
	}

	@Override
	public OrdPurchaseCentralizedDetailVO detail(Integer id) {
		OrdPurchaseCentralized entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, OrdPurchaseCentralizedDetailVO.class);
	}

	@Override
	public Page<OrdPurchaseCentralizedPageVO> page(OrdPurchaseCentralizedPageDTO pageDTO) {
		Page<OrdPurchaseCentralizedPageVO> page = this.baseMapper.page(pageDTO);

		// 查询关联的订单号列表
		if (CollUtil.isNotEmpty(page.getRecords())) {
			List<Integer> purchaseIds = page.getRecords().stream()
				.map(OrdPurchaseCentralizedPageVO::getId)
				.collect(Collectors.toList());

			LambdaQueryWrapper<OrdPurchaseCentralizedOrder> wrapper = new LambdaQueryWrapper<>();
			wrapper.in(OrdPurchaseCentralizedOrder::getPurchaseId, purchaseIds);
			List<OrdPurchaseCentralizedOrder> orderList = ordPurchaseCentralizedOrderMapper.selectList(wrapper);

			Map<Integer, List<String>> orderSnMap = orderList.stream()
				.collect(Collectors.groupingBy(
					OrdPurchaseCentralizedOrder::getPurchaseId,
					Collectors.mapping(OrdPurchaseCentralizedOrder::getOrderSn, Collectors.toList())
				));

			page.getRecords().forEach(record -> {
				List<String> orderSnList = orderSnMap.get(record.getId());
				record.setOrderSnList(orderSnList != null ? orderSnList : new ArrayList<>());
			});
		}

		return page;
	}

	@Transactional
	@Override
	public void delete(OrdPurchaseCentralizedDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Transactional
	@Override
	public void confirmStatus(Integer id, String status) {
		OrdPurchaseCentralized entity = new OrdPurchaseCentralized();
		entity.setId(id);
		entity.setPurchaseStatus(status);
		entity.setConfirmUserId(StpUtil.getLoginIdAsInt());
		entity.setConfirmTime(LocalDateTime.now());
		entity.setUpdateTime(LocalDateTime.now());
		this.baseMapper.updateById(entity);
	}

	/**
	 * 检查采购单号是否已存在
	 * @param purchaseNumber 采购单号
	 * @param excludeId 排除的记录ID（修改时使用）
	 */
	private void checkPurchaseNumberExists(String purchaseNumber, Integer excludeId) {
		LambdaQueryWrapper<OrdPurchaseCentralized> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(OrdPurchaseCentralized::getPurchaseNumber, purchaseNumber);

		// 修改时排除当前记录
		if (excludeId != null) {
			queryWrapper.ne(OrdPurchaseCentralized::getId, excludeId);
		}

		long count = this.count(queryWrapper);
		if (count > 0) {
			BusinessException.by("采购单号【{}】已存在，请使用其他单号", purchaseNumber);
		}
	}
}
