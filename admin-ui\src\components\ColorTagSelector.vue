<template>
  <div class="color-tag-selector">
    <!-- 展示当前选中的颜色标签，带点击事件 -->
    <div v-if="!showOptions" @click="toggleOptions" class="selected-tag-container">
      <el-tag
        v-if="selectedColor"
        :type="selectedColor"
        size="medium"
        class="clickable-tag">
        {{ displayText }}
      </el-tag>
      <el-tag
        v-else
        type="info"
        size="medium"
        class="clickable-tag placeholder-tag">
        点击选择颜色
      </el-tag>
      <i class="el-icon-caret-bottom" style="margin-left: 5px; color: #999;"></i>
    </div>

    <!-- 颜色选择面板 -->
    <div v-if="showOptions" class="color-options-panel">
      <div class="options-header">
        <span>选择颜色：</span>
        <el-button type="text" @click="toggleOptions" style="padding: 0;">
          <i class="el-icon-close"></i>
        </el-button>
      </div>

      <!-- 清除按钮 -->
      <div class="clear-option" v-if="selectedColor">
        <el-button
          type="text"
          size="mini"
          @click="clearColor"
          class="clear-button">
          <i class="el-icon-delete"></i> 清除颜色
        </el-button>
      </div>

      <div class="color-tags">
        <el-tag
          v-for="option in colorOptions"
          :key="option.value"
          :type="option.value"
          size="medium"
          class="color-option-tag"
          :class="{ 'selected': selectedColor === option.value }"
          @click="selectColor(option.value)">
          {{ displayText }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ColorTagSelector',
  props: {
    value: {
      type: String,
      default: ''
    },
    // 用于显示在标签上的文本
    displayText: {
      type: String,
      default: '示例'
    }
  },
  data() {
    return {
      showOptions: false,
      colorOptions: [
        { value: 'success', label: '成功（绿色）' },
        { value: 'primary', label: '主要（蓝色）' },
        { value: 'info', label: '信息（灰色）' },
        { value: 'warning', label: '警告（橙色）' },
        { value: 'danger', label: '危险（红色）' },
      ]
    }
  },
  computed: {
    selectedColor() {
      return this.value
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside)
  },

  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  },

  methods: {
    toggleOptions() {
      this.showOptions = !this.showOptions
    },

    selectColor(color) {
      this.$emit('input', color)
      this.$emit('change', color)
      this.showOptions = false
    },

    clearColor() {
      this.$emit('input', '')
      this.$emit('change', '')
      this.showOptions = false
    },

    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.showOptions = false
      }
    }
  }
}
</script>

<style scoped>
.color-tag-selector {
  position: relative;
  display: inline-block;
}

.selected-tag-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.clickable-tag:hover {
  transform: scale(1.05);
}

.placeholder-tag {
  color: #999 !important;
  border-style: dashed !important;
}

.color-options-panel {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  padding: 12px;
  margin-top: 4px;
  min-width: 200px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.clear-option {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.clear-button {
  color: #f56c6c;
  padding: 4px 0;
}

.clear-button:hover {
  color: #f56c6c;
  background-color: rgba(245, 108, 108, 0.1);
}

.color-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.color-option-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.color-option-tag:hover {
  transform: scale(1.1);
}

.color-option-tag.selected {
  box-shadow: 0 0 0 2px #409eff;
}
</style>
