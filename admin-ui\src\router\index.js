// 导入组件
import Vue from 'vue';
import Router from 'vue-router';

import index from '../views/index';
import login from '../views/login';

import SysUserMyPasswordEdit from '../views/sys/SysUserMyPasswordEdit';

// 工作台模块 (wkb)
import DashBoard from '../views/schedule/DashBoard';
import WkbNote from '../views/wkb/WkbNote';
import WkbNotificationDetail from '../views/wkb/WkbNotificationDetail';
import WkbTodoDetail from '../views/wkb/WkbTodoDetail';

// 系统管理模块 (sys)
import SysDictItem from '../views/sys/SysDictItem';
import SysLog from '../views/sys/SysLog';
import SysMenu from '../views/sys/SysMenu';
import SysNotification from '../views/sys/SysNotification';
import SysNotificationAdd from '../views/sys/SysNotificationAdd';
import SysNotificationDetail from '../views/sys/SysNotificationDetail';
import SysParam from '../views/sys/SysParam';
import SysShop from '../views/sys/SysShop';
import SysUser from '../views/sys/SysUser';

// 结算管理模块 (stl)
import SysAttendance from '../views/schedule/SysAttendance';
import SysShift from '../views/schedule/SysShift';
import StlPurchase from '../views/stl/StlPurchase';
import StlRefund from '../views/stl/StlRefund';










// 订单管理模块
import OrdAfterSale from '../views/order/OrdAfterSale.vue';
import OrdClaim from '../views/order/OrdClaim.vue';

// 财务管理模块
import FinancialMonthlyReport from '../views/finance/FinancialMonthlyReport.vue';

import OrdLogisticsDoneNotWarehoused from '../views/order/OrdLogisticsDoneNotWarehoused.vue';
import OrdLogisticsNotComplete from '../views/order/OrdLogisticsNotComplete.vue';
import OrdPurchase from '../views/order/OrdPurchase.vue';
import OrdPurchaseCentralized from '../views/order/OrdPurchaseCentralized.vue';

import OrdRefund from '../views/order/OrdRefund.vue';
import OrdRepurchase from '../views/order/OrdRepurchase.vue';
import OrdTaiwan from '../views/order/OrdTaiwan.vue';
import OrdWarehousedNotOutbound from '../views/order/OrdWarehousedNotOutbound.vue';
import OrdWarehousedRepurchase from '../views/order/OrdWarehousedRepurchase.vue';
import OrderStatusSummary from '../views/order/OrderStatusSummary.vue';

// router enable
Vue.use(Router);

// export router
export default new Router({
    routes: [
        {path:'/', name:'', component:login, hidden:true, meta:{requireAuth:false}},
        {path:'/login', name:'登录', component:login, hidden:true, meta:{requireAuth:false}},
        {path:'/index', name:'首页', component:index, iconCls:'el-icon-tickets', children:[

            {path:'/SysUserMyPasswordEdit', name:'MyPasswordEdit', component:SysUserMyPasswordEdit, meta:{requireAuth:true}},

            {path:'/DashBoard', name:'工作台', component:DashBoard, meta:{requireAuth:true}},
            {path:'/WkbNote', name:'工作笔记', component:WkbNote, meta:{requireAuth:true}},
            {path:'/WkbTodoDetail', name:'待办事项详情', component:WkbTodoDetail, meta:{requireAuth:true}},
            {path:'/WkbNotificationDetail', name:'通知公告详情', component:WkbNotificationDetail, meta:{requireAuth:true}},

            {path:'/SysParam', name:'系统参数配置', component:SysParam, meta:{requireAuth:true}},
            {path:'/SysShop', name:'店铺管理', component:SysShop, meta:{requireAuth:true}},
            {path:'/SysUser', name:'人员管理', component:SysUser, meta:{requireAuth:true}},
            {path:'/StlPurchase', name:'采购结算', component:StlPurchase, meta:{requireAuth:true}},
            {path:'/StlRefund', name:'退款结算', component:StlRefund, meta:{requireAuth:true}},

            // 订单管理模块
            {path:'/OrdLogisticsNotComplete', name:'物流未完成', component:OrdLogisticsNotComplete, meta:{requireAuth:true}},
            {path:'/OrdLogisticsDoneNotWarehoused', name:'物流已完成但未入库', component:OrdLogisticsDoneNotWarehoused, meta:{requireAuth:true}},
            {path:'/OrdWarehousedNotOutbound', name:'已入库但未出库', component:OrdWarehousedNotOutbound, meta:{requireAuth:true}},

            {path:'/OrdPurchase', name:'采购管理', component:OrdPurchase, meta:{requireAuth:true}},
            {path:'/OrdPurchaseCentralized', name:'集中采购', component:OrdPurchaseCentralized, meta:{requireAuth:true}},
            {path:'/OrdWarehousedRepurchase', name:'已入库重复采购', component:OrdWarehousedRepurchase, meta:{requireAuth:true}},

            {path:'/OrdRepurchase', name:'重新采购订单', component:OrdRepurchase, meta:{requireAuth:true}},
            {path:'/OrdAfterSale', name:'售后服务', component:OrdAfterSale, meta:{requireAuth:true}},
            {path:'/OrdRefund', name:'退款管理', component:OrdRefund, meta:{requireAuth:true}},
            {path:'/OrdClaim', name:'理赔管理', component:OrdClaim, meta:{requireAuth:true}},
            {path:'/OrdTaiwan', name:'台湾订单', component:OrdTaiwan, meta:{requireAuth:true}},
            {path:'/OrderStatusSummary', name:'订单状态汇总', component:OrderStatusSummary, meta:{requireAuth:true}},

            {path:"/SysShift", name:"排班管理", component:SysShift, meta:{requireAuth:true}},
            {path:"/SysAttendance", name:"考勤管理", component:SysAttendance, meta:{requireAuth:true}},

            // 财务管理模块
            {path:'/FinancialMonthlyReport', name:'财务月报', component:FinancialMonthlyReport, meta:{requireAuth:true}},










            {path:'/SysDictItem', name:'字典管理', component:SysDictItem, meta:{requireAuth:true}},
            {path:'/SysLog', name:'系统日志', component:SysLog, meta:{requireAuth:true}},
            {path:'/SysMenu', name:'菜单权限', component:SysMenu, meta:{requireAuth:true}},
            {path:'/SysNotification', name:'通知公告', component:SysNotification, meta:{requireAuth:true}},
            {path:'/SysNotificationAdd', name:'新增通知公告', component:SysNotificationAdd, meta:{requireAuth:true}},
            {path:'/SysNotificationDetail', name:'查看通知公告详情', component:SysNotificationDetail, meta:{requireAuth:true}},
        ]}]
})
