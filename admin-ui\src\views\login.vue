<template>
  <div class="login-wrap">
    <!-- 左侧区域 -->
    <div class="login-left">
      <div class="left-background">
        <div class="overlay"></div>
        <div class="left-content">
          <h2 class="system-title">跨境电商运营管理系统</h2>

          <!-- 特性列表 -->
          <div class="features-list">
            <!-- 智能计算管理 -->
            <div class="feature-item">
              <div class="feature-icon">
                <img src="../assets/img/f1.png" alt="智能计算管理" />
              </div>
              <div class="feature-text">
                <h4>智能计算管理</h4>
                <p>实时监控订单状态，综合计算成本与利润</p>
              </div>
            </div>

            <!-- 全球客服协同 -->
            <div class="feature-item">
              <div class="feature-icon">
                <img src="../assets/img/f2.png" alt="全球客服协同" />
              </div>
              <div class="feature-text">
                <h4>物流状态跟踪</h4>
                <p>抓取物流节点状态掌握包裹进度，保障货物交付效率，提升客户对物流可视化的体验</p>
              </div>
            </div>

            <!-- 高效售后服务 -->
            <div class="feature-item">
              <div class="feature-icon">
                <img src="../assets/img/f3.png" alt="高效售后服务" />
              </div>
              <div class="feature-text">
                <h4>采购及退换货跟踪</h4>
                <p>统筹库存、订单发货/退货的衔接，避免缺货或积压，加速问题解决，提升售后满意度</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="login-right">
      <div class="login-container">
        <h3 class="welcome-title">欢迎登录</h3>
        <p class="welcome-subtitle">请输入您的账号密码登录系统</p>

        <el-form label-position="left" :model="ruleForm" :rules="rules" ref="ruleForm" label-width="0px" class="demo-ruleForm">
          <el-form-item prop="username">
            <el-input
              type="text"
              v-model="ruleForm.username"
              auto-complete="off"
              @keyup.enter.native="submitForm('ruleForm')"
              placeholder="用户名"
              prefix-icon="el-icon-user">
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              type="password"
              v-model="ruleForm.password"
              auto-complete="off"
              @keyup.enter.native="submitForm('ruleForm')"
              placeholder="密码"
              prefix-icon="el-icon-lock">
            </el-input>
          </el-form-item>
          <el-checkbox class="remember" v-model="rememberpwd">记住密码</el-checkbox>
          <el-form-item style="width:100%;">
            <el-button type="primary" style="width:100%;" @click="submitForm('ruleForm')" :loading="logining">登录</el-button>
          </el-form-item>
        </el-form>

        <div class="footer-text">
          © 2025 跨境电商运营管理系统
        </div>
      </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
import { authLogin } from '../api/Auth.js'
import { sysUserProfile } from '../api/SysUser'
import { getPermissions } from '../api/SysMenu'
import { setCookie, getCookie, delCookie } from '../utils/util'
export default {
  name: 'login',
  data() {
    return {
      logining: false,
      rememberpwd: false,
      ruleForm: {
        username: '',
        password: '',
      },
      //rules前端验证
      rules: {
        username: [{ required: true, message: '必填', trigger: 'blur' }],
        password: [{ required: true, message: '必填', trigger: 'blur' }],
      }
    }
  },
  created() {
    // 加载缓存的密码
    this.loadCachePwd()
  },
  methods: {
    loadCachePwd() {
      if (getCookie('user') != '' && getCookie('pwd') != '') {
        this.ruleForm.username = getCookie('user')
        this.ruleForm.password = getCookie('pwd')
        this.rememberpwd = true
      }
    },
    //submit
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (!valid) {
          this.logining = false
          return false
        }
        this.logining = false;
        // login submit
        authLogin(this.ruleForm).then(res => {
          if (this.rememberpwd) {
            // expire after 7 days
            setCookie('user', this.ruleForm.username, 7)
            setCookie('pwd', this.ruleForm.password, 7)
          } else {
            delCookie('user')
            delCookie('pwd')
          }
          // 缓存token
          localStorage.setItem('logintoken', res.data)
          // 缓存用户资料
          sysUserProfile()
            .then(res => {
              localStorage.setItem('userEntity', JSON.stringify(res.data))
              
              // 获取用户权限列表
              this.$store.dispatch('getPermissions').then(() => {
                // 跳转
                setTimeout(() => {
                  this.logining = true
                  this.$store.commit('login', 'true')
                  this.$router.push({ path: '/DashBoard' })
                }, 1000)
              }).catch(err => {
                console.error('获取权限列表失败:', err)
                // 即使获取权限失败也继续登录
                setTimeout(() => {
                  this.logining = true
                  this.$store.commit('login', 'true')
                  this.$router.push({ path: '/DashBoard' })
                }, 1000)
              })
            })
        })
      })
    },
  }
}
</script>

<style scoped>
.login-wrap {
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  display: flex;
  background-color: #f5f7fa;
}

/* 左侧区域 */
.login-left {
  flex: 0 0 45%;
  position: relative;
  min-height: 100vh;
  box-sizing: border-box;
}

.left-background {
  width: 100%;
  height: 100%;
  position: relative;
  background-image: url('../assets/img/login-left-backgroud.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-sizing: border-box;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(131deg, rgb(151 135 135 / 50%) 0%, rgba(0, 0, 0, 0.3) 100%);
  filter: grayscale(10%);
}

.left-content {
  position: absolute;
  top: 50%;
  left: 20vh;
  transform: translateY(-50%);
  color: white;
  z-index: 10;
  max-width: 500px;
}

.system-title {
  font-size: 40px;
  font-weight: bold;
  margin-bottom: 50px;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 25px 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 12px;
}

.feature-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.feature-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.feature-text h4 {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #ffffff;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8), 1px 1px 3px rgba(0, 0, 0, 0.9);
  letter-spacing: 0.5px;
}

.feature-text p {
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
  color: #f8f9fa;
  opacity: 1;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.7), 1px 1px 2px rgba(0, 0, 0, 0.8);
  font-weight: 400;
}

.subtitle {
  font-size: 28px;
  margin-bottom: 30px;
  color: #e8f4fd;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.description {
  font-size: 18px;
  line-height: 1.6;
  color: #d1e7f5;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 右侧登录区域 */
.login-right {
  flex: 0 0 55%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background: white;
  box-sizing: border-box;
}

.login-container {
  width: 100%;
  max-width: 550px;
  padding: 50px 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  box-sizing: border-box;
}

.welcome-title {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 15px;
}

.welcome-subtitle {
  font-size: 16px;
  color: #7f8c8d;
  text-align: center;
  margin-bottom: 40px;
}

.demo-ruleForm .el-form-item {
  margin-bottom: 25px;
}

.demo-ruleForm .el-input__inner {
  height: 50px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  font-size: 16px;
  padding-left: 45px;
}

.demo-ruleForm .el-input__inner:focus {
  border-color: #409eff;
}

.demo-ruleForm .el-input__prefix {
  left: 15px;
}

.remember {
  margin: 0px 0px 30px 0px;
  font-size: 14px;
  color: #606266;
}

.el-button--primary {
  height: 50px;
  font-size: 16px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 500;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.footer-text {
  text-align: center;
  margin-top: 30px;
  font-size: 14px;
  color: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .login-left {
    flex: 0 0 45%;
    box-sizing: border-box;
  }

  .login-right {
    flex: 0 0 55%;
    padding: 20px;
    box-sizing: border-box;
  }

  .left-content {
    left: 30px;
    top: 50%;
  }

  .system-title {
    font-size: 32px;
    margin-bottom: 40px;
  }

  .feature-item {
    gap: 15px;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
  }

  .feature-text h4 {
    font-size: 18px;
  }

  .feature-text p {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .login-wrap {
    flex-direction: column;
  }

  .login-left {
    flex: 0 0 300px;
    box-sizing: border-box;
  }

  .login-right {
    flex: 1;
    padding: 20px;
    box-sizing: border-box;
  }

  .login-container {
    width: 100%;
    max-width: 400px;
    padding: 30px 20px;
    box-sizing: border-box;
  }

  .left-content {
    left: 20px;
    right: 20px;
    max-width: none;
    top: 50%;
  }

  .system-title {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .features-list {
    gap: 20px;
  }

  .feature-item {
    gap: 12px;
  }

  .feature-icon {
    width: 40px;
    height: 40px;
  }

  .feature-text h4 {
    font-size: 16px;
  }

  .feature-text p {
    font-size: 13px;
  }
}
</style>
