import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 汇率日期区间API
 */

// 分页查询汇率日期区间列表
export function pageSysExchangeRateDayRange(params) {
  return reqGet('/sys-exchange-rate-day-range/page', params)
}

// 根据ID查询汇率日期区间详情
export function getSysExchangeRateDayRangeById(startDay, endDay) {
  return reqGet(`/sys-exchange-rate-day-range/${startDay}/${endDay}`)
}

// 新增汇率日期区间
export function insertSysExchangeRateDayRange(data) {
  return reqPost('/sys-exchange-rate-day-range', data)
}

// 修改汇率日期区间
export function updateSysExchangeRateDayRange(data) {
  return reqPut('/sys-exchange-rate-day-range', data)
}

// 删除汇率日期区间
export function deleteSysExchangeRateDayRange(data) {
  return reqDelete('/sys-exchange-rate-day-range', data)
}
