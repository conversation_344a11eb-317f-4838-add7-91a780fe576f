package com.my.crossborder.forest.erp990;

import com.dtflys.forest.annotation.Address;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Headers;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.callback.OnError;
import com.my.crossborder.forest.erp990.address.Erp990Address;
import com.my.crossborder.forest.erp990.dto.LoginDTO;
import com.my.crossborder.forest.erp990.vo.CaptchaimageVO;
import com.my.crossborder.forest.erp990.vo.LoginVO;

/**
 * 禾晨物流登录相关API
 * 
 * <AUTHOR>
 */
@Address(source = Erp990Address.class)
public interface Erp990AuthClient {

	
    /**
     * 验证码图片
     */
    @Get(url = "/api/captchaImage", connectTimeout = 15000, readTimeout = 15000)
    @Headers({
        "user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    })
    CaptchaimageVO captchaImage(OnError onError);
    
    
    /**
     * 登录
     */
    @Post(url = "/api/login", connectTimeout = 15000, readTimeout = 15000)
    @Headers({
    	"user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    })
    LoginVO login(@JSONBody LoginDTO loginDTO, OnError onError);
    
    
} 