package com.my.crossborder.mybatis.conf;

import java.time.LocalDateTime;

import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import cn.dev33.satoken.stp.StpUtil;

/**
 * Mybatis-plus自动填充
 */
@Component
public class MpFieldAutoFill implements MetaObjectHandler {

    /**
     * 创建时间
     */
    private static final String CREATE_TIME_FIELD = "createTime";
    
    /**
     * 是否可用
     */
    private static final String ENABLE_FIELD = "enable";
    
    /**
     * 修改时间
     */
    private static final String UPDATE_TIME_FIELD = "updateTime";

    /**
     * 录入人ID
     */
    private static final String ISSUE_USER_ID_FIELD = "issueUserId";

    /**
     * 创建人ID
     */
    private static final String CREATE_USER_ID_FIELD = "createUserId";


    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
		this.strictInsertFill(metaObject, CREATE_TIME_FIELD, LocalDateTime.class, now);
        this.strictInsertFill(metaObject, UPDATE_TIME_FIELD, LocalDateTime.class, now);
        this.strictInsertFill(metaObject, ENABLE_FIELD, Boolean.class, true);

        // 自动填充用户ID字段
        try {
            if (StpUtil.isLogin()) {
                Integer currentUserId = StpUtil.getLoginIdAsInt();
                this.strictInsertFill(metaObject, ISSUE_USER_ID_FIELD, Integer.class, currentUserId);
                this.strictInsertFill(metaObject, CREATE_USER_ID_FIELD, Integer.class, currentUserId);
            }
        } catch (Exception e) {
            // 如果获取用户ID失败，不影响其他字段的自动填充
            // 可以记录日志，但不抛出异常
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, UPDATE_TIME_FIELD, LocalDateTime.class, LocalDateTime.now());
    }
    
}
