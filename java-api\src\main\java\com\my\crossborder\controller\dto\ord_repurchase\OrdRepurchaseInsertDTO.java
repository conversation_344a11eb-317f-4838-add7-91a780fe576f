package com.my.crossborder.controller.dto.ord_repurchase;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_重新采购
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdRepurchaseInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单号
     */
	@NotBlank(message="orderSn不能为空")
    private String orderSn;

    /**
     * 问题描述
     */
	@NotBlank(message="issue不能为空")
    private String issue;

    /**
     * 处理办法 字典参数repurchase_close_way
     */
	@NotBlank(message="closeWay不能为空")
    private String closeWay;

   

}
