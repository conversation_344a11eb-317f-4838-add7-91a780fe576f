package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRatePageDTO;
import com.my.crossborder.controller.vo.sys_exchange_rate.SysExchangeRatePageVO;
import com.my.crossborder.mybatis.entity.SysExchangeRate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 汇率表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface SysExchangeRateMapper extends BaseMapper<SysExchangeRate> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysExchangeRatePageVO> page(SysExchangeRatePageDTO pageDTO);
	
}
