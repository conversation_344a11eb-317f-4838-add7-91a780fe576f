package com.my.crossborder.controller.dto.wkb_notification;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_通知表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知id
     */
	@NotNull(message="id不能为空")
    private Integer id;

    /**
     * 类别
     */
	@NotNull(message="type不能为空")
    private String type;

    /**
     * 标题
     */
	@NotNull(message="title不能为空")
    private String title;

    /**
     * 内容
     */
	@NotNull(message="content不能为空")
    private String content;

    /**
     * 发布时间
     */
	@NotNull(message="publishTime不能为空")
    private LocalDateTime publishTime;

    /**
     * 发布用户id
     */
	@NotNull(message="publishUserId不能为空")
    private Integer publishUserId;

}
