package com.my.crossborder.controller.vo.sys_exchange_rate_day_range;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_汇率日期区间
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysExchangeRateDayRangeDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
    private LocalDate startDay;

    /**
     * 结束日期
     */
    private LocalDate endDay;

    /**
     * 汇率（CNY/TWD的值，比如：4.0814）
     */
    private BigDecimal exchangeRate;

    /**
     * 更新人id
     */
    private Integer updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
