package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.wkb_todo.WkbTodoPageDTO;
import com.my.crossborder.controller.vo.wkb_todo.WkbTodoPageVO;
import com.my.crossborder.mybatis.entity.WkbTodo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 通知表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface WkbTodoMapper extends BaseMapper<WkbTodo> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<WkbTodoPageVO> page(WkbTodoPageDTO pageDTO);
	
}
