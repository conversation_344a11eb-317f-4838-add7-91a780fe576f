package com.my.crossborder.forest.siliconflow.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import com.my.crossborder.forest.siliconflow.dto.component.Message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会话补全
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatCompletionsDTO {
	
	@NotBlank
	String model;
	
	@NotEmpty
	List<Message> messages;
	
	Integer maxTokens;
	
	Double temperature;

}
