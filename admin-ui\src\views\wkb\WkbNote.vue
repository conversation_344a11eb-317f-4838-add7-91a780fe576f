<template>
  <div class="note-container">
    <!-- 页面头部 -->
    <div class="note-header">
      <div class="header-left">
        <h2 class="page-title">
          <i class="el-icon-notebook-1"></i>
          工作笔记
        </h2>
      </div>
      <!-- 视图切换 -->
      <div class="header-right">
        <el-button-group>
          <el-button :type="viewMode === 'grid' ? 'primary' : ''" icon="el-icon-s-grid"
            @click="viewMode = 'grid'"></el-button>
          <el-button :type="viewMode === 'list' ? 'primary' : ''" icon="el-icon-s-order"
            @click="viewMode = 'list'"></el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-loading="loading" class="note-content">
      <!-- 查询条件 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="订单号">
            <el-input v-model="searchForm.orderSn" placeholder="请输入订单号" style="width: 200px;" clearable>
            </el-input>
          </el-form-item>
          <el-form-item label="场景">
            <dict-select v-model="searchForm.scene" category-id="scene" placeholder="请选择场景" style="width: 220px;"
              clearable>
            </dict-select>
          </el-form-item>
          <el-form-item label="处理状态">
            <el-select v-model="searchForm.sceneComplete" placeholder="处理状态" style="width: 120px;" clearable>
              <el-option label="待处理" :value="0"></el-option>
              <el-option label="已处理" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button type="primary" icon="el-icon-plus" @click="handleAdd">添加</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="note-grid">
        <div v-for="note in noteList" :key="note.id" class="note-card" :class="getCardClass(note.sceneComplete)">
          <div class="card-header">
            <div class="order-info">
              <div class="order-title">
                <h4>{{ note.orderSn }}</h4>
                <el-tag :type="getStatusType(note.sceneComplete)" size="small" @click="handleStatusClick(note)"
                  :style="!note.sceneComplete ? 'cursor: pointer;' : ''">
                  {{ getStatusText(note.sceneComplete) }}
                </el-tag>
              </div>
              <p class="logistics-no">{{ note.sceneName }}</p>
            </div>
            <div class="card-actions">
              <el-button type="text" v-if="note.sceneComplete" icon="el-icon-delete" @click="handleDelete(note)"></el-button>
              <el-button v-if="!note.sceneComplete" type="text" icon="el-icon-edit"  @click="handleEdit(note)"></el-button>
              <el-button type="text" icon="el-icon-view" @click="handleView(note)"></el-button>
              <el-button v-if="note.scene !== '99'" type="text" icon="el-icon-position" @click="handleViewNote(note)"></el-button>
            </div>
          </div>
          <div class="card-body">
            <p class="note-content-text">{{ note.content }}</p>
          </div>
          <div class="card-footer">
            <span class="time-info">
              <i class="el-icon-time"></i>
              {{ formatTime(note.updateTime) }}
            </span>
          </div>
        </div>
        <div v-if="noteList.length === 0 && !loading" class="empty-note-placeholder">
          <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
          <p>暂无数据</p>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="note-list">
        <el-table :data="noteList" stripe>
          <el-table-column prop="orderSn" label="订单号" min-width="80"></el-table-column>
          <el-table-column prop="sceneName" label="场景" min-width="80"></el-table-column>
          <el-table-column prop="status" label="处理状态" min-width="50">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.sceneComplete)" size="small">{{ getStatusText(scope.row.sceneComplete)
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="content" label="内容" show-overflow-tooltip min-width="150"></el-table-column>
          <el-table-column prop="updateTime" label="更新时间" min-width="150">
            <template slot-scope="scope">
              {{ formatTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="120">
            <template slot-scope="scope">
              <el-button v-if="scope.row.scene !== '99'" type="text" size="medium" icon="el-icon-position" @click="handleViewNote(scope.row)">场景</el-button>
              <el-button v-if="!scope.row.sceneComplete" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button v-else type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
              <el-button v-if="scope.row.sceneComplete" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="noteList.length === 0 && !loading" class="empty-note-placeholder">
          <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
          <p>暂无数据</p>
        </div>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
        </el-pagination>
      </div>
    </div>

    <!-- 添加/编辑工作笔记对话框 -->
    <el-dialog :title="dialogMode === 'add' ? '添加工作笔记' : '编辑工作笔记'" :visible.sync="dialogVisible" width="600px"
      @closed="resetForm">
      <el-form :model="noteForm" :rules="noteRules" ref="noteForm" label-width="100px">
        <el-form-item label="订单号" prop="orderSn">
          <el-input v-model="noteForm.orderSn" placeholder="请输入订单号" maxlength="16" :readonly="dialogMode === 'edit' && noteForm.scene !== '99'"></el-input>
        </el-form-item>
        <el-form-item label="场景" prop="scene">
          <dict-select v-model="noteForm.scene" category-id="SCENE" placeholder="请选择场景" readonly disabled>
          </dict-select>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-model="noteForm.content" type="textarea" :rows="4" placeholder="请输入备注内容" maxlength="1024" show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 查看工作笔记对话框 -->
    <el-dialog title="查看工作笔记" :visible.sync="viewDialogVisible" width="600px">
      <el-form label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="viewForm.orderSn" readonly></el-input>
        </el-form-item>
        <el-form-item label="场景">
          <el-input :value="viewForm.sceneName" readonly></el-input>
        </el-form-item>
        <el-form-item label="处理状态">
          <el-tag :type="getStatusType(viewForm.sceneComplete)" size="small">{{ getStatusText(viewForm.sceneComplete) }}</el-tag>
        </el-form-item>
        <el-form-item label="内容">
          <el-input v-model="viewForm.content" type="textarea" :rows="4" readonly>
          </el-input>
        </el-form-item>
        <el-form-item label="更新时间">
          <el-input :value="formatTime(viewForm.updateTime)" readonly></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getNotePage,
  insertNote,
  updateNote,
  deleteNote,
  getByOrderSnAndScene,
  markCompleted
} from '../../api/WkbNote'
import DictSelect from '../../components/DictSelect.vue'

export default {
  name: 'WkbNote',
  components: {
    DictSelect
  },
  data() {
    return {
      // 查询表单
      searchForm: {
        orderSn: '',
        scene: '',
        sceneComplete: 0  // 默认选中"待处理"
      },

      // 视图模式
      viewMode: 'grid', // grid 或 list

      // 加载状态
      loading: false,
      submitLoading: false,

      // 备注列表
      noteList: [],

      // 分页信息
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 场景列表
      sceneList: [
        { code: '01', name: '采购登记', routePath: 'OrdPurchase' },
        { code: '02', name: '未完整填写物流编号', routePath: 'OrdLogisticsNotComplete' },
        { code: '03', name: '已填物流编号未入库', routePath: 'OrdLogisticsDoneNotWarehoused' },
        { code: '04', name: '货物已入库未出库', routePath: 'OrdWarehousedNotOutbound' },
        { code: '05', name: '已采购但出库前取消', routePath: 'OrdRefund' },
        { code: '06', name: '货物已入库重新采购', routePath: 'OrdRepurchase' },
        { code: '07', name: '已出库售后', routePath: 'OrdAfterSale' },
        { code: '08', name: '物流理赔', routePath: 'OrdClaim' },
      ],

      // 对话框相关
      dialogVisible: false,
      dialogMode: 'add', // add 或 edit
      viewDialogVisible: false,

      // 表单数据
      noteForm: {
        id: '',
        orderSn: '',
        scene: '',
        content: ''
      },

      // 查看表单数据
      viewForm: {
        id: '',
        orderSn: '',
        scene: '',
        sceneName: '',
        content: '',
        sceneComplete: false,
        updateTime: ''
      },

      // 表单验证规则
      noteRules: {
        orderSn: [
          { required: true, message: '请输入订单号', trigger: 'blur' }
        ],
        scene: [
          { required: true, message: '场景不能为空', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入备注内容', trigger: 'blur' },
          { max: 1024, message: '内容长度不能超过1024个字符', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.loadNoteList();
  },
  methods: {
    // 加载备注列表
    async loadNoteList() {
      this.loading = true;
      try {
        const params = {
          current: this.pagination.currentPage,
          size: this.pagination.pageSize
        };

        // 添加查询条件
        if (this.searchForm.orderSn) {
          params.orderSn = this.searchForm.orderSn;
        }
        if (this.searchForm.scene) {
          params.scene = this.searchForm.scene;
        }
        if (this.searchForm.sceneComplete !== '') {
          params.sceneComplete = this.searchForm.sceneComplete;
        }

        // 调用API接口
        const response = await getNotePage(params);

        if (response.success) {
          this.noteList = response.data.records || [];
          this.pagination.total = response.data.total || 0;
        } else {
          this.$message.error(response.message || '加载失败');
        }
      } catch (error) {
        console.error('加载列表失败:', error);
        // 模拟数据，便于开发测试
        this.loadMockData();
      } finally {
        this.loading = false;
      }
    },

    // 模拟数据（开发测试用）
    loadMockData() {
      const mockData = [
        {
          id: '1',
          orderSn: 'ORD2024001',
          scene: '02',
          sceneName: '未完整填写物流编号',
          sceneComplete: false,
          content: '客户要求尽快处理，需要联系仓库确认货物状态。',
          updateTime: '2024-01-20T14:30:00'
        },
        {
          id: '2',
          orderSn: 'ORD2024002',
          scene: '06',
          sceneName: '货物已入库重新采购',
          sceneComplete: true,
          content: '已完成货物打包，等待发货。请通知客户准备收货。',
          updateTime: '2024-01-20T15:45:00'
        },
        {
          id: '3',
          orderSn: 'ORD2024003',
          scene: '07',
          sceneName: '已出库售后',
          sceneComplete: false,
          content: '需要与客户确认送货地址，联系电话已更新。',
          updateTime: '2024-01-20T16:20:00'
        },
        {
          id: '4',
          orderSn: 'ORD2024004',
          scene: '08',
          sceneName: '物流理赔',
          sceneComplete: true,
          content: '已完成送货，客户签收，请通知财务结算。',
          updateTime: '2024-01-20T17:15:00'
        },
        {
          id: '5',
          orderSn: 'ORD2024005',
          scene: '99',
          sceneName: '其他',
          sceneComplete: false,
          content: '这是一个场景为99的工作笔记，不需要场景跳转按钮。',
          updateTime: '2024-01-20T18:30:00'
        }
      ];

      this.noteList = mockData;
      this.pagination.total = mockData.length;
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.loadNoteList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        orderSn: '',
        scene: '',
        sceneComplete: 0  // 重置时也默认选中"待处理"
      };
      this.pagination.currentPage = 1;
      this.loadNoteList();
    },

    // 状态文本转换
    getStatusText(status) {
      switch (status) {
        case false: return '待处理';
        case true: return '已处理';
        default: return '未知';
      }
    },

    // 获取状态标签类型
    getStatusType(status) {
      switch (status) {
        case false: return 'warning';
        case true: return 'success';
        default: return 'info';
      }
    },

    // 获取卡片样式class
    getCardClass(status) {
      switch (status) {
        case false: return 'card-pending';
        case true: return 'card-processed';
        default: return '';
      }
    },

    // 处理状态点击事件
    handleStatusClick(note) {
      if (!note.sceneComplete) { // 待处理状态可以点击变更为已处理
        this.$confirm('确定要标记为已处理吗？', '状态变更', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            await markCompleted(note.id);
            this.$message.success('状态变更成功');
            this.loadNoteList();
          } catch (error) {
            this.$message.error('状态变更失败');
          }
        });
      }
    },

    // 时间格式化
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + ' ' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0');
    },

    // 根据场景跳转到对应页面
    handleViewNote(note) {
      console.log('点击查看工作笔记:', note);
      console.log('工作笔记场景代码:', note.scene);
      console.log('订单号:', note.orderSn);
      console.log('场景列表:', this.sceneList);

      const scene = this.sceneList.find(s => s.code === note.scene);
      console.log('找到的场景配置:', scene);

      if (scene) {
        // 使用path方式跳转（因为路由配置中name是中文）
        const routeData = {
          path: `/${scene.routePath}`,
          query: {
            orderSn: note.orderSn
          }
        };
        console.log('准备跳转的路由数据:', routeData);

        // 跳转到目标页面
        this.$router.push(routeData).then(() => {
          console.log('路由跳转成功');
          this.$message.success(`已跳转到${scene.name}页面`);
        }).catch(err => {
          console.error('路由跳转失败:', err);
          this.$message.error(`页面跳转失败: ${err.message}`);
        });
      } else {
        console.error('未找到场景配置，scene code:', note.scene);
        this.$message.warning(`未找到场景代码 "${note.scene}" 对应的页面路由`);
      }
    },

    // 新建
    handleAdd() {
      this.dialogMode = 'add';
      this.noteForm.scene = '99'; // 添加模式时设置场景为99
      this.dialogVisible = true;
    },

    // 编辑
    handleEdit(note) {
      this.dialogMode = 'edit';
      this.noteForm = { ...note };
      this.dialogVisible = true;
    },

    // 查看
    handleView(note) {
      this.viewForm = { ...note };
      this.viewDialogVisible = true;
    },

    // 删除
    handleDelete(note) {
      this.$confirm('确定要删除这条工作笔记吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteNote({ idList: [note.id] });

          this.$message.success('删除成功');
          this.loadNoteList();
        } catch (error) {
          this.$message.error('删除失败');
        }
      });
    },

    // 提交表单
    handleSubmit() {
      this.$refs.noteForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          try {
            if (this.dialogMode === 'add') {
              // 新建接口
              await insertNote(this.noteForm);
              this.$message.success('新建成功');
            } else {
              // 编辑接口
              await updateNote(this.noteForm);
              this.$message.success('编辑成功');
            }

            this.dialogVisible = false;
            this.loadNoteList();
          } catch (error) {
            this.$message.error(this.dialogMode === 'add' ? '新建失败' : '编辑失败');
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },

    // 重置表单
    resetForm() {
      this.noteForm = {
        id: '',
        orderSn: '',
        scene: '',
        content: ''
      };
      if (this.$refs.noteForm) {
        this.$refs.noteForm.resetFields();
      }
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadNoteList();
    },

    // 页码变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadNoteList();
    }
  }
}
</script>

<style scoped>
.note-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 100px);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-right {
  display: flex;
  align-items: center;
}

.note-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

/* 网格视图样式 */
.note-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.note-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.note-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 不同状态的卡片背景色 */
.note-card.card-pending {
  background-color: #fefbf3;
  /* 米白色 */
}

.note-card.card-processed {
  background-color: #f0f9f4;
  /* 浅绿色 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.order-info h4 {
  margin: 0;
  font-size: 15px;
  font-weight: bold;
  color: #303133;
}

.logistics-no {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.card-actions {
  display: flex;
  gap: 1px;
}

.card-body {
  margin-bottom: 12px;
}

.note-content-text {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
}

.time-info {
  font-size: 12px;
  color: #c0c4cc;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 列表视图样式 */
.note-list {
  margin-bottom: 20px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

/* 空数据提示样式 */
.empty-note-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
  text-align: center;
  grid-column: 1 / -1;
  /* 在网格布局中占满整行 */
}

.empty-note-placeholder p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

/* 页面头部样式 */
.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left .page-title {
  margin: 0;
  font-size: 20px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 查询表单样式 */
.search-form {
  background: #f8f9fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .note-header {
    flex-direction: column;
    gap: 15px;
  }

  .header-right {
    width: 100%;
    flex-wrap: wrap;
    gap: 10px;
  }

  .note-grid {
    grid-template-columns: 1fr;
  }
}
</style>
