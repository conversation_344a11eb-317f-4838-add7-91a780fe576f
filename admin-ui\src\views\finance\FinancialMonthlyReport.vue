<template>
  <div class="financial-monthly-report">
    <!-- 顶部控制栏 -->
    <div class="header-controls">
      <div class="left-controls">
        <el-date-picker
          v-model="selectedMonth"
          type="month"
          placeholder="选择月份"
          format="yyyy年MM月"
          value-format="yyyy-MM"
          @change="loadReportData">
        </el-date-picker>
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>(可能还需要显示具体店铺的)
      </div>
      <!-- <div class="right-info">
        <span class="update-time">数据更新时间: {{ updateTime }}</span> 
        <el-tag type="warning" size="small">数据会实时更新</el-tag>
      </div> -->
    </div>

    <!-- 主要数据展示区域 -->
    <div class="main-content">
      <!-- 收入概览 -->
      <el-card class="revenue-card">
        <div slot="header" class="card-header">
          <i class="el-icon-money"></i>
          <span>收入概览</span>
        </div>
        <div class="revenue-content">
          <div class="revenue-item">
            <div class="label">订单总收入</div>
            <div class="value positive">¥{{ formatNumber(reportData.revenue.totalOrderAmount) }}</div>
          </div>
          <div class="revenue-item">
            <div class="label">出库前取消</div>
            <div class="value negative">-¥{{ formatNumber(reportData.revenue.cancelledAmount) }}</div>
          </div>
          <div class="revenue-item total">
            <div class="label">净收入</div>
            <div class="value positive">¥{{ formatNumber(reportData.revenue.netRevenue) }}</div>
          </div>
        </div>
      </el-card>

      <!-- 支出概览 -->
      <el-card class="expense-card">
        <div slot="header" class="card-header">
          <i class="el-icon-shopping-cart-2"></i>
          <span>支出概览</span>
        </div>
        <div class="expense-content">
          <div class="expense-item">
            <div class="label">采购总支出</div>
            <div class="value negative">¥{{ formatNumber(reportData.expenses.totalPurchaseAmount) }}</div>
          </div>
          <div class="expense-item">
            <div class="label">取消采购退款</div>
            <div class="value positive">+¥{{ formatNumber(reportData.expenses.cancelledRefund) }}</div>
          </div>
          <div class="expense-item">
            <div class="label">净采购支出</div>
            <div class="value negative">¥{{ formatNumber(reportData.expenses.netPurchaseAmount) }}</div>
          </div>
          <div class="expense-item">
            <div class="label">售后支出</div>
            <div class="value negative">¥{{ formatNumber(reportData.expenses.afterSaleAmount) }}</div>
          </div>
          <div class="expense-item">
            <div class="label">理赔收入</div>
            <div class="value positive">+¥{{ formatNumber(reportData.expenses.claimAmount) }}</div>
          </div>
          <div class="expense-item">
            <div class="label">人员开支</div>
            <div class="value negative">¥{{ formatNumber(reportData.expenses.staffCost) }}</div>
          </div>
        </div>
      </el-card>

      <!-- 利润概览 -->
      <el-card class="profit-card">
        <div slot="header" class="card-header">
          <i class="el-icon-trophy"></i>
          <span>利润概览</span>
        </div>
        <div class="profit-content">
          <div class="profit-item">
            <div class="label">毛利润</div>
            <div class="value">¥{{ formatNumber(reportData.profit.grossProfit) }}</div>
          </div>
          <div class="profit-item total">
            <div class="label">净利润</div>
            <div class="value" :class="reportData.profit.netProfit >= 0 ? 'positive' : 'negative'">
              ¥{{ formatNumber(reportData.profit.netProfit) }}
            </div>
          </div>
          <div class="profit-item">
            <div class="label">利润率</div>
            <div class="value" :class="reportData.profit.profitRate >= 0 ? 'positive' : 'negative'">
              {{ reportData.profit.profitRate }}%
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 详细数据区域 -->
    <div class="detail-section">
      <!-- 收支明细表格 -->
      <el-card class="detail-table-card">
        <div slot="header" class="card-header">
          <i class="el-icon-document"></i>
          <span>收支明细</span>
        </div>
        <el-table :data="detailTableData" style="width: 100%">
          <el-table-column prop="category" label="类别" width="150"></el-table-column>
          <el-table-column prop="description" label="说明" width="200"></el-table-column>
          <el-table-column prop="amount" label="金额" align="right">
            <template slot-scope="scope">
              <span :class="scope.row.type === 'income' ? 'positive' : 'negative'">
                {{ scope.row.type === 'income' ? '+' : '-' }}¥{{ formatNumber(Math.abs(scope.row.amount)) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="note" label="备注"></el-table-column>
        </el-table>
      </el-card>

      <!-- 计算公式说明 -->
      <el-card class="formula-card">
        <div slot="header" class="card-header">
          <i class="el-icon-edit-outline"></i>
          <span>计算公式说明</span>
        </div>
        <div class="formula-content">
          <div class="formula-item">
            <strong>净收入</strong> = 订单总收入 - 出库前取消收入
          </div>
          <div class="formula-item">
            <strong>净采购支出</strong> = 采购总支出 - 取消采购退款
          </div>
          <div class="formula-item">
            <strong>毛利润</strong> = 净收入 - 净采购支出
          </div>
          <div class="formula-item">
            <strong>净利润</strong> = 毛利润 - 售后支出 + 理赔收入 - 人员开支
          </div>
          <div class="formula-item">
            <strong>人员开支</strong> = 固定工资(店铺数×30元/天×天数) + 采购提成(采购单数×2元)
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FinancialMonthlyReport',
  data() {
    return {
      selectedMonth: this.getCurrentMonth(),
      updateTime: this.getCurrentTime(),
      reportData: {
        revenue: {
          totalOrderAmount: 125000,
          cancelledAmount: 8000,
          netRevenue: 117000
        },
        expenses: {
          totalPurchaseAmount: 75000,
          cancelledRefund: 5000,
          netPurchaseAmount: 70000,
          afterSaleAmount: 3500,
          claimAmount: 1200,
          staffCost: 8800
        },
        profit: {
          grossProfit: 47000,
          netProfit: 35900,
          profitRate: 30.7
        }
      },
      detailTableData: [
        { category: '收入', description: '订单总收入', amount: 125000, type: 'income', note: '本月所有订单入账金额' },
        { category: '收入', description: '出库前取消', amount: -8000, type: 'expense', note: '已采购但出库前取消的订单' },
        { category: '支出', description: '采购总支出', amount: -75000, type: 'expense', note: '本月所有采购支出' },
        { category: '支出', description: '取消采购退款', amount: 5000, type: 'income', note: '采购后取消的退款' },
        { category: '支出', description: '售后支出', amount: -3500, type: 'expense', note: '售后服务相关支出' },
        { category: '收入', description: '理赔收入', amount: 1200, type: 'income', note: '物流理赔收入' },
        { category: '支出', description: '人员开支', amount: -8800, type: 'expense', note: '员工工资及提成' }
      ]
    }
  },
  methods: {
    getCurrentMonth() {
      const now = new Date()
      return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
    },
    getCurrentTime() {
      return new Date().toLocaleString('zh-CN')
    },
    formatNumber(num) {
      return num.toLocaleString('zh-CN')
    },
    loadReportData() {
      // 这里后续可以调用真实的API
      console.log('加载月报数据:', this.selectedMonth)
      this.updateTime = this.getCurrentTime()
    },
    refreshData() {
      this.loadReportData()
      this.$message.success('数据已刷新')
    }
  }
}
</script>

<style scoped>
.financial-monthly-report {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.right-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.update-time {
  color: #666;
  font-size: 14px;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  font-size: 16px;
}

.card-header i {
  font-size: 18px;
}

.revenue-card .card-header {
  color: #67C23A;
}

.expense-card .card-header {
  color: #F56C6C;
}

.profit-card .card-header {
  color: #409EFF;
}

.revenue-content, .expense-content, .profit-content {
  padding: 10px 0;
}

.revenue-item, .expense-item, .profit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.revenue-item:last-child, .expense-item:last-child, .profit-item:last-child {
  border-bottom: none;
}

.revenue-item.total, .profit-item.total {
  border-top: 2px solid #e0e0e0;
  margin-top: 10px;
  padding-top: 15px;
  font-weight: bold;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  font-size: 16px;
  font-weight: bold;
}

.value.positive {
  color: #67C23A;
}

.value.negative {
  color: #F56C6C;
}

.detail-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-table-card, .formula-card {
  background: white;
}

.formula-content {
  padding: 10px 0;
}

.formula-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  line-height: 1.6;
}

.formula-item:last-child {
  border-bottom: none;
}

.formula-item strong {
  color: #409EFF;
  margin-right: 10px;
  display: inline-block;
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr 1fr;
  }

  .detail-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }

  .header-controls {
    flex-direction: column;
    gap: 15px;
  }

  .left-controls {
    width: 100%;
    justify-content: center;
  }

  .right-info {
    width: 100%;
    justify-content: center;
  }
}
</style>
