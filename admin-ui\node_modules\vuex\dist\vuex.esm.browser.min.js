/*!
 * vuex v3.6.2
 * (c) 2021 Evan You
 * @license MIT
 */
const t=("undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).__VUE_DEVTOOLS_GLOBAL_HOOK__;function e(t,s=[]){if(null===t||"object"!=typeof t)return t;const o=(i=e=>e.original===t,s.filter(i)[0]);var i;if(o)return o.copy;const n=Array.isArray(t)?[]:{};return s.push({original:t,copy:n}),Object.keys(t).forEach(o=>{n[o]=e(t[o],s)}),n}function s(t,e){Object.keys(t).forEach(s=>e(t[s],s))}function o(t){return null!==t&&"object"==typeof t}class i{constructor(t,e){this.runtime=e,this._children=Object.create(null),this._rawModule=t;const s=t.state;this.state=("function"==typeof s?s():s)||{}}get namespaced(){return!!this._rawModule.namespaced}addChild(t,e){this._children[t]=e}removeChild(t){delete this._children[t]}getChild(t){return this._children[t]}hasChild(t){return t in this._children}update(t){this._rawModule.namespaced=t.namespaced,t.actions&&(this._rawModule.actions=t.actions),t.mutations&&(this._rawModule.mutations=t.mutations),t.getters&&(this._rawModule.getters=t.getters)}forEachChild(t){s(this._children,t)}forEachGetter(t){this._rawModule.getters&&s(this._rawModule.getters,t)}forEachAction(t){this._rawModule.actions&&s(this._rawModule.actions,t)}forEachMutation(t){this._rawModule.mutations&&s(this._rawModule.mutations,t)}}class n{constructor(t){this.register([],t,!1)}get(t){return t.reduce((t,e)=>t.getChild(e),this.root)}getNamespace(t){let e=this.root;return t.reduce((t,s)=>(e=e.getChild(s),t+(e.namespaced?s+"/":"")),"")}update(t){!function t(e,s,o){if(s.update(o),o.modules)for(const i in o.modules){if(!s.getChild(i))return;t(e.concat(i),s.getChild(i),o.modules[i])}}([],this.root,t)}register(t,e,o=!0){const n=new i(e,o);if(0===t.length)this.root=n;else{this.get(t.slice(0,-1)).addChild(t[t.length-1],n)}e.modules&&s(e.modules,(e,s)=>{this.register(t.concat(s),e,o)})}unregister(t){const e=this.get(t.slice(0,-1)),s=t[t.length-1],o=e.getChild(s);o&&o.runtime&&e.removeChild(s)}isRegistered(t){const e=this.get(t.slice(0,-1)),s=t[t.length-1];return!!e&&e.hasChild(s)}}let r;class c{constructor(e={}){!r&&"undefined"!=typeof window&&window.Vue&&f(window.Vue);const{plugins:s=[],strict:o=!1}=e;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new n(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new r,this._makeLocalGettersCache=Object.create(null);const i=this,{dispatch:c,commit:a}=this;this.dispatch=function(t,e){return c.call(i,t,e)},this.commit=function(t,e,s){return a.call(i,t,e,s)},this.strict=o;const u=this._modules.root.state;h(this,u,[],this._modules.root),l(this,u),s.forEach(t=>t(this));(void 0!==e.devtools?e.devtools:r.config.devtools)&&function(e){t&&(e._devtoolHook=t,t.emit("vuex:init",e),t.on("vuex:travel-to-state",t=>{e.replaceState(t)}),e.subscribe((e,s)=>{t.emit("vuex:mutation",e,s)},{prepend:!0}),e.subscribeAction((e,s)=>{t.emit("vuex:action",e,s)},{prepend:!0}))}(this)}get state(){return this._vm._data.$$state}set state(t){}commit(t,e,s){const{type:o,payload:i,options:n}=p(t,e,s),r={type:o,payload:i},c=this._mutations[o];c&&(this._withCommit(()=>{c.forEach((function(t){t(i)}))}),this._subscribers.slice().forEach(t=>t(r,this.state)))}dispatch(t,e){const{type:s,payload:o}=p(t,e),i={type:s,payload:o},n=this._actions[s];if(!n)return;try{this._actionSubscribers.slice().filter(t=>t.before).forEach(t=>t.before(i,this.state))}catch(t){}const r=n.length>1?Promise.all(n.map(t=>t(o))):n[0](o);return new Promise((t,e)=>{r.then(e=>{try{this._actionSubscribers.filter(t=>t.after).forEach(t=>t.after(i,this.state))}catch(t){}t(e)},t=>{try{this._actionSubscribers.filter(t=>t.error).forEach(e=>e.error(i,this.state,t))}catch(t){}e(t)})})}subscribe(t,e){return a(t,this._subscribers,e)}subscribeAction(t,e){return a("function"==typeof t?{before:t}:t,this._actionSubscribers,e)}watch(t,e,s){return this._watcherVM.$watch(()=>t(this.state,this.getters),e,s)}replaceState(t){this._withCommit(()=>{this._vm._data.$$state=t})}registerModule(t,e,s={}){"string"==typeof t&&(t=[t]),this._modules.register(t,e),h(this,this.state,t,this._modules.get(t),s.preserveState),l(this,this.state)}unregisterModule(t){"string"==typeof t&&(t=[t]),this._modules.unregister(t),this._withCommit(()=>{const e=d(this.state,t.slice(0,-1));r.delete(e,t[t.length-1])}),u(this)}hasModule(t){return"string"==typeof t&&(t=[t]),this._modules.isRegistered(t)}hotUpdate(t){this._modules.update(t),u(this,!0)}_withCommit(t){const e=this._committing;this._committing=!0,t(),this._committing=e}}function a(t,e,s){return e.indexOf(t)<0&&(s&&s.prepend?e.unshift(t):e.push(t)),()=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)}}function u(t,e){t._actions=Object.create(null),t._mutations=Object.create(null),t._wrappedGetters=Object.create(null),t._modulesNamespaceMap=Object.create(null);const s=t.state;h(t,s,[],t._modules.root,!0),l(t,s,e)}function l(t,e,o){const i=t._vm;t.getters={},t._makeLocalGettersCache=Object.create(null);const n=t._wrappedGetters,c={};s(n,(e,s)=>{c[s]=function(t,e){return function(){return t(e)}}(e,t),Object.defineProperty(t.getters,s,{get:()=>t._vm[s],enumerable:!0})});const a=r.config.silent;r.config.silent=!0,t._vm=new r({data:{$$state:e},computed:c}),r.config.silent=a,t.strict&&function(t){t._vm.$watch((function(){return this._data.$$state}),()=>{},{deep:!0,sync:!0})}(t),i&&(o&&t._withCommit(()=>{i._data.$$state=null}),r.nextTick(()=>i.$destroy()))}function h(t,e,s,o,i){const n=!s.length,c=t._modules.getNamespace(s);if(o.namespaced&&(t._modulesNamespaceMap[c],t._modulesNamespaceMap[c]=o),!n&&!i){const i=d(e,s.slice(0,-1)),n=s[s.length-1];t._withCommit(()=>{r.set(i,n,o.state)})}const a=o.context=function(t,e,s){const o=""===e,i={dispatch:o?t.dispatch:(s,o,i)=>{const n=p(s,o,i),{payload:r,options:c}=n;let{type:a}=n;return c&&c.root||(a=e+a),t.dispatch(a,r)},commit:o?t.commit:(s,o,i)=>{const n=p(s,o,i),{payload:r,options:c}=n;let{type:a}=n;c&&c.root||(a=e+a),t.commit(a,r,c)}};return Object.defineProperties(i,{getters:{get:o?()=>t.getters:()=>function(t,e){if(!t._makeLocalGettersCache[e]){const s={},o=e.length;Object.keys(t.getters).forEach(i=>{if(i.slice(0,o)!==e)return;const n=i.slice(o);Object.defineProperty(s,n,{get:()=>t.getters[i],enumerable:!0})}),t._makeLocalGettersCache[e]=s}return t._makeLocalGettersCache[e]}(t,e)},state:{get:()=>d(t.state,s)}}),i}(t,c,s);o.forEachMutation((e,s)=>{!function(t,e,s,o){(t._mutations[e]||(t._mutations[e]=[])).push((function(e){s.call(t,o.state,e)}))}(t,c+s,e,a)}),o.forEachAction((e,s)=>{const o=e.root?s:c+s,i=e.handler||e;!function(t,e,s,o){(t._actions[e]||(t._actions[e]=[])).push((function(e){let i=s.call(t,{dispatch:o.dispatch,commit:o.commit,getters:o.getters,state:o.state,rootGetters:t.getters,rootState:t.state},e);var n;return(n=i)&&"function"==typeof n.then||(i=Promise.resolve(i)),t._devtoolHook?i.catch(e=>{throw t._devtoolHook.emit("vuex:error",e),e}):i}))}(t,o,i,a)}),o.forEachGetter((e,s)=>{!function(t,e,s,o){if(t._wrappedGetters[e])return;t._wrappedGetters[e]=function(t){return s(o.state,o.getters,t.state,t.getters)}}(t,c+s,e,a)}),o.forEachChild((o,n)=>{h(t,e,s.concat(n),o,i)})}function d(t,e){return e.reduce((t,e)=>t[e],t)}function p(t,e,s){return o(t)&&t.type&&(s=e,e=t,t=t.type),{type:t,payload:e,options:s}}function f(t){r&&t===r||(r=t,function(t){if(Number(t.version.split(".")[0])>=2)t.mixin({beforeCreate:e});else{const s=t.prototype._init;t.prototype._init=function(t={}){t.init=t.init?[e].concat(t.init):e,s.call(this,t)}}function e(){const t=this.$options;t.store?this.$store="function"==typeof t.store?t.store():t.store:t.parent&&t.parent.$store&&(this.$store=t.parent.$store)}}(r))}const m=v((t,e)=>{const s={};return w(e).forEach(({key:e,val:o})=>{s[e]=function(){let e=this.$store.state,s=this.$store.getters;if(t){const o=$(this.$store,"mapState",t);if(!o)return;e=o.context.state,s=o.context.getters}return"function"==typeof o?o.call(this,e,s):e[o]},s[e].vuex=!0}),s}),g=v((t,e)=>{const s={};return w(e).forEach(({key:e,val:o})=>{s[e]=function(...e){let s=this.$store.commit;if(t){const e=$(this.$store,"mapMutations",t);if(!e)return;s=e.context.commit}return"function"==typeof o?o.apply(this,[s].concat(e)):s.apply(this.$store,[o].concat(e))}}),s}),_=v((t,e)=>{const s={};return w(e).forEach(({key:e,val:o})=>{o=t+o,s[e]=function(){if(!t||$(this.$store,"mapGetters",t))return this.$store.getters[o]},s[e].vuex=!0}),s}),y=v((t,e)=>{const s={};return w(e).forEach(({key:e,val:o})=>{s[e]=function(...e){let s=this.$store.dispatch;if(t){const e=$(this.$store,"mapActions",t);if(!e)return;s=e.context.dispatch}return"function"==typeof o?o.apply(this,[s].concat(e)):s.apply(this.$store,[o].concat(e))}}),s}),b=t=>({mapState:m.bind(null,t),mapGetters:_.bind(null,t),mapMutations:g.bind(null,t),mapActions:y.bind(null,t)});function w(t){return function(t){return Array.isArray(t)||o(t)}(t)?Array.isArray(t)?t.map(t=>({key:t,val:t})):Object.keys(t).map(e=>({key:e,val:t[e]})):[]}function v(t){return(e,s)=>("string"!=typeof e?(s=e,e=""):"/"!==e.charAt(e.length-1)&&(e+="/"),t(e,s))}function $(t,e,s){return t._modulesNamespaceMap[s]}function M({collapsed:t=!0,filter:s=((t,e,s)=>!0),transformer:o=(t=>t),mutationTransformer:i=(t=>t),actionFilter:n=((t,e)=>!0),actionTransformer:r=(t=>t),logMutations:c=!0,logActions:a=!0,logger:u=console}={}){return l=>{let h=e(l.state);void 0!==u&&(c&&l.subscribe((n,r)=>{const c=e(r);if(s(n,h,c)){const e=O(),s=i(n),r=`mutation ${n.type}${e}`;C(u,r,t),u.log("%c prev state","color: #9E9E9E; font-weight: bold",o(h)),u.log("%c mutation","color: #03A9F4; font-weight: bold",s),u.log("%c next state","color: #4CAF50; font-weight: bold",o(c)),E(u)}h=c}),a&&l.subscribeAction((e,s)=>{if(n(e,s)){const s=O(),o=r(e),i=`action ${e.type}${s}`;C(u,i,t),u.log("%c action","color: #03A9F4; font-weight: bold",o),E(u)}}))}}function C(t,e,s){const o=s?t.groupCollapsed:t.group;try{o.call(t,e)}catch(s){t.log(e)}}function E(t){try{t.groupEnd()}catch(e){t.log("—— log end ——")}}function O(){const t=new Date;return` @ ${j(t.getHours(),2)}:${j(t.getMinutes(),2)}:${j(t.getSeconds(),2)}.${j(t.getMilliseconds(),3)}`}function j(t,e){return s="0",o=e-t.toString().length,new Array(o+1).join(s)+t;var s,o}var A={Store:c,install:f,version:"3.6.2",mapState:m,mapMutations:g,mapGetters:_,mapActions:y,createNamespacedHelpers:b,createLogger:M};export default A;export{c as Store,M as createLogger,b as createNamespacedHelpers,f as install,y as mapActions,_ as mapGetters,g as mapMutations,m as mapState};
