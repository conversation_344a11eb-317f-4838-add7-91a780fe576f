### mysql数据源
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************
    username: crossborder
    password: crossborder
    druid:
      # 连接池的配置信息
      # 初始化大小，最小，最大
      initial-size: 3
      min-idle: 3
      maxActive: 15
      # 获取连接时最大等待时间，单位毫秒
      max-wait: 60000
      # 检测连接是否有效的SQL
      validation-query: SELECT 1
      # 申请连接时执行validationQuery检测连接是否有效
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效
      test-on-return: false
      # 空闲连接检测
      test-while-idle: true
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 连接保活查询超时时间
      validation-query-timeout: 3
      # 是否缓存preparedStatement，也就是PSCache
      pool-prepared-statements: true
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
        
### mp配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  type-aliases-package: com.my.crossborder.mybatis.entity
  global-config:
    db-config:
      id-type: AUTO
      table-underline: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  #打印SQL和参数      
    
#仅打印SQL和总记录数
logging:
  level:
    com.my.crossborder.mybatis.mapper: debug     