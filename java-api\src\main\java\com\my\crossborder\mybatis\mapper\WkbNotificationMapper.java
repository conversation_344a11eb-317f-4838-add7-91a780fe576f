package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationPageDTO;
import com.my.crossborder.controller.vo.wkb_notification.WkbNotificationPageVO;
import com.my.crossborder.mybatis.entity.WkbNotification;

/**
 * 通知表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface WkbNotificationMapper extends BaseMapper<WkbNotification> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<WkbNotificationPageVO> page(WkbNotificationPageDTO pageDTO);
	
}
