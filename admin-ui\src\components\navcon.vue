/**
* 头部菜单
*/
<template>
  <el-menu class="el-menu-demo" mode="horizontal" background-color="#334157" text-color="#fff" active-text-color="#fff">
    <!-- 左侧系统名称 -->
    <div class="system-title-left">
      跨境电商运营管理系统

    </div>

    <!-- 右侧用户信息和操作区域 -->
    <div class="user-actions">
      <!-- 消息和待办图标 -->
      <div class="notification-icons">
        <!-- <el-badge :value="todoCount" :hidden="todoCount === 0" class="notification-todo">
          <i class="el-icon-s-check" @click="showTodos" title="待办事项"></i>
        </el-badge> -->
        <!-- <span style="color: #fff; margin-right: 10px;" :title="loginToken">TOKEN == > {{ displayToken }}</span> -->
        <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="notification-bell">
          <i class="el-icon-bell" @click="showNotifications" title="通知公告"></i>
        </el-badge>
      </div>
      <el-submenu index="2" class="submenu">
        <template slot="title"><i class="el-icon-s-custom"></i> {{userEntity.username}} </template>
        <el-menu-item @click="popWin('MyPasswordEdit')" index="2-3">修改密码</el-menu-item>
        <el-menu-item @click="exit()" index="2-5">退出登录</el-menu-item>
      </el-submenu>
    </div>

    <!-- Add/Edit -->
    <MyPasswordEdit ref="MyPasswordEdit"></MyPasswordEdit>
  </el-menu>
</template>
<script>
import MyPasswordEdit from '../views/sys/SysUserMyPasswordEdit'
import { mapGetters } from 'vuex'

export default {
  name: 'navcon',
  data() {
    return {
      userEntity : undefined,
      loading: false,
      loginToken: ''
    }
  },
  components: {
    MyPasswordEdit,
  },
  computed: {
    ...mapGetters([
      'unreadNotificationCount',
      'unreadTodoCount'
    ]),
    // 映射到原来的变量名，保持兼容性
    unreadCount() {
      return this.unreadNotificationCount;
    },
    todoCount() {
      return this.unreadTodoCount;
    },
    // 显示的token，如果太长则截断
    displayToken() {
      if (!this.loginToken) return '无';
      return this.loginToken;
    }
  },
  async created() {
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'))
    this.loginToken = localStorage.getItem('logintoken') || ''
    // 设置用户实体到store
    this.$store.commit('setUserEntity', this.userEntity)
    // 加载未读数据
    await this.loadUnreadData()
  },
  mounted() {
    // 监听localStorage变化，实时更新token显示
    window.addEventListener('storage', this.handleStorageChange)
  },
  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('storage', this.handleStorageChange)
  },
  methods: {
    // 处理localStorage变化
    handleStorageChange(event) {
      if (event.key === 'logintoken') {
        this.loginToken = event.newValue || ''
      }
    },
    // 加载未读数据
    async loadUnreadData() {
      try {
        await this.$store.dispatch('refreshUnreadData')
      } catch (error) {
        console.error('加载未读数据失败:', error)
      }
    },
    // 弹窗
    popWin(windownName) {
      if (this.$refs[windownName]){
        this.$refs[windownName].show();
      }
    },
    // 退出登录
    exit() {
      this.$confirm('是否退出登录 ?', '退出登录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          setTimeout(() => {
            this.loginToken = ''
            this.$store.commit('logout', 'false')
            this.$router.push({ path: '/login' })
            this.$message({ type: 'success', message: 'Success!'})
          }, 1000)
        })
        .catch(() => {
          this.$message({ type: 'info', message: '已取消'})
        })
    },
    // 显示通知
    showNotifications() {
      this.$router.push({ path: '/WkbNotificationDetail' })
    },

    // 显示待办事项
    showTodos() {
      this.$router.push({ path: '/WkbTodoDetail' })
    },
  }
}
</script>
<style scoped>
.el-menu-demo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  width: 100%;
  border: none !important;
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
  border: none;
}

/* 系统标题样式 */
.system-title-left {
  flex: 1;
  text-align: left;
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  margin: 0 0px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 用户操作区域 */
.user-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* 通知图标样式 */
.notification-icons {
  display: flex;
  align-items: center;
  margin-top: 6px;
  margin-right: 20px;
}

.notification-bell,
.notification-todo {
  margin-left: 18px;
}

.notification-bell i,
.notification-todo i {
  font-size: 20px;
  color: #fff;
  cursor: pointer;
  transition: color 0.3s;
}

.notification-bell i:hover,
.notification-todo i:hover {
  color: #ffd04b;
}

/* 调整徽章偏移位置 */
.notification-bell .el-badge__content,
.notification-todo .el-badge__content {
  top: 8px;
  right: 8px;
}

.submenu {
  position: relative;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .system-title-left {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .system-title-left {
    font-size: 14px;
    margin: 0 10px;
  }

  .notification-icons {
    margin-right: 10px;
  }

  .notification-bell,
  .notification-todo {
    margin-right: 10px;
  }
}
</style>
