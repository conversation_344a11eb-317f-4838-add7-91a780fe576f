package com.my.crossborder.controller.vo.sys_param;

import java.io.Serializable;
import java.lang.reflect.Field;

import com.my.crossborder.service.check.ICheck;
import com.my.crossborder.service.check.annotation.Check;
import com.my.crossborder.service.check.impl.AiApiKeyCheck;
import com.my.crossborder.service.check.impl.AiModelCheck;
import com.my.crossborder.service.check.impl.AiSystemPromptCheck;
import com.my.crossborder.service.check.impl.AiUserPromptCheck;
import com.my.crossborder.service.check.impl.FileAllowExtCheck;
import com.my.crossborder.service.check.impl.FileMaxSizeCheck;
import com.my.crossborder.service.check.impl.FileUploadPathCheck;
import com.my.crossborder.service.check.impl.OrderSyncDaysCheck;
import com.my.crossborder.service.check.impl.TaiwanWithdrawFeeRatioCheck;
import com.my.crossborder.service.check.result.CheckResult;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数据库配置的业务参数
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysParamVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 文件上传保存路径
     */
    @Check(chkClass = FileUploadPathCheck.class, label = "文件上传保存路径")
    private String fileUploadPath;
    
    /**
     * 文件大小最大限制 (M)
     */
    @Check(chkClass = FileMaxSizeCheck.class, label = "文件大小最大限制")
    private String fileMaxSize;

    /**
     * 允许的文件后缀
     */
    @Check(chkClass = FileAllowExtCheck.class, label = "允许的文件后缀")
    private String fileAllowExt;
    
    
    /**
     * AI模型
     */
    @Check(chkClass = AiModelCheck.class, label = "AI模型")
    private String aiModel;

    /**
     * AI系统提示词
     */
    @Check(chkClass = AiSystemPromptCheck.class, label = "AI系统提示词")
    private String aiSystemPrompt;
    
    /**
     * AI用户提示词
     */
    @Check(chkClass = AiUserPromptCheck.class, label = "AI用户提示词")
    private String aiUserPrompt;

    /**
     * AI接口秘钥
     */
    @Check(chkClass = AiApiKeyCheck.class, label = "AI密钥")
    private String aiApiKey;
    
    /**
     *  台湾提款手续费比例
     */
    @Check(chkClass = TaiwanWithdrawFeeRatioCheck.class, label = "台湾提款手续费比例")
    public String taiwanWithdrawFeeRatio;
    
    /**
     *  订单同步天数
     */
    @Check(chkClass = OrderSyncDaysCheck.class, label = "订单同步天数")
    public String orderSyncDays;
    
    /**
     * 调试: erp990Token
     */
    public String debugErp990Token;
    
    
    /**
	 * 检测对象中标注了@Check注解的字段
	 * @param obj 要检测的对象
	 * @return 检测结果Map，key为字段名，value为检测结果
	 */
	public SysParamCheckVO check() {
		SysParamCheckVO resultMap = new SysParamCheckVO();
		Class<?> clazz = this.getClass();
		Field[] fields = clazz.getDeclaredFields();
		
		for (Field field : fields) {
			Check checkAnnotation = field.getAnnotation(Check.class);
			if (checkAnnotation != null) {
				try {
					// 设置字段可访问
					field.setAccessible(true);
					
					// 获取字段值
					Object fieldValue = field.get(this);
					
					// 获取检测器类
					Class<? extends ICheck> checkClass = checkAnnotation.chkClass();
					ICheck checker = checkClass.newInstance();
					
					// 执行检测
					CheckResult result = checker.check(fieldValue, field, this);
					resultMap.put(field.getName(), result);
					
				} catch (Exception e) {
					// 如果检测过程中出现异常，记录错误结果
					CheckResult errorResult = new CheckResult(
						field.getName(), 
						field.getName(), 
						false, 
						e.getMessage()
					);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
					resultMap.put(field.getName(), errorResult);
				}
			}
		}
		
		return resultMap;
	}
}
