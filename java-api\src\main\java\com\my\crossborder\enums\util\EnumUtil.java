package com.my.crossborder.enums.util;

import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.my.crossborder.enums.annotation.StringEnum;

/**
 * 枚举工具类
 * <AUTHOR>
 * @date 2020年8月24日
 */
public class EnumUtil {

	
	/**
	 * 字符串枚举
	 * @param val
	 * @param clz
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T valueOf(String val, Class<T> clz) {
		try {
			Method method = clz.getMethod("values");
			StringEnum[] enums = (StringEnum[]) method.invoke(null);
			for (StringEnum en : enums) {
				if (en.name().equals(val)) {
					return (T)en;
				}
			}
			List<String> keys = Arrays.asList(enums).stream().map(e->e.name()).collect(Collectors.toList());
			String msg = MessageFormat.format("值[{0}]不在枚举范围内:{1}", val, keys);
			throw new RuntimeException(msg);
		} catch (Exception e) {
			throw new RuntimeException(e.getMessage());
		}
	}
	
}
