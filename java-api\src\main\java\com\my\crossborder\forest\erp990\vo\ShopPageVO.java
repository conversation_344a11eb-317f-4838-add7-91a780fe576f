package com.my.crossborder.forest.erp990.vo;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor @AllArgsConstructor @Builder @Data  @EqualsAndHashCode(callSuper=false)
public class ShopPageVO extends Erp990VO {

    @JSONField(name = "rows", ordinal = 3)
    @Builder.Default
    List<Row> rows = new ArrayList<Row>();

    @JSONField(name = "total", ordinal = 4)
    String total;


    // ======== 以下为内部类 =========
    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Params {
    	String nothing;
    }

    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Row {

//        @JSONField(name = "accessCreateTime", ordinal = 1)
//        String accessCreateTime;
//
//        @JSONField(name = "accessToken", ordinal = 2)
//        String accessToken;
//
//        @JSONField(name = "authTime", ordinal = 3)
//        String authTime;
//
//        @JSONField(name = "cookieEditor", ordinal = 4)
//        String cookieEditor;
//
//        @JSONField(name = "createBy", ordinal = 5)
//        String createBy;
//
//        @JSONField(name = "createTime", ordinal = 6)
//        String createTime;
//
//        @JSONField(name = "deptId", ordinal = 7)
//        String deptId;
//
//        @JSONField(name = "deptName", ordinal = 8)
//        String deptName;
//
//        @JSONField(name = "expireIn", ordinal = 9)
//        String expireIn;
//
//        @JSONField(name = "expireTime", ordinal = 10)
//        String expireTime;
//
//        @JSONField(name = "firstDown", ordinal = 11)
//        String firstDown;
//
//        @JSONField(name = "id", ordinal = 12)
//        String id;
//
//        @JSONField(name = "loginFailReason", ordinal = 13)
//        String loginFailReason;
//
//        @JSONField(name = "loginStatus", ordinal = 14)
//        String loginStatus;
//
//        @JSONField(name = "nickName", ordinal = 15)
//        String nickName;
//
//        @JSONField(name = "palt", ordinal = 16)
//        String palt;
//
//        @JSONField(name = "params", ordinal = 17)
//        @Builder.Default
//        Params params = new Params();
//
//        @JSONField(name = "pf", ordinal = 18)
//        String pf;
//
//        @JSONField(name = "proxyAccount", ordinal = 19)
//        String proxyAccount;
//
//        @JSONField(name = "proxyIp", ordinal = 20)
//        String proxyIp;
//
//        @JSONField(name = "proxyPassword", ordinal = 21)
//        String proxyPassword;
//
//        @JSONField(name = "proxyPort", ordinal = 22)
//        String proxyPort;
//
//        @JSONField(name = "refreshAccessToken", ordinal = 23)
//        String refreshAccessToken;
//
//        @JSONField(name = "refreshCreateTime", ordinal = 24)
//        String refreshCreateTime;
//
//        @JSONField(name = "refreshExpireIn", ordinal = 25)
//        String refreshExpireIn;
//
//        @JSONField(name = "region", ordinal = 26)
//        String region;
//
//        @JSONField(name = "remark", ordinal = 27)
//        String remark;
//
//        @JSONField(name = "searchValue", ordinal = 28)
//        String searchValue;
//
//        @JSONField(name = "shopAccount", ordinal = 29)
//        String shopAccount;
//
//        @JSONField(name = "shopCookie", ordinal = 30)
//        String shopCookie;

        @JSONField(name = "shopId", ordinal = 31)
        Integer shopId;

//        @JSONField(name = "shopLoginStatus", ordinal = 32)
//        String shopLoginStatus;
//
//        @JSONField(name = "shopLoginType", ordinal = 33)
//        String shopLoginType;

        @JSONField(name = "shopName", ordinal = 34)
        String shopName;

//        @JSONField(name = "shopOtherName", ordinal = 35)
//        String shopOtherName;
//
//        @JSONField(name = "shopPassword", ordinal = 36)
//        String shopPassword;
//
//        @JSONField(name = "status", ordinal = 37)
//        String status;
//
//        @JSONField(name = "time", ordinal = 38)
//        String time;
//
//        @JSONField(name = "updateBy", ordinal = 39)
//        String updateBy;
//
//        @JSONField(name = "updateTime", ordinal = 40)
//        String updateTime;
//
//        @JSONField(name = "useProxy", ordinal = 41)
//        String useProxy;
//
//        @JSONField(name = "userId", ordinal = 42)
//        String userId;
//
//        @JSONField(name = "userName", ordinal = 43)
//        String userName;
//
//        @JSONField(name = "whiteFlag", ordinal = 44)
//        String whiteFlag;
    }

}