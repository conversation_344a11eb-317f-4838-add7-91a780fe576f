package com.my.crossborder.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.my.crossborder.controller.dto.erp_order.ErpOrderDeleteDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderInsertDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderPageDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderStatusSummaryPageDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderUpdateDTO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderDetailVO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderStatusSummaryPageVO;
import com.my.crossborder.controller.vo.erp_order.RepurchaseErpOrderPageVO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemDetailVO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemWithExpressVO;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;
import com.my.crossborder.mybatis.entity.ErpOrder;
import com.my.crossborder.mybatis.entity.ErpOrderItem;
import com.my.crossborder.mybatis.mapper.ErpOrderItemMapper;
import com.my.crossborder.mybatis.mapper.ErpOrderMapper;
import com.my.crossborder.mybatis.mapper.WkbNoteMapper;
import com.my.crossborder.service.ErpOrderService;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;

/**
 * 订单主表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@RequiredArgsConstructor
public class ErpOrderServiceImpl extends ServiceImpl<ErpOrderMapper, ErpOrder> implements ErpOrderService {

	private final ErpOrderItemMapper erpOrderItemMapper;
	private final WkbNoteMapper wkbNoteMapper;

	@Transactional
	@Override
	public void insert(ErpOrderInsertDTO insertDTO) {
		ErpOrder entity = BeanUtil.copyProperties(insertDTO, ErpOrder.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(ErpOrderUpdateDTO updateDTO) {
		ErpOrder entity = BeanUtil.copyProperties(updateDTO, ErpOrder.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public ErpOrderDetailVO detail(String orderId) {
		ErpOrder entity = this.baseMapper.selectById(orderId);
		ErpOrderDetailVO detailVO = BeanUtil.copyProperties(entity, ErpOrderDetailVO.class);
		
		// 查询订单项
		QueryWrapper<ErpOrderItem> orderItemQuery = new QueryWrapper<>();
		orderItemQuery.eq("order_id", orderId);
		List<ErpOrderItem> orderItems = erpOrderItemMapper.selectList(orderItemQuery);
		
		// 转换为VO
		List<ErpOrderItemDetailVO> orderItemVOs = orderItems.stream()
			.map(item -> BeanUtil.copyProperties(item, ErpOrderItemDetailVO.class))
			.collect(Collectors.toList());
		
		detailVO.setOrderItems(orderItemVOs);
		return detailVO;
	}

	@Override
	public Page<ErpOrderPageVO> incompleteLogisticsPageWithItems(ErpOrderPageDTO pageDTO) {
		// 先查询订单分页数据
		Page<ErpOrderPageVO> orderPage = this.baseMapper.incompleteLogisticsPageWithItems(pageDTO);
		
		// 批量获取订单项和备注信息
		batchSetOrderItemsAndNotes(orderPage);
		
		return orderPage;
	}

	@Override
	public Page<ErpOrderPageVO> logisticsDoneNotWarehoused(ErpOrderPageDTO pageDTO) {
		// 先查询订单分页数据
		Page<ErpOrderPageVO> orderPage = this.baseMapper.logisticsDoneNotWarehoused(pageDTO);
		
		// 批量获取订单项和备注信息
		batchSetOrderItemsAndNotes(orderPage);
		
		return orderPage;
	}

	@Override
	public Page<ErpOrderPageVO> warehousedNotOutbound(ErpOrderPageDTO pageDTO) {
		// 先查询订单分页数据
		Page<ErpOrderPageVO> orderPage = this.baseMapper.warehousedNotOutbound(pageDTO);
		
		// 批量获取订单项和备注信息
		batchSetOrderItemsAndNotes(orderPage);
		
		return orderPage;
	}

//	@Override
//	public Page<ErpOrderPageVO> purchaseDoneOrderCancelled(ErpOrderPageDTO pageDTO) {
//		// 先查询订单分页数据
//		Page<ErpOrderPageVO> orderPage = this.baseMapper.purchaseDoneOrderCancelled(pageDTO);
//
//		// 批量获取订单项和备注信息
//		batchSetOrderItemsAndNotes(orderPage);
//
//		return orderPage;
//	}

	@Override
	public Page<RepurchaseErpOrderPageVO> repurchasePageWithItems(ErpOrderPageDTO pageDTO) {
		// 先查询订单分页数据
		Page<RepurchaseErpOrderPageVO> orderPage = this.baseMapper.repurchasePageWithItems(pageDTO);

		// 批量获取订单项和备注信息
		batchSetOrderItemsAndNotes(orderPage);

		return orderPage;
	}

	@Override
	public Page<ErpOrderPageVO> selectorPageWithItems(ErpOrderPageDTO pageDTO) {
		// 先查询订单分页数据
		Page<ErpOrderPageVO> orderPage = this.baseMapper.selectorPageWithItems(pageDTO);

		// 批量获取订单项和备注信息
		batchSetOrderItemsAndNotes(orderPage);

		return orderPage;
	}

	/**
	 * 批量设置订单项和备注信息到分页结果中（泛型版本）
	 *
	 * @param orderPage 订单分页结果
	 * @param <T> 继承自ErpOrderPageVO的类型
	 */
	private <T extends ErpOrderPageVO> void batchSetOrderItemsAndNotesGeneric(Page<T> orderPage) {
		List<T> records = orderPage.getRecords();
		if (records == null || records.isEmpty()) {
			return;
		}

		// 收集所有订单ID、订单号
		Set<String> orderIds = records.stream()
				.map(ErpOrderPageVO::getOrderId)
				.collect(Collectors.toSet());
		Set<String> orderSnList = records.stream()
				.map(ErpOrderPageVO::getOrderSn)
				.collect(Collectors.toSet());

		// 一次性查询所有订单项
		List<ErpOrderItemWithExpressVO> allOrderItems = this.baseMapper.selectOrderItemListWithExpress(orderIds);

		// 一次性查询所有备注信息
		List<WkbNoteDetailVO> allNotes = this.wkbNoteMapper.selectByOrderSnSet(orderSnList);

		// 将订单项按订单ID分组
		Map<String, List<ErpOrderItemWithExpressVO>> orderItemMap = allOrderItems.stream()
				.collect(Collectors.groupingBy(ErpOrderItemWithExpressVO::getOrderId));

		// 将备注信息按订单号分组
		Map<String, List<WkbNoteDetailVO>> noteMap = allNotes.stream()
				.collect(Collectors.groupingBy(WkbNoteDetailVO::getOrderSn));

		// 为每个订单设置对应的订单项和备注
		records.forEach(order -> {
			String orderId = order.getOrderId();
			String orderSn = order.getOrderSn();
			// 设置订单项
			order.setOrderItems(orderItemMap.getOrDefault(orderId, Lists.newLinkedList()));
			// 设置备注信息
			order.setNotes(noteMap.getOrDefault(orderSn, Lists.newLinkedList()));
		});
	}

	/**
	 * 批量设置订单项和备注信息到分页结果中
	 *
	 * @param orderPage 订单分页结果
	 * @param <T> 继承自ErpOrderPageVO的类型
	 */
	@Override
	public <T extends ErpOrderPageVO> void batchSetOrderItemsAndNotes(Page<T> orderPage) {
		batchSetOrderItemsAndNotesGeneric(orderPage);
	}



	@Transactional
	@Override
	public void delete(ErpOrderDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Override
	public Page<ErpOrderStatusSummaryPageVO> orderStatusSummaryPage(ErpOrderStatusSummaryPageDTO pageDTO) {
		// 先查询订单状态汇总分页数据
		Page<ErpOrderStatusSummaryPageVO> page = this.baseMapper.orderStatusSummaryPage(pageDTO);

		// 批量获取备注信息
		batchSetNotesForStatusSummary(page);

		return page;
	}

	/**
	 * 批量设置订单状态汇总的备注信息
	 *
	 * @param page 订单状态汇总分页结果
	 */
	private void batchSetNotesForStatusSummary(Page<ErpOrderStatusSummaryPageVO> page) {
		List<ErpOrderStatusSummaryPageVO> records = page.getRecords();
		if (records == null || records.isEmpty()) {
			return;
		}

		// 收集所有订单号
		Set<String> orderSnList = records.stream()
				.map(ErpOrderStatusSummaryPageVO::getOrderSn)
				.collect(Collectors.toSet());

		// 一次性查询所有备注信息
		List<WkbNoteDetailVO> allNotes = this.wkbNoteMapper.selectByOrderSnSet(orderSnList);

		// 将备注信息按订单号分组
		Map<String, List<WkbNoteDetailVO>> noteMap = allNotes.stream()
				.collect(Collectors.groupingBy(WkbNoteDetailVO::getOrderSn));

		// 为每个订单设置对应的备注
		records.forEach(order -> {
			String orderSn = order.getOrderSn();
			order.setNotes(noteMap.getOrDefault(orderSn, Lists.newLinkedList()));
		});
	}
}
