<template>
  <div class="test-page">
    <h2>理赔编辑组件测试</h2>
    
    <el-button type="primary" @click="openAddDialog">添加理赔</el-button>
    <el-button type="success" @click="openEditDialog">编辑理赔(退款)</el-button>
    <el-button type="warning" @click="openEditDialogForResend">编辑理赔(补寄)</el-button>
    
    <!-- 理赔编辑对话框 -->
    <OrdClaimEdit
      :visible.sync="dialogVisible"
      :editData="currentEditData"
      :isEdit="isEdit"
      @submit="handleSubmit"
    />
    
    <div v-if="lastSubmitData" style="margin-top: 20px;">
      <h3>最后提交的数据：</h3>
      <pre>{{ JSON.stringify(lastSubmitData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import OrdClaimEdit from './OrdClaimEdit'

export default {
  name: 'OrdClaimEditTest',
  components: {
    OrdClaimEdit
  },
  data() {
    return {
      dialogVisible: false,
      isEdit: false,
      currentEditData: {},
      lastSubmitData: null
    }
  },
  methods: {
    openAddDialog() {
      this.isEdit = false
      this.currentEditData = {}
      this.dialogVisible = true
    },
    
    openEditDialog() {
      this.isEdit = true
      this.currentEditData = {
        id: 1,
        orderItemId: '12345',
        orderSn: 'ORD20250712001',
        shopName: '测试店铺',
        expressNo: 'SF1234567890',
        // 模拟后端返回的数据：只有itemPrice，没有productPrice
        itemPrice: 99.99,
        itemName: '测试商品',
        itemModelName: '红色-L码',
        issue: '商品破损',
        issueType: '1',
        purchaseCost: 50.00,
        closeWay: '1', // 测试退款校验
        refundScreenshotId: null,
        resendTrackingNumber: ''
      }
      this.dialogVisible = true
    },

    openEditDialogForResend() {
      this.isEdit = true
      this.currentEditData = {
        id: 2,
        orderItemId: '12346',
        orderSn: 'ORD20250712002',
        shopName: '测试店铺2',
        expressNo: 'SF1234567891',
        // 模拟后端返回的数据：只有itemPrice，没有productPrice
        itemPrice: 199.99,
        itemName: '测试商品2',
        itemModelName: '蓝色-XL码',
        issue: '物流丢失',
        issueType: '2',
        purchaseCost: 100.00,
        closeWay: '2', // 测试补寄校验
        refundScreenshotId: null,
        resendTrackingNumber: ''
      }
      this.dialogVisible = true
    },
    
    handleSubmit(data) {
      console.log('提交的数据:', data)
      this.lastSubmitData = data
      this.$message.success('数据提交成功！')
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
