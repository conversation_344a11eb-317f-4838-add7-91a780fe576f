package com.my.crossborder.service.impl;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.my.crossborder.controller.dto.sys_shift.SysShiftDeleteDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftInsertDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftPageDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftQuickCopyDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftUpdateDTO;
import com.my.crossborder.controller.vo.sys_shift.MonthShift;
import com.my.crossborder.controller.vo.sys_shift.MonthShift.ShiftObj;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.controller.vo.sys_shift.SysShiftPageVO;
import com.my.crossborder.controller.vo.sys_shift.SysShiftQuickCopyVO;
import com.my.crossborder.mybatis.entity.SysShift;
import com.my.crossborder.mybatis.entity.SysShiftAttendance;
import com.my.crossborder.mybatis.mapper.SysShiftMapper;
import com.my.crossborder.service.SysShiftAttendanceService;
import com.my.crossborder.service.SysShiftDayService;
import com.my.crossborder.service.SysShiftService;
import com.my.crossborder.service.SysShopService;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;

/**
 * 排班表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Service
@RequiredArgsConstructor
public class SysShiftServiceImpl extends ServiceImpl<SysShiftMapper, SysShift> implements SysShiftService {

	private final SysShiftDayService sysShiftDayService;
	private final SysShopService sysShopService;
	private final SysShiftAttendanceService sysShiftAttendanceService;

	@Transactional
	@Override
	public void insert(SysShiftInsertDTO insertDTO) {
		// 1. 日期表创建记录
		LocalDate shiftDay = insertDTO.getShiftDay();
		this.sysShiftDayService.createIfAbsense(shiftDay);

		// 2. 查询店铺数量
		int shopCount = this.sysShopService.count();

		// 3. 排班表批量新增
		List<SysShift> entityList = BeanUtil.copyToList(insertDTO.getShiftList(), SysShift.class);
		entityList.forEach(t -> {
			t.setShiftDay(shiftDay);
		});
		this.saveBatch(entityList);

		// 4. 考勤表批量新增
		Set<SysShiftAttendance> attendanceSet = new HashSet<>();
		entityList.forEach(t -> {
			attendanceSet.add(new SysShiftAttendance(shiftDay, t.getServiceUserId()));
			attendanceSet.add(new SysShiftAttendance(shiftDay, t.getSupervisorUserId()));
		});
		this.sysShiftAttendanceService.createIfAbsense(attendanceSet);

		// 5 日期表更新数量
		int shiftCount = insertDTO.getShiftList().size();
		this.sysShiftDayService.update(shiftDay, shopCount, shiftCount);
	}

	@Transactional
	@Override
	public void update(SysShiftUpdateDTO updateDTO) {
		// 1. 先查询入参对应的数据库记录
		LocalDate shiftDay = updateDTO.getShiftDay();
		Integer shopId = updateDTO.getShopId();
		Integer serviceUserId = updateDTO.getServiceUserId();
		Integer supervisorUserId = updateDTO.getSupervisorUserId();
		SysShift entity = this.get(shiftDay, shopId);
		if (entity == null) {
			return;
		}
		int oldServiceUserId = entity.getServiceUserId();
		int oldSupervisorUserId = entity.getSupervisorUserId();
		
		// 2. 更新排班记录
		LambdaUpdateWrapper<SysShift> updateWrapper = new LambdaUpdateWrapper<SysShift>()
			.set(SysShift::getServiceUserId, serviceUserId)
			.set(SysShift::getSupervisorUserId, supervisorUserId)
			.eq(SysShift::getShiftDay, shiftDay)
			.eq(SysShift::getShopId, shopId);
		this.update(updateWrapper);

		// 3. 更新考勤记录
		// 3.1 没有换人，则不处理
		// 3.2 换了人，则添加新的考勤记录，同时清理attendance表
		if (oldServiceUserId != serviceUserId.intValue()) {
			SysShiftAttendance attendance = new SysShiftAttendance(shiftDay, serviceUserId);
			this.sysShiftAttendanceService.createIfAbsense(attendance);
			this.deleteAttendanceWhenShiftNotExists(shiftDay, oldServiceUserId);
		}
		if (oldSupervisorUserId != supervisorUserId.intValue()) {
			SysShiftAttendance attendance = new SysShiftAttendance(shiftDay, supervisorUserId);
			this.sysShiftAttendanceService.createIfAbsense(attendance);
			this.deleteAttendanceWhenShiftNotExists(shiftDay, oldSupervisorUserId);
		}
	}

	@Transactional
	@Override
	public void delete(SysShiftDeleteDTO deleteDTO) {
		// 1. 先查询入参对应的数据库记录
		LocalDate shiftDay = deleteDTO.getShiftDay();
		Integer shopId = deleteDTO.getShopId();
		SysShift entity = this.get(shiftDay, shopId);
		if (entity == null) {
			return;
		}
		// 2. 删除排班记录
		this.baseMapper.delete(new LambdaQueryWrapper<SysShift>()
			.eq(SysShift::getShiftDay, shiftDay)
			.eq(SysShift::getShopId, shopId));

		// 3. 如果客户和主管当天没有其他排班了，则从Attendance里面也删除
		this.deleteAttendanceWhenShiftNotExists(shiftDay, entity.getServiceUserId());
		this.deleteAttendanceWhenShiftNotExists(shiftDay, entity.getSupervisorUserId());

		// 4. 更新某日排班店铺数量，或者删除sysShiftDay
		int shiftCount = this.shiftCount(shiftDay);
		if (shiftCount == 0) {
			this.sysShiftDayService.removeById(shiftDay);
		} else {
			int shopCount = this.sysShopService.count();
			this.sysShiftDayService.update(shiftDay, shopCount, shiftCount);
		}
	}

	@Transactional
	@Override
	public void deleteAttendanceWhenShiftNotExists(LocalDate shiftDay, Integer userId) {
		Integer serviceShiftCount = this.shiftCount(shiftDay, userId);
		if (serviceShiftCount == 0) {
			this.sysShiftAttendanceService.removeByUserId(shiftDay, userId);
		}
	}
	
	@Override
	public MonthShift monthShift(Integer userId, Integer year, Integer month) {
		MonthShift result = new MonthShift();
		Map<LocalDate, List<ShiftObj>> map = Maps.newTreeMap();
		result.setScheduleData(map);

		// 构建查询日期范围
		String startDate = String.format("%d-%02d-01", year, month);
		LocalDate start = LocalDate.parse(startDate);
		LocalDate end = start.withDayOfMonth(start.lengthOfMonth());
		String endDate = end.toString();

		// 查询该用户在指定月份的排班记录
		List<SysShiftPageVO> shiftList = this.listByDateRange(startDate, endDate)
				.stream()
				.filter(row -> userId.intValue() == row.getServiceUserId().intValue() 
						|| userId.intValue() == row.getSupervisorUserId().intValue())
				.collect(java.util.stream.Collectors.toList());
		for (SysShiftPageVO vo : shiftList) {
			LocalDate day = vo.getDate();
			map.putIfAbsent(day, Lists.newLinkedList());
			ShiftObj row = new ShiftObj(vo.getShopId(), vo.getShopName());
			map.get(day).add(row);
		}

		return result;
	}
	
	@Override
	public Page<SysShiftPageVO> page(SysShiftPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Override
	public List<SysShiftPageVO> listByShiftDay(String shiftDay) {
		return this.baseMapper.listByShiftDay(shiftDay);
	}

	@Override
	public List<SysShiftPageVO> listByDateRange(String startDate, String endDate) {
		return this.baseMapper.listByDateRange(startDate, endDate);
	}

	private int shiftCount(LocalDate shiftDay) {
		LambdaQueryWrapper<SysShift> wrapper = new LambdaQueryWrapper<SysShift>().eq(SysShift::getShiftDay, shiftDay);
		return this.count(wrapper);
	}
	
	private int shiftCount(LocalDate shiftDay, Integer serviceUserId) {
		return this.count(new LambdaQueryWrapper<SysShift>()
			.eq(SysShift::getShiftDay, shiftDay)
			.eq(SysShift::getServiceUserId, serviceUserId));
	}
	
	private SysShift get(LocalDate shiftDay, Integer shopId) {
		SysShift entity = this.baseMapper.selectOne(new LambdaQueryWrapper<SysShift>()
				.eq(SysShift::getShiftDay, shiftDay)
				.eq(SysShift::getShopId, shopId));
		return entity;
	}

	@Transactional
	@Override
	public SysShiftQuickCopyVO quickCopy(SysShiftQuickCopyDTO quickCopyDTO) {
		LocalDate sourceDate = quickCopyDTO.getSourceDate();

		// 1. 查询源日期的排班数据
		List<SysShift> sourceShifts = this.list(new LambdaQueryWrapper<SysShift>()
				.eq(SysShift::getShiftDay, sourceDate));
		BusinessException.when(sourceShifts.isEmpty(), "源日期 " + sourceDate + " 没有排班数据，无法复制");

		// 2. 计算目标日期范围：从今天开始往后7个自然日
		LocalDate today = LocalDate.now();
		List<LocalDate> targetDates = Lists.newLinkedList();
		for (int i = 1; i <= 7; i++) {
			targetDates.add(today.plusDays(i));
		}

		// 3. 复制排班数据到目标日期
		int totalAffectedRecords = 0;
		for (LocalDate targetDate : targetDates) {
			// 3.1 删除目标日期的现有排班数据（如果存在）
			this.remove(new LambdaQueryWrapper<SysShift>()
					.eq(SysShift::getShiftDay, targetDate));

			// 3.2 复制源日期的排班数据到目标日期
			List<SysShift> newShifts = Lists.newLinkedList();
			Set<SysShiftAttendance> attendanceSet = new HashSet<>();

			for (SysShift sourceShift : sourceShifts) {
				SysShift newShift = SysShift.builder()
						.shiftDay(targetDate)
						.shopId(sourceShift.getShopId())
						.serviceUserId(sourceShift.getServiceUserId())
						.supervisorUserId(sourceShift.getSupervisorUserId())
						.build();
				newShifts.add(newShift);

				// 准备考勤数据
				attendanceSet.add(new SysShiftAttendance(targetDate, sourceShift.getServiceUserId()));
				attendanceSet.add(new SysShiftAttendance(targetDate, sourceShift.getSupervisorUserId()));
			}

			// 3.3 批量保存新的排班数据
			if (!newShifts.isEmpty()) {
				this.saveBatch(newShifts);
				totalAffectedRecords += newShifts.size();

				// 3.4 创建或更新日期表记录
				this.sysShiftDayService.createIfAbsense(targetDate);
				int shopCount = this.sysShopService.count();
				this.sysShiftDayService.update(targetDate, shopCount, newShifts.size());

				// 3.5 创建考勤记录
				this.sysShiftAttendanceService.createIfAbsense(attendanceSet);
			}
		}

		// 4. 构建返回结果
		return SysShiftQuickCopyVO.builder()
				.sourceDate(sourceDate)
				.targetDates(targetDates)
				.copiedShiftCount(sourceShifts.size())
				.totalAffectedRecords(totalAffectedRecords)
				.message(String.format("成功将 %s 的 %d 个排班记录复制到 %d 个目标日期，共影响 %d 条记录",
						sourceDate, sourceShifts.size(), targetDates.size(), totalAffectedRecords))
				.build();
	}

}
