package com.my.crossborder.controller;

import java.util.List;
import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanInsertDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanPageDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanUpdateDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanDeleteDTO;
import com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanDetailVO;
import com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanPageVO;
import com.my.crossborder.controller.vo.StdResp;

import com.my.crossborder.service.OrdTaiwanService;
import com.my.crossborder.service.SysShopService;

import cn.dev33.satoken.annotation.SaCheckPermission;

import org.springframework.web.bind.annotation.RestController;

/**
 * 台湾上架物品 
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/api/ord-taiwan")
@RequiredArgsConstructor
public class OrdTaiwanController {

    private final OrdTaiwanService ordTaiwanService;
    private final SysShopService sysShopService;

    /**
    * 新增
    */
    @SaCheckPermission("ord-taiwan:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody OrdTaiwanInsertDTO insertDTO) {
    	this.ordTaiwanService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("ord-taiwan:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody OrdTaiwanUpdateDTO updateDTO) {
    	this.ordTaiwanService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<OrdTaiwanDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.ordTaiwanService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<OrdTaiwanPageVO>> page(OrdTaiwanPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds.stream().map(String::valueOf).collect(java.util.stream.Collectors.toList()));
        }

        Page<OrdTaiwanPageVO> page = this.ordTaiwanService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("ord-taiwan:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody OrdTaiwanDeleteDTO deleteDTO) {
    	this.ordTaiwanService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
