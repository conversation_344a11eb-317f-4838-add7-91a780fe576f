package com.my.crossborder.controller.dto.sys_menu_ref_role;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_菜单_角色_关联表
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenuRefRoleDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<Integer> idList;
	
}
