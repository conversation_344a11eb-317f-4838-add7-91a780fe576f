<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdClaimMapper">


	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.ord_claim.OrdClaimPageVO">
        SELECT
            t1.id,
            t1.order_item_id,
            t1.issue,
            t1.issue_type,
            t1.issue_user_id,
            t1.issue_time,
            t1.close_way,
            t1.close_user_id,
            tCloseUser.real_name AS close_user_name,
            t1.close_time,
            t1.confirm_user_id,
            t1.confirm_time,
            t1.purchase_cost,
            t1.update_time,
            t1.claim_status,
            t1.waybill_number,
            tExpress.express_no,
            tOrder.shop_name,
            tOrder.shop_id,
            tOrder.order_sn,
            tItem.item_name,
            tItem.item_model_name,
            tItem.item_image,
            tItem.item_model_sku,
            tItem.item_price,
            tItem.item_price AS product_price
        FROM
            ord_claim AS t1
        LEFT JOIN erp_order_item AS tItem ON t1.order_item_id = tItem.id
        LEFT JOIN erp_order AS tOrder ON tItem.order_id = tOrder.order_id
        LEFT JOIN erp_order_item_express AS tExpress ON tItem.id = tExpress.order_item_id
        LEFT JOIN sys_user AS tCloseUser ON t1.close_user_id = tCloseUser.user_id
		<where>
	        <if test="id != null">
	           	AND t1.id = #{id}
            </if>
	        <if test="orderSn != null and orderSn != ''">
	           	AND t1.order_sn = #{orderSn}
            </if>
	        <if test="issueType != null and issueType != ''">
	           	AND t1.issue_type = #{issueType}
            </if>
	        <if test="orderItemId != null">
	           	AND t1.order_item_id = #{orderItemId}
            </if>
	        <if test="closeWay != null and closeWay != ''">
	           	AND t1.close_way = #{closeWay}
            </if>
	        <if test="claimStatus != null and claimStatus != ''">
	           	AND t1.claim_status = #{claimStatus}
            </if>
	        <if test="issueUserId != null">
	           	AND t1.issue_user_id = #{issueUserId}
            </if>
	        <!-- <if test="issueTime != null and issueTime != ''">
	           	AND t1.issue_time = #{issueTime}
            </if> -->
	        <!-- <if test="closeUserId != null">
	           	AND t1.close_user_id = #{closeUserId}
            </if> -->
	        <!-- <if test="closeTime != null and closeTime != ''">
	           	AND t1.close_time = #{closeTime}
            </if> -->
	        <!-- <if test="confirmUserId != null">
	           	AND t1.confirm_user_id = #{confirmUserId}
            </if> -->
	        <!-- <if test="confirmTime != null and confirmTime != ''">
	           	AND t1.confirm_time = #{confirmTime}
            </if>
	        <if test="updateTime != null and updateTime != ''">
	           	AND t1.update_time = #{updateTime}
            </if> -->
	        <if test="expressNo != null and expressNo != ''">
	           	AND tExpress.express_no = #{expressNo} 
            </if>
	        <if test="shopIds != null and shopIds.size() > 0">
	           	AND tOrder.shop_id IN
	           	<foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
	           		#{shopId}
	           	</foreach>
            </if>
        </where>
        ORDER BY t1.issue_time DESC
    </select>

</mapper>
