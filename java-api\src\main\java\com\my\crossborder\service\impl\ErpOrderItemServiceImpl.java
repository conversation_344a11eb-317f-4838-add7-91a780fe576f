package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.ErpOrderItem;
import com.my.crossborder.mybatis.mapper.ErpOrderItemMapper;
import com.my.crossborder.service.ErpOrderItemService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemInsertDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemPageDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemUpdateDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemDeleteDTO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemDetailVO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单项表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
public class ErpOrderItemServiceImpl extends ServiceImpl<ErpOrderItemMapper, ErpOrderItem> implements ErpOrderItemService {


	@Transactional
	@Override
	public void insert(ErpOrderItemInsertDTO insertDTO) {
		ErpOrderItem entity = BeanUtil.copyProperties(insertDTO, ErpOrderItem.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(ErpOrderItemUpdateDTO updateDTO) {
		ErpOrderItem entity = BeanUtil.copyProperties(updateDTO, ErpOrderItem.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public ErpOrderItemDetailVO detail(Long id) {
		ErpOrderItem entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, ErpOrderItemDetailVO.class);
	}

	@Override
	public Page<ErpOrderItemPageVO> page(ErpOrderItemPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(ErpOrderItemDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
