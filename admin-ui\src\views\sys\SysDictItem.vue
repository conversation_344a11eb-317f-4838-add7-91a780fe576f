<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>字典管理</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主要内容区域 -->
    <div class="dict-management-container" style="margin-top: 20px;">
      <el-row :gutter="20">
        <!-- 左侧字典分类 -->
        <el-col :span="6">
          <el-card class="box-card category-card">
            <div slot="header" class="clearfix">
              <span>字典分类</span>
              <div style="float: right;">
                <el-button v-permission="['sys-dict:insert']" type="primary" size="mini" @click="addDictCategory">
                  <i class="el-icon-plus"></i>
                </el-button>
              </div>
            </div>

            <!-- 搜索框 -->
            <el-input placeholder="请输入分类ID或名称搜索" v-model="categorySearchText" size="small" class="search-input"
              style="margin-bottom: 15px;">
              <i slot="prefix" class="el-icon-search search-icon"></i>
            </el-input>

            <!-- 分类列表 -->
            <div class="category-list">
              <div v-for="category in filteredCategories" :key="category.categoryId"
                :class="['category-item', { active: selectedCategory && selectedCategory.categoryId === category.categoryId }]"
                @click="selectCategory(category)">
                <div class="category-content">
                  <div class="category-name">{{ category.categoryName }}</div>
                  <div class="category-id">{{ category.categoryId }}</div>
                </div>
                <div class="category-actions">
                  <el-button v-permission="['sys-dict:update']" type="text" size="mini"
                    @click.stop="editDictCategory(category)">
                    编辑
                  </el-button>
                  <el-button v-permission="['sys-dict:delete']" type="text" size="mini"
                    @click.stop="deleteDictCategory(category)" style="color: #F56C6C;">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧字典项管理 -->
        <el-col :span="18">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>字典项管理</span>
              <span v-if="selectedCategory" style="margin-left: 10px; color: #999; font-size: 14px;">
                （{{ selectedCategory.categoryName }} ）{{ selectedCategory.categoryId }}
              </span>
              <div style="float: right;">
                <el-button v-permission="['sys-dict:insert']" type="primary" size="mini" @click="addDictItem"
                  :disabled="!selectedCategory">
                  <i class="el-icon-plus"></i> 新增
                </el-button>
              </div>
            </div>

            <!-- 字典项搜索表单 -->
            <el-form :inline="true" :model="itemSearchForm" class="demo-form-inline" v-if="selectedCategory">
              <el-form-item label="字典项名称">
                <el-input v-model="itemSearchForm.itemName" placeholder="请输入字典项名称" clearable
                  style="width: 200px;"></el-input>
              </el-form-item>
              <el-form-item label="字典项值">
                <el-input v-model="itemSearchForm.itemValue" placeholder="请输入字典项值" clearable
                  style="width: 150px;"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" @click="searchDictItems">查询</el-button>
                <el-button size="small" @click="resetItemSearch">重置</el-button>
              </el-form-item>
            </el-form>

            <!-- 字典项表格 -->
            <el-table :data="dictItems" border style="width: 100%; margin-top: 15px;" v-loading="itemLoading"
              v-if="selectedCategory">
              <el-table-column type="index" label="序号" width="60" align="center" v-if="false">
              </el-table-column>
              <el-table-column prop="sortNum" label="排序号" width="80" align="center">
              </el-table-column>
              <el-table-column prop="itemName" label="字典项名称" width="220" align="center">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.color" size="medium" v-if="scope.row.color">
                    {{ scope.row.itemName }}
                  </el-tag>
                  <span v-else> {{ scope.row.itemName }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="itemValue" label="字典项值" width="220" align="center">
              </el-table-column>
              <el-table-column label="操作" align="left">
                <template slot-scope="scope">
                  <el-button v-permission="['sys-dict:update']" size="mini" type="text"
                    @click="editDictItem(scope.row)">
                    <i class="el-icon-edit"></i> 编辑
                  </el-button>
                  <el-button v-permission="['sys-dict:delete']" size="mini" type="text"
                    @click="deleteDictItem(scope.row)" style="color: #F56C6C;">
                    <i class="el-icon-delete"></i> 删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 空状态 -->
            <div class="empty-state" v-if="!selectedCategory" style="text-align: center; padding: 50px;">
              <i class="el-icon-folder-opened" style="font-size: 48px; color: #C0C4CC;"></i>
              <div style="margin-top: 16px; color: #606266;">请从左侧选择字典分类</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 添加/编辑字典分类对话框 -->
    <el-dialog :title="categoryForm.categoryId ? '编辑字典分类' : '添加字典分类'" :visible.sync="categoryDialogVisible"
      width="500px">
      <el-form :model="categoryForm" :rules="categoryRules" ref="categoryForm" label-width="100px">
        <el-form-item label="分类ID" prop="categoryId" v-if="!isEditingCategory">
          <el-input v-model="categoryForm.categoryId" placeholder="请输入分类ID"></el-input>
        </el-form-item>
        <el-form-item label="分类ID" prop="categoryId" v-if="isEditingCategory">
          <el-input v-model="categoryForm.categoryId" placeholder="分类ID" disabled></el-input>
        </el-form-item>
        <el-form-item label="新分类ID" prop="newCategoryId" v-if="isEditingCategory">
          <el-input v-model="categoryForm.newCategoryId" placeholder="请输入新分类ID"></el-input>
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="categoryForm.categoryName" placeholder="请输入分类名称"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="categoryForm.description" type="textarea" placeholder="请输入描述"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveDictCategory">确 定</el-button>
        <el-button @click="categoryDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <!-- 添加/编辑字典项对话框 -->
    <el-dialog :title="itemForm.isEdit ? '编辑字典项' : '添加字典项'" :visible.sync="itemDialogVisible" width="500px">
      <el-form :model="itemForm" :rules="itemRules" ref="itemForm" label-width="100px">
        <el-form-item label="排序号" prop="sortNum">
          <el-input-number v-model="itemForm.sortNum" :min="0" placeholder="请输入排序号"></el-input-number>
        </el-form-item>
        <el-form-item label="字典项名称" prop="itemName">
          <el-input v-model="itemForm.itemName" placeholder="请输入字典项名称"></el-input>
        </el-form-item>
        <el-form-item label="字典项值" prop="itemValue" v-if="!itemForm.isEdit">
          <el-input v-model="itemForm.itemValue" placeholder="请输入字典项值"></el-input>
        </el-form-item>
        <el-form-item label="字典项值" prop="itemValue" v-if="itemForm.isEdit">
          <el-input v-model="itemForm.itemValue" placeholder="字典项值" disabled></el-input>
        </el-form-item>
        <el-form-item label="新字典项值" prop="newItemValue" v-if="itemForm.isEdit">
          <el-input v-model="itemForm.newItemValue" placeholder="请输入新字典项值"></el-input>
        </el-form-item>
        <el-form-item label="颜色" prop="color">
          <color-tag-selector v-model="itemForm.color" :display-text="itemForm.itemName || '示例'"
            @change="handleColorChange">
          </color-tag-selector>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveDictItem">确 定</el-button>
        <el-button @click="itemDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  dictCategoryList,
  createDictCategory,
  updateDictCategory,
  deleteDictCategory,
  dictItemPage,
  createDictItem,
  updateDictItem,
  deleteDictItem
} from '@/api/SysDictItem'
import ColorTagSelector from '@/components/ColorTagSelector.vue'

export default {
  name: 'DictManagement',
  components: {
    ColorTagSelector
  },
  data() {
    return {
      // 颜色选项
      colorOptions: [
        { value: 'success' },
        { value: 'primary' },
        { value: 'info' },
        { value: 'warning' },
        { value: 'danger' },
      ],

      // 分类搜索
      categorySearchText: '',
      selectedCategory: null,
      categoryDialogVisible: false,
      categories: [],
      categoryLoading: false,

      // 字典项搜索
      itemSearchForm: {
        itemName: '',
        itemValue: ''
      },
      dictItems: [],
      itemLoading: false,
      itemDialogVisible: false,

      // 表单数据
      categoryForm: {
        categoryId: '',
        newCategoryId: '',
        categoryName: '',
        description: ''
      },
      itemForm: {
        isEdit: false,
        categoryId: '',
        itemName: '',
        itemValue: '',
        newItemValue: '', // 新字典项值
        sortNum: 1,
        color: '',
        originalCategoryId: '', // 用于编辑时保存原始值
        originalItemValue: ''   // 用于编辑时保存原始值
      },

      // 验证规则
      categoryRules: {
        categoryName: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请输入分类ID', trigger: 'blur' }
        ],
        newCategoryId: [
          { required: true, message: '请输入新分类ID', trigger: 'blur' }
        ],
        description: [
          // 描述不是必填项
        ]
      },
      itemRules: {
        itemName: [
          { required: true, message: '请输入字典项名称', trigger: 'blur' }
        ],
        itemValue: [
          { required: true, message: '请输入字典项值', trigger: 'blur' }
        ],
        newItemValue: [
          { required: true, message: '请输入新字典项值', trigger: 'blur' }
        ],
        sortNum: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 过滤后的分类数据
    filteredCategories() {
      if (!this.categorySearchText) {
        return this.categories
      }
      return this.categories.filter(category =>
        category.categoryId.toLowerCase().includes(this.categorySearchText.toLowerCase()) ||
        category.categoryName.toLowerCase().includes(this.categorySearchText.toLowerCase())
      )
    },
    // 是否在编辑分类
    isEditingCategory() {
      return this.categoryForm.categoryId && this.categories.some(c => c.categoryId === this.categoryForm.categoryId)
    }
  },
  mounted() {
    this.loadCategories()
  },
  methods: {
    // 加载字典分类
    async loadCategories() {
      this.categoryLoading = true
      try {
        const response = await dictCategoryList()
        if (response.success) {
          this.categories = response.data || []
        }
      } catch (error) {
        this.$message.error('加载字典分类失败')
      } finally {
        this.categoryLoading = false
      }
    },

    // 选择分类
    selectCategory(category) {
      this.selectedCategory = category
      this.loadDictItems()
    },

    // 加载字典项
    async loadDictItems() {
      if (!this.selectedCategory) return

      this.itemLoading = true
      try {
        const params = {
          categoryId: this.selectedCategory.categoryId,
          current: 1,
          size: 1000, // 获取所有数据
          ...this.itemSearchForm
        }
        const response = await dictItemPage(params)
        if (response.success) {
          this.dictItems = response.data.records || []
        }
      } catch (error) {
        this.$message.error('加载字典项失败')
      } finally {
        this.itemLoading = false
      }
    },

    // 搜索字典项
    searchDictItems() {
      this.loadDictItems()
    },

    // 重置字典项搜索
    resetItemSearch() {
      this.itemSearchForm = {
        itemName: '',
        itemValue: ''
      }
      this.loadDictItems()
    },

    // 添加字典分类
    addDictCategory() {
      this.categoryForm = {
        categoryId: '',
        newCategoryId: '',
        categoryName: '',
        description: ''
      }
      this.categoryDialogVisible = true
      // 清除之前的验证结果
      this.$nextTick(() => {
        this.$refs.categoryForm && this.$refs.categoryForm.clearValidate()
      })
    },

    // 编辑字典分类
    editDictCategory(category) {
      this.categoryForm = {
        categoryId: category.categoryId,
        newCategoryId: category.categoryId, // 默认复制原分类ID
        categoryName: category.categoryName,
        description: category.description
      }
      this.categoryDialogVisible = true
      // 清除之前的验证结果
      this.$nextTick(() => {
        this.$refs.categoryForm && this.$refs.categoryForm.clearValidate()
      })
    },

    // 保存字典分类
    saveDictCategory() {
      this.$refs.categoryForm.validate(async (valid) => {
        if (valid) {
          try {
            if (this.isEditingCategory) {
              // 编辑 - 使用newCategoryId作为新的分类ID
              const updateData = {
                categoryId: this.categoryForm.categoryId, // 原分类ID
                newCategoryId: this.categoryForm.newCategoryId, // 新分类ID
                categoryName: this.categoryForm.categoryName,
                description: this.categoryForm.description
              }
              await updateDictCategory(updateData)
            } else {
              // 新增 - 使用categoryId
              const createData = {
                categoryId: this.categoryForm.categoryId,
                categoryName: this.categoryForm.categoryName,
                description: this.categoryForm.description
              }
              await createDictCategory(createData)
            }
            this.categoryDialogVisible = false
            this.$message.success('保存成功')
            this.loadCategories()
            // 如果编辑的是当前选中的分类，需要更新选中状态
            if (this.isEditingCategory && this.selectedCategory && this.selectedCategory.categoryId === this.categoryForm.categoryId) {
              this.selectedCategory = null
              this.dictItems = []
            }
          } catch (error) {
            this.$message.error('保存失败')
          }
        }
      })
    },

    // 删除字典分类
    deleteDictCategory(category) {
      this.$confirm('确定要删除该字典分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deleteDictCategory({ idList: [category.categoryId] })
        this.$message.success('删除成功')
        this.loadCategories()
        if (this.selectedCategory && this.selectedCategory.categoryId === category.categoryId) {
          this.selectedCategory = null
          this.dictItems = []
        }
      }).catch(() => { })
    },

    // 添加字典项
    addDictItem() {
      this.itemForm = {
        isEdit: false,
        categoryId: this.selectedCategory.categoryId,
        itemName: '',
        itemValue: '',
        newItemValue: '',
        color: '',
        sortNum: 1,
        originalCategoryId: '',
        originalItemValue: ''
      }
      this.itemDialogVisible = true
    },

    // 编辑字典项
    editDictItem(item) {
      this.itemForm = {
        ...item,
        isEdit: true,
        newItemValue: item.itemValue, // 默认复制原字典项值
        originalCategoryId: item.categoryId,
        originalItemValue: item.itemValue
      }
      this.itemDialogVisible = true
    },

    // 保存字典项
    saveDictItem() {
      this.$refs.itemForm.validate(async (valid) => {
        if (valid) {
          try {
            if (this.itemForm.isEdit) {
              // 编辑 - 需要传递原始的联合主键用于定位记录和新字典项值
              const updateData = {
                categoryId: this.itemForm.originalCategoryId,
                itemValue: this.itemForm.originalItemValue,
                newItemValue: this.itemForm.newItemValue,
                itemName: this.itemForm.itemName,
                sortNum: this.itemForm.sortNum,
                color: this.itemForm.color
              }
              await updateDictItem(updateData)
            } else {
              // 新增
              const insertData = {
                categoryId: this.itemForm.categoryId,
                itemValue: this.itemForm.itemValue,
                itemName: this.itemForm.itemName,
                sortNum: this.itemForm.sortNum,
                color: this.itemForm.color
              }
              await createDictItem(insertData)
            }
            this.itemDialogVisible = false
            this.$message.success('保存成功')
            this.loadDictItems()
          } catch (error) {
            this.$message.error('保存失败')
          }
        }
      })
    },

    // 删除字典项
    deleteDictItem(item) {
      this.$confirm('确定要删除该字典项吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteDictItem({
            keyList: [{
              categoryId: item.categoryId,
              itemValue: item.itemValue
            }]
          })
          this.$message.success('删除成功')
          this.loadDictItems()
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => { })
    },

    // 处理颜色变化
    handleColorChange(color) {
      this.itemForm.color = color
    }
  }
}
</script>

<style scoped>
.dict-management-container {
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.category-card {
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.category-card .el-card__body {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
}

.search-input {
  margin-bottom: 15px;
  flex-shrink: 0;
}

.search-icon {
  position: relative;
  top: 8px;
}

.category-list {
  flex: 1;
  overflow-y: auto;
  max-height: none;
}

.category-item {
  padding: 12px 16px;
  margin-bottom: 8px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-item:hover {
  border-color: #409EFF;
  background-color: #F5F7FA;
}

.category-item.active {
  border-color: #409EFF;
  background-color: #ECF5FF;
}

.category-content {
  flex: 1;
}

.category-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.category-id {
  font-size: 12px;
  color: #909399;
}

.category-actions {
  opacity: 0;
  transition: opacity 0.3s;
}

.category-item:hover .category-actions {
  opacity: 1;
}

.demo-form-inline .el-form-item {
  margin-bottom: 15px;
}

.empty-state {
  text-align: center;
  padding: 50px;
  color: #909399;
}
</style>
