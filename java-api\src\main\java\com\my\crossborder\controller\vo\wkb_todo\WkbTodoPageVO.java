package com.my.crossborder.controller.vo.wkb_todo;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_通知表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbTodoPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知id
     */
    private Integer id;

    /**
     * 类别
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;
    
    /**
     * 截止日期
     */
    private LocalDate deadline;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布用户id
     */
    private Integer publishUserId;
    

    /**
     * 是否已读
     */
    private Boolean read;

    /**
     * 公告的接收用户id
     */
    private Integer userId;
    
    /**
     * 发布人姓名
     */
    private String publishUserName;

}
