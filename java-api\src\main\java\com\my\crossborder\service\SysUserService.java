package com.my.crossborder.service;

import java.util.List;

import javax.validation.Valid;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_user.SysUserChangeMyPasswordDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserDeleteDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserInsertDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserPageDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserResetPwdDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserUpdateDTO;
import com.my.crossborder.controller.vo.sys_user.SysUserDetailVO;
import com.my.crossborder.controller.vo.sys_user.SysUserPageVO;
import com.my.crossborder.mybatis.entity.SysUser;

/**
 * system user 服务类
 *
 * @date 2024-05-20
 */
public interface SysUserService extends IService<SysUser> {

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysUserDetailVO detail(Integer userId);

	/**
	 * 查询用户
	 * @param username
	 * @param password
	 * @return
	 */
	SysUser getByUsernameAndPassword(String username, String password);
	
	/**
	 * 查询用户
	 * @param id
	 * @return
	 */
	SysUser getById(Integer userId);
	
	/**
	 * 修改我的密码
	 * @param dto
	 */
	void changeMyPassword(@Valid SysUserChangeMyPasswordDTO dto);
	
	/**
	 * 新增
	 */
	void insert(SysUserInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysUserUpdateDTO updateDTO);

	/**
	 * 分页
	 */
	Page<SysUserPageVO> page(SysUserPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysUserDeleteDTO deleteDTO);

	/**
	 * 根据角色id查询
	 * @param roleIdList
	 * @return
	 */
	List<SysUser> listByRoleIdList(List<Integer> roleIdList);

	/**
	 * 查询用户id查询
	 * @param userIdList
	 * @return
	 */
	List<SysUser> list(List<Integer> userIdList);

	/**
	 * 重置密码
	 * @param dto
	 */
	void resetPwd(@Valid SysUserResetPwdDTO dto);	

}
