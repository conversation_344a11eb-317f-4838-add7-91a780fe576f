package com.my.crossborder.forest.erp990.vo;

import com.dtflys.forest.http.HttpStatus;

import lombok.Data;

@Data
public abstract class Erp990VO {

	
	/**
	 * 响应码
	 */
    protected Integer code;

    /**
     * 响应码说明
     */
    protected String msg;
    
    
    /**
     * 是否响应成功
     * @return
     */
    public boolean isSuccess() {
    	return this.code.intValue() == HttpStatus.OK;
    }
    
}
