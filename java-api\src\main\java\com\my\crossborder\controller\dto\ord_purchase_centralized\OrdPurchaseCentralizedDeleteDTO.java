package com.my.crossborder.controller.dto.ord_purchase_centralized;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_集中采购
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseCentralizedDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<Integer> idList;
	
}
