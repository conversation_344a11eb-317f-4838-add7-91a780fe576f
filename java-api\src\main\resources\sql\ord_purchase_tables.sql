-- 采购订单相关表的建表语句
-- 执行前请确保数据库已存在

-- 1. 采购订单主表
CREATE TABLE IF NOT EXISTS `ord_purchase` (
  `order_sn` varchar(50) NOT NULL COMMENT '订单号',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT '总金额',
  `income_amount` decimal(10,2) DEFAULT NULL COMMENT '订单入账金额',
  `expect_weight` decimal(10,2) DEFAULT NULL COMMENT '预计重量',
  `create_user_id` int(11) DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_sn`),
  KEY `idx_create_user_id` (`create_user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购订单主表';

-- 2. 采购订单明细表
CREATE TABLE IF NOT EXISTS `ord_purchase_item` (
  `order_item_id` varchar(50) NOT NULL COMMENT '订单明细id',
  `purchase_channel` varchar(20) DEFAULT NULL COMMENT '采购途径 字典PURCHASE_CHANNEL',
  `purchase_amount` decimal(10,2) DEFAULT NULL COMMENT '采购金额',
  `purchase_date` date DEFAULT NULL COMMENT '采购日期',
  `purchase_user_id` int(11) DEFAULT NULL COMMENT '采购人id',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_item_id`),
  KEY `idx_purchase_channel` (`purchase_channel`),
  KEY `idx_purchase_date` (`purchase_date`),
  KEY `idx_purchase_user_id` (`purchase_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购订单明细表';

-- 3. 添加外键约束（可选，根据实际需要）
-- ALTER TABLE `ord_purchase_item` ADD CONSTRAINT `fk_ord_purchase_item_order_sn` 
-- FOREIGN KEY (`order_sn`) REFERENCES `ord_purchase` (`order_sn`) ON DELETE CASCADE;

-- 4. 插入PURCHASE_CHANNEL字典数据（如果sys_dict_item表存在）
-- 先检查字典分类是否存在
INSERT IGNORE INTO `sys_dict_category` (`category_id`, `category_name`, `sort_num`, `create_time`, `update_time`) 
VALUES ('PURCHASE_CHANNEL', '采购途径', 1, NOW(), NOW());

-- 插入字典项
INSERT IGNORE INTO `sys_dict_item` (`category_id`, `item_value`, `item_name`, `sort_num`, `create_time`, `update_time`) VALUES
('PURCHASE_CHANNEL', '1', '拼多多', 1, NOW(), NOW()),
('PURCHASE_CHANNEL', '2', '1688', 2, NOW(), NOW()),
('PURCHASE_CHANNEL', '3', '淘宝及其他', 3, NOW(), NOW()),
('PURCHASE_CHANNEL', '4', '调用库存', 4, NOW(), NOW()),
('PURCHASE_CHANNEL', '5', '缺货打包', 5, NOW(), NOW());

-- 5. 创建索引以优化查询性能
CREATE INDEX `idx_ord_purchase_create_time` ON `ord_purchase` (`create_time`);
CREATE INDEX `idx_ord_purchase_item_order_sn_channel` ON `ord_purchase_item` (`order_sn`, `purchase_channel`);
CREATE INDEX `idx_ord_purchase_item_purchase_date_user` ON `ord_purchase_item` (`purchase_date`, `purchase_user_id`);

-- 执行完成后的验证SQL
-- SELECT 'ord_purchase表创建成功' as message;
-- SELECT 'ord_purchase_item表创建成功' as message;
-- SELECT COUNT(*) as purchase_channel_count FROM sys_dict_item WHERE category_id = 'PURCHASE_CHANNEL';
