package com.my.crossborder.mybatis.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 采购订单明细表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ord_purchase_item")
public class OrdPurchaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单明细id
     */
    @TableId(value = "order_item_id", type = IdType.INPUT)
    private String orderItemId;

    /**
     * 采购途径 字典PURCHASE_CHANNEL
     */
    private String purchaseChannel;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购人id
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer purchaseUserId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


}
