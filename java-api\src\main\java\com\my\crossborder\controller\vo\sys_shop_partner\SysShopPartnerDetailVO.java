package com.my.crossborder.controller.vo.sys_shop_partner;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_店铺合伙人表
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShopPartnerDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 店铺ID（关联 sys_shop.id）
     */
    private Integer shopId;

    /**
     * 合伙人用户ID（关联 sys_user.user_id）
     */
    private Integer partnerUserId;

}
