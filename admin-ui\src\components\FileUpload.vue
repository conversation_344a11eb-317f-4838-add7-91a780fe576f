<template>
  <div class="file-upload-component">
    <el-upload
      ref="upload"
      action="#"
      :auto-upload="false"
      :show-file-list="true"
      :on-change="handleFileChange"
      :on-remove="removeFile"
      :on-exceed="handleExceed"
      :file-list="fileList"
      :limit="1"
      :accept="accept"
      class="upload-demo"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">支持格式：{{ acceptText }}，单个文件不超过{{ maxSize }}MB</div>
    </el-upload>

    <!-- 上传进度条 -->
    <el-progress
      v-if="uploading"
      :percentage="uploadProgress"
      :show-text="false"
      class="upload-progress"
    ></el-progress>
  </div>
</template>

<script>
import { sysAttachmentUpload } from '@/api/SysAttachment';
import { sysParamGet } from '@/api/SysParam';

export default {
  name: 'FileUpload',
  props: {
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true
    },
    // 初始文件列表
    initialFileList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      uploadedFile: null,
      uploading: false,
      uploadProgress: 0,
      attachmentId: null,
      accept: '.jpg', // 默认值，将通过系统参数获取
      maxSize: 2, // 默认值，将通过系统参数获取，单位MB
      fileList: []
    };
  },
  watch: {
    // 监听initialFileList变化
    initialFileList: {
      handler(newVal) {
        console.log('[FileUpload] initialFileList changed:', newVal);
        if (newVal && newVal.length > 0) {
          // 使用map处理初始文件列表，以确保每个文件都有name, url和id属性
          this.fileList = newVal.map(file => {
            return {
              name: file.name || '附件',
              url: file.url || '',
              id: file.id || ''
            };
          });

          // 如果有文件ID，设置attachmentId
          if (newVal[0] && newVal[0].id) {
            this.attachmentId = newVal[0].id;
          }

          console.log('[FileUpload] fileList set to:', this.fileList);
        } else {
          this.fileList = [];
          console.log('[FileUpload] fileList cleared');
        }

        // 强制更新
        this.$forceUpdate();
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // 组件创建时获取系统参数中的文件后缀限制
    this.fetchFileExtensions();

    console.log('[FileUpload] Created with initialFileList:', this.initialFileList);

    // 初始化文件列表
    if (this.initialFileList && this.initialFileList.length > 0) {
      // 已在watch中处理
    }
  },
  computed: {
    acceptText() {
      if (this.accept === '*') {
        return '所有格式';
      }
      return this.accept.replace(/\./g, '').toUpperCase();
    }
  },
  methods: {
    // 获取允许的文件后缀和文件大小限制
    async fetchFileExtensions() {
      try {
        const response = await sysParamGet();
        if (response.success && response.data) {
          // 获取允许的文件后缀
          if (response.data.fileAllowExt) {
            this.accept = response.data.fileAllowExt;
          }

          // 获取文件大小限制
          if (response.data.fileMaxSize) {
            this.maxSize = response.data.fileMaxSize;
          }
        }
      } catch (error) {
        console.error('获取文件上传参数失败:', error);
      }
    },

    // 处理手动上传（覆盖el-upload的默认上传请求）
    handleManualUpload(options) {
      const file = options.file;
      this.handleFile(file);
      return {
        abort: () => {
          console.log('Upload aborted');
        }
      };
    },

    // 文件选择变化处理
    handleFileChange(file) {
      // 如果已有文件，新文件会替换旧文件
      if (this.fileList.length > 0 && this.attachmentId) {
        // 清除旧的附件ID，因为是替换操作
        this.attachmentId = null;
      }

      // 只取最新的文件
      if (file.raw) {
        this.handleFile(file.raw);
      }
    },

    // 处理文件
    handleFile(file) {
      // 文件大小验证
      if (file.size > this.maxSize * 1024 * 1024) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB`);
        return;
      }

      // 文件类型验证
      if (this.accept !== '*' && !this.isValidFileType(file)) {
        this.$message.error(`文件格式不支持，仅支持: ${this.acceptText}`);
        return;
      }

      this.uploadedFile = file;
      // 更新文件列表
      this.fileList = [{
        name: file.name,
        size: file.size,
        raw: file
      }];

      if (this.autoUpload) {
        this.uploadFile();
      } else {
        // 触发文件选择事件，但不自动上传
        this.$emit('file-selected', file);
      }
    },

    // 文件移除前确认
    handleBeforeRemove(file, fileList) {
      // 总是允许移除
      return true;
    },

    // 文件类型验证
    isValidFileType(file) {
      const acceptTypes = this.accept.split(',');
      return acceptTypes.some(type => {
        if (type.includes('*')) {
          // 处理通配符，如 image/*
          const mainType = type.split('/')[0];
          return file.type.startsWith(mainType);
        }
        return file.type === type || file.name.toLowerCase().endsWith(type);
      });
    },

    // 上传文件
    async uploadFile() {
      if (!this.uploadedFile) return;

      this.uploading = true;
      this.uploadProgress = 0;

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        if (this.uploadProgress < 90) {
          this.uploadProgress += Math.random() * 10;
        }
      }, 200);

      try {
        const response = await sysAttachmentUpload(this.uploadedFile);

        clearInterval(progressInterval);
        this.uploadProgress = 100;

        if (response.success) {
          this.attachmentId = response.data;
          this.$message.success('文件上传成功');

          // 更新文件列表中的文件ID
          this.fileList = [{
            name: this.uploadedFile.name,
            size: this.uploadedFile.size,
            id: this.attachmentId
          }];

          // 触发上传成功事件，返回文件ID和文件名
          this.$emit('upload-success', {
            id: this.attachmentId,
            file: this.uploadedFile,
            response: response,
            name: this.uploadedFile.name,    // 添加文件原始名称
            originalName: this.uploadedFile.name  // 保存一份原始名称
          });
        } else {
          throw new Error(response.errMsg || '上传失败');
        }
      } catch (error) {
        clearInterval(progressInterval);
        console.error('上传失败:', error);
        this.$message.error(error.message || '上传失败，请重试');
        this.uploadedFile = null;
        this.attachmentId = null;
        this.fileList = [];

        // 触发上传失败事件
        this.$emit('upload-error', error);
      } finally {
        this.uploading = false;
        setTimeout(() => {
          this.uploadProgress = 0;
        }, 1000);
      }
    },

    // 替换文件
    replaceFile() {
      this.$refs.upload.handleClick();
    },

    // 重置组件状态
    reset() {
      console.log('[FileUpload] Resetting component');

      // 重置所有状态
      this.uploadedFile = null;
      this.uploading = false;
      this.uploadProgress = 0;
      this.attachmentId = null;
      this.fileList = [];

      // 触发文件删除事件
      this.$emit('file-removed');

      // 强制更新视图
      this.$forceUpdate();

      // 重置el-upload组件
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles();
      }
    },

    // 删除文件
    removeFile(file, fileList) {
      console.log('[FileUpload] File removed:', file);
      // 重置文件相关状态
      this.uploadedFile = null;
      this.attachmentId = null;
      this.uploadProgress = 0;
      this.fileList = [];

      // 触发文件删除事件
      this.$emit('file-removed');

      // 强制更新视图
      this.$forceUpdate();
    },

    // 手动触发上传（用于非自动上传模式）
    manualUpload() {
      if (this.uploadedFile && !this.uploading) {
        this.uploadFile();
      }
    },

    // 获取当前文件ID
    getAttachmentId() {
      return this.attachmentId;
    },

    // 处理文件数超过限制时的处理
    handleExceed(files, fileList) {
      // 自动替换文件，而不是显示错误
      this.$refs.upload.handleRemove(fileList[0]);
      this.$refs.upload.handleStart(files[0]);

      // 清除旧的附件ID
      this.attachmentId = null;
    }
  }
};
</script>

<style scoped>
.file-upload-component {
  width: 100%;
}

.upload-progress {
  margin-top: 12px;
  width: 100%;
}

.el-upload__tip {
  line-height: 1.2;
  padding-top: 5px;
}
</style>
