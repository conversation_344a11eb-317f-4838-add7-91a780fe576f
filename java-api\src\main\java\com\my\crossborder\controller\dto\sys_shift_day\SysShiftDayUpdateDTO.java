package com.my.crossborder.controller.dto.sys_shift_day;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_排班日期表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftDayUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排班日期（主键）
     */
	@NotNull(message="shiftDay不能为空")
    private LocalDate shiftDay;

    /**
     * 当日应排店铺总数（固化值，避免店铺变化影响历史数据）
     */
	@NotNull(message="shopCount不能为空")
    private Integer shopCount;

    /**
     * 创建人ID（关联用户表）
     */
	@NotNull(message="createUserId不能为空")
    private Integer createUserId;

    /**
     * 创建时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
	@NotNull(message="updateTime不能为空")
    private LocalDateTime updateTime;

}
