package com.my.crossborder.mybatis.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 重新采购
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ord_repurchase")
public class OrdRepurchase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 问题描述
     */
    private String issue;

    /**
     * 处理办法 字典参数repurchase_close_way
     */
    private String closeWay;

    /**
     * 处理状态 字典close_status
     */
    private String closeStatus;

    /**
     * 录入人id
     */
    private Integer issueUserId;

    /**
     * 录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理人id
     */
    private Integer closeUserId;

    /**
     * 处理时间
     */
    private LocalDateTime closeTime;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;


}
