<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysLog">
        <id column="log_id" property="logId" />
        <result column="module_name" property="moduleName" />
        <result column="menu_name" property="menuName" />
        <result column="operation_name" property="operationName" />
        <result column="operation_detail" property="operationDetail" />
        <result column="success" property="success" />
        <result column="err_msg" property="errMsg" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        log_id, module_name, menu_name, operation_name, operation_detail, success, err_msg, create_user_id, create_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_log.SysLogPageVO">
		SELECT
			log_id, module_name, menu_name, operation_name, operation_detail, success, err_msg, create_user_id, create_time
		FROM
			sys_log AS t1
		<where>
        	1=1
	        <if test="logId != null and logId != ''">
	           	AND t1.log_id = #{logId}
            </if>
	        <if test="moduleName != null and moduleName != ''">
	           	AND t1.module_name like concat('%', #{moduleName}, '%')
            </if>
	        <if test="menuName != null and menuName != ''">
	           	AND t1.menu_name like concat('%', #{menuName}, '%')
            </if>
	        <if test="operationName != null and operationName != ''">
	           	AND t1.operation_name like concat('%', #{operationName}, '%')
            </if>
	        <if test="operationDetail != null and operationDetail != ''">
	           	AND t1.operation_detail like concat('%', #{operationDetail}, '%')
            </if>
	        <if test="createUserId != null and createUserId != ''">
	           	AND t1.create_user_id = #{createUserId}
            </if>
	        <if test="success != null">
	           	AND t1.success = #{success}
            </if>
	        <if test="createTimeStart != null">
	           	AND t1.create_time &gt;= #{createTimeStart}
            </if>
	        <if test="createTimeEnd != null">
	           	AND t1.create_time &lt;= #{createTimeEnd}
            </if>
        </where>
    </select>

</mapper>
