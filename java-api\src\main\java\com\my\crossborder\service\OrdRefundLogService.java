package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.ord_refund_log.OrdRefundLogInsertDTO;
import com.my.crossborder.controller.vo.ord_refund_log.OrdRefundLogPageVO;
import com.my.crossborder.mybatis.entity.OrdRefundLog;
import java.util.List;

/**
 * 退款日志表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface OrdRefundLogService extends IService<OrdRefundLog> {

	/**
	 * 新增
	 */
	void insert(OrdRefundLogInsertDTO insertDTO);

	/**
	 * 根据订单号查询退款日志
	 */
	List<OrdRefundLogPageVO> listByOrderSn(String orderSn);

}
