package com.my.crossborder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考勤状态枚举
 * 用于表示订单在电商平台中的各种处理状态
 * 
 * <AUTHOR>
 * @date 2025
 */
@Getter
@AllArgsConstructor
public enum ClockStatusEnum {
    
    /** 待打卡 */
    PENDING_CHECK(0, "待打卡"),
    
    /** 已打卡 */
    CHECKED(1, "已打卡"),
    
    /** 缺勤 */
    ABSENT(2, "缺勤"),

    ;
    
    
    /** 状态代码 */
    private final Integer code;
    
    /** 状态描述 */
    private final String description;
    
    /**
     * 根据代码获取枚举
     * @param code 状态代码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static ClockStatusEnum getByCode(int code) {
        for (ClockStatusEnum status : values()) {
            if (status.getCode().intValue() == code) {
                return status;
            }
        }
        return null;
    }
} 