package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationDeleteDTO;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationPageDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.wkb_notification.WkbNotificationDetailVO;
import com.my.crossborder.controller.vo.wkb_notification.WkbNotificationPageVO;
import com.my.crossborder.service.WkbNotificationService;

import lombok.RequiredArgsConstructor;

/**
 * 我的通知公告
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/api/wkb-notification")
@RequiredArgsConstructor
public class WkbNotificationController {

    private final WkbNotificationService wkbNotificationService;
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<WkbNotificationDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.wkbNotificationService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<WkbNotificationPageVO>> page(WkbNotificationPageDTO pageDTO) {
        Page<WkbNotificationPageVO> page = this.wkbNotificationService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 删除我的公告
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody WkbNotificationDeleteDTO deleteDTO) {
    	this.wkbNotificationService.delete(deleteDTO);
		return StdResp.success();
    }
    
}