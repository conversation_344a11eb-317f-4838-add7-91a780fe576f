package com.my.crossborder.auth;
import java.util.List;

import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.service.SysMenuRefRoleService;
import com.my.crossborder.service.SysUserService;

import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;

/**
 * 自定义权限加载接口实现类
 */
@Component    
@RequiredArgsConstructor
public class StpInterfaceImpl implements StpInterface {
	
	private final SysUserService sysUserService;
	private final SysMenuRefRoleService sysMenuRefRoleService;
	

    /**
     * 返回一个账号所拥有的权限码集合 
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        return this.sysMenuRefRoleService.getPermissionList();
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
    	SysUser user = this.sysUserService.getById(StpUtil.getLoginIdAsInt());
    	Integer roleId = user.getRoleId();
        return Lists.newArrayList(roleId.toString());
    }

}
