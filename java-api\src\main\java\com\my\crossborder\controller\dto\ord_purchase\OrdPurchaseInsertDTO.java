package com.my.crossborder.controller.dto.ord_purchase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_采购订单主表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单号
     */
    @NotBlank(message="orderSn不能为空")
    private String orderSn;

    /**
     * 订单入账金额
     */
	@NotNull(message="incomeAmount不能为空")
    private BigDecimal incomeAmount;

    /**
     * 预计重量
     */
	@NotNull(message="expectWeight不能为空")
    private BigDecimal expectWeight;



}
