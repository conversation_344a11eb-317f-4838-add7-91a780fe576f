<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.WkbTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.WkbTag">
        <id column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="scene" property="scene" />
        <result column="tag" property="tag" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_sn, scene, tag, create_user_id, create_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.wkb_tag.WkbTagPageVO">
		SELECT
			id, order_sn, scene, tag, create_user_id, create_time
		FROM
			wkb_tag AS t1
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="orderSn != null and orderSn != ''">
	           	AND t1.order_sn = #{orderSn}
            </if>
	        <if test="scene != null and scene != ''">
	           	AND t1.scene = #{scene}
            </if>
	        <if test="tag != null and tag != ''">
	           	AND t1.tag = #{tag}
            </if>
	        <if test="createUserId != null and createUserId != ''">
	           	AND t1.create_user_id = #{createUserId}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
        </where>
    </select>

</mapper>
