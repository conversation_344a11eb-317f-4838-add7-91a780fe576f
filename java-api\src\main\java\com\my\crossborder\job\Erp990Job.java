package com.my.crossborder.job;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.my.crossborder.controller.vo.sys_param.SysParamVO;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.forest.erp990.Erp990Client;
import com.my.crossborder.forest.erp990.context.Erp990Context;
import com.my.crossborder.forest.erp990.onerror.Erp990OnError;
import com.my.crossborder.forest.erp990.vo.ShopPageVO;
import com.my.crossborder.mybatis.entity.SysErpAccount;
import com.my.crossborder.mybatis.entity.SysShop;
import com.my.crossborder.service.SysErpAccountService;
import com.my.crossborder.service.SysErpClientLogService;
import com.my.crossborder.service.SysParamService;
import com.my.crossborder.service.SysShopService;
import com.my.crossborder.service.order.ErpOrderSyncService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 获取禾晨物流接口数据的调度
 * <AUTHOR>
 *
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class Erp990Job {

	private final SysErpAccountService sysErpAccountService;
	private final SysShopService sysShopService;
	private final SysErpClientLogService sysErpClientLogService;
	private final Erp990Client erp990Client;
	private final SysParamService sysParamService;
	private final ErpOrderSyncService erpOrderSyncService;
	
	@Value("${com.my.crossborder.job.Erp990Job.clientLogStoreDays}")
	private Integer clientLogStoreDays;
	
	
	/**
	 * 获取店铺信息
	 */
	@Scheduled(cron = "${com.my.crossborder.job.Erp990Job.fetchShop}")
	public void fetchShop() {
		List<SysErpAccount> accountList = this.sysErpAccountService.list();
		accountList.stream().forEach(t -> {
			String username = t.getUsername();
			String password = t.getPassword();
			Erp990Context.set(username, password);
			Erp990OnError onError = new Erp990OnError();
			Integer pageSize = 10000;  
			Integer pageNum = 1; 
			ShopPageVO shopPageVO = this.erp990Client.shopPage(pageNum, pageSize, onError);
			BusinessException.when(onError.isError(), onError.getErrMsg());
			if (!shopPageVO.isSuccess()) {
				return;
			}
			List<SysShop> shopList = shopPageVO.getRows().stream()
						.map(row -> SysShop.builder()
						.id(row.getShopId())
						.shopName(row.getShopName())
						.build())
						.collect(Collectors.toList());
			this.sysShopService.saveIfAbsense(shopList);
			Erp990Context.remove();
		});
	}
	
	/**
	 * 获取订单信息
	 */
	@Scheduled(cron = "${com.my.crossborder.job.Erp990Job.fetchOrder}") 
	public void fetchOrder() {
		List<SysErpAccount> accountList = this.sysErpAccountService.list();
		accountList.stream().forEach(t -> {
			String username = t.getUsername();
			String password = t.getPassword();
			Erp990Context.set(username, password);
			
			try {
				SysParamVO sysParam = this.sysParamService.get();
				Integer orderSyncDays = Integer.parseInt(sysParam.getOrderSyncDays());
	            log.info("[{}]开始定时同步订单数据 ({}天的订单)", username, orderSyncDays);
	            this.erpOrderSyncService.syncRecentOrders(orderSyncDays); 
	            log.info("[{}]定时同步订单数据完成", username);
	        } catch (Exception e) {
	            log.error("[{}]定时同步订单数据失败", username, e);
	        } finally {
	        	Erp990Context.remove();
	        }
			
			Erp990Context.remove();
		});
	}
	
	/**
	 * 获取打包信息
	 */
	@Scheduled(cron = "${com.my.crossborder.job.Erp990Job.fetchPackageRecord}") 
	public void fetchPackageRecord() {
		List<SysErpAccount> accountList = this.sysErpAccountService.list();
		accountList.stream().forEach(t -> {
			String username = t.getUsername();
			String password = t.getPassword();
			Erp990Context.set(username, password);
			
			try {
	            log.info("[{}]开始定时同步打包信息", username);
	            this.erpOrderSyncService.syncPackageRecord(); 
	            log.info("[{}]定时同步打包信息完成", username);
	        } catch (Exception e) {
	            log.error("[{}]定时同步打包信息失败", username, e);
	        } finally {
	        	Erp990Context.remove();
	        }
			
			Erp990Context.remove();
		});
	}
	
	/**
	 * 清理Erp990接口日志
	 */
	@Scheduled(cron = "${com.my.crossborder.job.Erp990Job.clearClintLog}")
	public void clearClintLog() {
		this.sysErpClientLogService.deleteDaysAgo(this.clientLogStoreDays);
	}
	
}
