<template>
  <el-dialog
    title="退款结算"
    :visible.sync="visible"
    width="500px"
    :before-close="handleClose"
    @close="handleClose">
    
    <div class="settlement-form">
      <div class="form-row">
        <label class="form-label">结算周期：</label>
        <span class="form-value">第{{ refundData.week }}周 ({{ refundData.dateRange }})</span>
      </div>
      
      <div class="form-row">
        <label class="form-label">退款金额：</label>
        <span class="form-value">¥ {{ refundData.amount ? refundData.amount.toFixed(2) : '0.00' }}</span>
      </div>
      
      <div class="form-row">
        <label class="form-label">结算金额：</label>
        <el-input
          v-model="form.settlementAmount"
          placeholder="请输入结算金额"
          type="number"
          step="0.01"
          style="width: 200px;">
          <template slot="prepend">¥</template>
        </el-input>
        <span class="required-mark">*</span>
      </div>
      
      <div class="form-row">
        <label class="form-label">当前状态：</label>
        <el-tag type="warning">待结算</el-tag>
      </div>
      
      <div class="form-row">
        <label class="form-label">备注：</label>
        <span class="required-mark">*</span>
        <div style="flex: 1; margin-left: 8px;">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入结算备注（必填）"
            maxlength="200"
            show-word-limit>
          </el-input>

          <!-- 常用备注标签 -->
          <div class="remark-tags" v-if="remarkOptions.length > 0">
            <div class="tags-label">常用备注：</div>
            <div class="tags-container">
              <el-tag
                v-for="option in remarkOptions"
                :key="option.value"
                class="remark-tag"
                @click="selectRemark(option.label)"
                :type="form.remark === option.label ? 'primary' : ''"
                effect="plain">
                {{ option.label }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button v-permission="['stl-refund:insert']" type="primary" @click="handleSubmit" :loading="submitting">确认已结算</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { insertStlRefund } from '../../api/StlRefund'
import { dictCategoryItems } from '@/api/SysDictItem'

export default {
  name: 'StlRefundEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    refundData: {
      type: Object,
      default: () => ({
        week: 1,
        dateRange: '',
        amount: 0,
        userId: null,
        year: null,
        month: null
      })
    }
  },
  data() {
    return {
      form: {
        settlementAmount: '',
        remark: ''
      },
      remarkOptions: [], // 备注选项
      submitting: false
    }
  },
  created() {
    // 加载备注字典项
    this.loadRemarkOptions()
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
        // 默认结算金额等于退款金额
        this.form.settlementAmount = this.refundData.amount || ''
      }
    }
  },
  methods: {
    // 加载备注选项
    async loadRemarkOptions() {
      try {
        const res = await dictCategoryItems({ categoryId: 'SETTLEMENT_REFUND_REMARK' })
        if (res.success && res.data) {
          this.remarkOptions = res.data
        }
      } catch (error) {
        console.error('加载备注选项失败:', error)
      }
    },

    // 选择备注
    selectRemark(remarkText) {
      this.form.remark = remarkText
    },

    resetForm() {
      this.form = {
        settlementAmount: '',
        remark: ''
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 根据年月周计算该周的开始和结束日期
    calculateWeekDateRange(year, month, week) {
      // 获取月份第一天
      const firstDay = new Date(year, month - 1, 1)

      // 计算第一周的开始日期（周一）
      let firstMondayDate = new Date(firstDay)
      const firstDayOfWeek = firstDay.getDay() // 0是周日，1是周一

      if (firstDayOfWeek === 0) {
        // 如果第一天是周日，则往前推6天到周一
        firstMondayDate.setDate(firstDay.getDate() - 6)
      } else if (firstDayOfWeek !== 1) {
        // 如果第一天不是周一，则往前推到周一
        firstMondayDate.setDate(firstDay.getDate() - (firstDayOfWeek - 1))
      }

      // 计算指定周的开始日期
      const weekStartDate = new Date(firstMondayDate)
      weekStartDate.setDate(firstMondayDate.getDate() + (week - 1) * 7)

      // 计算指定周的结束日期（周日）
      const weekEndDate = new Date(weekStartDate)
      weekEndDate.setDate(weekStartDate.getDate() + 6)

      // 格式化为YYYY-MM-DD格式
      const formatDate = (date) => {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }

      return {
        startDate: formatDate(weekStartDate),
        endDate: formatDate(weekEndDate)
      }
    },
    
    async handleSubmit() {
      // 表单验证
      if (!this.form.settlementAmount || this.form.settlementAmount <= 0) {
        this.$message.error('请输入有效的结算金额')
        return
      }

      if (!this.form.remark || this.form.remark.trim() === '') {
        this.$message.error('请输入结算备注')
        return
      }

      // 确认对话框
      try {
        await this.$confirm(
          `确认结算金额为 ¥${parseFloat(this.form.settlementAmount).toFixed(2)} 吗？结算后将无法修改。`,
          '确认结算',
          {
            confirmButtonText: '确认结算',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      } catch (error) {
        // 用户取消确认
        return
      }

      this.submitting = true

      try {
        // 计算该周的开始和结束日期
        const dateRange = this.calculateWeekDateRange(
          this.refundData.year,
          this.refundData.month,
          this.refundData.week
        )

        const params = {
          refundUserId: this.refundData.userId,
          refundDateStart: dateRange.startDate,
          refundDateEnd: dateRange.endDate,
          settlementAmount: parseFloat(this.form.settlementAmount),
          remark: this.form.remark.trim()
        }

        const res = await insertStlRefund(params)

        if (res.success) {
          this.$message.success('结算记录保存成功')
          this.$emit('success', params)
          this.handleClose()
        } else {
          this.$message.error(res.message || '保存失败')
        }
      } catch (error) {
        console.error('保存结算记录失败:', error)
        this.$message.error('保存失败，请重试')
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.settlement-form {
  padding: 10px 0;
}

.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  font-size: 14px;
}

.form-label {
  width: 100px;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 32px;
}

.form-value {
  color: #333;
  font-weight: 500;
  line-height: 32px;
}

.required-mark {
  color: #F56C6C;
  margin-left: 4px;
  line-height: 32px;
}

.remark-tags {
  margin-top: 10px;
}

.tags-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.remark-tag {
  cursor: pointer;
  font-size: 12px;
}

.remark-tag:hover {
  opacity: 0.8;
}
</style>
