package com.my.crossborder.controller.vo.ord_purchase_item;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_采购订单明细表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseItemPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单明细id
     */
    private String orderItemId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 产品图片（来自erp_order_item.item_image）
     */
    private String itemImage;

    /**
     * 产品名称（来自erp_order_item.item_name）
     */
    private String itemName;

    /**
     * 规格（来自erp_order_item.item_model_name）
     */
    private String itemModelName;

    /**
     * 数量（来自erp_order_item.amount）
     */
    private Integer amount;

    /**
     * 采购途径 字典PURCHASE_CHANNEL
     */
    private String purchaseChannel;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 快递单号（来自erp_order_item相关的快递信息）
     */
    private String trackingNumber;

    /**
     * 采购人id
     */
    private Integer purchaseUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
