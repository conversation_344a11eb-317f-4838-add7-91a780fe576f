package com.my.crossborder.controller.vo.sys_dict_category;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_数据字典-类别表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictCategoryDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类别id
     */
    private String categoryId;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 描述
     */
    private String description;

}
