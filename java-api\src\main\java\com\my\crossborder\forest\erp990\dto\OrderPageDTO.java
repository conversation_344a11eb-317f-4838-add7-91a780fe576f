package com.my.crossborder.forest.erp990.dto;

import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * 订单分页查询请求参数DTO
 * 用于跨境电商ERP系统的订单查询接口
 * 
 * <AUTHOR>
 * @date 2024
 */
@NoArgsConstructor @AllArgsConstructor @Builder @Data 
public class OrderPageDTO {

    /** 页码，从1开始 */
    @JSONField(name = "pageNum", ordinal = 1)
    private Integer pageNum;

    /** 每页显示数量 */
    @JSONField(name = "pageSize", ordinal = 2)
    private Integer pageSize;

    /** 隔离标记，用于数据隔离（通常为0） */
    @JSONField(name = "isolateFlag", ordinal = 3)
    private Integer isolateFlag;

    /** 订单开始时间，格式：yyyy-MM-dd HH:mm:ss */
    @JSONField(name = "orderStartTime", ordinal = 4)
    private String orderStartTime;

    /** 订单结束时间，格式：yyyy-MM-dd HH:mm:ss */
    @JSONField(name = "orderEndTime", ordinal = 5)
    private String orderEndTime;

    /** 入库状态标记
     * 99: 无需处理
     * 100: 未入库
     * 101: 部分入库
     * 102: 完全入库
     */
    @JSONField(name = "putInFalg", ordinal = 6)
    private Integer putInFalg;

    /** 出库状态标记
     * 0: 未出库
     * 1: 已出库
     * 99: 无需出库
     */
    @JSONField(name = "realOutFlag", ordinal = 7)
    private Integer realOutFlag;

    /** 订单状态代码
     * 001: 待出货
     * 002: 已出货
     * 003: 不成立
     * 004: 不成立-申请取消订单
     * 005: 已完成
     * 006: 退货退款
     * 007: 尚未付款
     * 999: 未知
     */
    @JSONField(name = "orderStates", ordinal = 8)
    private String orderStates;

} 