package com.my.crossborder.controller.vo.sys_dict_item;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_数据字典-字典项表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictItemPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类别id (联合主键)
     */
    private String categoryId;

    /**
     * 字典项值 (联合主键)
     */
    private String itemValue;

    /**
     * 字典项名称
     */
    private String itemName;

    /**
     * 排序编号
     */
    private Integer sortNum;
	
	/**
	 * 颜色
	 */
	private String color;
	
}
