package com.my.crossborder.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 订单项_快递信息
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("erp_order_item_express")
public class ErpOrderItemExpress implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键(order_item_id + express_no)
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;
    
    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 快递入仓状态 (400:已入仓)
     */
    @TableField("expressIn_flag")
    private String expressinFlag;

    /**
     * 入仓时间
     */
    private LocalDateTime putInTime;

    /**
     * 数据创建时间
     */
    private LocalDateTime putCreateTime;

    /**
     * 订单项id
     */
    private String orderItemId;
    
    /**
     * 订单id
     */
    private String orderId;


}
