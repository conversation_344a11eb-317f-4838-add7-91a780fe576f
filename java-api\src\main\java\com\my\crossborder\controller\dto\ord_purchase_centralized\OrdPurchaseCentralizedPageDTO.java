package com.my.crossborder.controller.dto.ord_purchase_centralized;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedPageVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 分页_集中采购
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseCentralizedPageDTO 
						extends PageDTO<OrdPurchaseCentralizedPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 采购单号
     */
    private String purchaseNumber;

    /**
     * 品名
     */
    private String productName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 采购人id
     */
    private Integer purchaseUserId;

    /**
     * 采购人id列表（多选）
     */
    private List<Integer> purchaseUserIdList;

    /**
     * 订单号列表（多选查询）
     */
    private List<String> orderSnList;

    /**
     * 采购日期开始
     */
    private LocalDate purchaseDateStart;

    /**
     * 采购日期结束
     */
    private LocalDate purchaseDateEnd;

    /**
     * 采购状态
     */
    private String purchaseStatus;

    /**
     * 采购途径
     */
    private String purchaseChannel;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 店铺ID列表（用于权限过滤）
     */
    private List<Integer> shopIds;

}
