package com.my.crossborder.controller.dto.sys_dict_category;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_数据字典-类别表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictCategoryPageDTO 
						extends PageDTO<SysDictCategoryPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类别id
     */
    private String categoryId;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 描述
     */
    private String description;

}
