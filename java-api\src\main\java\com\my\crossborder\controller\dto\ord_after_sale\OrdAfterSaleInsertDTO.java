package com.my.crossborder.controller.dto.ord_after_sale;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_售后表
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdAfterSaleInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单编号
     */
	@NotNull(message="orderSn不能为空")
    private String orderSn;

    /**
     * 问题类别
     */
	@NotNull(message="issueType不能为空")
    private String issueType;

    /**
     * 处理办法 字典参数aftersale_close_way
     */
	@NotNull(message="closeWay不能为空")
    private String closeWay;

    /**
     * 是否已处理
     */
    private String closeStatus;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 新订单编号
     */
    private String newOrderSn;

    /**
     * 问题录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理完成时间
     */
    private LocalDateTime closeTime;

    /**
     * 录入人id
     */
    private Integer issueUserId;

    /**
     * 处理人id
     */
    private Integer closeUserId;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

}
