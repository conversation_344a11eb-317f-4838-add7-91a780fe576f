import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun.js'

/**
 * 排班管理API
 */

// 新增排班
export function insert(data) {
  return reqPost('/sys-shift', data)
}

// 修改排班
export function update(data) {
  return reqPut('/sys-shift', data)
}

// 查询排班详情
export function detail(id) {
  return reqGet(`/sys-shift/${id}`)
}

// 分页查询排班
export function page(params) {
  return reqGet('/sys-shift/page', params)
}

// 删除排班
export function deleteShift(data) {
  return reqDelete('/sys-shift', data)
}

// 月度排班
export function monthShift(params) {
  return reqGet('/sys-shift/month-shift', params)
}

// 快速复制排班
export function quickCopy(data) {
  return reqPost('/sys-shift/quick-copy', data)
}
