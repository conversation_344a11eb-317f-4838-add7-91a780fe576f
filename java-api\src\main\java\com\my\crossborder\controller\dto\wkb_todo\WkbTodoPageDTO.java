package com.my.crossborder.controller.dto.wkb_todo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.wkb_todo.WkbTodoPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_待办表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbTodoPageDTO 
						extends PageDTO<WkbTodoPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 待办id
     */
    private Integer id;

    /**
     * 类型
     */
    private String type;

    /**
     * 待办的接收用户id
     */
    private Integer receiveUserId;

    /**
     * 是否已读
     */
    private Boolean read;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布用户id
     */
    private Integer publishUserId;

    /**
     * 构造函数
     * @param current
     * @param size
     */
    public WkbTodoPageDTO(long current, long size) {
    	super(current, size);
    }
}
