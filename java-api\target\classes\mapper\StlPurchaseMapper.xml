<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.StlPurchaseMapper">

    <!-- 获取用户月度采购数据列表 -->
    <select id="monthlyPurchaseData" resultType="com.my.crossborder.controller.vo.stl_purchase.MonthlyPurchaseDataVO">
        SELECT
				purchase_data.purchase_date as purchaseDate,
				purchase_data.purchase_amount as purchaseAmount,
				CASE WHEN stl.settlement_date IS NOT NULL THEN true ELSE false END as settlementFlag,
				stl.settlement_amount as settlementAmount,
				stl.settlement_date as settlementDate,
				stl.remark as remark
		FROM (
			  SELECT 
				  purchase_date,
				  SUM(purchase_amount) AS purchase_amount,
					purchase_user_id
				FROM 
				(
					SELECT
							purchase_date,
							SUM(total_amount) as purchase_amount,
							purchase_user_id
					FROM ord_purchase_centralized 
					WHERE purchase_user_id = #{userId}
					    AND purchase_channel in (2,3)
						AND YEAR(purchase_date) = #{year}
						AND MONTH(purchase_date) = #{month}
					GROUP BY purchase_user_id, purchase_date
		
					UNION ALL
		
					SELECT
							purchase_date,
							SUM(purchase_amount) as purchase_amount,
							purchase_user_id
					FROM ord_purchase_item
					WHERE purchase_user_id = #{userId}
						AND purchase_channel in (2,3)
						AND YEAR(purchase_date) = #{year}
						AND MONTH(purchase_date) = #{month}
					GROUP BY purchase_date, purchase_user_id
				) tUnionTable
				GROUP BY purchase_date, purchase_user_id
		) purchase_data
		LEFT JOIN stl_purchase stl ON stl.purchase_user_id = purchase_data.purchase_user_id AND stl.purchase_date = purchase_data.purchase_date
		ORDER BY purchase_data.purchase_date DESC
    </select>

    <!-- 获取用户指定日期的采购详情 -->
    <select id="dailyPurchaseDetails" resultType="com.my.crossborder.controller.vo.stl_purchase.DailyPurchaseDetail$Purchase">
		SELECT
			tOrder.shop_name,
			tOrder.order_sn, 
			t1.item_name AS itemName,
			t1.item_image AS itemImage,
			t1.item_price AS itemPrice,
			t1.amount,
			t1.item_model_name AS itemModelName,
			t2.express_no AS expressNo,
			tPurchaseItem.purchase_channel AS purchaseChannel,
			tPurchaseItem.purchase_amount AS purchaseAmount
		FROM
			ord_purchase_item tPurchaseItem
			LEFT JOIN erp_order_item t1 ON tPurchaseItem.order_item_id = t1.id
			LEFT JOIN erp_order tOrder ON t1.order_id = tOrder.order_id
			LEFT JOIN erp_order_item_express t2 ON t2.order_item_id = t1.id
			LEFT JOIN stl_purchase tSet ON tPurchaseItem.purchase_date = tSet.purchase_date and tSet.purchase_user_id = tPurchaseItem.purchase_user_id
		WHERE
			tPurchaseItem.purchase_date = #{purchaseDate}
			AND tPurchaseItem.purchase_user_id = #{userId}
			AND tPurchaseItem.purchase_channel IN (2, 3)
		ORDER BY t1.item_id
    </select>

</mapper>
