<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>订单状态汇总</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="店铺">
          <el-select
            v-model="searchForm.shopIds"
            placeholder="店铺"
            clearable
            multiple
            style="width: 200px"
          >
            <el-option
              v-for="shop in shopList"
              :key="shop.id"
              :label="shop.shopName"
              :value="shop.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderSn"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter.native="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column
          label="店铺名称"
          width="120"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.shopName }}
          </template>
        </el-table-column>
        <el-table-column label="订单号" width="160" align="center">
          <template slot-scope="scope">
            <span class="clickable-order-sn" @click="copyOrderSn(scope.row.orderSn)">
              {{ scope.row.orderSn }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="采购登记" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.purchaseRegistration === '√'"
              class="clickable-status"
              @click="navigateToPage('purchaseRegistration', scope.row.orderSn)"
            >
              {{ scope.row.purchaseRegistration }}
            </span>
            <span v-else>{{ scope.row.purchaseRegistration }}</span>
          </template>
        </el-table-column>
        <el-table-column label="集中采购登记" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.centralizedPurchase === '√'"
              class="clickable-status"
              @click="navigateToPage('centralizedPurchase', scope.row.orderSn)"
            >
              {{ scope.row.centralizedPurchase }}
            </span>
            <span v-else>{{ scope.row.centralizedPurchase }}</span>
          </template>
        </el-table-column>
        <el-table-column label="未完整填写物流编号" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.incompleteLogistics === '√'"
              class="clickable-status"
              @click="navigateToPage('incompleteLogistics', scope.row.orderSn)"
            >
              {{ scope.row.incompleteLogistics }}
            </span>
            <span v-else>{{ scope.row.incompleteLogistics }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已填物流编号未入库" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.logisticsDoneNotWarehoused === '√'"
              class="clickable-status"
              @click="navigateToPage('logisticsDoneNotWarehoused', scope.row.orderSn)"
            >
              {{ scope.row.logisticsDoneNotWarehoused }}
            </span>
            <span v-else>{{ scope.row.logisticsDoneNotWarehoused }}</span>
          </template>
        </el-table-column>
        <el-table-column label="货物已入库未出库" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.warehousedNotOutbound === '√'"
              class="clickable-status"
              @click="navigateToPage('warehousedNotOutbound', scope.row.orderSn)"
            >
              {{ scope.row.warehousedNotOutbound }}
            </span>
            <span v-else>{{ scope.row.warehousedNotOutbound }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已采购但出库前取消" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.purchaseDoneOrderCancelled === '√'"
              class="clickable-status"
              @click="navigateToPage('purchaseDoneOrderCancelled', scope.row.orderSn)"
            >
              {{ scope.row.purchaseDoneOrderCancelled }}
            </span>
            <span v-else>{{ scope.row.purchaseDoneOrderCancelled }}</span>
          </template>
        </el-table-column>

        <el-table-column label="货物已入库重新采购" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.warehousedRepurchase === '√'"
              class="clickable-status"
              @click="navigateToPage('warehousedRepurchase', scope.row.orderSn)"
            >
              {{ scope.row.warehousedRepurchase }}
            </span>
            <span v-else>{{ scope.row.warehousedRepurchase }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已出库售后" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.outboundAfterSale === '√'"
              class="clickable-status"
              @click="navigateToPage('outboundAfterSale', scope.row.orderSn)"
            >
              {{ scope.row.outboundAfterSale }}
            </span>
            <span v-else>{{ scope.row.outboundAfterSale }}</span>
          </template>
        </el-table-column>
        <el-table-column label="物流理赔" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.logisticsClaim === '√'"
              class="clickable-status"
              @click="navigateToPage('logisticsClaim', scope.row.orderSn)"
            >
              {{ scope.row.logisticsClaim }}
            </span>
            <span v-else>{{ scope.row.logisticsClaim }}</span>
          </template>
        </el-table-column>
        <el-table-column label="台湾上架物品" min-width="100" align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.taiwanListing === '√'"
              class="clickable-status"
              @click="navigateToPage('taiwanListing', scope.row.orderSn)"
            >
              {{ scope.row.taiwanListing }}
            </span>
            <span v-else>{{ scope.row.taiwanListing }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="openNotesDrawer(scope.row)"
              icon="el-icon-tickets"
              >备注</el-button
            >
          </template>
        </el-table-column>

        <template slot="empty">
          <div class="empty-table-placeholder">
            <i
              class="el-icon-warning-outline"
              style="font-size: 32px; color: #c0c4cc; margin-bottom: 10px"
            ></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-bind:child-msg="pageParam"
        @callback_getPageData="callback_getPageData"
      ></Pagination>
    </div>

    <!-- 备注抽屉 -->
    <el-drawer
      title="订单备注"
      :visible.sync="notesDrawerVisible"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose"
    >
      <div style="padding: 20px">
        <OrderNotesDrawer
          :notes="currentOrderNotes"
          :orderSn="currentOrderSn"
          :currentScene="currentScene"
          :showDebug="false"
          @note-updated="handleNoteUpdated"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getOrderStatusSummaryPage } from "../../api/ErpOrderStatusSummary";
import { getAllEnabledShops } from "../../api/SysShop";
import Pagination from "../../components/Pagination";
import noteDrawerMixin from "../../mixins/noteDrawerMixin";
import copyMixin from "../../mixins/copyMixin";
import OrderNotesDrawer from "./OrderNotesDrawer";

export default {
  name: "OrderStatusSummary",
  mixins: [noteDrawerMixin, copyMixin],
  components: {
    Pagination,
    OrderNotesDrawer,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: "",
      },
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      // 店铺列表
      shopList: [],
      // 当前场景代码 - 订单状态汇总
      currentScene: "09",
    };
  },
  mounted() {
    this.getPageData();
    this.loadShops();
  },
  methods: {
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true;
      if (!parameter) {
        parameter = {
          current: this.searchForm.current,
          size: this.searchForm.size,
          shopIds:
            this.searchForm.shopIds && this.searchForm.shopIds.length > 0
              ? this.searchForm.shopIds
              : undefined,
          orderSn: this.searchForm.orderSn || undefined,
        };
      }

      getOrderStatusSummaryPage(parameter)
        .then((res) => {
          this.loading = false;
          if (res.success && res.data) {
            this.tableData = res.data.records;
            this.pageParam.currentPage = res.data.current;
            this.pageParam.pageSize = res.data.size;
            this.pageParam.total = res.data.total;
          }
        })
        .catch((err) => {
          this.loading = false;
          this.$message.error("获取数据失败：" + err.message);
        });
    },

    // 分页回调
    callback_getPageData(parm) {
      this.searchForm.current = parm.currentPage;
      this.searchForm.size = parm.pageSize;
      this.getPageData();
    },

    // 搜索
    onSearch() {
      this.searchForm.current = 1;
      this.getPageData();
    },

    // 重置
    onReset() {
      this.searchForm = {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: "",
      };
      this.getPageData();
    },

    // 加载店铺数据
    loadShops() {
      getAllEnabledShops()
        .then((res) => {
          if (res.success && res.data && res.data.records) {
            this.shopList = res.data.records;
          }
        })
        .catch((err) => {
          console.error("加载店铺数据失败：", err);
        });
    },

    // 导航到对应页面
    navigateToPage(statusType, orderSn) {
      const routeMap = {
        purchaseRegistration: "/OrdPurchase",
        centralizedPurchase: "/OrdPurchaseCentralized",
        incompleteLogistics: "/OrdLogisticsNotComplete",
        logisticsDoneNotWarehoused: "/OrdLogisticsDoneNotWarehoused",
        warehousedNotOutbound: "/OrdWarehousedNotOutbound",
        purchaseDoneOrderCancelled: "/OrdRefund",
        warehousedRepurchase: "/OrdRepurchase",
        outboundAfterSale: "/OrdAfterSale",
        logisticsClaim: "/OrdClaim",
        taiwanListing: "/OrdTaiwan",
      };

      const targetRoute = routeMap[statusType];
      if (targetRoute) {
        this.$router.push({
          path: targetRoute,
          query: { orderSn: orderSn },
        });
      } else {
        this.$message.warning("该功能页面暂未配置");
      }
    },
  },
};
</script>

<style scoped>
.content-container {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-table-placeholder {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.demo-form-inline .el-form-item {
  margin-bottom: 0;
}

/* 面包屑样式 */
.el-breadcrumb {
  margin-bottom: 0;
  font-size: 14px;
}

/* 表格样式优化 */
.el-table {
  margin-top: 20px;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

/* 可点击订单号样式 */
.clickable-order-sn {
  cursor: pointer;
  color: inherit; /* 保持原有文字颜色 */
  transition: opacity 0.2s;
}

.clickable-order-sn:hover {
  opacity: 0.7; /* 悬停时稍微变透明，提示可点击 */
}

/* 可点击状态样式 */
.clickable-status {
  color: #409eff;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
}

.clickable-status:hover {
  text-decoration: underline;
  background-color: #f0f9ff;
  padding: 2px 4px;
  border-radius: 3px;
}
</style>
