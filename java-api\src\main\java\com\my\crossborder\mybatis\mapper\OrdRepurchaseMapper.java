package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchasePageDTO;
import com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchasePageVO;
import com.my.crossborder.mybatis.entity.OrdRepurchase;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 重新采购 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface OrdRepurchaseMapper extends BaseMapper<OrdRepurchase> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<OrdRepurchasePageVO> page(OrdRepurchasePageDTO pageDTO);
	
}
