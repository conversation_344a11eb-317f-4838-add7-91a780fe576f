package com.my.crossborder.job;

import javax.annotation.PostConstruct;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.my.crossborder.service.SysShiftAttendanceService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 考勤定时任务
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AttendanceJob {

    private final SysShiftAttendanceService sysShiftAttendanceService;

    
    @PostConstruct
    public void init() {
    	this.autoAbsentStatus();
    }
    
    /**
     * 将前一天未打卡的记录标记为缺勤
     */
    @Scheduled(cron = "${com.my.crossborder.job.AttendanceJob.processAbsentStatus}")
    public void autoAbsentStatus() {
        log.info("开始执行缺勤状态处理任务");
        
        try {
        	this.sysShiftAttendanceService.autoAbsentStatus();
            log.info("缺勤状态处理任务执行成功");
        } catch (Exception e) {
            log.error("缺勤状态处理任务执行失败", e);
        }
    }
    
} 