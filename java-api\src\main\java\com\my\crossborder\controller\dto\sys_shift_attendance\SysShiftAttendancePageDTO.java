package com.my.crossborder.controller.dto.sys_shift_attendance;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_shift_attendance.SysShiftAttendancePageVO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_考勤表
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftAttendancePageDTO 
						extends PageDTO<SysShiftAttendancePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排班日期
     */
    private LocalDate shiftDay;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 打卡时间
     */
    private LocalDateTime clockTime;

    /**
     * 打卡状态：0=待打卡/1=已打卡/2=缺勤
     */
    private Integer closeStatus;

}
