<template>
  <div class="order-selector">
    <el-input v-model="displayText" :placeholder="placeholder" readonly @focus="openOrderDialog" :disabled="disabled">
      <el-button slot="append" icon="el-icon-search" @click="openOrderDialog" :disabled="disabled"></el-button>
    </el-input>

    <!-- 订单商品选择对话框 -->
    <el-dialog title="选择订单商品" top="5vh" :visible="showOrderSelectDialog" width="1200px" append-to-body @close="closeOrderDialog" :close-on-click-modal="false">
      <div>
        <!-- 订单搜索区域 -->
        <el-form :inline="true" :model="orderSearchForm" class="demo-form-inline">
          <el-form-item label="店铺">
            <el-select v-model="orderSearchForm.shopId" placeholder="请选择店铺" clearable style="width: 140px;">
              <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="orderSearchForm.orderSn" style="width:180px" placeholder="订单号" clearable></el-input>
          </el-form-item>
          <el-form-item label="订单状态">
            <dict-select v-model="orderSearchForm.orderStates" category-id="ERP_ORDER_STATUS" placeholder="订单状态" style="width:120px" clearable></dict-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchOrders">查询</el-button>
            <el-button @click="resetOrderSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 订单表格 -->
        <el-table ref="orderTable" v-loading="orderLoading" :data="orderTableData" style="width: 100%" height="530px"
          border @row-click="handleRowSelect" :expand-row-keys="expandedRows" :row-key="getRowKey" @expand-change="handleExpandChange"
          highlight-current-row>
          <!-- 展开列 -->
          <el-table-column type="expand">
            <template slot-scope="scope">
              <div class="order-items-section">
                <div v-if="scope.row.itemsLoading" style="text-align: center; padding: 20px;">
                  <i class="el-icon-loading"></i> 加载中...
                </div>
                <el-table v-else-if="scope.row.orderItems && scope.row.orderItems.length > 0" :data="scope.row.orderItems" border style="width: 100%;" header-cell-class-name="dark-header" @row-click="handleOrderItemSelect" @row-dblclick="(orderItem) => handleOrderItemDoubleClick(orderItem, scope.row)" highlight-current-row>
                  <el-table-column label="产品图片" width="150" align="center">
                    <template slot-scope="item">
                      <img :src="item.row.itemImage || '/static/img/default-product.png'"
                        style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;"
                        @error="handleImageError" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="itemName" label="产品名称" min-width="200" show-overflow-tooltip>
                    <template slot-scope="item">
                      <span class="item-name clickable-item-name"
                            @click.stop="copyItemName(item.row.itemName)"
                            :title="'点击复制商品名称: ' + item.row.itemName">
                        {{ item.row.itemName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="itemModelName" label="规格" min-width="150" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="amount" label="数量" width="90" align="center"></el-table-column>
                  <el-table-column prop="itemPrice" label="单价" width="100" align="center"></el-table-column>
                  <el-table-column prop="expressNo" label="快递编号" min-width="150" align="center">
                    <template slot-scope="item">
                      <span v-if="item.row.expressNo"
                            class="reissue-waybill-no"
                            style="color: #67C23A; font-weight: bold; cursor: pointer;"
                            @click.stop="copyExpressNo(item.row.expressNo)"
                            :title="'点击复制快递号: ' + item.row.expressNo">
                        {{ item.row.expressNo }}
                      </span>
                      <span v-else class="no-express">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" align="center">
                    <template slot-scope="item">
                      <el-button type="primary" size="mini" @click.stop="selectOrderItem(item.row, scope.row)">选择</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div v-else style="text-align: center; padding: 20px; color: #999;">
                  暂无订单子项数据
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="shopName" label="店铺名称" min-width="130" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="orderSn" label="订单号" min-width="180" align="center">
            <template slot-scope="scope">
              <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
                    @click.stop="copyOrderSn(scope.row.orderSn)"
                    :title="'点击复制订单号'">
                {{ scope.row.orderSn }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="下单时间" min-width="140" align="center">
            <!-- <template slot-scope="scope">
              {{ formatDate(scope.row.createTime) }}
            </template> -->
          </el-table-column>
          <el-table-column prop="orderStatesName" label="订单状态" min-width="100" align="center"></el-table-column>
          <el-table-column prop="totalPrice" label="订单总价" min-width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.totalPrice }}
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty-table-placeholder">
              <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
              <p>暂无数据</p>
            </div>
          </template>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination @size-change="handleOrderSizeChange" @current-change="handleOrderCurrentChange"
            :current-page="orderSearchForm.current" :page-sizes="[10, 20, 30, 50]" :page-size="orderSearchForm.size"
            layout="total, sizes, prev, pager, next, jumper" :total="orderTotal">
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeOrderDialog">关闭</el-button>
        <div style="color: #999; font-size: 12px; margin-top: 8px;">
          提示：请展开订单并点击商品行的"选择"按钮来选择具体商品
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { selectorPageWithItems, erpOrderDetail } from '../api/ErpOrder'
import { getAllEnabledShops } from '../api/SysShop'
import DictSelect from './DictSelect'
import copyMixin from '../mixins/copyMixin'

export default {
  name: 'OrderItemSelector',
  mixins: [copyMixin],
  components: {
    DictSelect
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择订单商品'
    },
    // 预设的商品信息（用于编辑时直接显示，避免API调用）
    presetItemInfo: {
      type: Object,
      default: null
    },
    // 售后过滤类型 (taiwan: 台湾商品特殊过滤)
    afterSaleFilter: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedOrderItemId: this.value || '',
      selectedOrderItemInfo: null,
      showOrderSelectDialog: false,
      orderSearchForm: {
        shopId: '',
        orderSn: '',
        orderStates: '',
        current: 1,
        size: 10
      },
      shopList: [],
      orderTableData: [],
      orderTotal: 0,
      orderLoading: false,
      expandedRows: [],
      selectedOrderItem: null
    }
  },
  computed: {
    displayText() {
      if (this.selectedOrderItemId && this.selectedOrderItemInfo) {
        return `${this.selectedOrderItemInfo.itemName} (${this.selectedOrderItemInfo.orderSn})`;
      }
      return '';
    }
  },
  watch: {
    value(newVal, oldVal) {
      console.log('🔄 OrderItemSelector value变化:', { oldVal, newVal })
      console.log('🔄 预设商品信息:', this.presetItemInfo)

      this.selectedOrderItemId = newVal || '';

      if (newVal) {
        // 优先使用预设的商品信息（编辑时传入）
        if (this.presetItemInfo && this.presetItemInfo.productName) {
          console.log('✅ 使用预设商品信息:', this.presetItemInfo)
          this.selectedOrderItemInfo = {
            itemName: this.presetItemInfo.productName,
            itemModelName: this.presetItemInfo.productSpec || '',
            orderSn: this.presetItemInfo.originalOrderNumber || '未知订单'
          }
        } else {
          // 如果没有预设信息，则通过API加载
          console.log('🔍 没有预设信息，通过API加载订单项信息:', newVal)
          this.selectedOrderItemInfo = null
          this.loadOrderItemInfo(newVal);
        }
      } else {
        // 如果没有值，清空显示信息
        this.selectedOrderItemInfo = null
        console.log('🧹 清空订单项信息显示')
      }
    },
    selectedOrderItemId(newVal) {
      this.$emit('input', newVal);
    },
    // 监听预设商品信息的变化
    presetItemInfo: {
      handler(newVal) {
        console.log('🔄 预设商品信息变化:', newVal)
        if (newVal && newVal.productName && this.selectedOrderItemId) {
          console.log('✅ 应用预设商品信息:', newVal)
          this.selectedOrderItemInfo = {
            itemName: newVal.productName,
            itemModelName: newVal.productSpec || '',
            orderSn: newVal.originalOrderNumber || '未知订单'
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 获取行的key
    getRowKey(row) {
      return row.orderId;
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '';
      return new Date(dateTime).toLocaleString('zh-CN');
    },

    // 格式化日期（只显示日期部分）
    formatDate(dateTime) {
      if (!dateTime) return '';
      return new Date(dateTime).toLocaleDateString('zh-CN');
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png';
    },



    // 复制快递号
    copyExpressNo(expressNo) {
      this.copyToClipboard(expressNo, '快递号');
    },

    // 处理表格展开事件
    handleExpandChange(row, expandedRows) {
      // 如果行被展开且还没有加载订单子项
      if (expandedRows.includes(row) && !row.orderItems && !row.itemsLoading) {
        this.loadOrderItems(row);
      }
    },

    // 加载订单子项
    loadOrderItems(row) {
      this.$set(row, 'itemsLoading', true);
      erpOrderDetail(row.orderId)
        .then(res => {
          this.$set(row, 'itemsLoading', false);
          if (res.success && res.data) {
            this.$set(row, 'orderItems', res.data.orderItems || []);
          } else {
            this.$set(row, 'orderItems', []);
            this.$message.error(res.message || '加载订单子项失败');
          }
        })
        .catch(error => {
          this.$set(row, 'itemsLoading', false);
          this.$set(row, 'orderItems', []);
          this.$message.error('加载订单子项失败: ' + error.message);
        });
    },

    // 搜索订单
    searchOrders() {
      this.orderSearchForm.current = 1;
      this.loadOrderData();
    },

    // 重置订单搜索
    resetOrderSearch() {
      this.orderSearchForm = {
        shopId: '',
        orderSn: '',
        orderStates: '',
        current: 1,
        size: 10
      };
      this.loadOrderData();
    },

    // 加载店铺列表
    loadShopList() {
      getAllEnabledShops()
        .then(res => {
          if (res.success) {
            this.shopList = res.data.records || [];
          } else {
            this.$message.error(res.message || '加载店铺列表失败');
          }
        })
        .catch(error => {
          this.$message.error('加载店铺列表失败: ' + error.message);
        });
    },

    // 加载订单数据
    loadOrderData() {
      this.orderLoading = true;
      // 构建查询参数，包含售后过滤条件
      const queryParams = {
        ...this.orderSearchForm,
        afterSaleFilter: this.afterSaleFilter
      };
      selectorPageWithItems(queryParams)
        .then(res => {
          this.orderLoading = false;
          if (res.success) {
            this.orderTableData = res.data.records || [];
            this.orderTotal = res.data.total || 0;
          } else {
            this.$message.error(res.message || '加载订单数据失败');
          }
        })
        .catch(error => {
          this.orderLoading = false;
          this.$message.error('加载订单数据失败: ' + error.message);
        });
    },

    // 加载订单项信息（用于显示已选订单项的详情）
    loadOrderItemInfo(orderItemId) {
      if (!orderItemId) {
        this.selectedOrderItemInfo = null
        return
      }

      console.log('🔍 开始加载订单项信息，ID:', orderItemId)

      // 导入API函数
      import('../api/ErpOrderItem.js').then(module => {
        const { erpOrderItemDetail } = module

        // 调用后端API查询订单项详情
        erpOrderItemDetail(orderItemId)
          .then(response => {
            console.log('📦 订单项详情响应:', response)
            if (response && response.success) {
              this.selectedOrderItemInfo = response.data
              console.log('✅ 订单项信息加载成功:', this.selectedOrderItemInfo)
            } else {
              console.warn('⚠️ 订单项详情查询失败:', response)
              // 如果查询失败，设置一个基本的显示信息
              this.selectedOrderItemInfo = {
                itemName: `订单项 ${orderItemId}`,
                orderSn: '未知订单'
              }
            }
          })
          .catch(error => {
            console.error('❌ 加载订单项信息失败:', error)
            // 设置一个基本的显示信息
            this.selectedOrderItemInfo = {
              itemName: `订单项 ${orderItemId}`,
              orderSn: '未知订单'
            }
          })
      }).catch(error => {
        console.error('❌ 导入API模块失败:', error)
        this.selectedOrderItemInfo = {
          itemName: `订单项 ${orderItemId}`,
          orderSn: '未知订单'
        }
      })
    },

    // 订单分页大小变化
    handleOrderSizeChange(val) {
      this.orderSearchForm.size = val;
      this.loadOrderData();
    },

    // 订单分页页码变化
    handleOrderCurrentChange(val) {
      this.orderSearchForm.current = val;
      this.loadOrderData();
    },

    // 打开订单选择对话框
    openOrderDialog() {
      if (this.disabled) return;
      this.showOrderSelectDialog = true;
      this.expandedRows = [];
      // 打开对话框时加载数据
      this.loadOrderData();
    },

    // 行点击事件（选择订单）
    handleRowSelect(row) {
      // 展开/折叠订单行，显示订单项
      this.$refs.orderTable.toggleRowExpansion(row);
    },

    // 订单项行点击事件
    handleOrderItemSelect(orderItem) {
      // 可以在这里添加订单项行点击的逻辑
      console.log('点击了订单项:', orderItem);
    },

    // 订单项行双击事件
    handleOrderItemDoubleClick(orderItem, order) {
      // 双击直接选择订单项，相当于点击选择按钮
      this.selectOrderItem(orderItem, order);
    },

    // 选择订单项
    selectOrderItem(orderItem, order) {
      // 设置选中的订单项信息
      this.selectedOrderItemId = orderItem.id;
      this.selectedOrderItemInfo = {
        ...orderItem,
        orderSn: order.orderSn,
        shopName: order.shopName
      };
      this.selectedOrderItem = orderItem;

      // 关闭对话框
      this.closeOrderDialog();

      // 触发事件，通知父组件选择了订单项
      this.$emit('select', {
        orderItem: orderItem,
        order: order
      });
    },

    // 确认选择订单（保留原有方法，但改为选择订单项）
    confirmOrderSelection() {
      this.$message.warning('请展开订单并选择具体的商品');
    },

    // 关闭订单对话框
    closeOrderDialog() {
      this.showOrderSelectDialog = false;
      this.expandedRows = [];
      this.selectedOrderItem = null;
      // 清除表格选中状态
      if (this.$refs.orderTable) {
        this.$refs.orderTable.setCurrentRow();
      }
    }
  },
  mounted() {
    // 组件挂载时加载店铺列表
    this.loadShopList();
  }
}
</script>

<style scoped>
.order-selector {
  width: 100%;
}

.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
}

.order-items-section {
  background-color: #f8f9fa;
  border-radius: 6px;
  /* padding: 15px; */
  /* margin: 10px; */
}

.order-items-section .el-table {
  background-color: white;
  border-radius: 4px;
}

/* 快递单号样式 */
.reissue-waybill-no {
  color: #67C23A !important;
  font-weight: bold !important;
  background-color: #F0F9FF !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
  border: 1px solid #67C23A !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  display: inline-block !important;
}

.reissue-waybill-no:hover {
  background-color: #E8F5E8 !important;
  border-color: #5CB85C !important;
}

.no-express {
  color: #C0C4CC;
  font-style: italic;
}

.empty-table-placeholder {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.empty-table-placeholder p {
  margin: 0;
  font-size: 14px;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa !important;
}

/* 订单号样式 */
.el-table .cell {
  word-break: break-all;
}

/* 展开行样式 */
.el-table .el-table__expanded-cell {
  padding: 0;
  background-color: #fafafa;
}

/* 深色表头样式 */
.dark-header {
  background-color: #2c3e50 !important;
  color: #ffffff !important;
  font-weight: bold;
}

.dark-header .cell {
  color: #ffffff !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .demo-form-inline .el-form-item {
    display: block;
    margin-right: 0;
  }

  .order-items-section {
    padding: 10px;
  }
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>
