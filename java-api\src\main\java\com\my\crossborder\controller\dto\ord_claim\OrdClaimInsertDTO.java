package com.my.crossborder.controller.dto.ord_claim;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import com.my.crossborder.controller.dto.AttachmentIdListDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_物流理赔
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdClaimInsertDTO  extends AttachmentIdListDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单号
     */
	@NotNull(message="orderSn不能为空")
    private String orderSn;

    /**
     * 问题描述
     */
	@NotNull(message="issue不能为空")
    private String issue;

    /**
     * 问题类别 字典claim_issue_type
     */
	@NotNull(message="issueType不能为空")
    private String issueType;

    /**
     * 订单项id
     */
	@NotNull(message="orderItemId不能为空")
    private String orderItemId;

    /**
     * 采购金额
     */
	@NotNull(message="purchaseCost不能为空")
    private BigDecimal purchaseCost;

    /**
     * 补寄单号
     */
    private String waybillNumber;

    /**
     * 处理办法 字典claim_close_way
     */
	@NotNull(message="closeWay不能为空")
    private String closeWay;

}
