package com.my.crossborder.controller.dto.sys_erp_client_log;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_erp接口日志表
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysErpClientLogInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 禾宸用户名
     */
	@NotNull(message="username不能为空")
    private String username;

    /**
     * 接口名称
     */
	@NotNull(message="operationName不能为空")
    private String operationName;

    /**
     * 接口详情
     */
	@NotNull(message="operationContent不能为空")
    private String operationContent;

    /**
     * 是否成功
     */
	@NotNull(message="success不能为空")
    private Boolean success;

    /**
     * 异常信息
     */
	@NotNull(message="errMsg不能为空")
    private String errMsg;

    /**
     * 日志时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

}
