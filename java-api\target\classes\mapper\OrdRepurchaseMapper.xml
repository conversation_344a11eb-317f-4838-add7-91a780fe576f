<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdRepurchaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.OrdRepurchase">
        <id column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="issue" property="issue" />
        <result column="close_way" property="closeWay" />
        <result column="close_status" property="closeStatus" />
        <result column="issue_user_id" property="issueUserId" />
        <result column="issue_time" property="issueTime" />
        <result column="close_user_id" property="closeUserId" />
        <result column="close_time" property="closeTime" />
        <result column="confirm_user_id" property="confirmUserId" />
        <result column="confirm_time" property="confirmTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_sn, issue, close_way, close_status, issue_user_id, issue_time, close_user_id, close_time, confirm_user_id, confirm_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchasePageVO">
		SELECT
			id, order_sn, issue, close_way, close_status, issue_user_id, issue_time, close_user_id, close_time, confirm_user_id, confirm_time
		FROM
			ord_repurchase AS t1
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="orderSn != null and orderSn != ''">
	           	AND t1.order_sn = #{orderSn}
            </if>
	        <if test="issue != null and issue != ''">
	           	AND t1.issue = #{issue}
            </if>
	        <if test="closeWay != null and closeWay != ''">
	           	AND t1.close_way = #{closeWay}
            </if>
	        <if test="closeStatus != null and closeStatus != ''">
	           	AND t1.close_status = #{closeStatus}
            </if>
	        <if test="issueUserId != null and issueUserId != ''">
	           	AND t1.issue_user_id = #{issueUserId}
            </if>
	        <if test="issueTime != null and issueTime != ''">
	           	AND t1.issue_time = #{issueTime}
            </if>
	        <if test="closeUserId != null and closeUserId != ''">
	           	AND t1.close_user_id = #{closeUserId}
            </if>
	        <if test="closeTime != null and closeTime != ''">
	           	AND t1.close_time = #{closeTime}
            </if>
	        <if test="confirmUserId != null and confirmUserId != ''">
	           	AND t1.confirm_user_id = #{confirmUserId}
            </if>
	        <if test="confirmTime != null and confirmTime != ''">
	           	AND t1.confirm_time = #{confirmTime}
            </if>
        </where>
    </select>

</mapper>
