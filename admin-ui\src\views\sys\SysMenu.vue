<template>
  <div class="menu-permission">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>菜单与权限</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 操作工具栏 -->
    <div class="toolbar" style="margin-top: 20px;">
      <div class="toolbar-left">
        <el-button type="primary" size="small" @click="handleExpandAll">
          <i class="el-icon-sort"></i> 展开
        </el-button>
        <el-button type="primary" size="small" @click="handleCollapseAll">
          <i class="el-icon-minus"></i> 折叠
        </el-button>
        <el-input placeholder="请输入菜单名称搜索" v-model="searchText" size="small" style="width: 200px;" clearable>
          <i slot="prefix" class="el-icon-search"></i>
        </el-input>
        <el-button type="success" size="small" @click="addMenu">
          <i class="el-icon-plus"></i> 添加菜单
        </el-button>
        <el-button v-permission="['sys-menu:grant']" type="warning" size="small" @click="showRolePermissionDialog">
          <i class="el-icon-key"></i> 角色授权
        </el-button>
      </div>
    </div>

    <!-- 菜单权限表格 -->
    <el-table ref="menuTable" :data="filteredMenuData" style="width: 100%; margin-top: 15px;" row-key="id" border
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :default-expand-all="isExpandAll"
      v-loading="loading" :indent="12" class="menu-tree-table">

      <el-table-column prop="name" label="菜单名称" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span class="menu-name">
            <i :class="scope.row.icon || 'el-icon-menu'" style="margin-right: 8px; color: #606266;"></i>
            {{ scope.row.name }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="type" label="类型" width="80" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === 'menu' ? 'primary' : 'warning'" size="small">
            {{ scope.row.type === 'menu' ? '菜单' : '按钮' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="path" label="路由" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.path || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="icon" label="icon图标路径" min-width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.icon || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="permission" label="权限标识" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.permission || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="sort" label="排序" width="80" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sort || 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="80" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="200" align="center" fixed="right">
        <template slot-scope="scope">
          <!-- <el-button type="text" size="mini" @click="addChildMenu(scope.row)">
            <i class="el-icon-plus"></i> 添加
          </el-button>
          <el-button v-permission="['sys-menu:update']" type="text" size="mini" @click="editMenu(scope.row)">
            <i class="el-icon-edit"></i> 编辑
          </el-button>
          <el-button type="text" size="mini" @click="deleteMenu(scope.row)" style="color: #f56c6c;">
            <i class="el-icon-delete"></i> 删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!-- 引入菜单编辑组件 -->
    <sys-menu-edit :visible.sync="menuDialogVisible" :menu="currentMenu" :menuData="menuData" @save="handleSaveMenu"
      @close="handleDialogClose" @refresh="handleRefresh" />

    <!-- 角色授权对话框 -->
    <el-dialog title="角色授权" :visible.sync="rolePermissionDialogVisible" width="800px">
      <div class="role-permission-container">
        <div class="role-list">
          <el-card class="box-card" style="height: 400px;">
            <div slot="header" class="clearfix">
              <span>角色</span>
              <div style="float: right;">
                <span style="margin-right: 10px; color: #999; font-size: 14px;">
                  {{ selectedRole ? selectedRole.label : '请选择角色' }}
                </span>
              </div>
            </div>
            <el-tree ref="roleTree" :data="roleTreeData" :props="{ children: 'children', label: 'label' }" node-key="id"
              @node-click="selectRole" highlight-current :default-expand-all="true" :expand-on-click-node="false"
              :check-on-click-node="false" class="role-tree">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span>{{ node.label }}</span>
              </span>
            </el-tree>
          </el-card>
        </div>
        <div class="permission-list" v-if="selectedRole">
          <h4>{{ selectedRole.label }} - 菜单权限</h4>
          <div v-if="isGroupRole" class="empty-permission-tip">
            <i class="el-icon-info"></i>
            <p>请选择具体角色</p>
          </div>
          <el-tree v-else ref="permissionTree" :data="menuData" :props="{ children: 'children', label: 'name' }"
            show-checkbox node-key="id" :default-checked-keys="selectedRole.permissions || []"
            @check-change="handlePermissionCheck"
            style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px; max-height: 300px; overflow-y: auto;">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>
                <i :class="data.icon || 'el-icon-menu'" style="margin-right: 5px;"></i>
                {{ data.name }}
                <el-tag v-if="data.type === 'button'" size="mini" type="warning" style="margin-left: 5px;">按钮</el-tag>
              </span>
            </span>
          </el-tree>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveRolePermissions">保 存</el-button>
        <el-button @click="rolePermissionDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SysMenuEdit from './SysMenuEdit.vue'
import { sysMenuTreeNodes, sysMenuGrant, sysMenuListByRoleId, sysMenuDelete } from '@/api/SysMenu'

export default {
  name: 'MenuPermission',
  components: {
    SysMenuEdit
  },
  data() {
    return {
      searchText: '',
      selectedMenu: null,
      activeTab: 'menu',
      menuDialogVisible: false,
      permissionDialogVisible: false,
      rolePermissionDialogVisible: false,
      selectedRole: null,
      selectedPermissions: [],
      loading: false,
      isExpandAll: false,
      currentMenu: {},

      // 树形组件配置
      treeProps: {
        children: 'children',
        label: 'name'
      },

      // 菜单数据
      menuData: [],


      // 按钮权限
      buttonPermissions: [
        { code: 'add', name: '新增' },
        { code: 'edit', name: '编辑' },
        { code: 'delete', name: '删除' },
        { code: 'view', name: '查看' },
        { code: 'export', name: '导出' },
        { code: 'import', name: '导入' }
      ],

      // 角色树数据（用于角色授权）
      roleTreeData: [
        {
          id: '11',
          label: '系统管理员',
          permissions: []
        },
        {
          id: '12',
          label: '合伙人',
          permissions: []
        },
        {
          id: '21',
          label: '客服',
          permissions: []
        },
        {
          id: '22',
          label: '客服主管',
          permissions: []
        }
      ]
    }
  },
  computed: {
    filteredMenuData() {
      if (!this.searchText) {
        return this.menuData
      }
      return this.filterMenuData(this.menuData, this.searchText)
    },
    // 判断当前选中的是否为分组角色（现在所有角色都可以设置权限）
    isGroupRole() {
      return false
    }
  },
  watch: {
    searchText(val) {
      this.$refs.menuTable.filter(val)
    },
    selectedMenu: {
      handler(newVal) {
        if (newVal) {
          // 更新按钮权限
        }
      },
      deep: true
    }
  },
  methods: {
    // 过滤菜单数据
    filterMenuData(data, searchText) {
      const result = []
      data.forEach(item => {
        if (item.name.toLowerCase().includes(searchText.toLowerCase())) {
          result.push(item)
        } else if (item.children && item.children.length > 0) {
          const filteredChildren = this.filterMenuData(item.children, searchText)
          if (filteredChildren.length > 0) {
            result.push({
              ...item,
              children: filteredChildren
            })
          }
        }
      })
      return result
    },

    // 树形过滤
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },

    // 选择菜单
    selectMenu(data) {
      this.selectedMenu = { ...data }
      this.activeTab = 'menu'
    },

    // 展开全部
    handleExpandAll() {
      this.isExpandAll = true;
      this.$nextTick(() => {
        const el = this.$refs.menuTable.$el.querySelector('.el-table__body-wrapper');
        const trs = el.querySelectorAll('.el-table__row');
        trs.forEach(tr => {
          const expandIcon = tr.querySelector('.el-table__expand-icon');
          if (expandIcon && !expandIcon.classList.contains('el-table__expand-icon--expanded')) {
            expandIcon.click();
          }
        });
      });
      this.$message.success('已展开所有菜单');
    },

    // 收起全部
    handleCollapseAll() {
      this.isExpandAll = false;
      this.$nextTick(() => {
        const el = this.$refs.menuTable.$el.querySelector('.el-table__body-wrapper');
        const trs = el.querySelectorAll('.el-table__row');
        trs.forEach(tr => {
          const expandIcon = tr.querySelector('.el-table__expand-icon.el-table__expand-icon--expanded');
          if (expandIcon) {
            expandIcon.click();
          }
        });
      });
      this.$message.success('已折叠所有菜单');
    },

    // 获取所有节点key
    getAllNodeKeys(data) {
      let keys = []
      data.forEach(item => {
        keys.push(item.id)
        if (item.children && item.children.length > 0) {
          keys = keys.concat(this.getAllNodeKeys(item.children))
        }
      })
      return keys
    },

    // 添加菜单
    addMenu() {
      this.currentMenu = {}
      this.menuDialogVisible = true
    },

    // 添加子菜单
    addChildMenu(data) {
      this.currentMenu = {
        parentId: data.id
      }
      this.menuDialogVisible = true
    },

    // 编辑菜单
    editMenu(data) {
      this.currentMenu = {
        ...data
      }
      this.menuDialogVisible = true
    },

    // 删除菜单
    deleteMenu(data) {
      this.$confirm('确认删除该菜单吗？删除后将同时删除相关的角色权限关联。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用后端 API 删除菜单
        const deleteParams = {
          idList: [data.id]
        }

        this.loading = true
        sysMenuDelete(deleteParams).then(res => {
          this.loading = false
          if (res.success) {
            this.$message.success('删除成功')
            // 刷新菜单数据
            this.loadMenuData()
            if (this.selectedMenu && this.selectedMenu.id === data.id) {
              this.selectedMenu = null
            }
          } else {
            this.$message.error(res.message || '删除失败')
          }
        }).catch(err => {
          this.loading = false
          console.error('删除菜单错误:', err)
          this.$message.error('删除失败')
        })
      }).catch(() => { })
    },

    // 保存菜单处理
    handleSaveMenu(menuForm) {
      // 保存成功后刷新菜单数据
      this.loadMenuData()
      this.menuDialogVisible = false
    },

    // 处理刷新事件
    handleRefresh() {
      this.loadMenuData()
    },

    // 对话框关闭处理
    handleDialogClose() {
      this.currentMenu = {}
    },

    // 获取父级路径
    getParentPath(id) {
      const findParent = (data, targetId, path = []) => {
        for (let item of data) {
          if (item.children && item.children.some(child => child.id === targetId)) {
            return [...path, item.id]
          }
          if (item.children) {
            const result = findParent(item.children, targetId, [...path, item.id])
            if (result) return result
          }
        }
        return null
      }
      return findParent(this.menuData, id)
    },

    // 从数据中移除菜单
    removeMenuFromData(data, id) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].id === id) {
          data.splice(i, 1)
          return true
        }
        if (data[i].children && this.removeMenuFromData(data[i].children, id)) {
          return true
        }
      }
      return false
    },

    // 更新菜单数据
    updateMenuInData(data, menuForm) {
      for (let item of data) {
        if (item.id === menuForm.id) {
          Object.assign(item, menuForm)
          return true
        }
        if (item.children && this.updateMenuInData(item.children, menuForm)) {
          return true
        }
      }
      return false
    },

    // 添加菜单到数据
    addMenuToData(data, newMenu) {
      if (!newMenu.parentId || newMenu.parentId.length === 0) {
        data.push(newMenu)
      } else {
        const parentId = newMenu.parentId[newMenu.parentId.length - 1]
        this.findAndAddToParent(data, parentId, newMenu)
      }
    },

    // 查找并添加到父级
    findAndAddToParent(data, parentId, newMenu) {
      for (let item of data) {
        if (item.id === parentId) {
          if (!item.children) {
            item.children = []
          }
          item.children.push(newMenu)
          return true
        }
        if (item.children && this.findAndAddToParent(item.children, parentId, newMenu)) {
          return true
        }
      }
      return false
    },

    // 显示角色授权对话框
    showRolePermissionDialog() {
      this.rolePermissionDialogVisible = true
    },

    // 选择角色
    selectRole(node) {
      this.selectedRole = node

      // 确保 permissions 字段为数组类型
      if (!Array.isArray(this.selectedRole.permissions)) {
        this.selectedRole.permissions = []
      }

      // 加载该角色的菜单权限
      if (node.id) {
        this.loadRoleMenus(node.id)
      }
    },

    // 加载角色菜单权限
    loadRoleMenus(roleId) {
      // 显示加载状态
      this.loading = true

      // 调用接口获取角色对应的菜单ID列表
      sysMenuListByRoleId(roleId)
        .then(res => {
          this.loading = false
          if (res.success && res.data) {
            // 更新当前选中角色的权限
            this.selectedRole.permissions = res.data

            // 等待DOM更新后设置选中状态
            this.$nextTick(() => {
              if (this.$refs.permissionTree) {
                // 清空当前选中状态
                this.$refs.permissionTree.setCheckedKeys([])

                // 逐个设置节点的选中状态，避免自动联动
                res.data.forEach(menuId => {
                  const node = this.$refs.permissionTree.getNode(menuId)
                  if (node) {
                    // 使用setChecked方法，第三个参数false表示不触发父子联动
                    this.$refs.permissionTree.setChecked(menuId, true, false)
                  }
                })
              }
            })
          } else {
            this.$message.warning('获取菜单权限失败')
            this.selectedRole.permissions = []
          }
        })
    },

    // 处理权限检查
    handlePermissionCheck(data, checked, indeterminate) {
      // Element UI标准的check-change事件
      // data: 当前节点数据
      // checked: 当前节点是否选中
      // indeterminate: 当前节点是否半选中
      console.log('权限变更:', data.name, 'checked:', checked, 'indeterminate:', indeterminate)

      // 更新权限列表
      this.selectedRole.permissions = this.$refs.permissionTree.getCheckedKeys()
    },

    // 保存角色权限
    saveRolePermissions() {
      if (!this.selectedRole) {
        this.$message.warning('请先选择角色')
        return
      }

      // 只获取完全选中的菜单ID列表（不包含半选中的父节点）
      // 这样可以避免父节点被误认为完全选中，导致其所有子节点在加载时被自动选中
      const menuIdList = this.$refs.permissionTree.getCheckedKeys()

      // 构造请求参数
      const params = {
        roleId: parseInt(this.selectedRole.id),
        menuIdList: menuIdList.map(String) // 确保所有ID都是字符串类型
      }

      // 显示加载中
      this.loading = true

      // 调用授权接口
      sysMenuGrant(params)
        .then(res => {
          this.loading = false
          if (res.success) {
            this.$message.success('角色权限保存成功')
            this.rolePermissionDialogVisible = false
          } else {
            this.$message.error(res.message || '角色权限保存失败')
          }
        })
        .catch(err => {
          this.loading = false
          console.error('保存角色权限出错:', err)
          this.$message.error('保存角色权限出错')
        })
    },

    // 设置权限
    setPermission(data) {
      this.selectedMenu = data
      this.permissionDialogVisible = true
    },

    // 从后台加载菜单数据
    loadMenuData() {
      this.loading = true
      sysMenuTreeNodes()
        .then(res => {
          this.loading = false
          if (res.success) {
            // 使用后台返回的菜单数据
            this.menuData = res.data
          } else {
            // 如果接口返回错误，则使用模拟数据
            this.$message.warning('获取菜单数据失败，将使用模拟数据')
          }
        })
    }
  },
  mounted() {
    // 默认展开第一级
    this.$nextTick(() => {
      this.handleExpandAll()
    })
    // 从后台加载菜单数据
    this.loadMenuData()
  },
}
</script>

<style scoped>
.menu-permission {
  background: #f5f7fa;
  min-height: calc(100vh - 120px);
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 表格样式 */
.el-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.menu-name {
  display: flex;
  align-items: center;
}

/* 表格缩进和树形结构样式 */
.menu-tree-table /deep/ .el-table__indent {
  padding-left: 15px !important;
}

.menu-tree-table /deep/ .el-table__expand-icon {
  margin-right: 8px;
}

/* 菜单层级样式 */
.menu-tree-table /deep/ .el-table__row {
  transition: background-color 0.2s;
}

.menu-tree-table /deep/ .el-table__row:hover {
  background-color: #f0f9eb !important;
}

.menu-tree-table /deep/ .el-table__row .cell {
  display: flex;
  align-items: center;
}

/* 角色授权对话框样式 */
.role-permission-container {
  display: flex;
  gap: 20px;
  height: 400px;
}

.role-list {
  width: 300px;
}

.permission-list {
  flex: 1;
  padding-left: 20px;
}

.permission-list h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

/* 角色树样式 */
.role-tree {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.role-tree /deep/ .el-tree-node__content {
  height: 32px;
  line-height: 32px;
}

.role-tree /deep/ .el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.role-tree /deep/ .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 权限设置对话框样式 */
.permission-roles {
  max-height: 300px;
  overflow-y: auto;
}

.role-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.role-item:hover {
  background: #e8f4fd;
  border-color: #b3d8ff;
}

.role-desc {
  margin-left: 15px;
  color: #909399;
  font-size: 13px;
}

/* 自定义树节点样式 */
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-right: 8px;
}

/* 表格行样式优化 */
.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
  border-bottom: 2px solid #EBEEF5;
}

.el-table td {
  border-bottom: 1px solid #EBEEF5;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #FAFAFA;
}

.el-table tbody tr:hover td {
  background-color: #F5F7FA !important;
}

/* 标签样式优化 */
.el-tag {
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
}

/* 按钮样式优化 */
.el-button--small {
  border-radius: 4px;
  padding: 6px 12px;
}

.el-button--text {
  padding: 4px 8px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

/* 表单样式优化 */
.el-form-item__content {
  display: flex;
  align-items: center;
}

.el-cascader {
  width: 100%;
}

.el-input-number {
  width: 100%;
}

/* 树形选择器样式 */
.el-tree {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.el-tree-node__content {
  height: 32px;
  line-height: 32px;
}

.el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .role-permission-container {
    flex-direction: column;
    height: auto;
  }

  .role-list {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #ebeef5;
    padding-right: 0;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  .permission-list {
    padding-left: 0;
  }
}

@media (max-width: 768px) {
  .menu-permission {
    padding: 15px;
  }

  .toolbar {
    padding: 12px 15px;
  }

  .toolbar-left {
    flex-wrap: wrap;
    gap: 8px;
  }

  .el-table .el-table__cell {
    padding: 8px 4px;
  }

  .el-button--small {
    padding: 4px 8px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .toolbar-left .el-button {
    font-size: 12px;
    padding: 6px 10px;
  }

  .el-input {
    width: 100% !important;
  }
}

/* 空权限提示样式 */
.empty-permission-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: #f8f8f8;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  color: #909399;
}

.empty-permission-tip i {
  font-size: 32px;
  margin-bottom: 15px;
  color: #c0c4cc;
}

.empty-permission-tip p {
  font-size: 16px;
  margin: 0;
}
</style>
