package com.my.crossborder.job;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.my.crossborder.controller.vo.sys_param.SysParamVO;
import com.my.crossborder.service.SysParamService;

import lombok.RequiredArgsConstructor;

/**
 * 系统参数定时检测
 * <AUTHOR>
 *
 */
@Component
@RequiredArgsConstructor
public class SysParamCheckJob {
	
	private final SysParamService sysParamService;

	
	/**
	 * 检查系统参数
	 */
	@Scheduled(cron = "${com.my.crossborder.job.SysParamCheckJob.check}")
	public void check() {
		SysParamVO sysParamVO = this.sysParamService.get();
		sysParamVO.check();
		
		// FIXME 系统参数检测未通过，要及时通知。
	}
	
	
}
