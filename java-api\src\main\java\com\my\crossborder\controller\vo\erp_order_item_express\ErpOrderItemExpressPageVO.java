package com.my.crossborder.controller.vo.erp_order_item_express;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_订单项_快递信息
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpOrderItemExpressPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键(order_item_id + express_no)
     */
    private String id;
    
    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 快递入仓状态 (400:已入仓)
     */
    private String expressinFlag;

    /**
     * 入仓时间
     */
    private LocalDateTime putInTime;

    /**
     * 数据创建时间
     */
    private LocalDateTime putCreateTime;

    /**
     * 订单项id
     */
    private String orderItemId;
    
    /**
     * 订单id
     */
    private String orderId;

}
