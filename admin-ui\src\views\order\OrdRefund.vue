<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>已采购但出库前取消</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="店铺">
          <el-select v-model="formInline.shopIds" placeholder="请选择店铺" clearable multiple style="width: 180px;">
            <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="formInline.orderSn" placeholder="请输入订单号" clearable style="width: 180px;"></el-input>
        </el-form-item>
        <el-form-item label="处理状态">
          <dict-select v-model="formInline.applyStatus" category-id="REFUND_APPLY_STATUS" placeholder="处理状态" style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item label="处理结果">
          <dict-select v-model="formInline.resultStatus" category-id="REFUND_RESULT_STATUS" placeholder="处理结果"
            style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        ref="table"
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
        :row-key="getRowKey"
        :expand-row-keys="expandedRows"
        @expand-change="handleExpandChange">
        
        <!-- 展开行 -->
        <el-table-column type="expand">
          <template slot-scope="scope">
            <div class="order-items-section">
              <el-table :data="scope.row.orderItems" border style="width: 100%;">
                <el-table-column label="产品图片" width="150" align="center">
                  <template slot-scope="item">
                    <img :src="item.row.itemImage || '/static/img/default-product.png'"
                      style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;"
                      @error="handleImageError" />
                  </template>
                </el-table-column>
                <el-table-column prop="itemName" label="产品名称" min-width="200" show-overflow-tooltip>
                  <template slot-scope="item">
                    <span class="item-name clickable-item-name"
                          @click="copyItemName(item.row.itemName)"
                          :title="'点击复制商品名称: ' + item.row.itemName">
                      {{ item.row.itemName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="itemModelName" label="规格" min-width="150" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="amount" label="数量" width="90" align="center">
                </el-table-column>
                <el-table-column prop="itemPrice" label="单价" width="100" align="center"></el-table-column>
                <el-table-column prop="expressNo" label="快递编号" min-width="150" align="center">
                  <template slot-scope="item">
                    <span v-if="item.row.expressNo" class="express-no clickable-express"
                      @click="copyExpressNo(item.row.expressNo)" :title="'点击复制快递号: ' + item.row.expressNo">
                      {{ item.row.expressNo }}
                    </span>
                    <span v-else class="no-express">未填写</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="shopName" label="店铺名称" width="90" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="orderSn" label="订单号" width="160" align="center">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
              @click="copyOrderSn(scope.row.orderSn)" :title="'点击复制订单号'">
              {{ scope.row.orderSn }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="下单时间" width="140" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="订单总金额" width="110" align="center">
          <template slot-scope="scope">
            <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.totalPrice ? scope.row.totalPrice.toFixed(2) : '0.00' }}</span>
          </template>          
        </el-table-column>
        <dict-table-column prop="applyStatus" label="处理状态" align="center" width="120" category-id="REFUND_APPLY_STATUS">
        </dict-table-column>
        <el-table-column prop="applyDetail" label="处理详情" width="110" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.applyStatus === '1'" style="text-align: center; line-height: 1.6;">
              <!-- <div>{{ formatAmount(scope.row.applyAmount) || '-' }}</div>
              <div>{{ formatDate(scope.row.applyTime) || '-' }}</div>
              <div> {{ scope.row.applyUserName || '未知' }}</div> -->
              <el-tooltip effect="dark" placement="top">
                <div slot="content">
                  <div>申请金额：{{ formatAmount(scope.row.applyAmount) }}</div>
                  <div>填写时间：{{ formatDate(scope.row.applyTime) }}</div>
                  <div>填写人：{{ scope.row.applyUserName }}</div>
                </div>
                <div style="cursor: pointer;">{{ formatAmount(scope.row.applyAmount)}}</div>
              </el-tooltip>                  
            </div>
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>
        <dict-table-column prop="resultStatus" label="处理结果" align="center" width="90" category-id="REFUND_RESULT_STATUS">
        </dict-table-column>        
        <el-table-column prop="applyDetail" label="结果详情" width="110" align="center">
          <template slot-scope="scope">
            <!-- 退款成功 -->
            <div v-if="scope.row.resultStatus === '1'" style="text-align: center; line-height: 1.6;">
              <el-tooltip effect="dark" placement="top">
                <div slot="content">
                  <div>填写时间：{{ formatDate(scope.row.resultTime) }}</div>
                  <div>填写人：{{ scope.row.resultUserName }}</div>
                </div>
                <div style="color: #A1C65C; font-weight: bold; cursor: pointer;">{{ formatAmount(scope.row.refundSuccessAmount)}}</div>
              </el-tooltip>              
            </div>
            <!-- 退款失败 -->
            <div v-else-if="scope.row.resultStatus === '2'" style="text-align: center; line-height: 1.6;">
              <el-tooltip effect="dark" placement="top">
                <div slot="content">
                  <div>填写时间：{{ formatDate(scope.row.resultTime) }}</div>
                  <div>填写人：{{ scope.row.resultUserName }}</div>
                </div>
                <div style="color: #F56C6C; font-weight: bold; cursor: pointer;">{{ scope.row.refundFailReason || '退款失败' }}</div>
              </el-tooltip>
            </div>
            <!-- 已入库 -->
            <div v-else-if="scope.row.resultStatus === '3'" style="text-align: center; line-height: 1.6;">
              <el-tooltip effect="dark" placement="top">
                <div slot="content">
                  <div>确认时间：{{ formatDate(scope.row.resultTime) }}</div>
                  <div>确认人：{{ scope.row.resultUserName }}</div>
                </div>
                <div style="color: #4CA0FF; font-weight: bold; cursor: pointer;">已入库</div>
              </el-tooltip>
            </div>
            <!-- 无结果状态 -->
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="settlementFlag" label="结算状态" width="100" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.resultStatus == null">
              -
            </span>
            <span v-else-if="scope.row.settlementFlag === '-1'">
              <el-tag type="info">无需结算</el-tag>
            </span>
            <span v-else-if="scope.row.settlementFlag === '0'">
              <el-tag type="warning">待结算</el-tag>
            </span>
            <span v-else-if="scope.row.settlementFlag === '1'">
              <el-tag type="success">已结算</el-tag>
            </span>
            <span v-else>
              <el-tag type="info">无需结算</el-tag>
            </span>
          </template>
        </el-table-column>
        <!-- 使用NoteColumn组件 -->
        <NoteColumn :current-scene="currentScene" />
        
        <el-table-column label="操作" min-width="100" align="center">
          <template slot-scope="scope">
            <el-button v-permission="['ord-refund:apply']" v-if="scope.row.applyStatus !== '2' && scope.row.resultStatus !== '1'" size="mini" type="text" @click="handleApply(scope.row)">
              <i class="el-icon-edit"></i> 处理
            </el-button>
            <el-button v-permission="['ord-refund:result']" v-if="scope.row.applyStatus === '1' && scope.row.resultStatus !== '1'" size="mini" type="text" @click="handleResult(scope.row)">
              <i class="el-icon-data-analysis"></i> 结果
            </el-button>
            <el-button v-permission="['ord-refund:confirm']" v-if="scope.row.applyStatus === '2' && scope.row.resultStatus == null" size="mini" type="text" @click="handleConfirmPutIn(scope.row)">
              <i class="el-icon-check"></i> 确认入库
            </el-button>
            <el-button v-permission="['ord-refund:delete']" v-if="scope.row.applyStatus !== '0' && (scope.row.resultStatus !== '1' || scope.row.settlementFlag !== '1')" size="mini" type="text" @click="handleDelete(scope.row)" style="color: #F56C6C;">
              <i class="el-icon-delete"></i> 删除
            </el-button>
            <el-button size="mini" type="text" @click="openNotesDrawer(scope.row)" icon="el-icon-tickets">备注</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

    <!-- 申请弹窗 -->
    <OrdRefundEdit
      :visible="refundDialogVisible"
      :edit-data="currentRefundData"
      :is-edit="isEditRefund"
      @close="handleRefundDialogClose"
      @success="handleRefundSuccess"
    />

    <!-- 结果弹窗 -->
    <OrdRefundResultEdit
      :visible="resultDialogVisible"
      :edit-data="currentRefundData"
      @close="handleResultDialogClose"
      @success="handleResultSuccess"
    />

    <!-- 备注抽屉 -->
    <el-drawer
      title="订单备注"
      :visible.sync="notesDrawerVisible"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose">
      <div style="padding: 20px;">
        <OrderNotesDrawer
          :notes="currentOrderNotes"
          :orderSn="currentOrderSn"
          :currentScene="currentScene"
          :showDebug="false"
          @note-updated="handleNoteUpdated" />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getOrdRefundPage, confirmPutIn } from '../../api/OrdRefund'
import { deleteOrdRefund } from '../../api/ErpOrder'
import { getAllEnabledShops } from '../../api/SysShop'
import Pagination from '../../components/Pagination'
import DictSelect from '../../components/DictSelect'
import DictTableColumn from '../../components/DictTableColumn'
import DictTag from '../../components/DictTag'
import OrdRefundEdit from './OrdRefundEdit'
import OrdRefundResultEdit from './OrdRefundResultEdit'
import NoteColumn from '../../components/NoteColumn'
import OrderNotesDrawer from './OrderNotesDrawer'
import noteDrawerMixin from '../../mixins/noteDrawerMixin'
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'OrdRefund',
  mixins: [copyMixin, noteDrawerMixin],
  components: {
    Pagination,
    DictSelect,
    DictTableColumn,
    DictTag,
    OrdRefundEdit,
    OrdRefundResultEdit,
    NoteColumn,
    OrderNotesDrawer
  },
  data() {
    return {
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        orderStates: ['003'],
        applyStatus: '',
        resultStatus: ''
      },
      // 表格数据
      tableData: [],
      // 店铺列表
      shopList: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,

      // 退款申请对话框
      refundDialogVisible: false,
      currentRefundData: {},
      isEditRefund: false,

      // 退款结果对话框
      resultDialogVisible: false,

      currentScene: '05',  // 已采购但出库前取消
      expandedRows: []
    }
  },
  created() {
    // 检查URL参数中是否有orderSn
    if (this.$route.query.orderSn) {
      this.formInline.orderSn = this.$route.query.orderSn;
    }
    this.getPageData()
    this.loadShops()
  },
  methods: {
    // API方法
    deleteOrdRefund,

    // 格式化金额，添加千分位逗号
    formatAmount(amount) {
      if (!amount && amount !== 0) return '-'
      const num = parseFloat(amount)
      if (isNaN(num)) return '-'
      return '￥ ' + num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    // 获取分页数据
    getPageData() {
      this.loading = true
      getOrdRefundPage(this.formInline).then(response => {
        this.tableData = response.data.records
        this.pageParam.total = response.data.total
        this.pageParam.currentPage = response.data.current
        this.pageParam.pageSize = response.data.size
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 加载店铺列表
    loadShops() {
      getAllEnabledShops().then(response => {
        if (response.success && response.data && response.data.records) {
          this.shopList = response.data.records
        } else {
          this.shopList = []
        }
      }).catch(error => {
        console.error('加载店铺数据失败：', error)
        this.shopList = []
      })
    },

    // 搜索
    onSearch() {
      this.formInline.current = 1
      this.getPageData()
    },

    // 重置
    onReset() {
      this.formInline = {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        orderStates: ['003'],
        applyStatus: '',
        resultStatus: ''
      }
      this.getPageData()
    },

    // 分页回调
    callback_getPageData(pageParam) {
      this.formInline.current = pageParam.currentPage
      this.formInline.size = pageParam.pageSize
      this.getPageData()
    },

    // 申请操作
    handleApply(row) {
      this.currentRefundData = { ...row }
      this.isEditRefund = false
      this.refundDialogVisible = true
    },

    // 结果操作
    handleResult(row) {
      this.currentRefundData = { ...row }
      this.resultDialogVisible = true
    },

    // 申请弹窗关闭
    handleRefundDialogClose() {
      this.refundDialogVisible = false
      this.currentRefundData = {}
    },

    // 申请成功
    handleRefundSuccess() {
      this.refundDialogVisible = false
      this.getPageData()
    },

    // 结果弹窗关闭
    handleResultDialogClose() {
      this.resultDialogVisible = false
      this.currentRefundData = {}
    },

    // 结果成功
    handleResultSuccess() {
      this.resultDialogVisible = false
      this.getPageData()
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      const dateObj = new Date(date)
      const year = dateObj.getFullYear()
      const month = String(dateObj.getMonth() + 1).padStart(2, '0')
      const day = String(dateObj.getDate()).padStart(2, '0')
      const hours = String(dateObj.getHours()).padStart(2, '0')
      const minutes = String(dateObj.getMinutes()).padStart(2, '0')
      // const seconds = String(dateObj.getSeconds()).padStart(2, '0')
      // return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 获取行key
    getRowKey(row) {
      return row.orderSn
    },

    // 展开行变化
    handleExpandChange(row, expandedRows) {
      this.expandedRows = expandedRows.map(r => r.orderSn)
    },



    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png'
    },

    // 确认入库
    handleConfirmPutIn(row) {
      this.$confirm('确认商品已入库？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          orderSn: row.orderSn
        }
        confirmPutIn(params).then(() => {
          this.$message.success('确认入库成功')
          this.getPageData()
        }).catch(() => {
          // API调用失败，错误信息已在拦截器中处理
        })
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 删除退款记录
    handleDelete(row) {
      this.$confirm('确定要删除这条退款记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          idList: [row.orderSn]
        }
        this.deleteOrdRefund(params).then(() => {
          this.$message.success('删除成功')
          this.getPageData()
        })
      }).catch(() => {
        // 用户取消删除
      })
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.demo-form-inline {
  margin-bottom: 10px;
}

.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}

/* 订单项展开区域样式 */
.order-items-section {
  background-color: #f8f9fa;
  border-radius: 6px;
}

.order-items-section h4 {
  color: #409EFF;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

.price {
  color: #E6A23C;
  font-weight: bold;
}

.express-no {
  color: #67C23A;
  font-weight: bold;
  background-color: #F0F9FF;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #67C23A;
}

.no-express {
  color: #909399;
  font-style: italic;
}

.clickable-express {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-express:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>
