package com.my.crossborder.controller;


import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_refund.ApplyPutInDTO;
import com.my.crossborder.controller.dto.ord_refund.ApplyRefundDTO;
import com.my.crossborder.controller.dto.ord_refund.ConfirmPutInDTO;
import com.my.crossborder.controller.dto.ord_refund.OrdRefundDeleteDTO;
import com.my.crossborder.controller.dto.ord_refund.OrdRefundPageDTO;
import com.my.crossborder.controller.dto.ord_refund.RefundResultDTO;
import com.my.crossborder.controller.dto.ord_refund.WeeklySettlementStatsDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.ord_refund.OrdRefundPageVO;
import com.my.crossborder.controller.vo.ord_refund.WeeklySettlementStatsVO;
import com.my.crossborder.controller.vo.ord_refund_log.OrdRefundLogPageVO;
import com.my.crossborder.service.OrdRefundLogService;
import com.my.crossborder.service.OrdRefundService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 退款表 
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/api/ord-refund")
@RequiredArgsConstructor
public class OrdRefundController {

    private final OrdRefundService ordRefundService;
    private final OrdRefundLogService ordRefundLogService;

    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<OrdRefundPageVO>> page(OrdRefundPageDTO pageDTO) {
        Page<OrdRefundPageVO> page = this.ordRefundService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("ord-refund:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody OrdRefundDeleteDTO deleteDTO) {
    	this.ordRefundService.delete(deleteDTO);
		return StdResp.success();
    }

    /**
     * 申请退款
     */
    @SaCheckPermission("ord-refund:apply")
    @PostMapping("/apply-refund")
    public StdResp<?> applyRefund(@Valid @RequestBody ApplyRefundDTO applyRefundDTO) {
        this.ordRefundService.applyRefund(applyRefundDTO);
        return StdResp.success();
    }

    /**
     * 申请入库
     */
    @SaCheckPermission("ord-refund:apply")
    @PostMapping("/apply-put-in")
    public StdResp<?> applyPutIn(@Valid @RequestBody ApplyPutInDTO applyPutInDTO) {
        this.ordRefundService.applyPutIn(applyPutInDTO);
        return StdResp.success();
    }

    /**
     * 退款结果
     */
    @SaCheckPermission("ord-refund:result")
    @PostMapping("/refund-result")
    public StdResp<?> refundResult(@Valid @RequestBody RefundResultDTO refundResultDTO) {
        this.ordRefundService.refundResult(refundResultDTO);
        return StdResp.success();
    }

    /**
     * 确认已入库
     */
    @SaCheckPermission("ord-refund:confirm")
    @PostMapping("/confirm-put-in")
    public StdResp<?> confirmPutIn(@Valid @RequestBody ConfirmPutInDTO confirmPutInDTO) {
        this.ordRefundService.confirmPutIn(confirmPutInDTO);
        return StdResp.success();
    }

    /**
     * 查询退款日志
     */
    @GetMapping("/logs")
    public StdResp<List<OrdRefundLogPageVO>> logs(@RequestParam String orderSn) {
        List<OrdRefundLogPageVO> logs = this.ordRefundLogService.listByOrderSn(orderSn);
        return StdResp.success(logs);
    }

    /**
     * 员工周结算统计
     */
//    @SaCheckPermission("ord-refund:view")
    @GetMapping("/weekly-settlement-stats")
    public StdResp<List<WeeklySettlementStatsVO>> weeklySettlementStats(@Valid WeeklySettlementStatsDTO dto) {
        List<WeeklySettlementStatsVO> stats = this.ordRefundService.getWeeklySettlementStats(dto);
        return StdResp.success(stats);
    }

}
