<template>
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h5>已入库重复采购</h5>
          </div>
          <div class="card-body text-center">
            <h3>功能开发中</h3>
            <p>该功能正在开发中，敬请期待。</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OrdWarehousedRepurchase',
  data() {
    return {
      // 数据字段
    }
  },
  mounted() {
    // 组件挂载后的操作
  },
  methods: {
    // 方法
  }
}
</script>

<style scoped>
.card {
  margin-top: 20px;
}
</style>
