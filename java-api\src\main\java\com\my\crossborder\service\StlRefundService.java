package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.StlRefund;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.stl_refund.StlRefundInsertDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundPageDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundUpdateDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundDeleteDTO;
import com.my.crossborder.controller.vo.stl_refund.StlRefundDetailVO;
import com.my.crossborder.controller.vo.stl_refund.StlRefundPageVO;

/**
 * 结算_退款结算表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface StlRefundService extends IService<StlRefund> {

	/**
	 * 新增
	 */
	void insert(StlRefundInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(StlRefundUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	StlRefundDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<StlRefundPageVO> page(StlRefundPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(StlRefundDeleteDTO deleteDTO);

	/**
	 * 根据周期查询退款结算详情
	 * @param refundUserId 用户ID
	 * @param refundYear 年份
	 * @param refundMonth 月份
	 * @param refundWeek 周次
	 * @return 结算详情
	 */
	StlRefundDetailVO getByWeek(Integer refundUserId, Integer refundYear, Integer refundMonth, Integer refundWeek);

}
