package com.my.crossborder.forest.erp990.interceptor;

import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import com.google.common.collect.Maps;
import com.my.crossborder.controller.vo.sys_param.SysParamVO;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.forest.BaseOnError;
import com.my.crossborder.forest.erp990.Erp990AuthClient;
import com.my.crossborder.forest.erp990.context.Erp990Context;
import com.my.crossborder.forest.erp990.dto.LoginDTO;
import com.my.crossborder.forest.erp990.onerror.Erp990OnError;
import com.my.crossborder.forest.erp990.vo.CaptchaimageVO;
import com.my.crossborder.forest.erp990.vo.LoginVO;
import com.my.crossborder.forest.siliconflow.SiliconflowClient;
import com.my.crossborder.forest.siliconflow.dto.ChatCompletionsDTO;
import com.my.crossborder.forest.siliconflow.dto.component.ImageMessage;
import com.my.crossborder.forest.siliconflow.dto.component.Message;
import com.my.crossborder.forest.siliconflow.onerrror.SiliconflowOnError;
import com.my.crossborder.forest.siliconflow.vo.ChatCompletionsVO;
import com.my.crossborder.service.SysParamService;

import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 需要认证的接口，请求拦截器
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("rawtypes") 
@Slf4j
public class Erp990TokenInterceptor<T> implements Interceptor<T> {

	private static final String PROFILE_DEV = "dev";
	private static final String PROFILE_LOCAL = "local";
	private static final String HEAD_AUTHORIZATION = "Authorization";
	private static final String HEAD_USER_AGENT = "User-Agent";
	private static final String UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36";
	
	private final Erp990AuthClient erp990AuthClient;
	private final SiliconflowClient siliconflowClient;
	private final SysParamService sysParamService;
	
	// 缓存每个sys_erp_account的username对应的token
	private Map<String, String> tokenMap;  	// "比例：key=>zhangsan, value=>eyJhbGciOi.eyX2tleSI....."
	
	
	/** 初始化Map */
	@PostConstruct
	public void init() {
		this.tokenMap = Maps.newHashMap();
	}

	
	/**
     * 在发送请求前, 加上默认的header
     * @Param req 
     */
    @Override
    public boolean beforeExecute(ForestRequest req) {
    	// 调试模式：所有请求使用统一token
    	SysParamVO sysParamVO = this.sysParamService.get();
    	String activeProfile = SpringUtil.getActiveProfile();
    	if (PROFILE_DEV.equals(activeProfile)
    		|| PROFILE_LOCAL.equals(activeProfile)) {
    		this.tokenMap.put(Erp990Context.getUsername(), sysParamVO.getDebugErp990Token());
    	}

    	String username = Erp990Context.getUsername();
    	String token = this.tokenMap.get(username);
    	if (StringUtils.isBlank(token)) {
    		this.refreshToken();
    	}
        req.addHeader(HEAD_AUTHORIZATION, this.authorization(token));  
        req.addHeader(HEAD_USER_AGENT, UA);  
        
        return true;  
    }
    
    // FIXME 不管erp接口成功或者失败，都要写日志. 写到拦截器里
    
    /**
     * 登录获取token
     */
    public void refreshToken() {
    	// 如果破解失败，继续重试
    	boolean loginSuccess = false;
    	int tryCount = 0;
    	do {
    		// 获取base64编码 
        	CaptchaimageVO captchaImageVO = this.captchaImage();
    		String picBase64 = captchaImageVO.getImgBase64();
    		String picUuid = captchaImageVO.getUuid();
        	
        	// 破解验证码
        	ChatCompletionsVO chatCompletionsVO = this.decryptCaptcha(picBase64);
    		String captcha = chatCompletionsVO.getChoices().get(0).getMessage().getContent();
    		
    		// 禾宸登录
    		LoginVO loginVO = this.login(picUuid, captcha);
    		if (loginVO.isSuccess()) {
    			String username = Erp990Context.getUsername();
				String token = loginVO.getToken();
				this.tokenMap.put(username, token);
    			loginSuccess = true;
    		}
    		// 最多重试5次
    		if (tryCount++ > 5) {
    			log.error("[{}]连续5次获取token失败!", Erp990Context.getUsername());
    			break;
    		}
    	} while(!loginSuccess);
    	
    }


    /**
     * 获取验证码
     * @param onError
     * @return
     */
	private CaptchaimageVO captchaImage() {
		BaseOnError onError = new Erp990OnError();
		CaptchaimageVO vo = this.erp990AuthClient.captchaImage(onError);
		BusinessException.when(onError.isError(), onError.getErrMsg());
		BusinessException.when(!vo.isSuccess(), vo.getMsg());
		return vo;
	}

    /**
     * 破解验证码图片
     * @param picBase64 
     * @return
     */
	private ChatCompletionsVO decryptCaptcha(String picBase64) {
		// 系统参数
		SysParamVO sysParamVO = this.sysParamService.get();
		String model = sysParamVO.getAiModel();
		String aiApiKey = sysParamVO.getAiApiKey();
		String aiUserPrompt = sysParamVO.getAiUserPrompt();
		// DTO参数		
		List<Message> messages = Lists.newArrayList(
			ImageMessage.user(picBase64, aiUserPrompt)
		);
		ChatCompletionsDTO chatCompletionsDTO = ChatCompletionsDTO.builder()
				.model(model)
				.messages(messages)
				.build();
		BaseOnError onError = new SiliconflowOnError();
		ChatCompletionsVO vo = this.siliconflowClient.chatCompletions(chatCompletionsDTO, aiApiKey, onError);
		BusinessException.when(onError.isError(), onError.getErrMsg());
		return vo;
	}
	
    /**
     * 登录
     * @param picUuid
     * @param captcha
     * @return
     */
	private LoginVO login(String picUuid, String captcha) {
		BaseOnError onError = new Erp990OnError();
		LoginDTO loginDTO = LoginDTO.builder()
				.username(Erp990Context.getUsername())
				.password(Erp990Context.getPassword())
				.uuid(picUuid)
				.code(captcha)
				.build();
		LoginVO vo = this.erp990AuthClient.login(loginDTO, onError);
		BusinessException.when(onError.isError(), onError.getErrMsg());
		return vo;
	}

    /**
     * 返回 Authorization的值
     * @return
     */
    private String authorization(String token) {
    	return "Bearer " + token;
    }
    
}

