package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemDeleteDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemInsertDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemPageDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_dict_item.SysDictItemDetailVO;
import com.my.crossborder.controller.vo.sys_dict_item.SysDictItemPageVO;
import com.my.crossborder.service.SysDictItemService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 数据字典-字典项表 
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@RestController
@RequestMapping("/api/sys-dict-item")
@RequiredArgsConstructor
public class SysDictItemController {

    private final SysDictItemService sysDictItemService;

    /**
    * 新增
    */
    @SaCheckPermission("sys-dict:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysDictItemInsertDTO insertDTO) {
    	this.sysDictItemService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("sys-dict:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody SysDictItemUpdateDTO updateDTO) {
    	this.sysDictItemService.update(updateDTO);
    	return StdResp.success();
    }

    /**
    * 根据联合主键查询
    * @param categoryId 字典类别id
    * @param itemValue 字典项值
    */
    @GetMapping("/{categoryId}/{itemValue}")
    public StdResp<SysDictItemDetailVO> detail(@PathVariable String categoryId, @PathVariable String itemValue) {
    	return StdResp.success(this.sysDictItemService.detail(categoryId, itemValue));
    }

    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysDictItemPageVO>> page(SysDictItemPageDTO pageDTO) {
        Page<SysDictItemPageVO> page = this.sysDictItemService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("sys-dict:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysDictItemDeleteDTO deleteDTO) {
    	this.sysDictItemService.delete(deleteDTO);
		return StdResp.success();
    }
    
}