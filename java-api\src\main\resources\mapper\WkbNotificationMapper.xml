<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.WkbNotificationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.WkbNotification">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="publish_time" property="publishTime" />
        <result column="publish_user_id" property="publishUserId" />
        <result column="receive_role_ids" property="receiveRoleIds" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, title, content, publish_time, publish_user_id, receive_role_ids
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.wkb_notification.WkbNotificationPageVO">
		SELECT
			id, type, title, content, publish_time, publish_user_id, receive_role_ids
		FROM
			wkb_notification AS t1
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="type != null and type != ''">
	           	AND t1.type = #{type}
            </if>
	        <if test="title != null and title != ''">
	           	AND t1.title = #{title}
            </if>
	        <if test="content != null and content != ''">
	           	AND t1.content = #{content}
            </if>
	        <if test="publishTime != null and publishTime != ''">
	           	AND t1.publish_time = #{publishTime}
            </if>
	        <if test="publishUserId != null and publishUserId != ''">
	           	AND t1.publish_user_id = #{publishUserId}
            </if>
        </where>
    </select>

</mapper>
