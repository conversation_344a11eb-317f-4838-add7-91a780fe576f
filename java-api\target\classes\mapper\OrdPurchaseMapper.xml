<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdPurchaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.OrdPurchase">
        <id column="order_sn" property="orderSn" />
        <result column="total_price" property="totalPrice" />
        <result column="income_amount" property="incomeAmount" />
        <result column="expect_weight" property="expectWeight" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        order_sn, total_price, income_amount, expect_weight, create_user_id, create_time, update_time
    </sql>

	<!-- 分页 -->
    <select id="page" resultType="com.my.crossborder.controller.vo.ord_purchase.OrdPurchasePageVO">
		SELECT
			t2.order_id,
			t2.order_sn,
			t2.order_states,
			t2.shop_id,
			t2.shop_name,
			t2.create_time,
			t2.total_price,
			t1.income_amount,
			t1.expect_weight,
			t2.out_time AS outTime,
			t2.charge_weight,
			t2.stick_fee,
			t2.in_shopee_fee,
			t2.unit_fee,
			t1.create_user_id,
			t1.update_time,
			IFNULL(tRate.exchange_rate, 1) AS exchange_rate,
			tPurchaseSum.total_purchase_amount,
			-- 计算实际利润：入账金额 × 台湾提款手续费比例 ÷ 当日汇率 - 实际重量 × 每吨运费 - 贴标费 - 进店费 - 采购总金额
			CASE
				WHEN t1.income_amount IS NOT NULL
					AND t2.charge_weight IS NOT NULL
					AND t2.unit_fee IS NOT NULL
					AND t2.stick_fee IS NOT NULL
					AND t2.in_shopee_fee IS NOT NULL
					AND tPurchaseSum.total_purchase_amount IS NOT NULL
					AND tParam.v IS NOT NULL
				THEN (t1.income_amount * IFNULL(tParam.v, 1)) / IFNULL(tRate.exchange_rate, 1)
					- (t2.charge_weight * (t2.unit_fee / t2.charge_weight))
					- t2.stick_fee
					- t2.in_shopee_fee
					- tPurchaseSum.total_purchase_amount
				ELSE NULL
			END AS actual_profit
		FROM
			ord_purchase AS t1
		RIGHT JOIN erp_order AS t2 ON t1.order_sn = t2.order_sn
		LEFT JOIN sys_exchange_rate tRate ON tRate.day = date(t2.create_time)
		LEFT JOIN sys_param tParam ON tParam.k = 'taiwanWithdrawFeeRatio'
		LEFT JOIN (
			SELECT
				eo.order_sn,
				SUM(opi.purchase_amount) AS total_purchase_amount
			FROM ord_purchase_item opi
			INNER JOIN erp_order_item eoi ON opi.order_item_id = eoi.id
			INNER JOIN erp_order eo ON eoi.order_id = eo.order_id
			GROUP BY eo.order_sn
		) tPurchaseSum ON tPurchaseSum.order_sn = t2.order_sn
		<where>
        	1=1
        	<if test="orderStateList != null and orderStateList.size() > 0">
	        	AND t2.order_states IN
	        	<foreach collection="orderStateList" item="orderState" open="(" separator="," close=")">
	        		#{orderState}
	        	</foreach>
        	</if>
	        <if test="shopIds != null and shopIds.size() > 0">
	           	AND t2.shop_id IN
	           	<foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
	           		#{shopId}
	           	</foreach>
            </if>
	        <if test="orderSn != null and orderSn != ''">
	           	AND t2.order_sn = #{orderSn}
            </if>
	        <if test="purchaseChannel != null and purchaseChannel != ''">
	           	AND EXISTS (
	           		SELECT 1 FROM ord_purchase_item opi
	           		INNER JOIN erp_order_item eoi ON opi.order_item_id = eoi.id
	           		INNER JOIN erp_order eo ON eoi.order_id = eo.order_id
	           		WHERE eo.order_sn = t1.order_sn
	           		AND opi.purchase_channel = #{purchaseChannel}
	           	)
            </if>
	        <if test="purchaseUserIdList != null and purchaseUserIdList.size() > 0">
	           	AND EXISTS (
	           		SELECT 1 FROM ord_purchase_item opi
	           		INNER JOIN erp_order_item eoi ON opi.order_item_id = eoi.id
	           		INNER JOIN erp_order eo ON eoi.order_id = eo.order_id
	           		WHERE eo.order_sn = t1.order_sn
	           		AND opi.purchase_user_id IN
	           		<foreach collection="purchaseUserIdList" item="userId" open="(" separator="," close=")">
	           			#{userId}
	           		</foreach>
	           	)
            </if>
	        <if test="purchaseDateStart != null">
	           	AND EXISTS (
	           		SELECT 1 FROM ord_purchase_item opi
	           		INNER JOIN erp_order_item eoi ON opi.order_item_id = eoi.id
	           		INNER JOIN erp_order eo ON eoi.order_id = eo.order_id
	           		WHERE eo.order_sn = t1.order_sn
	           		AND opi.purchase_date &gt;= #{purchaseDateStart}
	           	)
            </if>
	        <if test="purchaseDateEnd != null">
	           	AND EXISTS (
	           		SELECT 1 FROM ord_purchase_item opi
	           		INNER JOIN erp_order_item eoi ON opi.order_item_id = eoi.id
	           		INNER JOIN erp_order eo ON eoi.order_id = eo.order_id
	           		WHERE eo.order_sn = t1.order_sn
	           		AND opi.purchase_date &lt;= #{purchaseDateEnd}
	           	)
            </if>
            <if test="actualProfitLessThanZero != null and actualProfitLessThanZero == true">
            	AND (
            		CASE
						WHEN t1.income_amount IS NOT NULL
							AND t2.charge_weight IS NOT NULL
							AND t2.unit_fee IS NOT NULL
							AND t2.stick_fee IS NOT NULL
							AND t2.in_shopee_fee IS NOT NULL
							AND tPurchaseSum.total_purchase_amount IS NOT NULL
							AND tParam.v IS NOT NULL
						THEN (t1.income_amount * IFNULL(tParam.v, 1)) / IFNULL(tRate.exchange_rate, 1)
							- (t2.charge_weight * (t2.unit_fee / t2.charge_weight))
							- t2.stick_fee
							- t2.in_shopee_fee
							- tPurchaseSum.total_purchase_amount
						ELSE NULL
					END
            	) &lt; 0
            </if>
            <if test="actualProfitLessThanZero != null and actualProfitLessThanZero == false">
            	AND (
            		CASE
						WHEN t1.income_amount IS NOT NULL
							AND t2.charge_weight IS NOT NULL
							AND t2.unit_fee IS NOT NULL
							AND t2.stick_fee IS NOT NULL
							AND t2.in_shopee_fee IS NOT NULL
							AND tPurchaseSum.total_purchase_amount IS NOT NULL
							AND tParam.v IS NOT NULL
						THEN (t1.income_amount * IFNULL(tParam.v, 1)) / IFNULL(tRate.exchange_rate, 1)
							- (t2.charge_weight * (t2.unit_fee / t2.charge_weight))
							- t2.stick_fee
							- t2.in_shopee_fee
							- tPurchaseSum.total_purchase_amount
						ELSE NULL
					END
            	) &gt;= 0
            </if>
        </where>
		ORDER BY t2.create_time DESC
    </select>

	<!-- 批量查询订单项 -->
	<select id="selectOrderItems" resultType="com.my.crossborder.controller.vo.ord_purchase.OrdPurchaseItemWithExpressVO">
		SELECT
			t1.id,
			t1.order_id AS orderId,
			t1.item_name AS itemName,
			t1.item_image AS itemImage,
			t1.item_price AS itemPrice,
			t1.amount,
			t1.item_model_name AS itemModelName,
			t1.item_model_sku AS itemModelSku,
			t2.express_no AS expressNo,
			t2.expressin_flag AS expressinFlag,
			t2.put_in_time AS putInTime,
			tPurchaseItem.purchase_channel AS purchaseChannel,
			tPurchaseItem.purchase_amount AS purchaseAmount,
			tPurchaseItem.purchase_date AS purchaseDate,
			tPurchaseItem.purchase_user_id AS purchaseUserId,
		    CASE
		        WHEN tPurchaseItem.purchase_channel NOT IN (2, 3) THEN NULL
		        WHEN tSet.id IS NULL THEN NULL
		        ELSE 1
		    END AS settlement_flag
		FROM
			erp_order_item t1
			LEFT JOIN erp_order_item_express t2 ON t2.order_item_id = t1.id
			LEFT JOIN ord_purchase_item tPurchaseItem ON tPurchaseItem.order_item_id = t1.id
			LEFT JOIN stl_purchase tSet ON tPurchaseItem.purchase_date = tSet.purchase_date AND tSet.purchase_user_id = tPurchaseItem.purchase_user_id
		WHERE
			t1.order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
	</select>

</mapper>
