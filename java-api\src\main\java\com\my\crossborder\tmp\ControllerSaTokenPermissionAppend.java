package com.my.crossborder.tmp;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 批量添加SaToken权限控制注解到Controller方法
 * 
 * 使用此工具前，确保项目中已添加SaToken依赖，并在Controller类中导入:
 * import cn.dev33.satoken.annotation.SaCheckPermission;
 * 
 * 使用说明:
 * 1. 直接运行此类的main方法
 * 2. 工具将自动扫描com.my.crossborder.controller包下所有Controller类
 * 3. 提取@RequestMapping的路径作为menuUrl
 * 4. 为@PostMapping和@PutMapping和@DeletetMapping方法添加@SaCheckPermission("menuUrl:方法名")
 * 5. 为@GetMapping方法添加@SaCheckPermission("menuUrl:view")
 * 
 * 示例:
 * 对于SysMenuController的方法:
 *   @PostMapping
 *   public StdResp<?> insert(...) {...}
 * 
 * 将被修改为:
 *   @SaCheckPermission("sys-menu:insert")
 *   @PostMapping
 *   public StdResp<?> insert(...) {...}
 *
 * <AUTHOR>
 */
@Slf4j
public class ControllerSaTokenPermissionAppend {

    // 控制器包路径
    private static final String CONTROLLER_PACKAGE_PATH = "src/main/java/com/my/crossborder/controller";
    
    // 正则表达式模式
    private static final Pattern REQUEST_MAPPING_PATTERN = Pattern.compile("@RequestMapping\\(\\s*\"?([^\"]+)\"?\\s*\\)");
    private static final Pattern POST_MAPPING_PATTERN = Pattern.compile("(\\s+)@PostMapping[\\s\\S]*?public[\\s\\S]*?\\b(\\w+)\\([^)]*\\)");
    private static final Pattern PUT_MAPPING_PATTERN = Pattern.compile("(\\s+)@PutMapping[\\s\\S]*?public[\\s\\S]*?\\b(\\w+)\\([^)]*\\)");
    private static final Pattern DELETE_MAPPING_PATTERN = Pattern.compile("(\\s+)@DeleteMapping[\\s\\S]*?public[\\s\\S]*?\\b(\\w+)\\([^)]*\\)");
    private static final Pattern GET_MAPPING_PATTERN = Pattern.compile("(\\s+)@GetMapping[\\s\\S]*?public[\\s\\S]*?\\b(\\w+)\\([^)]*\\)");
    
    
    /**
     * 处理所有控制器
     */
    public static void processControllers() throws IOException {
        File controllerDir = new File(CONTROLLER_PACKAGE_PATH);
        
        if (!controllerDir.exists() || !controllerDir.isDirectory()) {
            throw new IOException("控制器目录不存在: " + CONTROLLER_PACKAGE_PATH);
        }
        
        // 递归查找所有Java文件
        List<File> controllerFiles = findJavaFiles(controllerDir);
        
        for (File file : controllerFiles) {
            processControllerFile(file);
        }
    }
    
    /**
     * 处理单个控制器文件
     */
    private static void processControllerFile(File file) throws IOException {
        Path filePath = file.toPath();
        List<String> lines = Files.readAllLines(filePath, StandardCharsets.UTF_8);
        String content = String.join("\n", lines);
        
        // 添加导入语句
        content = addImportStatement(content);
        
        // 提取RequestMapping路径
        String menuUrl = extractMenuUrl(content);
        if (menuUrl == null) {
            log.info("跳过 " + file.getName() + " - 未找到RequestMapping注解");
            return;
        }
        
        // 处理@PostMapping和@PutMapping
        content = addPermissionToPostPutDelete(content, menuUrl, file);
        
        // 处理@GetMapping
        content = addPermissionToGet(content, menuUrl);
        
        // 写回文件
        Files.write(filePath, content.getBytes(StandardCharsets.UTF_8));
        //log.info("已处理: {}, {}", file.getName(), menuUrl);
    }
    
    /**
     * 添加导入语句
     */
    private static String addImportStatement(String content) {
        // 检查是否已经导入了SaCheckPermission
        if (content.contains("import cn.dev33.satoken.annotation.SaCheckPermission;")) {
            return content;
        }
        
        // 查找最后一个import语句的位置
        Pattern importPattern = Pattern.compile("import\\s+[^;]+;");
        Matcher importMatcher = importPattern.matcher(content);
        
        int lastImportEnd = 0;
        while (importMatcher.find()) {
            lastImportEnd = importMatcher.end();
        }
        
        if (lastImportEnd > 0) {
            // 在最后一个import后添加新的import
            return content.substring(0, lastImportEnd) + 
                   "\nimport cn.dev33.satoken.annotation.SaCheckPermission;" + 
                   content.substring(lastImportEnd);
        }
        
        return content;
    }
    
    /**
     * 提取菜单URL
     */
    private static String extractMenuUrl(String content) {
        Matcher matcher = REQUEST_MAPPING_PATTERN.matcher(content);
        if (matcher.find()) {
            String fullPath = matcher.group(1);
            // 移除前导/和/api/前缀
            fullPath = fullPath.replaceAll("^/+", "").replaceAll("^api/", "");
            // 获取最后一部分
            String[] parts = fullPath.split("/");
            return parts[parts.length - 1];
        }
        return null;
    }
    
    // TODO 新的模块记得用这个自动生成权限
    /**
     * 为POST和PUT映射添加权限注解
     * @param file 
     */
    private static String addPermissionToPostPutDelete(String content, String menuUrl, File file) {
    	List<String> annoationList = Lists.newLinkedList();
        // 处理@PostMapping
        StringBuffer sb = new StringBuffer();
        Matcher postMatcher = POST_MAPPING_PATTERN.matcher(content);
        while (postMatcher.find()) {
            // 检查前面是否已有@SaCheckPermission注解
            String beforeMatch = content.substring(Math.max(0, postMatcher.start() - 100), postMatcher.start());
            if (beforeMatch.contains("@SaCheckPermission")) {
                continue; // 跳过已有注解的方法
            }
            
            String indent = postMatcher.group(1);
            String methodName = postMatcher.group(2);
            String insert = "@SaCheckPermission(\"" + menuUrl + ":" + methodName + "\")";
			String replacement = indent + insert + postMatcher.group(0);
            postMatcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            annoationList.add(menuUrl + ":" + methodName);
        }
        postMatcher.appendTail(sb);
        content = sb.toString();
        
        // 处理@PutMapping
        sb = new StringBuffer();
        Matcher putMatcher = PUT_MAPPING_PATTERN.matcher(content);
        while (putMatcher.find()) {
            // 检查前面是否已有@SaCheckPermission注解
            String beforeMatch = content.substring(Math.max(0, putMatcher.start() - 100), putMatcher.start());
            if (beforeMatch.contains("@SaCheckPermission")) {
                continue; // 跳过已有注解的方法
            }
            
            String indent = putMatcher.group(1);
            String methodName = putMatcher.group(2);
            String insert = "@SaCheckPermission(\"" + menuUrl + ":" + methodName + "\")";
			String replacement = indent + insert + putMatcher.group(0);
            putMatcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            annoationList.add(menuUrl + ":" + methodName);
        }
        putMatcher.appendTail(sb);
        content = sb.toString();
        
        // 处理@PutMapping
        sb = new StringBuffer();
        Matcher deleteMatcher = DELETE_MAPPING_PATTERN.matcher(content);
        while (deleteMatcher.find()) {
        	// 检查前面是否已有@SaCheckPermission注解
        	String beforeMatch = content.substring(Math.max(0, deleteMatcher.start() - 100), deleteMatcher.start());
        	if (beforeMatch.contains("@SaCheckPermission")) {
        		continue; // 跳过已有注解的方法
        	}
        	
        	String indent = deleteMatcher.group(1);
        	String methodName = deleteMatcher.group(2);
        	String insert = "@SaCheckPermission(\"" + menuUrl + ":" + methodName + "\")";
			String replacement = indent + insert + deleteMatcher.group(0);
        	deleteMatcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        	annoationList.add(menuUrl + ":" + methodName);
        }
        deleteMatcher.appendTail(sb);
        
        log.info("已处理: {} {}", file.getName(), CollectionUtil.join(annoationList, "、"));
        return sb.toString();
    }
    
    /**
     * 为GET映射添加权限注解
     */
    private static String addPermissionToGet(String content, String menuUrl) {
        StringBuffer sb = new StringBuffer();
        Matcher getMatcher = GET_MAPPING_PATTERN.matcher(content);
        
        while (getMatcher.find()) {
            // 检查前面是否已有@SaCheckPermission注解
            String beforeMatch = content.substring(Math.max(0, getMatcher.start() - 100), getMatcher.start());
            if (beforeMatch.contains("@SaCheckPermission")) {
                continue; // 跳过已有注解的方法
            }
            
            String indent = getMatcher.group(1);
            String replacement = indent + "@SaCheckPermission(\"" + menuUrl + ":view\")" + getMatcher.group(0);
            getMatcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        getMatcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 递归查找所有Java文件
     */
    private static List<File> findJavaFiles(File dir) {
        List<File> result = new ArrayList<>();
        File[] files = dir.listFiles();
        
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    result.addAll(findJavaFiles(file));
                } else if (file.getName().endsWith(".java") && file.getName().contains("Controller")) {
                    result.add(file);
                }
            }
        }
        
        return result;
    }
} 