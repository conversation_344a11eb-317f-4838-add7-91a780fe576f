<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysShopPartnerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysShopPartner">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="partner_user_id" property="partnerUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, partner_user_id
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_shop_partner.SysShopPartnerPageVO">
		SELECT
			id, shop_id, partner_user_id
		FROM
			sys_shop_partner AS t1
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="shopId != null and shopId != ''">
	           	AND t1.shop_id = #{shopId}
            </if>
	        <if test="partnerUserId != null and partnerUserId != ''">
	           	AND t1.partner_user_id = #{partnerUserId}
            </if>
        </where>
    </select>

</mapper>
