package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_param.SysParamUpdateDTO;
import com.my.crossborder.controller.vo.sys_param.SysParamVO;
import com.my.crossborder.mybatis.entity.SysParam;

/**
 *  服务类
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface SysParamService extends IService<SysParam> {


	/**
	 * 修改
	 */
	void update(SysParamUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysParamVO get();

}
