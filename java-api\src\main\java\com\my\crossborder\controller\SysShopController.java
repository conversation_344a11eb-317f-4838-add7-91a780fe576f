package com.my.crossborder.controller;

import java.util.List;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_shop.SysShopInsertDTO;
import com.my.crossborder.controller.dto.sys_shop.SysShopPageDTO;
import com.my.crossborder.controller.dto.sys_shop.SysShopUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_shop.SysShopPageVO;
import com.my.crossborder.service.SysShopService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 店铺管理表 
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/api/sys-shop")
@RequiredArgsConstructor
public class SysShopController {

    private final SysShopService sysShopService;


    /**
    * 修改
    */
    @SaCheckPermission("sys-shop:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody SysShopUpdateDTO updateDTO) {
    	this.sysShopService.update(updateDTO);
    	return StdResp.success();
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysShopPageVO>> page(SysShopPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds);
        }

        Page<SysShopPageVO> page = this.sysShopService.page(pageDTO);
        return StdResp.success(page);
    }
    
}
