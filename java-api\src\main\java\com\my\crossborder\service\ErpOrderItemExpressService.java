package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressDeleteDTO;
import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressInsertDTO;
import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressPageDTO;
import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressUpdateDTO;
import com.my.crossborder.controller.vo.erp_order_item_express.ErpOrderItemExpressDetailVO;
import com.my.crossborder.controller.vo.erp_order_item_express.ErpOrderItemExpressPageVO;
import com.my.crossborder.mybatis.entity.ErpOrderItemExpress;

/**
 * 订单项_快递信息 服务类
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface ErpOrderItemExpressService extends IService<ErpOrderItemExpress> {

	/**
	 * 新增
	 */
	void insert(ErpOrderItemExpressInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(ErpOrderItemExpressUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	ErpOrderItemExpressDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<ErpOrderItemExpressPageVO> page(ErpOrderItemExpressPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(ErpOrderItemExpressDeleteDTO deleteDTO);	

}
