package com.my.crossborder.controller.dto.sys_exchange_rate;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 修改_汇率表
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysExchangeRateUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
	@NotNull(message="day不能为空")
    private LocalDate day;

    /**
     * 汇率（CNY/TWD的值，比如：4.0814）
     */
	@NotNull(message="exchangeRate不能为空")
    private BigDecimal exchangeRate;

}
