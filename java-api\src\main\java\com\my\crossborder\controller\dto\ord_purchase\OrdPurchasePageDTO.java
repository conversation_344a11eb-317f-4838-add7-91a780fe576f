package com.my.crossborder.controller.dto.ord_purchase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchasePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_采购订单主表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchasePageDTO
						extends PageDTO<OrdPurchasePageVO>
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺ID列表
     */
    private List<Integer> shopIds;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 采购途径 字典PURCHASE_CHANNEL
     */
    private String purchaseChannel;

    /**
     * 采购人ID列表（多选）
     */
    private List<Integer> purchaseUserIdList;

    /**
     * 采购日期开始
     */
    private LocalDate purchaseDateStart;

    /**
     * 采购日期结束
     */
    private LocalDate purchaseDateEnd;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 订单入账金额
     */
    private BigDecimal incomeAmount;

    /**
     * 预计重量
     */
    private BigDecimal expectWeight;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 实际利润小于0筛选条件
     */
    private Boolean actualProfitLessThanZero;

    /**
     * 订单状态列表（多选）
     */
    private List<String> orderStateList;
}
