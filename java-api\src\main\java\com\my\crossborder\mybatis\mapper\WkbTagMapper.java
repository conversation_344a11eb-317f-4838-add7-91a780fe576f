package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.wkb_tag.WkbTagPageDTO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagPageVO;
import com.my.crossborder.mybatis.entity.WkbTag;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 工作台_订单标签 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface WkbTagMapper extends BaseMapper<WkbTag> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<WkbTagPageVO> page(WkbTagPageDTO pageDTO);
	
}
