package com.my.crossborder.controller.vo.erp_order;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 重新采购订单分页查询返回对象
 * 继承ErpOrderPageVO，并添加重新采购相关字段
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class RepurchaseErpOrderPageVO extends ErpOrderPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 重新采购id
     */
    private Integer id;

    /**
     * 问题描述
     */
    private String issue;

    /**
     * 处理办法 字典参数repurchase_close_way
     */
    private String closeWay;

    /**
     * 处理状态 字典close_status
     */
    private String closeStatus;
    
    /**
     * 录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理时间
     */
    private LocalDateTime closeTime;

    /**
     * 录入人id
     */
    private Integer issueUserId;

    /**
     * 录入人
     */
    private String issueUserName;

    /**
     * 处理人
     */
    private Integer closeUserId;

    /**
     * 处理人
     */
    private String closeUserName;

    /**
     * 确认人
     */
    private String confirmUserName;

}
