package com.my.crossborder.controller.dto.wkb_note;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.wkb_note.WkbNotePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_工作台_工作笔记表
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotePageDTO 
						extends PageDTO<WkbNotePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 笔记内容
     */
    private String content;

    /**
     * 场景
     */
    private String scene;

    /**
     * 场景完成状态：0=待处理，1=已处理
     */
    private Boolean sceneComplete;

    /**
     * 场景完成时间
     */
    private LocalDateTime sceneCompleteTime;

    /**
     * 创建人用户ID
     */
    private Integer createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
