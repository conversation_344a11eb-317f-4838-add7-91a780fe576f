<template>
  <el-dialog title="确认采购状态" top="30vh" :visible.sync="dialogVisible" width="500px" @close="handleDialogClose">
    <div class="purchase-info">
      <p><strong>采购单号：</strong>{{ purchaseData.purchaseNumber }}</p>
      <p><strong>品名：</strong>{{ purchaseData.productName }}</p>
      <p><strong>数量：</strong>{{ purchaseData.quantity }}</p>
      <p><strong>采购总金额：</strong>¥{{ purchaseData.totalAmount }}</p>
      <p><strong>采购途径：</strong>{{ getPurchaseChannelText(purchaseData.purchaseChannel) }}</p>
      <p><strong>采购日期：</strong>{{ purchaseData.purchaseDate }}</p>
      <p><strong>关联订单：</strong>
        <span v-if="purchaseData.orderSnList && purchaseData.orderSnList.length > 0">
          <el-tag v-for="(orderSn, index) in purchaseData.orderSnList"
                  :key="index"
                  type="info"
                  size="small"
                  style="margin-right: 8px; margin-bottom: 4px;">
            {{ orderSn }}
          </el-tag>
        </span>
        <span v-else>无</span>
      </p>
    </div>
    
    <el-form :model="confirmForm" :rules="confirmRules" ref="confirmForm" label-width="120px">
      <el-form-item label="采购状态" prop="status">
        <dict-select 
          v-model="confirmForm.status" 
          category-id="PURCHASE_CENTRALIZED_STATUS" 
          placeholder="请选择采购状态" 
          style="width: 300px;"
        />
      </el-form-item>
    </el-form>
    
    <span slot="footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import DictSelect from '../../components/DictSelect'

export default {
  name: 'OrdPurchaseCentralizedConfirm',
  components: {
    DictSelect
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    purchaseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      confirmForm: {
        status: ''
      },
      confirmRules: {
        status: [
          { required: true, message: '请选择确认状态', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    initForm() {
      this.confirmForm = {
        status: ''
      }
    },
    handleSubmit() {
      this.$refs.confirmForm.validate((valid) => {
        if (valid) {
          // 获取状态文本用于确认提示
          const statusText = this.getStatusText(this.confirmForm.status)

          // this.$confirm(`确定要将采购单【${this.purchaseData.purchaseNumber}】的状态更改为【${statusText}】吗？`, '确认操作', {
          //   confirmButtonText: '确定',
          //   cancelButtonText: '取消',
          //   type: 'warning'
          // }).then(() => {
            this.$emit('submit', this.confirmForm.status)
          // }).catch(() => {
          //   // 用户取消操作
          // })
        }
      })
    },

    // 获取状态文本
    getStatusText(statusValue) {
      // 使用字典服务获取状态文本
      return this.dictLabel('PURCHASE_CENTRALIZED_STATUS', statusValue) || statusValue
    },

    // 获取采购途径文本
    getPurchaseChannelText(channelValue) {
      // 使用字典服务获取采购途径文本
      return this.dictLabel('PURCHASE_CENTRALIZED_CHANNEL', channelValue) || channelValue
    },
    handleCancel() {
      this.dialogVisible = false
    },
    handleDialogClose() {
      this.$refs.confirmForm.resetFields()
    }
  }
}
</script>

<style scoped>
.purchase-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.purchase-info p {
  margin: 8px 0;
  color: #606266;
}
</style>
