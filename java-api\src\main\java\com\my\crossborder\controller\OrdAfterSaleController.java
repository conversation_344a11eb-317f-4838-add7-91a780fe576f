package com.my.crossborder.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleInsertDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSalePageDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleUpdateDTO;
import com.my.crossborder.controller.dto.ord_after_sale.OrdAfterSaleDeleteDTO;
import com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSaleDetailVO;
import com.my.crossborder.controller.vo.ord_after_sale.OrdAfterSalePageVO;
import com.my.crossborder.controller.vo.StdResp;

import com.my.crossborder.service.OrdAfterSaleService;

import cn.dev33.satoken.annotation.SaCheckPermission;

import org.springframework.web.bind.annotation.RestController;

/**
 * 售后表 
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/api/ord-after-sale")
@RequiredArgsConstructor
public class OrdAfterSaleController {

    private final OrdAfterSaleService ordAfterSaleService;

    /**
    * 新增
    */
    @SaCheckPermission("ord-after-sale:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody OrdAfterSaleInsertDTO insertDTO) {
    	this.ordAfterSaleService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("ord-after-sale:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody OrdAfterSaleUpdateDTO updateDTO) {
    	this.ordAfterSaleService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<OrdAfterSaleDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.ordAfterSaleService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<OrdAfterSalePageVO>> page(OrdAfterSalePageDTO pageDTO) {
        Page<OrdAfterSalePageVO> page = this.ordAfterSaleService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("ord-after-sale:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody OrdAfterSaleDeleteDTO deleteDTO) {
    	this.ordAfterSaleService.delete(deleteDTO);
		return StdResp.success();
    }

    /**
     * 确认完成
     * @param id 售后记录ID
     */
    @SaCheckPermission("ord-after-sale:confirm")    
    @PutMapping("/confirm-complete/{id}")
    public StdResp<?> confirmComplete(@PathVariable Integer id) {
        this.ordAfterSaleService.confirmComplete(id);
        return StdResp.success();
    }

}
