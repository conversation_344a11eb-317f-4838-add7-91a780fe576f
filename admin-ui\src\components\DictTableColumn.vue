
<template>
  <el-table-column
    :prop="prop"
    :label="label"
    :width="width"
    :min-width="minWidth"
    :fixed="fixed"
    :sortable="sortable"
    :align="align"
    :header-align="headerAlign"
    :show-overflow-tooltip="showOverflowTooltip"
    :class-name="className"
    :label-class-name="labelClassName">
    <template slot-scope="scope">
      <span v-if="loading">-</span>
      <span v-else-if="scope.row[prop] === null || scope.row[prop] === undefined || scope.row[prop] === ''">{{ emptyText }}</span>
      <el-tag :type="getDictColor(scope.row[prop])" size="small"
        v-else-if="getDictColor(scope.row[prop]) != '' && getDictColor(scope.row[prop]) != null">
        {{ getDictLabel(scope.row[prop]) }}
      </el-tag>
      <span v-else>{{ getDictLabel(scope.row[prop]) }}</span>
    </template>
  </el-table-column>
</template>

<script>
import dictService from '../utils/dictService'

export default {
  name: 'DictTableColumn',
  props: {
    // 字典分类ID
    categoryId: {
      type: String,
      required: true
    },
    // 字段属性名
    prop: {
      type: String,
      required: true
    },
    // 列标题
    label: {
      type: String,
      required: true
    },
    // 列宽度
    width: {
      type: [String, Number],
      default: undefined
    },
    // 最小列宽度
    minWidth: {
      type: [String, Number],
      default: undefined
    },
    // 列是否固定
    fixed: {
      type: [Boolean, String],
      default: false
    },
    // 是否可排序
    sortable: {
      type: [Boolean, String],
      default: false
    },
    // 对齐方式
    align: {
      type: String,
      default: 'center'
    },
    // 表头对齐方式
    headerAlign: {
      type: String,
      default: undefined
    },
    // 是否显示tooltip
    showOverflowTooltip: {
      type: Boolean,
      default: true
    },
    // 列的className
    className: {
      type: String,
      default: undefined
    },
    // 表头的className
    labelClassName: {
      type: String,
      default: undefined
    },
    // 空值时的默认显示文本
    emptyText: {
      type: String,
      default: '-'
    }
  },
  data() {
    return {
      dictItems: [],
      loading: true
    }
  },
  async created() {
    await this.loadDictItems()
  },
  watch: {
    categoryId: {
      handler() {
        this.loadDictItems()
      }
    }
  },
  methods: {
    // 加载字典数据
    async loadDictItems() {
      if (!this.categoryId) {
        this.loading = false
        return
      }

      try {
        this.loading = true
        this.dictItems = await dictService.getDictItems(this.categoryId)
      } catch (error) {
        console.error('加载字典数据失败:', error)
        this.dictItems = []
      } finally {
        this.loading = false
      }
    },
    // 获取字典项颜色
    getDictColor(value) {
      if (value === null || value === undefined || value === '') {
        return "";
      }
      
      const item = this.dictItems.find(item => String(item.value) === String(value))
      return item ? item.color : String(value)
    },
    // 获取字典项标签
    getDictLabel(value) {
      if (value === null || value === undefined || value === '') {
        return this.emptyText
      }

      const item = this.dictItems.find(item => String(item.value) === String(value))
      return item ? item.label : String(value)
    }
  }
}
</script>
