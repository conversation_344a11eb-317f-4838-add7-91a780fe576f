package com.my.crossborder.util;

import static java.util.stream.Collectors.joining;

import java.util.Arrays;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.support.ColumnCache;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

/**
 * lambda字段转换为表格列
 * <AUTHOR>
 */
// 参考：https://blog.csdn.net/qq_45473439/article/details/121322346
// https://blog.csdn.net/u011974797/article/details/130616631
// https://segmentfault.com/a/1190000039657414
@SuppressWarnings("serial")
public class ColumnLambda<T> extends AbstractLambdaWrapper<T, ColumnLambda<T>> {


	@Override
    protected ColumnLambda<T> instance() {
        return null;
    }

    /**
     * 查询表的字段名
     * @param fieldName 实体属性名称
     * @date 2024.06.04 liwenchao
     * @see https://blog.csdn.net/u011974797/article/details/130616631
     * @return
     */
    public final String colName(String fieldName, Class<?> cls) {
		Map<String, ColumnCache> colCache = LambdaUtils.getColumnMap(cls);
		String column = colCache.get(LambdaUtils.formatKey(fieldName)).getColumn();
		return column;
    }
    
    @SafeVarargs
    @Override
    public final String columnsToString(SFunction<T, ?>... columns) {
        return super.columnsToString(columns);
    }
    
    /**
     * description: 实体类属性转数据库字段名+前缀 批量
     *
     * @author: lixiangxiang
     * @param prefix 前缀
     * @param columns 字段
     * @return java.lang.String
     */
    @SafeVarargs
    public final String columnsToStringWithPrefix(String prefix, SFunction<T, ?>... columns) {
        return Arrays.stream(columns).map(i -> columnsToStringWithPrefix(prefix,i)).collect(joining(StringPool.COMMA));
    }

    /**
     * description: 实体类属性转数据库字段名+前缀
     *
     * @author: lixiangxiang
     * @param prefix 前缀
     * @param column 字段
     * @return java.lang.String
     */
    public final String columnsToStringWithPrefix(String prefix, SFunction<T, ?> column) {
        return prefix + StringPool.DOT + super.columnToString(column);
    }

    @SafeVarargs
    @Override
    public final String columnsToString(boolean onlyColumn, SFunction<T, ?>... columns) {
        return super.columnsToString(onlyColumn, columns);
    }

    @Override
    public String columnToString(SFunction<T, ?> column) {
        return super.columnToString(column);
    }

    @Override
    public String columnToString(SFunction<T, ?> column, boolean onlyColumn) {
        return super.columnToString(column, onlyColumn);
    }

}


