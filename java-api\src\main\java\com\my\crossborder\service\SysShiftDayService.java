package com.my.crossborder.service;

import java.time.LocalDate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_shift_day.SysShiftDayDeleteDTO;
import com.my.crossborder.controller.dto.sys_shift_day.SysShiftDayInsertDTO;
import com.my.crossborder.controller.dto.sys_shift_day.SysShiftDayPageDTO;
import com.my.crossborder.controller.dto.sys_shift_day.SysShiftDayUpdateDTO;
import com.my.crossborder.controller.vo.sys_shift_day.SysShiftDayDetailVO;
import com.my.crossborder.controller.vo.sys_shift_day.SysShiftDayPageVO;
import com.my.crossborder.mybatis.entity.SysShiftDay;

/**
 * 排班日期表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface SysShiftDayService extends IService<SysShiftDay> {


	/**
	 * 不存在则新增
	 * @param shiftDay 排班日期
	 * @return 排班日期
	 */
	void createIfAbsense(LocalDate shiftDay);

//	/**
//	 * 检查并清理空的排班主表记录
//	 * @param shiftDay 排班日期
//	 */
//	void checkAndCleanEmptyShift(LocalDate shiftDay);

	/**
	 * 更新店铺数量、实排店铺数量
	 * @param shiftDay 排班日期
	 * @param shopCount 
	 * @param shiftCount 
	 */
	void update(LocalDate shiftDay, Integer shopCount, Integer shiftCount);

}
