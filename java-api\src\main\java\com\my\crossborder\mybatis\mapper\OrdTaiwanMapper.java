package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanPageDTO;
import com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanPageVO;
import com.my.crossborder.mybatis.entity.OrdTaiwan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 台湾上架物品 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface OrdTaiwanMapper extends BaseMapper<OrdTaiwan> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<OrdTaiwanPageVO> page(OrdTaiwanPageDTO pageDTO);
	
}
