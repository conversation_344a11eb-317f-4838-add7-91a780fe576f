<template>
  <div class="dict-example">
    <h2>字典组件使用示例</h2>

    <!-- 方法一：使用 DictTableColumn 组件 -->
    <div class="example-section">
      <h3>方法一：使用 DictTableColumn 组件</h3>
      <p>推荐用法：直接替换普通的 el-table-column，自动处理字典转换</p>

      <el-table :data="tableData" border style="width: 100%">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column prop="pipelineName" label="管道名称" min-width="120"></el-table-column>
        <el-table-column prop="pipelineCode" label="管道编号" min-width="120"></el-table-column>

        <!-- 使用 DictTableColumn 自动转换字典 -->
        <dict-table-column
          prop="pipelineLevel"
          label="管道级别"
          category-id="PIPE_LEVEL"
          width="100">
        </dict-table-column>

        <el-table-column prop="designCompany" label="设计单位" min-width="150"></el-table-column>
      </el-table>
    </div>

    <!-- 方法二：使用 DictTable 组件 -->
    <div class="example-section">
      <h3>方法二：使用 DictTable 组件</h3>
      <p>适用于有多个字典字段的表格，统一配置字典映射</p>

      <dict-table
        :data="tableData"
        :dict-mapping="dictMapping"
        border
        style="width: 100%">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column prop="pipelineName" label="管道名称" min-width="120"></el-table-column>
        <el-table-column prop="pipelineCode" label="管道编号" min-width="120"></el-table-column>

        <!-- 使用转换后的字段名 -->
        <el-table-column prop="pipelineLevelLabel" label="管道级别" width="100"></el-table-column>

        <el-table-column prop="designCompany" label="设计单位" min-width="150"></el-table-column>
      </dict-table>
    </div>

    <!-- 方法三：使用插槽和全局方法 -->
    <div class="example-section">
      <h3>方法三：使用插槽和全局方法</h3>
      <p>灵活用法：在模板中使用字典转换方法</p>

      <el-table :data="tableData" border style="width: 100%">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column prop="pipelineName" label="管道名称" min-width="120"></el-table-column>
        <el-table-column prop="pipelineCode" label="管道编号" min-width="120"></el-table-column>

        <!-- 使用插槽和全局方法 -->
        <el-table-column label="管道级别" width="100">
          <template slot-scope="scope">
            {{ dictLabel('PIPE_LEVEL', scope.row.pipelineLevel) }}
          </template>
        </el-table-column>

        <el-table-column prop="designCompany" label="设计单位" min-width="150"></el-table-column>
      </el-table>
    </div>

    <!-- 方法四：使用过滤器 -->
    <div class="example-section">
      <h3>方法四：使用过滤器</h3>
      <p>简洁用法：直接在表达式中使用过滤器</p>

      <el-table :data="tableData" border style="width: 100%">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column prop="pipelineName" label="管道名称" min-width="120"></el-table-column>
        <el-table-column prop="pipelineCode" label="管道编号" min-width="120"></el-table-column>

        <!-- 使用过滤器 -->
        <el-table-column label="管道级别" width="100">
          <template slot-scope="scope">
            {{ scope.row.pipelineLevel | dictLabel('PIPE_LEVEL') }}
          </template>
        </el-table-column>

        <el-table-column prop="designCompany" label="设计单位" min-width="150"></el-table-column>
      </el-table>
    </div>

    <!-- 代码示例 -->
    <div class="code-section">
      <h3>代码示例</h3>
      <pre><code>{{codeExample}}</code></pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DictExample',
  data() {
    return {
      // 示例数据
      tableData: [
        {
          pipelineName: '主供汽管道',
          pipelineCode: 'PIPE001',
          pipelineLevel: 'GC1',
          designCompany: '中石化工程建设有限公司'
        },
        {
          pipelineName: '回水管道',
          pipelineCode: 'PIPE002',
          pipelineLevel: 'GC2',
          designCompany: '中石油工程建设有限公司'
        },
        {
          pipelineName: '排污管道',
          pipelineCode: 'PIPE003',
          pipelineLevel: 'GC3',
          designCompany: '华润工程有限公司'
        }
      ],

      // 字典映射配置
      dictMapping: {
        pipelineLevel: 'PIPE_LEVEL'
      },

      // 代码示例
      codeExample: `
// 方法一：使用 DictTableColumn 组件
<dict-table-column
  prop="pipelineLevel"
  label="管道级别"
  category-id="PIPE_LEVEL"
  width="100">
</dict-table-column>

// 方法二：使用 DictTable 组件
<dict-table
  :data="tableData"
  :dict-mapping="{ pipelineLevel: 'PIPE_LEVEL' }">
  <el-table-column prop="pipelineLevelLabel" label="管道级别"></el-table-column>
</dict-table>

// 方法三：使用全局方法
<el-table-column label="管道级别">
  <template slot-scope="scope">
    {{ dictLabel('PIPE_LEVEL', scope.row.pipelineLevel) }}
  </template>
</el-table-column>

// 方法四：使用过滤器
<el-table-column label="管道级别">
  <template slot-scope="scope">
    {{ scope.row.pipelineLevel | dictLabel('PIPE_LEVEL') }}
  </template>
</el-table-column>

// JavaScript 中使用
export default {
  async created() {
    // 预加载字典数据
    await this.preloadDicts(['PIPE_LEVEL', 'OTHER_DICT'])

    // 异步获取字典标签
    const label = await this.getDictLabel('PIPE_LEVEL', 'GC1')

    // 同步获取字典标签（仅从缓存）
    const labelSync = this.dictLabel('PIPE_LEVEL', 'GC1')

    // 批量转换表格数据
    const convertedData = await this.convertTableDictFields(this.tableData, {
      pipelineLevel: 'PIPE_LEVEL'
    })
  }
}
      `
    }
  },
  async created() {
    // 预加载示例中用到的字典数据
    await this.preloadDicts(['PIPE_LEVEL'])
  }
}
</script>

<style scoped>
.dict-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
}

.example-section h3 {
  color: #409EFF;
  margin-bottom: 10px;
}

.example-section p {
  color: #666;
  margin-bottom: 15px;
  font-size: 14px;
}

.code-section {
  margin-top: 40px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.code-section pre {
  background-color: #272822;
  color: #f8f8f2;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 13px;
  line-height: 1.4;
}

.code-section code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
