package com.my.crossborder.service;

import java.time.LocalDate;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_shift.SysShiftDeleteDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftInsertDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftPageDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftQuickCopyDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftUpdateDTO;
import com.my.crossborder.controller.vo.sys_shift.MonthShift;
import com.my.crossborder.controller.vo.sys_shift.SysShiftPageVO;
import com.my.crossborder.controller.vo.sys_shift.SysShiftQuickCopyVO;
import com.my.crossborder.mybatis.entity.SysShift;

/**
 * 排班表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
public interface SysShiftService extends IService<SysShift> {

	/**
	 * 新增
	 */
	void insert(SysShiftInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysShiftUpdateDTO updateDTO);

	/**
	 * 删除一条
	 */
	void delete(SysShiftDeleteDTO deleteDTO);

	/**
	 * 分页
	 */
	Page<SysShiftPageVO> page(SysShiftPageDTO pageDTO);

	/**
	 * 根据日期查询排班数据
	 * @param shiftDay 排班日期（格式：YYYY-MM-DD）
	 * @return 排班数据列表
	 */
	List<SysShiftPageVO> listByShiftDay(String shiftDay);

	/**
	 * 根据日期范围查询排班数据
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 排班数据列表
	 */
	List<SysShiftPageVO> listByDateRange(String startDate, String endDate);

	/**
	 * 月度排班
	 * @param userId
	 * @param year
	 * @param month
	 * @return
	 */
	MonthShift monthShift(Integer userId, Integer year, Integer month);

	/**
	 * 如果当天没有排班了，则删除考勤表里的用户数据
	 * @param shiftDay
	 * @param userId
	 */
	void deleteAttendanceWhenShiftNotExists(LocalDate shiftDay, Integer userId);

	/**
	 * 快速复制排班
	 * 将指定日期的排班安排复制到今天往后7个自然日
	 * @param quickCopyDTO 快速复制参数
	 * @return 复制结果
	 */
	SysShiftQuickCopyVO quickCopy(SysShiftQuickCopyDTO quickCopyDTO);

}
