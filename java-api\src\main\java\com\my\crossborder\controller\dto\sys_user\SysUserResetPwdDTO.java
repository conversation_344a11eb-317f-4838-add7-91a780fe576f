package com.my.crossborder.controller.dto.sys_user;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 重置密码
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserResetPwdDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@NotNull(message="userId不能为空")
    private Integer userId;

}
