import { dictCategoryItems } from '../api/SysDictItem'
import { getStore, setStore } from './util'

/**
 * 字典服务类
 * 用于管理字典数据的加载、缓存和转换
 */
class DictService {
  constructor() {
    // 字典数据缓存
    this.dictCache = new Map()
    // 加载状态缓存，避免重复请求
    this.loadingCache = new Map()
    // 缓存过期时间（毫秒），默认30分钟
    this.cacheExpireTime = 30 * 60 * 1000
  }

  /**
   * 获取字典数据
   * @param {string} categoryId - 字典分类ID
   * @returns {Promise<Array>} 字典项数组
   */
  async getDictItems(categoryId) {
    if (!categoryId) {
      return []
    }

    // 检查缓存
    const cached = this.getCachedDict(categoryId)
    if (cached) {
      return cached
    }

    // 检查是否正在加载
    if (this.loadingCache.has(categoryId)) {
      return this.loadingCache.get(categoryId)
    }

    // 创建加载Promise
    const loadPromise = this.loadDictFromApi(categoryId)
    this.loadingCache.set(categoryId, loadPromise)

    try {
      const result = await loadPromise
      this.loadingCache.delete(categoryId)
      return result
    } catch (error) {
      this.loadingCache.delete(categoryId)
      throw error
    }
  }

  /**
   * 从API加载字典数据
   * @param {string} categoryId - 字典分类ID
   * @returns {Promise<Array>} 字典项数组
   */
  async loadDictFromApi(categoryId) {
    try {
      const res = await dictCategoryItems({ categoryId })
      const items = res.data && Array.isArray(res.data) ? res.data : []

      // 标准化数据格式
      const standardItems = items.map(item => ({
        label: item.itemLabel || item.label || item.name || '',
        value: item.itemValue || item.value || item.code || '',
        ...item
      }))

      // 缓存数据
      this.setCacheDict(categoryId, standardItems)

      return standardItems
    } catch (error) {
      console.error(`加载字典数据失败 [${categoryId}]:`, error)
      return []
    }
  }

  /**
   * 获取缓存的字典数据
   * @param {string} categoryId - 字典分类ID
   * @returns {Array|null} 缓存的字典项数组或null
   */
  getCachedDict(categoryId) {
    const cached = this.dictCache.get(categoryId)
    if (!cached) {
      return null
    }

    // 检查是否过期
    if (Date.now() - cached.timestamp > this.cacheExpireTime) {
      this.dictCache.delete(categoryId)
      return null
    }

    return cached.data
  }

  /**
   * 设置字典缓存
   * @param {string} categoryId - 字典分类ID
   * @param {Array} data - 字典项数组
   */
  setCacheDict(categoryId, data) {
    this.dictCache.set(categoryId, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 根据字典值获取显示文本
   * @param {string} categoryId - 字典分类ID
   * @param {string|number} value - 字典值
   * @returns {Promise<string>} 显示文本
   */
  async getDictLabel(categoryId, value) {
    if (value === null || value === undefined || value === '') {
      return ''
    }

    try {
      const items = await this.getDictItems(categoryId)
      const item = items.find(item => String(item.value) === String(value))
      return item ? item.label : String(value)
    } catch (error) {
      console.error(`获取字典标签失败 [${categoryId}:${value}]:`, error)
      return String(value)
    }
  }

  /**
   * 同步获取字典标签（仅从缓存中获取）
   * @param {string} categoryId - 字典分类ID
   * @param {string|number} value - 字典值
   * @returns {string} 显示文本
   */
  getDictLabelSync(categoryId, value) {
    if (value === null || value === undefined || value === '') {
      return ''
    }

    const cached = this.getCachedDict(categoryId)
    if (!cached) {
      return String(value)
    }

    const item = cached.find(item => String(item.value) === String(value))
    return item ? item.label : String(value)
  }

  /**
   * 根据字典值获取颜色
   * @param {string} categoryId - 字典分类ID
   * @param {string|number} value - 字典值
   * @returns {Promise<string>} 颜色值
   */
  async getDictColor(categoryId, value) {
    if (value === null || value === undefined || value === '') {
      return ''
    }

    try {
      const items = await this.getDictItems(categoryId)
      const item = items.find(item => String(item.value) === String(value))
      return item ? item.color : ''
    } catch (error) {
      console.error(`获取字典颜色失败 [${categoryId}:${value}]:`, error)
      return ''
    }
  }

  /**
   * 同步获取字典颜色（仅从缓存中获取）
   * @param {string} categoryId - 字典分类ID
   * @param {string|number} value - 字典值
   * @returns {string} 颜色值
   */
  getDictColorSync(categoryId, value) {
    if (value === null || value === undefined || value === '') {
      return ''
    }

    const cached = this.getCachedDict(categoryId)
    if (!cached) {
      return ''
    }

    const item = cached.find(item => String(item.value) === String(value))
    return item ? item.color : ''
  }

  /**
   * 预加载字典数据
   * @param {Array<string>} categoryIds - 字典分类ID数组
   */
  async preloadDicts(categoryIds) {
    const promises = categoryIds.map(categoryId => this.getDictItems(categoryId))
    try {
      await Promise.all(promises)
    } catch (error) {
      console.error('预加载字典数据失败:', error)
    }
  }

  /**
   * 清空缓存
   * @param {string} categoryId - 可选，指定清空某个分类的缓存
   */
  clearCache(categoryId) {
    if (categoryId) {
      this.dictCache.delete(categoryId)
    } else {
      this.dictCache.clear()
    }
  }

  /**
   * 批量转换字典值
   * @param {Array} data - 数据数组
   * @param {Object} dictMapping - 字典映射配置，格式：{ fieldName: categoryId }
   * @returns {Promise<Array>} 转换后的数据数组
   */
  async convertDictFields(data, dictMapping) {
    if (!Array.isArray(data) || !dictMapping) {
      return data
    }

    // 预加载所有需要的字典
    const categoryIds = Object.values(dictMapping)
    await this.preloadDicts(categoryIds)

    // 转换数据
    return data.map(item => {
      const convertedItem = { ...item }

      Object.entries(dictMapping).forEach(([fieldName, categoryId]) => {
        if (item.hasOwnProperty(fieldName)) {
          const value = item[fieldName]
          const label = this.getDictLabelSync(categoryId, value)
          // 添加转换后的字段，使用 fieldName + 'Label' 作为新字段名
          convertedItem[fieldName + 'Label'] = label
        }
      })

      return convertedItem
    })
  }
}

// 创建单例实例
const dictService = new DictService()

export default dictService
