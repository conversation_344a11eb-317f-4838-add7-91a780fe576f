package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.ord_claim.OrdClaimPageDTO;
import com.my.crossborder.controller.vo.ord_claim.OrdClaimPageVO;
import com.my.crossborder.mybatis.entity.OrdClaim;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 物流理赔 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface OrdClaimMapper extends BaseMapper<OrdClaim> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<OrdClaimPageVO> page(OrdClaimPageDTO pageDTO);
	
}
