package com.my.crossborder.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.my.crossborder.enums.ClockStatusEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 考勤表
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@TableName("sys_shift_attendance")
public class SysShiftAttendance implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排班日期
     */
    @TableId(value = "shift_day", type = IdType.INPUT)
    private LocalDate shiftDay;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 打卡时间
     */
    private LocalDateTime clockTime;

    /**
     * 打卡状态：0=待打卡/1=已打卡/2=缺勤
     */
    private Integer clockStatus;
    
    
    /*
     * Constructor
     */
    public SysShiftAttendance(LocalDate shiftDay, Integer userId) {
    	this.shiftDay = shiftDay;
    	this.userId = userId;
    	this.clockStatus = ClockStatusEnum.PENDING_CHECK.getCode();
    }

    /**
     * 重写 equals 方法，基于 shiftDay 和 userId 判断对象相等性
     * 用于 Set<SysShiftAttendance> 中去重逻辑
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        SysShiftAttendance that = (SysShiftAttendance) obj;
        return Objects.equals(shiftDay, that.shiftDay) &&
               Objects.equals(userId, that.userId);
    }

    /**
     * 重写 hashCode 方法，基于 shiftDay 和 userId 生成哈希码
     * 必须与 equals 方法保持一致
     */
    @Override
    public int hashCode() {
        return Objects.hash(shiftDay, userId);
    }

}
