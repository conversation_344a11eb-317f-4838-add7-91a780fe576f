<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="handleClose" @open="handleOpen">
    <div class="dialog-content">
      <div class="staff-assignment">
        <h4>未排班店铺列表</h4>
        <el-table :data="localShopList" style="width: 100%" border>
          <el-table-column prop="shopName" label="店铺名称" min-width="120" align="center">
          </el-table-column>
          <el-table-column label="客服" min-width="120" align="center">
            <template slot-scope="scope">
              <el-select v-model="scope.row.serviceUserId" placeholder="请选择" filterable
                @change="(val) => handleStaffChange(val, scope.row)">
                <el-option v-for="item in staffOptions" :key="item.id" :label="item.name" :value="item.id"
                  :disabled="isStaffAssignedInOtherShops(item.id, scope.row.id)">
                  <span>{{ item.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    <span v-if="isStaffAssignedInOtherShops(item.id, scope.row.id)">已分配</span>
                  </span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="客服主管" min-width="120" align="center">
            <template slot-scope="scope">
              <el-select v-model="scope.row.supervisorUserId" placeholder="请选择" filterable>
                <el-option v-for="item in supervisorOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :disabled="!canSave" :loading="saving">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as SysShiftAPI from '@/api/SysShift.js'

export default {
  name: 'SysShiftEdit',
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 选中的日期
    selectedDate: {
      type: Date,
      required: true
    },
    // 可用店铺列表
    availableShops: {
      type: Array,
      default: () => []
    },
    // 客服选项
    staffOptions: {
      type: Array,
      default: () => []
    },
    // 主管选项
    supervisorOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      saving: false,
      localShopList: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },

    // 弹窗标题
    dialogTitle() {
      const dateStr = this.formatDateChinese(this.selectedDate)
      return `添加排班 - ${dateStr}`
    },

    // 是否可以保存
    canSave() {
      return this.localShopList && this.localShopList.length > 0
    }
  },
  watch: {
    availableShops: {
      handler(newShops) {
        this.initLocalShopList()
      },
      immediate: true
    }
  },
  methods: {
    // 格式化日期为中文
    formatDateChinese(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = d.getMonth() + 1
      const day = d.getDate()
      const weekdays = ['日', '一', '二', '三', '四', '五', '六']
      const weekday = weekdays[d.getDay()]
      return `${year}年${month}月${day}日 (周${weekday})`
    },

    // 格式化日期为数据键
    formatDateForKey(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 初始化本地店铺列表
    initLocalShopList() {
      this.localShopList = this.availableShops.map(shop => ({
        ...shop,
        serviceUserId: null,
        supervisorUserId: null
      }))
    },

    // 处理员工变更
    handleStaffChange(value, row) {
      // 可以根据业务需求添加其他逻辑
      console.log('员工变更:', value, row)
    },

    // 检查员工是否已分配在其他店铺
    isStaffAssignedInOtherShops(staffId, shopId) {
      // 允许一个人员同时服务多个店铺，所以不做限制
      return false
    },

    // 验证排班数据
    validateScheduleData() {
      // 收集所有选择的客服和主管
      const selectedStaff = []
      const selectedSupervisors = []

      this.localShopList.forEach(shop => {
        if (shop.serviceUserId && shop.serviceUserId !== '') {
          selectedStaff.push(shop.serviceUserId)
        }
        if (shop.supervisorUserId && shop.supervisorUserId !== '') {
          selectedSupervisors.push(shop.supervisorUserId)
        }
      })

      // 检查是否有选择的客服
      if (selectedStaff.length === 0) {
        return {
          isValid: false,
          message: '请至少选择一个客服'
        }
      }

      // 检查是否有选择的主管
      if (selectedSupervisors.length === 0) {
        return {
          isValid: false,
          message: '请选择一个客服主管'
        }
      }

      // 检查是否所有有客服的店铺都有主管，以及所有有主管的店铺都有客服
      const invalidShopsNoSupervisor = []
      const invalidShopsNoStaff = []

      this.localShopList.forEach(shop => {
        const hasStaff = shop.serviceUserId && shop.serviceUserId !== ''
        const hasSupervisor = shop.supervisorUserId && shop.supervisorUserId !== ''

        // 如果有客服但没有主管，则记录为无效
        if (hasStaff && !hasSupervisor) {
          invalidShopsNoSupervisor.push(shop.shopName)
        }

        // 如果有主管但没有客服，则记录为无效
        if (hasSupervisor && !hasStaff) {
          invalidShopsNoStaff.push(shop.shopName)
        }
      })

      if (invalidShopsNoSupervisor.length > 0) {
        return {
          isValid: false,
          message: `以下店铺有客服但缺少主管：${invalidShopsNoSupervisor.join('、')}`
        }
      }

      if (invalidShopsNoStaff.length > 0) {
        return {
          isValid: false,
          message: `以下店铺有主管但缺少客服：${invalidShopsNoStaff.join('、')}`
        }
      }

      return {
        isValid: true,
        message: ''
      }
    },

    // 弹窗打开时
    handleOpen() {
      this.initLocalShopList()
    },

    // 弹窗关闭时
    handleClose() {
      this.$emit('close')
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    },

    // 保存排班
    async handleSave() {
      if (!this.canSave) return

      try {
        this.saving = true
        const dateStr = this.formatDateForKey(this.selectedDate)
        console.log('开始保存排班，日期:', dateStr)

        // 验证排班数据
        const validationResult = this.validateScheduleData()
        if (!validationResult.isValid) {
          this.$message({
            message: validationResult.message,
            type: 'warning'
          })
          return
        }

        // 构建符合 SysShiftInsertDTO 格式的排班列表
        const shiftList = []

        this.localShopList.forEach(shop => {
          // 只处理有人员分配的排班
          if (shop.serviceUserId && shop.supervisorUserId) {
            shiftList.push({
              shopId: parseInt(shop.id), // 确保转换为整数
              serviceUserId: parseInt(shop.serviceUserId),
              supervisorUserId: parseInt(shop.supervisorUserId)
            })
          }
        })

        if (shiftList.length === 0) {
          this.$message({
            message: '请至少为一个店铺分配客服和主管',
            type: 'warning'
          })
          return
        }

        // 准备符合 SysShiftInsertDTO 格式的数据
        const insertData = {
          shiftDay: dateStr,
          shiftList: shiftList
        }

        console.log('准备保存排班数据 (SysShiftInsertDTO格式):', insertData)

        // 调用排班保存API
        const response = await SysShiftAPI.insert(insertData)
        console.log('批量保存响应:', response)

        this.$message({
          message: `排班保存成功，共为${shiftList.length}个店铺安排排班`,
          type: 'success'
        })

        // 触发保存成功事件
        this.$emit('save-success', insertData)

        // 关闭弹窗
        this.dialogVisible = false

      } catch (error) {
        console.error('保存排班失败:', error)
        this.$message.error('保存排班失败：' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.dialog-content {
  padding: 0;
}

.staff-assignment h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}
</style>
