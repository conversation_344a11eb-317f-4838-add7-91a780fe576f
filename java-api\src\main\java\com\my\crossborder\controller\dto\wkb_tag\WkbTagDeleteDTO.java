package com.my.crossborder.controller.dto.wkb_tag;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_工作台_订单标签
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbTagDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<Integer> idList;
	
}
