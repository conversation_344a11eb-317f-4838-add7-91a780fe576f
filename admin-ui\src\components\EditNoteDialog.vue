<template>
  <div>
    <el-dialog
      title="编辑备注"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleCloseDialog">
      <div class="note-dialog-content">
        <el-form :model="noteForm" ref="noteForm" label-width="100px" :rules="noteFormRules">
          <el-form-item label="订单号">
            <span>{{ noteForm.orderSn }}</span>
          </el-form-item>
          <el-form-item label="场景">
            <span>{{ getSceneName(noteForm.scene) }}</span>
          </el-form-item>
          <el-form-item label="备注" prop="content">
            <el-input
              type="textarea"
              :rows="8"
              placeholder="请输入备注"
              v-model="noteForm.content">
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="savingNote" @click="saveNote">保存</el-button>
        <el-button @click="handleCloseDialog">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getByOrderSnAndScene, insertNote, updateNote } from '../api/WkbNote'

export default {
  name: 'EditNoteDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderSn: {
      type: String,
      default: ''
    },
    scene: {
      type: String,
      default: '02'
    },
    sceneName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      savingNote: false,
      noteForm: {
        id: null,
        orderSn: '',
        content: '',
        scene: '02'
      },
      noteFormRules: {
        content: [
          { required: true, message: '备注内容不能为空', trigger: 'blur' }
        ]
      },
      sceneMap: {
        '01': '采购登记',
        '02': '未完整填写物流编号',
        '03': '已填物流编号未入库',
        '04': '货物已入库未出库',
        '05': '已采购但出库前取消',
        '06': '货物已入库重新采购',
        '07': '已出库售后',
        // '08': '物流理赔'
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.loadNoteData()
      }
    },
    orderSn(val) {
      this.noteForm.orderSn = val
    },
    scene(val) {
      this.noteForm.scene = val
    }
  },
  created() {
    this.noteForm.orderSn = this.orderSn
    this.noteForm.scene = this.scene
  },
  methods: {
    // 获取场景名称
    getSceneName(scene) {
      return this.sceneName || this.sceneMap[scene] || '未知场景'
    },
    
    // 加载备注数据
    loadNoteData() {
      if (!this.noteForm.orderSn || !this.noteForm.scene) return
      
      console.log('加载备注数据:', this.noteForm.orderSn, this.noteForm.scene)
      
      // 查询该订单的场景备注
      getByOrderSnAndScene({
        orderSn: this.noteForm.orderSn,
        scene: this.noteForm.scene
      }).then(res => {
        console.log('获取备注API响应:', res)
        if (res.success && res.data) {
          // 找到了备注，加载到表单中
          this.noteForm.id = res.data.id
          this.noteForm.content = res.data.content
          console.log('加载已有备注到表单:', this.noteForm)
        } else {
          // 没有找到备注，清空表单
          this.noteForm.id = null
          this.noteForm.content = ''
        }
      }).catch(err => {
        console.error('获取备注失败：', err)
        this.$message.error('获取备注失败：' + err.message)
      })
    },
    
    // 关闭编辑备注弹窗
    handleCloseDialog() {
      this.dialogVisible = false
      this.$emit('update:visible', false)
      this.resetForm()
    },
    
    // 重置表单
    resetForm() {
      this.noteForm = {
        id: null,
        orderSn: this.orderSn,
        content: '',
        scene: this.scene
      }
    },
    
    // 保存备注
    saveNote() {
      this.$refs.noteForm.validate(valid => {
        if (!valid) {
          return
        }
        
        this.savingNote = true
        console.log('正在保存备注:', this.noteForm)
        
        // 根据是否有ID判断是新增还是更新
        if (this.noteForm.id) {
          // 更新现有备注
          updateNote({
            id: this.noteForm.id,
            orderSn: this.noteForm.orderSn,
            content: this.noteForm.content,
            scene: this.noteForm.scene
          }).then(res => {
            this.savingNote = false
            console.log('更新备注API响应:', res)
            if (res.success) {
              this.$message.success('备注已更新')
              this.handleCloseDialog()
              
              // 通知父组件更新成功
              this.$emit('saved', {
                ...res.data,
                orderSn: this.noteForm.orderSn,
                scene: this.noteForm.scene,
                id: this.noteForm.id,
                content: this.noteForm.content,
                action: 'update'
              })
            } else {
              this.$message.error(res.message || '更新失败')
            }
          }).catch(err => {
            this.savingNote = false
            this.$message.error('更新备注失败：' + err.message)
          })
        } else {
          // 新增备注
          insertNote({
            orderSn: this.noteForm.orderSn,
            content: this.noteForm.content,
            scene: this.noteForm.scene
          }).then(res => {
            this.savingNote = false
            console.log('新增备注API响应:', res)
            if (res.success) {
              this.$message.success('备注已保存')
              this.handleCloseDialog()
              
              // 通知父组件保存成功
              this.$emit('saved', {
                ...res.data,
                orderSn: this.noteForm.orderSn,
                scene: this.noteForm.scene,
                content: this.noteForm.content,
                action: 'insert'
              })
            } else {
              this.$message.error(res.message || '保存失败')
            }
          }).catch(err => {
            this.savingNote = false
            this.$message.error('保存备注失败：' + err.message)
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.note-dialog-content {
  padding: 20px;
}
</style> 