package com.my.crossborder.controller.dto.erp_order;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONType;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_订单主表
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class RepurchaseErpOrderPageDTO
						extends ErpOrderPageDTO
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处理办法 字典参数repurchase_close_way
     */
    private String closeWay;

    /**
     * 处理状态 字典close_status
     */
    private String closeStatus;

}
