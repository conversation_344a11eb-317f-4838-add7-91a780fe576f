package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.sys_shop.SysShopPageDTO;
import com.my.crossborder.controller.vo.sys_shop.SysShopPageVO;
import com.my.crossborder.mybatis.entity.SysShop;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 店铺管理表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface SysShopMapper extends BaseMapper<SysShop> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysShopPageVO> page(SysShopPageDTO pageDTO);
	
}
