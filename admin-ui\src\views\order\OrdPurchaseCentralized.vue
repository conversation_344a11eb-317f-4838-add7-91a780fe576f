<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>集中采购登记</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline">
        <el-form-item label="采购单号">
          <el-input v-model="listQuery.purchaseNumber" placeholder="采购单号" clearable style="width: 110px;" />
        </el-form-item>
        <el-form-item label="关联订单">
          <multi-order-selector v-model="listQuery.orderSnList" :readonly-mode="true" placeholder="订单号"
            style="width: 120px;" />
        </el-form-item>
        <el-form-item label="采购途径">
          <dict-select v-model="listQuery.purchaseChannel" category-id="PURCHASE_CENTRALIZED_CHANNEL" placeholder="采购途径"
            clearable style="width: 110px;" />
        </el-form-item>
        <el-form-item label="采购状态">
          <dict-select v-model="listQuery.purchaseStatus" category-id="PURCHASE_CENTRALIZED_STATUS" placeholder="采购状态"
            clearable style="width: 110px;" />
        </el-form-item>
        <el-form-item label="采购人">
          <user-selector v-model="selectedUsers" :multiple="true" @select="handleUsersSelected" style="width: 120px;" />
        </el-form-item>
        <el-form-item label="采购日期">
          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 240px;"
            @change="handleDateRangeChange" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button v-permission="['ord-purchase-centralized:insert']" type="primary" icon="el-icon-plus" @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <el-table v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row
        style="width: 100%">
        <!-- <el-table-column align="center" label="ID" width="80">
        <template slot-scope="scope">
          {{ scope.row.id }}
        </template>
</el-table-column> -->
        <el-table-column label="采购单号" width="150" align="center">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
              @click="copyToClipboard(scope.row.purchaseNumber, '采购单号', 'blue')"
              :title="'点击复制采购单号: ' + scope.row.purchaseNumber">
              {{ scope.row.purchaseNumber }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="关联订单" width="160" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.orderSnList && scope.row.orderSnList.length > 0">
              <el-tag v-for="orderSn in scope.row.orderSnList" :key="orderSn" size="mini"
                style="margin: 2px; cursor: pointer;" @click="copyOrderSn(orderSn)" :title="'点击复制订单号: ' + orderSn">
                {{ orderSn }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="品名" width="150" align="center">
          <template slot-scope="scope">
            {{ scope.row.productName }}
          </template>
        </el-table-column>
        <el-table-column label="数量" width="90" align="center">
          <template slot-scope="scope">
            {{ scope.row.quantity }}
          </template>
        </el-table-column>
        <el-table-column label="采购总金额" width="100" align="center">
          <template slot-scope="scope">
            ¥ {{ scope.row.totalAmount }}
          </template>
        </el-table-column>
        <el-table-column label="单价" width="100" align="center">
          <template slot-scope="scope">
            ¥ {{ (scope.row.totalAmount / scope.row.quantity).toFixed(2) }}
          </template>
        </el-table-column>
        <dict-table-column prop="purchaseChannel" label="采购途径" category-id="PURCHASE_CENTRALIZED_CHANNEL"
          width="120"></dict-table-column>
        <el-table-column label="采购人" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.purchaseUserName }}
          </template>
        </el-table-column>
        <el-table-column label="采购日期" width="110" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.purchaseDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="purchaseStatus" label="采购状态" width="140" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.purchaseStatus !== '0'"
              :content="`${scope.row.confirmUserName || '未知'}, ${formatDateTime(scope.row.confirmTime) || '-'}`"
              placement="top">
              <dict-tag :value="scope.row.purchaseStatus" category-id="PURCHASE_CENTRALIZED_STATUS"
                style="cursor: pointer;"></dict-tag>
            </el-tooltip>
            <dict-tag v-else :value="scope.row.purchaseStatus" category-id="PURCHASE_CENTRALIZED_STATUS"></dict-tag>
          </template>
        </el-table-column>
        <el-table-column prop="settlementFlag" label="结算状态" width="100" align="center">
          <template slot-scope="scope">
            <span v-if="!scope.row.purchaseChannel"> - </span>
            <el-tag type="info" v-else-if="scope.row.purchaseChannel != 2 && scope.row.purchaseChannel != 3"> 无需结算 </el-tag>
            <el-tag v-else :type="scope.row.settlementFlag ? 'success' : 'warning'">
              {{ scope.row.settlementFlag ? '已结算' : '未结算' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="200" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="!scope.row.settlementFlag && scope.row.purchaseUserId === currentUserId" v-permission="['ord-purchase-centralized:update']" type="text"
              size="mini" icon="el-icon-edit" @click="handleUpdate(scope.row)">
              编辑
            </el-button>
            <el-button v-if="scope.row.purchaseUserId === currentUserId" v-permission="['ord-purchase-centralized:confirm']" type="text" icon="el-icon-check" size="mini"
              @click="handleConfirm(scope.row)">
              状态确认
            </el-button>
            <el-button v-if="!scope.row.settlementFlag && scope.row.purchaseUserId === currentUserId" v-permission="['ord-purchase-centralized:delete']" type="text"
              size="mini" icon="el-icon-delete" @click="handleDelete(scope.row)" style="color: #F56C6C">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

    <!-- 编辑对话框 -->
    <OrdPurchaseCentralizedEdit :visible.sync="dialogVisible" :edit-data="currentEditData" :is-edit="isEdit"
      @submit="handleSubmit" />

    <!-- 确认状态对话框 -->
    <OrdPurchaseCentralizedConfirm :visible.sync="confirmDialogVisible" :purchase-data="currentPurchaseData"
      @submit="handleConfirmSubmit" />
  </div>
</template>

<script>
import Pagination from '../../components/Pagination'
import OrdPurchaseCentralizedEdit from './OrdPurchaseCentralizedEdit'
import OrdPurchaseCentralizedConfirm from './OrdPurchaseCentralizedConfirm'
import DictSelect from '../../components/DictSelect'
import DictTableColumn from '../../components/DictTableColumn'
import DictTag from '../../components/DictTag'
import UserSelector from '../../components/UserSelector'
import MultiOrderSelector from '../../components/MultiOrderSelector'
import {
  getOrdPurchaseCentralizedPage,
  deleteOrdPurchaseCentralized,
  confirmStatusOrdPurchaseCentralized
} from '../../api/OrdPurchaseCentralized'
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'OrdPurchaseCentralized',
  components: {
    Pagination,
    OrdPurchaseCentralizedEdit,
    OrdPurchaseCentralizedConfirm,
    DictSelect,
    DictTableColumn,
    DictTag,
    UserSelector,
    MultiOrderSelector
  },
  mixins: [copyMixin],
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      selectedUsers: [], // 选中的用户列表
      listQuery: {
        current: 1,
        size: 20,
        purchaseNumber: '',
        purchaseChannel: '',
        purchaseStatus: '',
        purchaseUserIdList: [], // 改为用户ID列表
        orderSnList: [], // 订单号列表
        purchaseDateStart: '',
        purchaseDateEnd: ''
      },
      pageParam: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      dateRange: [],
      dialogVisible: false,
      confirmDialogVisible: false,
      currentEditData: {},
      currentPurchaseData: {},
      isEdit: false
    }
  },
  computed: {
    // 获取当前登录用户ID
    currentUserId() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.userId || userEntity.id
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      // 过滤空的查询参数
      const queryParams = { ...this.listQuery }
      if (!queryParams.purchaseDateStart) {
        delete queryParams.purchaseDateStart
      }
      if (!queryParams.purchaseDateEnd) {
        delete queryParams.purchaseDateEnd
      }
      if (!queryParams.purchaseNumber) {
        delete queryParams.purchaseNumber
      }
      if (!queryParams.purchaseChannel) {
        delete queryParams.purchaseChannel
      }
      if (!queryParams.purchaseStatus) {
        delete queryParams.purchaseStatus
      }
      if (!queryParams.purchaseUserIdList || queryParams.purchaseUserIdList.length === 0) {
        delete queryParams.purchaseUserIdList
      }
      if (!queryParams.orderSnList || queryParams.orderSnList.length === 0) {
        delete queryParams.orderSnList
      }

      console.log('查询参数:', queryParams) // 调试信息
      getOrdPurchaseCentralizedPage(queryParams).then(response => {
        this.list = response.data.records
        this.total = response.data.total
        this.pageParam.total = response.data.total
        this.pageParam.currentPage = response.data.current
        this.pageParam.pageSize = response.data.size
        this.listLoading = false
      }).catch(error => {
        console.error('获取数据失败:', error)
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.current = 1
      this.getList()
    },
    resetQuery() {
      this.selectedUsers = [] // 重置选中的用户
      this.listQuery = {
        current: 1,
        size: 20,
        purchaseNumber: '',
        purchaseChannel: '',
        purchaseStatus: '',
        purchaseUserIdList: [],
        orderSnList: [], // 重置订单号列表
        purchaseDateStart: '',
        purchaseDateEnd: ''
      }
      this.pageParam = {
        currentPage: 1,
        pageSize: 20,
        total: 0
      }
      this.dateRange = []
      this.getList()
    },
    handleDateRangeChange(val) {
      if (val && val.length === 2) {
        this.listQuery.purchaseDateStart = val[0]
        this.listQuery.purchaseDateEnd = val[1]
      } else {
        this.listQuery.purchaseDateStart = ''
        this.listQuery.purchaseDateEnd = ''
      }
    },
    handleAdd() {
      this.currentEditData = {}
      this.isEdit = false
      this.dialogVisible = true
    },
    handleUpdate(row) {
      this.currentEditData = { ...row }
      this.isEdit = true
      this.dialogVisible = true
    },
    handleConfirm(row) {
      this.currentPurchaseData = {
        ...row,
        orderSnList: row.orderSnList || [] // 确保 orderSnList 不为 undefined
      }
      this.confirmDialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOrdPurchaseCentralized({ idList: [row.id] }).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    handleSubmit() {
      this.dialogVisible = false
      this.getList()
    },
    handleConfirmSubmit(status) {
      confirmStatusOrdPurchaseCentralized(this.currentPurchaseData.id, status).then(() => {
        this.$message.success('确认成功')
        this.confirmDialogVisible = false
        this.getList()
      })
    },
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + ' ' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0')
    },
    // 格式化日期（仅日期部分）
    formatDate(date) {
      if (!date) return '-'
      // 如果是字符串格式的日期，直接返回
      if (typeof date === 'string' && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return date
      }
      // 如果是 Date 对象或其他格式，转换为日期字符串
      const dateObj = new Date(date)
      return dateObj.getFullYear() + '-' +
        String(dateObj.getMonth() + 1).padStart(2, '0') + '-' +
        String(dateObj.getDate()).padStart(2, '0')
    },
    // 处理用户选择
    handleUsersSelected(selectedUsers) {
      this.selectedUsers = selectedUsers
      // 提取用户ID列表
      this.listQuery.purchaseUserIdList = selectedUsers.map(user => user.userId)
    },
    // 分页回调
    callback_getPageData(pageParam) {
      this.listQuery.current = pageParam.currentPage
      this.listQuery.size = pageParam.pageSize
      this.getList()
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.demo-form-inline {
  margin-bottom: 20px;
}

.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}
</style>
