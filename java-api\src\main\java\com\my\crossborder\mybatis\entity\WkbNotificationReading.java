package com.my.crossborder.mybatis.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 通知阅读表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wkb_notification_reading")
public class WkbNotificationReading implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 通知id
     */
    private Integer notificationId;

    /**
     * 公告的接收用户id
     */
    private Integer userId;

    /**
     * 是否已读
     */
    @TableField(value = "is_read")
    private Boolean read;

    /**
     * 已读时间
     */
    private LocalDateTime readTime;


}
