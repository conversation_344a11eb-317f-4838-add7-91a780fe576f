package com.my.crossborder.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.mybatis.entity.SysShopPartner;
import com.my.crossborder.mybatis.mapper.SysShopPartnerMapper;
import com.my.crossborder.service.SysShopPartnerService;

/**
 * 店铺合伙人表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
public class SysShopPartnerServiceImpl extends ServiceImpl<SysShopPartnerMapper, SysShopPartner>
		implements SysShopPartnerService {

	@Override
	public void deleteByShopId(Integer id) {
		this.baseMapper.delete(
				new LambdaQueryWrapper<SysShopPartner>()
						.eq(SysShopPartner::getShopId, id));
	}

}
