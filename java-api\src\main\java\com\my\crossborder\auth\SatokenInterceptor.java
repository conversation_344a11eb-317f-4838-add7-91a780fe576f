package com.my.crossborder.auth;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;

/**
 * 接口认证相关配置
 */
@Configuration
@RequiredArgsConstructor
public class SatokenInterceptor implements WebMvcConfigurer {

    
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
    	// 注册Sa-Token拦截器，校验规则为AuthController中开启了注解鉴权的接口，进行鉴权
    	registry.addInterceptor(new SaInterceptor(handle -> {
    		// 登录校验 -- 拦截所有/api/**接口
    		SaRouter.match("/api/**")
    			// 排除登录接口
    			.notMatch("/api/auth/login")
    			// 检查是否登录
    			.check(r -> StpUtil.checkLogin());
    	})).addPathPatterns("/api/**");
    }
    
}
