export default {
  methods: {
    // 刷新未读数据缓存
    async refreshUnreadCache() {
      try {
        await this.$store.dispatch('refreshUnreadData')
      } catch (error) {
        console.error('刷新未读数据缓存失败:', error)
      }
    },

    // 从缓存中移除通知
    removeNotificationFromCache(notificationId) {
      this.$store.commit('removeNotificationFromCache', notificationId)
    },

    // 从缓存中移除待办
    removeTodoFromCache(todoId) {
      this.$store.commit('removeTodoFromCache', todoId)
    }
  },

  computed: {
    // 未读通知数量
    unreadNotificationCount() {
      return this.$store.getters.unreadNotificationCount
    },

    // 未读待办数量
    unreadTodoCount() {
      return this.$store.getters.unreadTodoCount
    }
  }
}
