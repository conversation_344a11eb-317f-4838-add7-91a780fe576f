package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.OrdTaiwan;
import com.my.crossborder.mybatis.mapper.OrdTaiwanMapper;
import com.my.crossborder.service.OrdTaiwanService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanInsertDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanPageDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanUpdateDTO;
import com.my.crossborder.controller.dto.ord_taiwan.OrdTaiwanDeleteDTO;
import com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanDetailVO;
import com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanPageVO;
import com.my.crossborder.exception.BusinessException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 台湾上架物品 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class OrdTaiwanServiceImpl extends ServiceImpl<OrdTaiwanMapper, OrdTaiwan> implements OrdTaiwanService {


	@Transactional
	@Override
	public void insert(OrdTaiwanInsertDTO insertDTO) {
		OrdTaiwan old = this.baseMapper.selectOne(new LambdaQueryWrapper<OrdTaiwan>()
			.eq(OrdTaiwan::getOrderItemId, insertDTO.getOrderItemId()));
		BusinessException.when(old != null, "此商品记录已存在");

		OrdTaiwan entity = BeanUtil.copyProperties(insertDTO, OrdTaiwan.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(OrdTaiwanUpdateDTO updateDTO) {
		OrdTaiwan entity = BeanUtil.copyProperties(updateDTO, OrdTaiwan.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public OrdTaiwanDetailVO detail(String id) {
		OrdTaiwan entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, OrdTaiwanDetailVO.class);
	}

	@Override
	public Page<OrdTaiwanPageVO> page(OrdTaiwanPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(OrdTaiwanDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
