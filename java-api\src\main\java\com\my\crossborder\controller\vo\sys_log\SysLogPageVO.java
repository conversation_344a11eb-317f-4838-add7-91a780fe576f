package com.my.crossborder.controller.vo.sys_log;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_操作日志表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysLogPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long logId;

    /**
     * 所属模块
     */
    private String moduleName;

    /**
     * 操作菜单
     */
    private String menuName;

    /**
     * 操作类型（如新增/修改/删除）
     */
    private String operationName;

    /**
     * 操作详情
     */
    private String operationDetail;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 异常信息
     */
    private String errMsg;

    /**
     * 操作用户ID
     */
    private Integer createUserId;

    /**
     * 操作时间
     */
    private LocalDateTime createTime;

}
