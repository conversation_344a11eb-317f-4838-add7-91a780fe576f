package com.my.crossborder.controller.dto.sys_shift;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 打卡请求DTO
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class SysShiftPunchInDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    /**
     * 打卡日期（格式：YYYY-MM-DD）
     */
    @NotBlank(message = "打卡日期不能为空")
    private String date;
}
