package com.my.crossborder.forest.erp990;

import com.dtflys.forest.annotation.Address;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.dtflys.forest.annotation.Retry;
import com.dtflys.forest.callback.OnError;
import com.my.crossborder.forest.erp990.address.Erp990Address;
import com.my.crossborder.forest.erp990.interceptor.Erp990TokenInterceptor;
import com.my.crossborder.forest.erp990.vo.ErpOrderPageVO;
import com.my.crossborder.forest.erp990.vo.ErpPackageRecordPageVO;
import com.my.crossborder.forest.erp990.vo.ShopPageVO;

/**
 * 禾晨物流API
 * 
 * <AUTHOR>
 */
@Address(source = Erp990Address.class)
@BaseRequest(interceptor = Erp990TokenInterceptor.class)
public interface Erp990Client {

    
    /**
     * 分页查询店铺
     */
	@Retry(maxRetryCount = "1")
    @Get(url = "/api/order/shopCookie/list", connectTimeout = 15000, readTimeout = 15000)
    ShopPageVO shopPage(@Query("pageNum") Integer pageNum,
			    		@Query("pageSize") Integer pageSize,
			    		OnError onError);

    /**
     * 分页查询订单
     */
    @Retry(maxRetryCount = "1")
    @Post(url = "/api/order/shopeeDownload/list1", connectTimeout = 15000, readTimeout = 15000)
    ErpOrderPageVO orderPage(@Query("pageNum") Integer pageNum,
                         @Query("pageSize") Integer pageSize,
                         @Query("isolateFlag") Integer isolateFlag,
                         @Query("orderStartTime") String orderStartTime,
                         @Query("orderEndTime") String orderEndTime,
                         @Query("putInFalg") Integer putInFalg,
                         @Query("realOutFlag") Integer realOutFlag,
                         @Query("orderStates") String orderStates,
                         OnError onError);

    /**
     * 打包记录
     */
    @Retry(maxRetryCount = "1")
    @Post(url = "/api/scan/mechineScanExpress/listPage", connectTimeout = 15000, readTimeout = 15000)
    ErpPackageRecordPageVO packageRecord(@JSONBody("pageNum") Integer pageNum,
                         @JSONBody("pageSize") Integer pageSize,
                         @JSONBody("trackid") String trackid,
                         OnError onError);
    
}