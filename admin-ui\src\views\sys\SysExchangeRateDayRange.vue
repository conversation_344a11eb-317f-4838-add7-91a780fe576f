<template>
  <div class="content-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="queryForm" class="demo-form-inline">
      <el-form-item label="日期">
        <el-date-picker v-model="queryForm.day" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getPageData" icon="el-icon-search">查询</el-button>
        <el-button @click="resetQuery" icon="el-icon-refresh">重置</el-button>
        <el-button type="primary" @click="handleAdd" icon="el-icon-plus">添加</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" border style="width: 100%; margin-top: 20px;" v-loading="loading">
      <el-table-column prop="startDay" label="开始日期" width="140" align="center" sortable>
        <template slot-scope="scope">
          {{ formatDate(scope.row.startDay) }}
        </template>
      </el-table-column>
      <el-table-column prop="endDay" label="结束日期" width="140" align="center" sortable>
        <template slot-scope="scope">
          {{ formatDate(scope.row.endDay) }}
        </template>
      </el-table-column>
      <el-table-column prop="exchangeRate" label="汇率" width="180" align="center" sortable>
        <template slot-scope="scope">
          <span class="exchange-rate-value">{{ scope.row.exchangeRate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="200" align="left">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">
            <i class="el-icon-edit"></i> 修改
          </el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)" style="color: #F56C6C">
            <i class="el-icon-delete"></i> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" :before-close="handleDialogClose">
      <el-form ref="dialogForm" :model="dialogForm" :rules="dialogRules" label-width="120px">
        <el-form-item label="开始日期" prop="startDay">
          <el-date-picker v-model="dialogForm.startDay" type="date" placeholder="选择开始日期" value-format="yyyy-MM-dd"
            :disabled="isEdit" style="width: 100%;">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="endDay">
          <el-date-picker v-model="dialogForm.endDay" type="date" placeholder="选择结束日期" value-format="yyyy-MM-dd"
            :disabled="isEdit" style="width: 100%;">
          </el-date-picker>
        </el-form-item>
        <el-form-item v-if="isEdit" label="新开始日期" prop="newStartDay">
          <el-date-picker v-model="dialogForm.newStartDay" type="date" placeholder="选择新开始日期" value-format="yyyy-MM-dd"
            style="width: 100%;">
          </el-date-picker>
        </el-form-item>
        <el-form-item v-if="isEdit" label="新结束日期" prop="newEndDay">
          <el-date-picker v-model="dialogForm.newEndDay" type="date" placeholder="选择新结束日期" value-format="yyyy-MM-dd"
            style="width: 100%;">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="汇率" prop="exchangeRate">
          <el-input v-model="dialogForm.exchangeRate" placeholder="请输入汇率（如：4.0814）" type="number" step="0.0001"
            style="width: 100%;">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取 消</el-button>
        <el-button type="primary" @click="handleDialogSubmit" :loading="submitting">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  pageSysExchangeRateDayRange,
  insertSysExchangeRateDayRange,
  updateSysExchangeRateDayRange,
  deleteSysExchangeRateDayRange
} from '@/api/SysExchangeRateDayRange'
import Pagination from '../../components/Pagination'

export default {
  name: 'SysExchangeRateDayRange',
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      submitting: false,
      // 查询表单
      queryForm: {
        day: null,
      },
      // 表格数据
      tableData: [],
      // 分页参数
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 弹窗相关
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      dialogForm: {
        startDay: '',
        endDay: '',
        newStartDay: '',
        newEndDay: '',
        exchangeRate: ''
      },
      // 表单验证规则
      dialogRules: {
        startDay: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endDay: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        newStartDay: [
          { required: true, message: '请选择新开始日期', trigger: 'change' }
        ],
        newEndDay: [
          { required: true, message: '请选择新结束日期', trigger: 'change' }
        ],
        exchangeRate: [
          { required: true, message: '请输入汇率', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,4})?$/, message: '汇率格式不正确，最多4位小数', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getPageData()
  },
  methods: {
    // 获取分页数据
    getPageData() {
      this.loading = true
      const params = {
        ...this.queryForm,
        current: this.pageParam.currentPage,
        size: this.pageParam.pageSize
      }

      pageSysExchangeRateDayRange(params)
        .then(res => {
          this.loading = false
          if (res.success && res.data) {
            this.tableData = res.data.records
            this.pageParam.currentPage = res.data.current
            this.pageParam.pageSize = res.data.size
            this.pageParam.total = res.data.total
          }
        })
    },

    // 重置查询
    resetQuery() {
      this.queryForm = {
        day: null,
      }
      this.pageParam.currentPage = 1
      this.getPageData()
    },

    // 分页回调方法
    callback_getPageData(pageParam) {
      this.pageParam = pageParam
      this.getPageData()
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增汇率日期区间'
      this.isEdit = false
      this.dialogForm = {
        startDay: null,
        endDay: null,
        newStartDay: null,
        newEndDay: null,
        exchangeRate: ''
      }
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑汇率日期区间'
      this.isEdit = true
      this.dialogForm = {
        startDay: row.startDay,
        endDay: row.endDay,
        newStartDay: row.startDay,
        newEndDay: row.endDay,
        exchangeRate: row.exchangeRate
      }
      this.dialogVisible = true
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除日期区间 ${row.startDay} 至 ${row.endDay} 的汇率记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const deleteData = {
          startDay: row.startDay,
          endDay: row.endDay
        }

        deleteSysExchangeRateDayRange(deleteData)
          .then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.getPageData()
            } else {
              this.$message.error(res.errMsg || '删除失败')
            }
          })
          .catch(err => {
            this.$message.error('删除失败：' + err.message)
          })
      })
    },

    // 弹窗提交
    handleDialogSubmit() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          this.submitting = true

          if (this.isEdit) {
            // 编辑
            updateSysExchangeRateDayRange(this.dialogForm)
              .then(res => {
                this.submitting = false
                if (res.success) {
                  this.$message.success('修改成功')
                  this.dialogVisible = false
                  this.getPageData()
                } else {
                  this.$message.error(res.errMsg || '修改失败')
                }
              })
          } else {
            // 新增
            insertSysExchangeRateDayRange(this.dialogForm)
              .then(res => {
                this.submitting = false
                if (res.success) {
                  this.$message.success('新增成功')
                  this.dialogVisible = false
                  this.getPageData()
                } else {
                  this.$message.error(res.errMsg || '新增失败')
                }
              })
          }
        }
      })
    },

    // 关闭弹窗
    handleDialogClose() {
      this.dialogVisible = false
      this.$refs.dialogForm.resetFields()
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      return dateStr
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return ''
      return dateTimeStr.replace('T', ' ')
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  /* border-radius: 8px; */
  padding: 24px;
  /* margin-top: 20px; */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.exchange-rate-value {
  color: #409EFF;
  font-weight: bold;
}

.dialog-footer {
  text-align: right;
}
</style>
