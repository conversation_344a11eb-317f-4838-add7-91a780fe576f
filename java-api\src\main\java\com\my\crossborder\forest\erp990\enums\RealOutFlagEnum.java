package com.my.crossborder.forest.erp990.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 出库状态枚举
 * 用于表示订单的实际出库状态
 * 
 * <AUTHOR>
 * @date 2025
 */
@Getter
@AllArgsConstructor
public enum RealOutFlagEnum {
    
    /** 未出库 */
    NOT_OUT(0, "未出库"),
    
    /** 已出库 */
    OUT(1, "已出库"),
    
    /** 无需出库 */
    NO_NEED_OUT(99, "无需出库");
    
    /** 状态代码 */
    private final Integer code;
    
    /** 状态描述 */
    private final String description;
    
    /**
     * 根据代码获取枚举
     * @param code 状态代码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static RealOutFlagEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RealOutFlagEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 