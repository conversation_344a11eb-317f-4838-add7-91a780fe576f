package com.my.crossborder.service.impl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.cache.PermissionCache;
import com.my.crossborder.controller.dto.sys_menu.SysMenuGrantDTO;
import com.my.crossborder.mybatis.entity.SysMenu;
import com.my.crossborder.mybatis.entity.SysMenuRefRole;
import com.my.crossborder.mybatis.mapper.SysMenuRefRoleMapper;
import com.my.crossborder.service.SysMenuRefRoleService;
import com.my.crossborder.service.SysMenuService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;

/**
 * 菜单_角色_关联表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@RequiredArgsConstructor
public class SysMenuRefRoleServiceImpl extends ServiceImpl<SysMenuRefRoleMapper, SysMenuRefRole> implements SysMenuRefRoleService {

	private final PermissionCache permissionCache;
	
	
	/**
	 * 递归获取指定菜单的所有父级菜单ID
	 * @param menuId 菜单ID
	 * @return 所有父级菜单ID的集合
	 */
	private Set<String> getAllParentMenuIds(String menuId) {
		Set<String> parentIds = new HashSet<>();
		if (menuId == null || menuId.trim().isEmpty()) {
			return parentIds;
		}

		SysMenuService sysMenuService = SpringUtil.getBean(SysMenuService.class);
		String currentMenuId = menuId;
		Set<String> visited = new HashSet<>(); // 防止循环引用

		while (currentMenuId != null && !currentMenuId.equals("0") && !visited.contains(currentMenuId)) {
			visited.add(currentMenuId);
			SysMenu menu = sysMenuService.getById(currentMenuId);
			if (menu == null || menu.getParentId() == null || menu.getParentId().equals("0")) {
				break;
			}
			parentIds.add(menu.getParentId());
			currentMenuId = menu.getParentId();
		}

		return parentIds;
	}

	@Transactional
	@Override
	public void grant(SysMenuGrantDTO dto) {
		// 先删除
		Integer roleId = dto.getRoleId();
		LambdaQueryWrapper<SysMenuRefRole> deleteWhere = new LambdaQueryWrapper<SysMenuRefRole>()
			.eq(SysMenuRefRole::getRoleId, roleId);
		this.baseMapper.delete(deleteWhere);

		// 补充父级菜单ID
		Set<String> allMenuIds = new HashSet<>(dto.getMenuIdList());

		for (String menuId : dto.getMenuIdList()) {
			Set<String> parentIds = getAllParentMenuIds(menuId);
			allMenuIds.addAll(parentIds);
		}

		// 补充工作台菜单id
		List<String> dashboardMenuIds = SpringUtil.getBean(SysMenuService.class).dashboardMenuIds();
		allMenuIds.addAll(dashboardMenuIds);

		// 再新增
		List<SysMenuRefRole> entityList = allMenuIds.stream()
			.map(menuId -> new SysMenuRefRole(roleId, menuId))
			.collect(Collectors.toList());
		this.saveBatch(entityList);

		// 重置缓存
		this.permissionCache.reloadCache();
	}
	
	@Override
	public List<String> getMenuIds(Integer roleId) {
		return this.permissionCache.getMenuIds(roleId);
	}

	@Override
	public List<String> getPermissionList() {
		List<Integer> roleList = StpUtil.getRoleList().stream().map(t -> new Integer(t)).collect(Collectors.toList());
		Integer roleId = roleList.get(0); // 本项目只有一个角色
		return this.permissionCache.getPermissions(roleId);
	}
}
