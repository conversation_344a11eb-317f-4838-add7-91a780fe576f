<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>公告管理</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="类型">
          <dict-select v-model="formInline.type" category-id="NOTIFICATION_TYPE" placeholder="公告类型"
            style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item label="标题">
          <el-input v-model="formInline.title" placeholder="标题"></el-input>
        </el-form-item>
        <!-- <el-form-item label="发布人">
          <user-selector v-model="selectedUsers" :multiple="true" @select="handleUsersSelected"></user-selector>
        </el-form-item> -->
        <el-form-item label="发布时间">
          <el-date-picker v-model="formInline.publishTimeRange" type="daterange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
          <el-button v-permission="['sys-notification:insert']" type="primary" icon="el-icon-plus" @click="handleAdd">添加</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" border style="width: 100%;" v-loading="loading"
        @sort-change="handleSortChange">
        <el-table-column prop="id" label="ID" width="80" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="type" label="类型" align="center" sortable="custom" width="110">
          <template slot-scope="scope">
            <el-tag :type="getNotificationTagType(scope.row.type)" size="mini">{{ getNotificationTypeName(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" width="180" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="content" label="内容" width="280" align="center" show-overflow-tooltip>
        </el-table-column>
        <!-- <el-table-column prop="publishUserId" label="发布人" width="100" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ getUserNameById(scope.row.publishUserId) }}
          </template>
        </el-table-column> -->
        <el-table-column prop="publishTime" label="发布时间" width="160" align="center" sortable="custom">
        </el-table-column>
        <el-table-column label="操作" min-width="120" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button v-permission="['sys-notification:delete']" size="mini" type="text" style="color: #F56C6C" @click="handleDelete(scope.row)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-table-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

     <!-- 新增对话框 -->
     <sys-notification-add :visible.sync="addDialogVisible" @success="handleAddSuccess" />

     <!-- 查看详情对话框 -->
     <sys-notification-detail :visible.sync="detailDialogVisible" :notification-data="currentNotificationData" />
  </div>
</template>

<script>
import { notificationPage, deleteNotification } from '../../api/WkbNotification'
import SysNotificationAdd from './SysNotificationAdd.vue'
import SysNotificationDetail from './SysNotificationDetail.vue'
import { dictCategoryItems } from '../../api/SysDictItem'
import { sysUserList } from '../../api/SysUser'
import Pagination from '../../components/Pagination'
import DictSelect from '../../components/DictSelect'
import UserSelector from '../../components/UserSelector'
import qs from 'qs'
import { mapState } from 'vuex'

export default {
  name: 'Notification',
  components: {
    Pagination,
    DictSelect,
    UserSelector,
    SysNotificationAdd,
    SysNotificationDetail
  },
  computed: {
    ...mapState(['userEntity'])
  },
  data() {
    return {
      selectedUsers: [],
      userMap: {}, // 用户ID到用户信息的映射
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        type: '',
        title: '',
        publishUserId: '',
        publishTimeStart: '',
        publishTimeEnd: '',
        publishTimeRange: [],
        notificationTypes: [], // 通知类型字典
        orders: []
      },
      // 表格数据
      tableData: [],
      // 新增对话框
      addDialogVisible: false,
      // 查看详情对话框
      detailDialogVisible: false,
      currentNotificationData: {},
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false
    }
  },
  created() {
    // 获取分页数据
    this.getPageData()
    this.loadNotificationTypes()
  },
  methods: {
    // 加载通知类型字典
    loadNotificationTypes() {
      dictCategoryItems({ categoryId: 'NOTIFICATION_TYPE' })
        .then(res => {
          if (res.success && res.data) {
            this.notificationTypes = res.data;
          }
        })
        .catch(err => {
          console.error('获取通知类型字典失败:', err);
        });
    },
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = this.formInline
      }

      // 处理日期范围
      if (parameter.publishTimeRange && parameter.publishTimeRange.length === 2) {
        parameter.publishTimeStart = parameter.publishTimeRange[0]
        parameter.publishTimeEnd = parameter.publishTimeRange[1]
      } else {
        parameter.publishTimeStart = undefined
        parameter.publishTimeEnd = undefined
      }

      notificationPage(parameter)
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
          this.pageParam.currentPage = res.data.current
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total

          // 批量查询用户信息
          this.loadUserInfo()
        })
        .catch(err => {
          this.loading = false
          this.$message.error('获取数据失败：' + err.message)
        })
    },

    // 批量查询用户信息
    loadUserInfo() {
      // 提取当前页所有记录中的userId
      const userIds = new Set()
      this.tableData.forEach(row => {
        if (row.publishUserId) {
          userIds.add(row.publishUserId)
        }
      })

      if (userIds.size > 0) {
        const userIdList = Array.from(userIds)
        sysUserList({ idList: userIdList })
          .then(res => {
            if (res.success && res.data) {
              // 构建用户ID到用户信息的映射
              this.userMap = {}
              res.data.forEach(user => {
                this.userMap[user.userId] = user
              })
            }
          })
          .catch(err => {
            console.error('批量查询用户信息失败:', err)
          })
      }
    },

    // 分页回调
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },

    // 排序处理
    handleSortChange(column) {
      if (column.order != null) {
        let sortProp = column.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
        let orderBy = { "column": sortProp, "asc": column.order === 'ascending' }
        this.formInline.orders[0] = orderBy
      } else {
        this.formInline.orders = []
      }
      this.getPageData()
    },
    // 新增
    handleAdd() {
      this.addDialogVisible = true;
    },
    // 新增成功回调
    handleAddSuccess() {
      this.getPageData();
    },
    // 查看详情
    handleView(row) {
      this.currentNotificationData = { ...row };
      this.detailDialogVisible = true;
    },

    // 获取通知类型名称
    getNotificationTypeName(type) {
      const item = this.notificationTypes.find(t => t.value === type);
      return item ? item.label : type;
    },
    // 获取通知类型标签样式
    getNotificationTagType(value) {
      if (!value) return '';
      const item = this.notificationTypes.find(item => item.value === value);
      return item ? item.color : '';
    },

    // 搜索
    onSearch() {
      this.formInline.current = 1
      this.getPageData()
    },

    // 重置
    onReset() {
      this.selectedUsers = []
      this.formInline = {
        current: 1,
        size: 10,
        type: '',
        title: '',
        publishUserId: '',
        publishTimeStart: '',
        publishTimeEnd: '',
        publishTimeRange: [],
        orders: []
      }
      this.getPageData()
    },

    // 处理用户选择
    handleUsersSelected(selectedUsers) {
      if (selectedUsers && selectedUsers.length > 0) {
        this.formInline.publishUserId = selectedUsers[0].userId
      } else {
        this.formInline.publishUserId = ''
      }
    },

    // 获取用户姓名
    getUserNameById(userId) {
      if (!userId) return ''
      const user = this.userMap[userId]
      return user ? user.realName : '-'
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该条公告吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteNotification({ idList: [row.id] })
          .then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getPageData()
          })
          .catch(err => {
            this.$message.error('删除失败：' + err.message)
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: left;
}

.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}

.empty-table-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}
</style>
