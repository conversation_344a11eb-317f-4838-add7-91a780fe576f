package com.my.crossborder.exception;

import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;

import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import com.google.common.collect.Sets;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.util.BeanValidators;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * 统一处理controller的各种Exception
 * @date 2020年5月15日
 */
@ControllerAdvice(basePackages = "com.my.crossborder")  // 重要：注意这个路径
@ResponseBody
@SuppressWarnings("rawtypes")
@RequiredArgsConstructor
@Slf4j
public class ExceptionAspect {
	
	
	// 401异常
	@ResponseStatus(HttpStatus.UNAUTHORIZED)
	@ExceptionHandler(value = NotLoginException.class)
	public StdResp $401Exception(HttpServletRequest req, NotLoginException e) {
		log.error("Err-uri:{}, Err-detail:{}", req.getRequestURL(), "接口401");
		return StdResp.fail("UNAUTHORIZED", e.getMessage());
	}
	
	// 404异常
    @ResponseStatus(HttpStatus.NOT_FOUND)
	@ExceptionHandler(value = NoHandlerFoundException.class)
	public StdResp $404Exception(HttpServletRequest req, NoHandlerFoundException e) {
		log.error("Err-uri:{}, Err-detail:{}", e.getRequestURL(), "接口404");
		return StdResp.fail("RESOURCE NOT FOUND");
	}
	
	// 405异常
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
	@ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
	public StdResp $HttpRequestMethodNotSupportedException(HttpServletRequest req, Exception e) {
		log.error("Err-uri:{}, Err-detail:{}", req.getRequestURI(), e.getMessage());
		return StdResp.fail("METHOD_NOT_SUPPORTED");
	}
    
    // contentType不正确
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = HttpMediaTypeNotSupportedException.class)
    public StdResp $HttpMediaTypeNotSupportedException(HttpServletRequest req, Exception e) {
    	log.error("Err-uri:{}, Err-detail:{}",req.getRequestURI(), e.getMessage());
    	return StdResp.fail("WRONG CONTENT TYPE", "接口请求格式有误");
    }
    
    // BusinessException
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = BusinessException.class)
    public StdResp $BusinessException(HttpServletRequest req, BusinessException e) {
    	log.error("Err-uri:{}, Err-detail:{}",req.getRequestURI(), e.getMessage());
    	return StdResp.fail("BusinessException", e.getMessage());
    }

	/**
	 * requestBody为空、JSON转换失败
	 */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler(value = HttpMessageNotReadableException.class)
	public StdResp $HttpMessageNotReadableException(HttpServletRequest req, HttpMessageNotReadableException e) {
		log.error("Err-uri:{}, Err-detail:{}", req.getRequestURI(), e.getMessage());
		if (e.getMessage().startsWith("Required request body is missing")) {
			return StdResp.fail("PARAM MISSING BODY");
		} else if (e.getMessage().startsWith("JSON parse error: Cannot deserialize")) {
			return StdResp.fail("PARAM JSON INVALID");
		} else {
			return StdResp.fail("PARAM INVALID");
		}
	}
	
	/** 
	 * requestParam为true, 但是没有传值时的异常
	 */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler({ MissingServletRequestParameterException.class })
	public StdResp jsr303(MissingServletRequestParameterException e, HttpServletRequest request) {
		log.error("Err-uri:{}, Err-detail:{}", request.getRequestURI(), e.getMessage());
		return StdResp.fail("PARAM INVALID", e.getMessage());
	}
	
	/** 
	 * jsr303, 请求参数有误
	 */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler({ MethodArgumentTypeMismatchException.class })
	public StdResp jsr303(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
		log.error("Err-uri:{}, Err-detail:{}", request.getRequestURI(), e.getMessage());
		return StdResp.fail("PARAM INVALID", e.getMessage());
	}
	
	/** 
	 * jsr303, 请求参数违反约束
	 */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
	@ExceptionHandler({ ConstraintViolationException.class })
	public StdResp jsr303(ConstraintViolationException e, HttpServletRequest request) {
		List<String> data = new LinkedList<String>();
		data = BeanValidators.extractPropertyAndMessageAsList(e.getConstraintViolations(), ":");  
		log.error("Err-uri:{}, Err-detail:{}", request.getRequestURI(), data.toString());
		
		return StdResp.fail("PARAM INVALID", CollectionUtil.join(data, ";"));
	}
	
	/** 
	 * jsr303, 请求参数违反约束
	 */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler( {MethodArgumentNotValidException.class, BindException.class} )
    public StdResp jsr303(Exception e, HttpServletRequest request) {
    	List<FieldError> fieldErrors = null;
    	if (e instanceof MethodArgumentNotValidException) {
    		fieldErrors = ((MethodArgumentNotValidException)e).getBindingResult().getFieldErrors(); 
    	} else {
    		fieldErrors = ((BindException)e).getBindingResult().getFieldErrors(); 
    	}
    	Set<String> data = Sets.newTreeSet();
		for (FieldError fieldError : fieldErrors) {
//			String field = fieldError.getField();
			String message = fieldError.getDefaultMessage();
			data.add(message);
		}
		log.error("Err-uri:{}, Err-detail:{}", request.getRequestURI(), data.toString());
		
		return StdResp.fail("PARAM INVALID", CollectionUtil.join(data, ";"));
    }
    
	
	// saToken注解鉴权 - 无此权限
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(value = NotPermissionException.class)
	public StdResp NotPermissionException(HttpServletRequest req, NotPermissionException e) {
		log.error("Err-uri:{}, Err-detail:{}", req.getRequestURL(), "无此权限");
		return StdResp.fail("NotPermission", e.getMessage());
	}
    
    /** saToken注解鉴权 - 无此角色 */
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler({ NotRoleException.class})
    public StdResp $NotRoleException(NotRoleException e, HttpServletRequest request) {
    	log.error("Err-uri:{}, Err-detail:{}",request.getRequestURI(), e.getMessage());
    	return StdResp.fail("NO SUCH ROLE", "无此权限");
    }
    
	/** 500错误 */
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler({ Exception.class})
	public StdResp server500(Exception e, HttpServletRequest request) {
		log.error("Err-uri:{}, Err-detail:{}",request.getRequestURI(), e.getMessage(), e);
		return StdResp.fail("SERVER ERROR", e.getMessage());
	}
}
