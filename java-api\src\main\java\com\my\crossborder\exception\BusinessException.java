package com.my.crossborder.exception;

import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

/**
 * 业务异常
 */
@SuppressWarnings("serial")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BusinessException extends RuntimeException {

    
    public BusinessException(String message) {
        super(message);
    }


    /**
     * 发生业务异常
     * @param errMsg 异常信息
     */
    public static void by(String errMsg) {
    	throw new BusinessException(errMsg);
    }
    
    /**
     * 发生业务异常
     * @param msgTemplate 异常信息模板
     * @param args 模板参数
     */
    public static void by(String msgTemplate, Object... args) {
    	String msg = StrUtil.format(msgTemplate, args);
    	by(msg);
    }

    /**
     * 当条件成立时，发生业务异常
     * @param condition
     * @param msgTemplate 异常信息模板
     * @param args 模板参数
     */
    public static void when(Boolean condition, String msgTemplate, Object... args) {
        if (condition) {
        	String msg = StrUtil.format(msgTemplate, args);
        	by(msg);
        }
    }


}
