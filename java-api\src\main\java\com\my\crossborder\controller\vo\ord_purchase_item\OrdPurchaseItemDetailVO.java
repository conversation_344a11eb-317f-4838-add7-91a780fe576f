package com.my.crossborder.controller.vo.ord_purchase_item;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_采购订单明细表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseItemDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单明细id
     */
    private String orderItemId;

    /**
     * 采购途径 字典PURCHASE_CHANNEL
     */
    private String purchaseChannel;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购人id
     */
    private Integer purchaseUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
