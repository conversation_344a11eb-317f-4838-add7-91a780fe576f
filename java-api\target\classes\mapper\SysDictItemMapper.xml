<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysDictItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysDictItem">
        <id column="category_id" property="categoryId" />
        <id column="item_value" property="itemValue" />
        <result column="item_name" property="itemName" />
        <result column="color" property="color" />
        <result column="sort_num" property="sortNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        category_id, item_value, item_name, color, sort_num
    </sql>

	<!-- 分页 -->
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_dict_item.SysDictItemPageVO">
		SELECT
			category_id, item_value, item_name, color, sort_num
		FROM
			sys_dict_item AS t1
		<where>
        	1=1
	        <if test="categoryId != null and categoryId != ''">
	           	AND t1.category_id = #{categoryId}
            </if>
	        <if test="itemValue != null and itemValue != ''">
	           	AND t1.item_value like concat('%', #{itemValue}, '%')
            </if>
	        <if test="itemName != null and itemName != ''">
	           	AND t1.item_name like concat('%', #{itemName}, '%')
            </if>
	        <if test="sortNum != null and sortNum != ''">
	           	AND t1.sort_num = #{sortNum}
            </if>
        </where>
    </select>

</mapper>
