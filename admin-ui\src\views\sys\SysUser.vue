<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>人员管理</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主要内容区域 -->
    <div class="personnel-management-container" style="margin-top: 20px;">
      <el-row :gutter="20">
        <!-- 左侧角色树 -->
        <el-col :span="4">
          <el-card class="box-card" style="height: 650px;">
            <div slot="header" class="clearfix">
              <span>角色</span>
              <div style="float: right;">
                <span style="margin-right: 10px; color: #999; font-size: 14px;">
                  {{ currentRole }}
                </span>
              </div>
            </div>
            <el-tree :data="roleTreeData" :props="defaultProps" :default-expand-all="true" :expand-on-click-node="false"
              :check-on-click-node="false" :auto-expand-parent="true" :disabled="false" @node-click="handleRoleClick"
              @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" class="role-tree">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span>{{ node.label }}</span>
              </span>
            </el-tree>
          </el-card>
        </el-col>

        <!-- 右侧人员管理 -->
        <el-col :span="20">
          <el-card class="box-card">

            <!-- 搜索表单 -->
            <el-form :inline="true" :model="formInline" class="demo-form-inline">
              <el-form-item label="用户名">
                <el-input v-model="formInline.username" placeholder="请输入用户名" clearable style="width: 150px;"></el-input>
              </el-form-item>
              <el-form-item label="姓名">
                <el-input v-model="formInline.realName" placeholder="请输入姓名" clearable style="width: 150px;"></el-input>
              </el-form-item>
              <el-form-item label="人员状态">
                <dict-select v-model="formInline.status" category-id="USER_STATUS" placeholder="人员状态"
                  style="width: 120px">
                </dict-select>
              </el-form-item>
              <el-form-item>
                <el-button size="small" type="primary" @click="onSearch">查询</el-button>
                <el-button size="small" @click="onReset">重置</el-button>
                <el-button v-permission="['sys-user:insert']" size="small" type="primary" icon="el-icon-plus"
                  @click="handleAdd">添加</el-button>
              </el-form-item>
            </el-form>

            <!-- 数据表格 -->
            <el-table :data="filteredTableData" border style="width: 100%; margin-top: 15px;" v-loading="loading">
              <el-table-column prop="username" label="用户名" width="150" align="center">
              </el-table-column>
              <el-table-column prop="realName" label="姓名" width="140" align="center">
              </el-table-column>
              <dict-table-column prop="roleId" label="角色" category-id="ROLE_ID" width="100">
              </dict-table-column>
              <dict-table-column prop="status" label="人员状态" category-id="USER_STATUS" width="90">
              </dict-table-column>
              <el-table-column prop="phone" label="联系电话" width="130" align="center">
              </el-table-column>
              <el-table-column prop="phoneSecondary" label="备用联系电话" width="130" align="center">
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="80" align="center" show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="操作" min-width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button v-permission="['sys-user:update']" size="mini" type="text" @click="handleEdit(scope.row)">
                    <i class="el-icon-edit"></i> 编辑
                  </el-button>
                  <el-button v-permission="['sys-user:reset-pwd']" size="mini" type="text"
                    @click="handleResetPwd(scope.row)">
                    <i class="el-icon-refresh"></i> 重置密码
                  </el-button>
                  <el-button v-permission="['sys-user:delete']" size="mini" type="text" style="color: #F56C6C"
                    @click="handleDelete(scope.row)">
                    <i class="el-icon-delete"></i> 删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" style="margin-top: 10px; text-align: left;">
              <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="currentPage" :page-sizes="[10, 20, 30, 50]" :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="total">
              </el-pagination>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 编辑对话框 -->
    <user-edit :visible.sync="editDialogVisible" :edit-data="currentEditData" :position-tree-data="roleTreeData"
      @success="handleEditSuccess" />

    <!-- 查看详情对话框 -->
    <sys-user-detail :visible.sync="detailDialogVisible" :user-data="currentViewData" />
  </div>
</template>

<script>
import DictTableColumn from '../../components/DictTableColumn.vue'
import UserEdit from './SysUserEdit.vue'
import SysUserDetail from './SysUserDetail.vue'
import { sysUserPage, sysUserResetPwd, sysUserDelete } from '@/api/SysUser'
import DictSelect from '../../components/DictSelect.vue'

export default {
  name: 'UserManagement',
  components: {
    UserEdit,
    DictTableColumn,
    SysUserDetail,
    DictSelect
  },
  data() {
    return {
      // 当前选中的角色
      currentRole: '全部',
      // 角色树数据
      roleTreeData: [
        {
          id: '0',
          label: '全部',
          children: [
            {
              id: '11',
              label: '系统管理员',
              leaf: true
            },
            {
              id: '12',
              label: '合伙人',
              leaf: true
            },
            {
              id: '21',
              label: '客服',
              leaf: true
            },
            {
              id: '22',
              label: '客服主管',
              leaf: true
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 搜索表单数据
      formInline: {
        username: '',
        realName: '',
        roleId: '',
        status: ''
      },
      // 表格数据
      tableData: [],
      // 分页数据
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      // 编辑对话框
      editDialogVisible: false,
      currentEditData: null,

      // 查看详情对话框
      detailDialogVisible: false,
      currentViewData: null
    }
  },
  created() {
    this.fetchData();
  },
  computed: {
    // 过滤后的表格数据
    filteredTableData() {
      let data = this.tableData;

      // 根据左侧组织架构选择过滤
      // if (this.currentRole && this.currentRole !== '全部角色') {
      //   // 如果选中的是父级节点，显示所有子级角色的人员
      //   if (this.currentRole === '管理人员') {
      //     data = data.filter(item => ['单位主要负责人', '安全总监', '安全员'].includes(item.roleName));
      //   } else if (this.currentRole === '运行人员') {
      //     data = data.filter(item => ['电站锅炉司炉', '锅炉水处理作业'].includes(item.roleName));
      //   } else if (this.currentRole === '技术人员') {
      //     data = data.filter(item => item.roleName === '技术人员');
      //   } else {
      //     // 如果选中的是具体角色，只显示该角色的人员
      //     data = data.filter(item => item.roleName === this.currentRole);
      //   }
      // }

      return data;
    }
  },
  methods: {
    // 获取数据
    fetchData() {
      this.loading = true;
      const params = {
        current: this.currentPage,
        size: this.pageSize,
        username: this.formInline.username || undefined,
        realName: this.formInline.realName || undefined,
        roleId: this.formInline.roleId || undefined,
        status: this.formInline.status || undefined
      };



      sysUserPage(params)
        .then(res => {
          if (res.success) {
            this.tableData = res.data.records;
            this.total = res.data.total;
          } else {
            this.$message.error(res.msg || '获取数据失败');
          }
        })
        .catch(err => {
          console.error('获取数据失败', err);
          this.$message.error('获取数据失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },



    // 角色树点击事件
    handleRoleClick(data) {
      this.currentRole = data.label;
      // 设置formInline.roleId
      if (data.id === '0') {
        // 全部
        this.formInline.roleId = '';
      } else {
        this.formInline.roleId = data.id;
      }

      // 重置分页到第一页
      this.currentPage = 1;
      // 重新获取数据
      this.fetchData();
      console.log('选中角色：', data.label, '表单角色ID：', this.formInline.roleId);
    },

    // 搜索
    onSearch() {
      this.currentPage = 1;
      this.fetchData();
    },

    // 重置搜索条件
    onReset() {
      this.formInline = {
        username: '',
        realName: '',
        roleId: '',
        status: ''
      };
      this.currentPage = 1;
      this.fetchData();
    },

    // 新增
    handleAdd() {
      this.currentEditData = null;
      this.editDialogVisible = true;
    },

    // 查看
    handleView(row) {
      this.currentViewData = { ...row };
      this.detailDialogVisible = true;
    },

    // 编辑
    handleEdit(row) {
      this.currentEditData = { ...row };
      this.editDialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该人员信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sysUserDelete({ idList: [row.userId] })
          .then(res => {
            if (res.success) {
              this.$message.success('删除成功');
              // 删除成功后刷新表格数据
              this.fetchData();
            } else {
              this.$message.error(res.msg || '删除失败');
            }
          })
          .catch(err => {
            console.error('删除失败', err);
          });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.fetchData();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchData();
    },

    // 编辑成功回调
    handleEditSuccess(data) {
      this.fetchData();
      this.$message({
        type: 'success',
        message: data.userId ? '更新成功!' : '新增成功!'
      });
    },

    // 节点展开事件
    handleNodeExpand(data) {
      console.log('节点展开：', data.label);
    },

    // 节点折叠事件
    handleNodeCollapse(data) {
      console.log('节点折叠：', data.label);
    },

    // 重置密码
    handleResetPwd(row) {
      this.$confirm('确认要重置该用户的密码吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用重置密码接口
        sysUserResetPwd({ userId: row.userId })
          .then(res => {
            if (res.success) {
              this.$message.success('密码重置成功');
            } else {
              this.$message.error(res.msg || '密码重置失败');
            }
          })
          .catch(err => {
            console.error('密码重置失败', err);
            this.$message.error('密码重置失败，请稍后重试');
          });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重置密码'
        });
      });
    },

  }
}
</script>

<style scoped>
.personnel-management-container {
  height: calc(100vh - 150px);
}

.role-tree {
  margin-top: 10px;
}

/* 隐藏树组件的展开/折叠图标，禁止用户手动折叠节点 */
.role-tree>>>.el-tree-node__expand-icon {
  display: none !important;
}

.role-tree>>>.el-tree-node__content {
  padding-left: 18px !important;
  cursor: pointer;
}

/* 禁用树节点的点击事件来防止折叠 */
.role-tree>>>.el-tree-node__content:hover {
  background-color: #f5f7fa;
}

/* 确保子节点正确缩进 */
.role-tree>>>.el-tree-node__children {
  overflow: visible;
}

/* 二级节点缩进 */
.role-tree>>>.el-tree-node__children .el-tree-node__content {
  padding-left: 36px !important;
}

/* 三级节点缩进 */
.role-tree>>>.el-tree-node__children .el-tree-node__children .el-tree-node__content {
  padding-left: 54px !important;
}

/* 激活状态样式 */
.role-tree>>>.el-tree-node.is-current>.el-tree-node__content {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 防止节点内容换行 */
.role-tree>>>.el-tree-node__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.box-card {
  overflow: hidden;
}

.demo-form-inline .el-form-item {
  margin-bottom: 15px;
}

.pagination-container {
  padding: 20px 0;
}

.el-icon-paperclip {
  margin-left: 5px;
  font-size: 16px;
  color: #409EFF;
  cursor: pointer;
}

.expired {
  color: #F56C6C;
  /* font-weight: bold; */
}
</style>
