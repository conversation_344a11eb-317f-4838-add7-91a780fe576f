package com.my.crossborder.forest.erp990.address;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.dtflys.forest.callback.AddressSource;
import com.dtflys.forest.http.ForestAddress;
import com.dtflys.forest.http.ForestRequest;

import lombok.Data;

/**
 * 接口baseURL
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "com.my.forest.erp990.address")
public class Erp990Address 
					implements AddressSource {

	/**
	 * 协议(http或https)
	 */
	private String scheme;
	
	/**
	 * IP
	 */
	private String host;
	
	/**
	 * 端口
	 */
	private Integer port;
	
	
	
	@SuppressWarnings("rawtypes")
	@Override
	public ForestAddress getAddress(ForestRequest req) {
		// 添加空值检查，防止配置未正确加载
		if (this.scheme == null || this.host == null) {
			throw new IllegalStateException(this.getClass().getSimpleName() + "配置未正确加载! 请检查forest配置项");
		}
		return new ForestAddress(this.scheme, this.host, this.port);
	}
	
}
