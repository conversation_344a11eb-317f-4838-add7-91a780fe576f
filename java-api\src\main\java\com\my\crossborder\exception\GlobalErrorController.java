package com.my.crossborder.exception;

import javax.servlet.RequestDispatcher;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.stereotype.Controller;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.util.NestedServletException;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import lombok.extern.slf4j.Slf4j;

/**
 * 全局错误控制器，将错误转换为异常并抛出
 * 让ExceptionAspect统一处理所有异常
 */
@Controller
@Slf4j
public class GlobalErrorController implements ErrorController {

    private static final String ERROR_PATH = "/error";

    @RequestMapping(ERROR_PATH)
    public void handleError(HttpServletRequest request, HttpServletResponse response) throws Throwable {
        // 获取错误状态码
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);
        Integer statusCode = null;
        if (status != null) {
            statusCode = Integer.valueOf(status.toString());
        }

        // 获取请求URI
        String requestUri = (String) request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI);
        if (requestUri == null) {
            requestUri = request.getRequestURI();
        }

        // 获取异常信息
        Throwable exception = (Throwable) request.getAttribute(RequestDispatcher.ERROR_EXCEPTION);
        String message = (String) request.getAttribute(RequestDispatcher.ERROR_MESSAGE);

        log.debug("Global Error Controller - URI: {}, Status: {}, Message: {}", 
                 requestUri, statusCode, message);

        // 特殊处理：如果是 NestedServletException 包含 Sa-Token 相关异常，直接抛出原始异常
        if (exception instanceof NestedServletException) {
            NestedServletException nested = (NestedServletException) exception;
            Throwable rootCause = nested.getRootCause();
            if (rootCause instanceof NotLoginException || 
                rootCause instanceof NotPermissionException || 
                rootCause instanceof NotRoleException) {
                // 直接抛出原始的 Sa-Token 异常，让 ExceptionAspect 处理
                throw rootCause;
            }
        }

        // 根据状态码抛出相应的异常，让ExceptionAspect处理
        if (statusCode != null) {
            switch (statusCode) {
                case 404:
                    // 抛出NoHandlerFoundException，让ExceptionAspect的404处理器处理
                    throw new NoHandlerFoundException(request.getMethod(), requestUri, null);
                case 405:
                    // 抛出HttpRequestMethodNotSupportedException，让ExceptionAspect处理
                    throw new HttpRequestMethodNotSupportedException(request.getMethod());
                case 500:
                    // 如果有原始异常，直接抛出 
                	throw exception;
                default:
                	throw exception;
            }
        }

        // 默认情况，抛出通用异常
        String errorMsg = message != null ? message : "Unknown Error";
        throw new RuntimeException(errorMsg);
    }

    /**
     * 返回错误路径
     * Spring Boot 2.3+ 版本已废弃此方法，但为了兼容性保留
     */
    public String getErrorPath() {
        return ERROR_PATH;
    }
} 