package com.my.crossborder.controller.dto.sys_erp_account;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_禾宸物流接口账号
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysErpAccountPageDTO 
						extends PageDTO<SysErpAccountPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 禾宸用户名
     */
    private String username;

//    /**
//     * 禾宸密码(明文)
//     */
//    private String password;

}
