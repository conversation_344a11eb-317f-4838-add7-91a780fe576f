package com.my.crossborder.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 排班表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_shift")
public class SysShift implements Serializable {

    private static final long serialVersionUID = 1L;

    
    /**
     * 排班日期
     */
    @TableId(value = "shift_day", type = IdType.ASSIGN_ID)
    private LocalDate shiftDay;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 客服ID
     */
    private Integer serviceUserId;

    /**
     * 主管ID
     */
    private Integer supervisorUserId;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
