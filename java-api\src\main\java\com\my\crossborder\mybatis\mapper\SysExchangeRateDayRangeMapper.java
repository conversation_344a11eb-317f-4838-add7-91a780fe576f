package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangePageDTO;
import com.my.crossborder.controller.vo.sys_exchange_rate_day_range.SysExchangeRateDayRangePageVO;
import com.my.crossborder.mybatis.entity.SysExchangeRateDayRange;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 汇率日期区间 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface SysExchangeRateDayRangeMapper extends BaseMapper<SysExchangeRateDayRange> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysExchangeRateDayRangePageVO> page(SysExchangeRateDayRangePageDTO pageDTO);
	
}
