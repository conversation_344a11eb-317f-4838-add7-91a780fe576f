<template>
  <div class="content-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="用户名">
        <el-input v-model="formInline.username" placeholder="请输入用户名"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch" icon="el-icon-search">查询</el-button>
        <el-button @click="onReset" icon="el-icon-refresh">重置</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">添加</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" border style="width: 100%; margin-top: 20px;" v-loading="loading">
      <!-- <el-table-column prop="id" label="编号" width="180" align="center">
      </el-table-column> -->
      <el-table-column prop="username" label="用户名" width="200" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="password" label="密码" width="200" align="center">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <span>{{ scope.row.showPassword ? scope.row.password : '***' }}</span>
            <el-button
              type="text"
              @click="togglePassword(scope.row)"
              style="margin-left: 10px; padding: 0;"
              icon="el-icon-view"
            ></el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="160" align="left">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">
            <i class="el-icon-edit"></i> 修改
          </el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)" style="color: #F56C6C">
            <i class="el-icon-delete"></i> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" placeholder="请输入用户名" :readonly="!isAdd"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="editForm.password"
            :type="showEditPassword ? 'text' : 'password'"
            placeholder="请输入密码"
          >
            <el-button
              slot="append"
              @click="showEditPassword = !showEditPassword"
              :icon="'el-icon-view'"
            ></el-button>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmEdit" :loading="editLoading">确定</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { pageSysErpAccount, insertSysErpAccount, updateSysErpAccount, deleteSysErpAccount } from '../../api/SysErpAccount'
import Pagination from '../../components/Pagination'

export default {
  name: 'SysErpAccount',
  components: {
    Pagination
  },
  data() {
    return {
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        username: ''
      },
      // 对话框
      dialogVisible: false,
      dialogTitle: '添加禾宸账号',
      isAdd: true,
      showEditPassword: false, // 编辑对话框密码显示状态
      // 编辑加载状态
      editLoading: false,
      // 编辑表单数据
      editForm: {
        id: '',
        username: '',
        password: ''
      },
      // 编辑表单验证规则
      editRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
        ]
      },
      // 表格数据
      tableData: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 加载状态
      loading: false
    }
  },
  created() {
    // 获取分页数据
    this.getPageData();
  },
  methods: {
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true;
      if (!parameter) {
        parameter = this.formInline;
      }

      pageSysErpAccount(parameter)
        .then(res => {
          this.loading = false;
          // 为每行数据添加密码显示状态
          this.tableData = (res.data.records || []).map(item => ({
            ...item,
            showPassword: false
          }));
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size;
          this.pageParam.total = res.data.total;
        })
    },

    // 查询
    onSearch() {
      this.formInline.current = 1;
      this.getPageData();
    },

    // 重置
    onReset() {
      this.formInline = {
        current: 1,
        size: 10,
        username: ''
      };
      this.getPageData();
    },

    // 切换密码显示状态
    togglePassword(row) {
      this.$set(row, 'showPassword', !row.showPassword);
    },

    // 添加
    handleAdd() {
      this.dialogTitle = '添加禾宸账号';
      this.isAdd = true;
      this.showEditPassword = false;
      this.editForm = {
        id: '',
        username: '',
        password: ''
      };
      this.dialogVisible = true;
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = '修改禾宸账号';
      this.isAdd = false;
      this.showEditPassword = false;
      this.editForm = {
        id: row.id,
        username: row.username,
        password: row.password
      };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该禾宸账号吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSysErpAccount({ idList: [row.id] })
          .then(res => {
            this.$message.success('删除成功!');
            this.getPageData();
          })
      }).catch(() => {
        this.$message('已取消删除');
      });
    },

    // 确认编辑
    confirmEdit() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          this.editLoading = true;

          const apiCall = this.isAdd ? insertSysErpAccount : updateSysErpAccount;
          const successMsg = this.isAdd ? '添加成功!' : '修改成功!';

          apiCall(this.editForm)
            .then(res => {
              this.editLoading = false;
              this.dialogVisible = false;
              this.$message.success(successMsg);
              this.getPageData();
            })
        } else {
          this.$message.error('请检查表单信息');
          return false;
        }
      });
    },

    // 分页回调
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage;
      this.formInline.size = parm.pageSize;
      this.getPageData();
    }
  }
}
</script>

<style scoped>
.content-container {
  padding: 0;
}

.el-button+.el-button {
  margin-left: 10px;
}
</style>
