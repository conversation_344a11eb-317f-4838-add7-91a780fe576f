<template>
  <div class="dashboard-container">
    <!-- 顶部问候 -->
    <div class="greeting-section">
      <div class="greeting-content">
        <h2>早上好，张工程师</h2>
        <div class="weather-date">
          <i class="el-icon-calendar"></i>
          <span>2024年2月15日 星期四</span>
          <!-- <i class="el-icon-cloudy"></i> -->
          <!-- <span>晴 23°C</span> -->
        </div>
      </div>
      <div class="quick-stats">
        <div class="stat-item">
          <div class="stat-number">5</div>
          <div class="stat-label">未读通知</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">12</div>
          <div class="stat-label">待办任务</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧待办事项 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>待办事项</h3>
          <div>
            <el-button type="success" size="mini" @click="handleAddTodo">
              <i class="el-icon-plus"></i> 添加
            </el-button>
            <el-link @click="viewAllTodos" style="margin-left: 10px;">查看全部 <i class="el-icon-arrow-right"></i></el-link>
          </div>
        </div>

        <!-- 分类标签 -->
        <div class="todo-tabs">
          <el-button
            :type="activeTab === 'all' ? 'primary' : ''"
            size="small"
            @click="activeTab = 'all'"
            class="tab-btn">
            全部
          </el-button>
          <el-button
            :type="activeTab === 'personnel' ? 'primary' : ''"
            size="small"
            @click="activeTab = 'personnel'"
            class="tab-btn">
            人员管理
          </el-button>
          <el-button
            :type="activeTab === 'management' ? 'primary' : ''"
            size="small"
            @click="activeTab = 'management'"
            class="tab-btn">
            管理制度
          </el-button>
          <el-button
            :type="activeTab === 'inspection' ? 'primary' : ''"
            size="small"
            @click="activeTab = 'inspection'"
            class="tab-btn">
            检验检测
          </el-button>
        </div>

        <!-- 待办列表 -->
        <div class="todo-list">
          <div v-for="item in filteredTodos" :key="item.id" class="todo-item" @click="handleTodo(item)">
            <div class="todo-icon">
              <i :class="item.icon" :style="{color: item.color}"></i>
            </div>
            <div class="todo-content">
              <div class="todo-title">{{ item.title }}</div>
              <div class="todo-desc">{{ item.description }}</div>
              <div class="todo-time">
                <i class="el-icon-time"></i>
                截止日期：{{ item.deadline }}
              </div>
            </div>
            <div class="todo-action">
              <el-button size="mini" :type="item.urgency" @click.stop="handleTodo(item)">
                {{ item.actionText }}
              </el-button>
            </div>
          </div>
          <div v-if="filteredTodos.length === 0" class="empty-todo-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </div>
      </div>

      <!-- 右侧通知公告 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>通知公告</h3>
          <div>
            <el-button type="success" size="mini" @click="handleAddNotification">
              <i class="el-icon-plus"></i> 添加
            </el-button>
            <el-link @click="viewAllNotifications" style="margin-left: 10px;">查看全部 <i class="el-icon-arrow-right"></i></el-link>
          </div>
        </div>

        <div class="notification-list">
          <div v-for="item in notifications" :key="item.id" class="notification-item" @click="handleNotification(item)">
            <div class="notification-indicator">
              <i class="el-icon-bell" :class="item.isNew ? 'new-notification' : ''"></i>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ item.title }}</div>
              <div class="notification-time">{{ item.time }}</div>
            </div>
          </div>
          <div v-if="notifications.length === 0" class="empty-notification-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </div>

        <div class="load-more">
          <el-button type="text" @click="loadMoreNotifications">加载更多</el-button>
        </div>
      </div>
    </div>

    <!-- 底部统计卡片 -->
    <!-- <div class="statistics-section">
      <div class="stat-card">
        <div class="stat-icon green">
          <i class="el-icon-user"></i>
        </div>
        <div class="stat-info">
          <div class="stat-title">在职人员总数</div>
          <div class="stat-value">156</div>
          <div class="stat-trend positive">+12人</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon blue">
          <i class="el-icon-document"></i>
        </div>
        <div class="stat-info">
          <div class="stat-title">制度文件总数</div>
          <div class="stat-value">238</div>
          <div class="stat-trend positive">+5个</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon orange">
          <i class="el-icon-s-tools"></i>
        </div>
        <div class="stat-info">
          <div class="stat-title">设备运行正常率</div>
          <div class="stat-value">98.5%</div>
          <div class="stat-trend positive">+1.2%</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon teal">
          <i class="el-icon-circle-check"></i>
        </div>
        <div class="stat-info">
          <div class="stat-title">检验任务完成率</div>
          <div class="stat-value">92.3%</div>
          <div class="stat-trend positive">+5%</div>
        </div>
      </div>
    </div> -->

    <!-- 添加待办事项对话框 -->
    <el-dialog
      title="添加待办事项"
      :visible.sync="addTodoVisible"
      width="600px"
      @close="resetAddTodoForm">
      <el-form :model="addTodoForm" :rules="addTodoRules" ref="addTodoForm" label-width="100px">
        <el-form-item label="待办标题" prop="title">
          <el-input v-model="addTodoForm.title" placeholder="请输入待办标题"></el-input>
        </el-form-item>
        <el-form-item label="所属模块" prop="module">
          <el-select v-model="addTodoForm.module" placeholder="请选择所属模块" style="width: 100%">
            <el-option label="人员管理" value="personnel"></el-option>
            <el-option label="管理制度" value="management"></el-option>
            <el-option label="设备管理" value="device"></el-option>
            <el-option label="检验检测" value="inspection"></el-option>
            <el-option label="风险管理" value="risk"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="待办内容" prop="content">
          <el-input
            type="textarea"
            v-model="addTodoForm.content"
            placeholder="请输入待办内容描述"
            :rows="3">
          </el-input>
        </el-form-item>
        <el-form-item label="截止日期" prop="deadline">
          <el-date-picker
            v-model="addTodoForm.deadline"
            type="date"
            placeholder="选择截止日期"
            style="width: 100%"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="分配方式" prop="assignType">
          <el-radio-group v-model="addTodoForm.assignType">
            <el-radio label="position">按岗位</el-radio>
            <el-radio label="person">按人员</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="addTodoForm.assignType === 'position'"
          label="选择岗位"
          prop="positions">
          <el-tree
            ref="positionTree"
            :data="positionTreeData"
            :props="{ children: 'children', label: 'label' }"
            show-checkbox
            node-key="id"
            style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px; max-height: 200px; overflow-y: auto;">
          </el-tree>
        </el-form-item>
        <el-form-item
          v-if="addTodoForm.assignType === 'person'"
          label="选择人员"
          prop="persons">
          <el-select
            v-model="addTodoForm.persons"
            multiple
            placeholder="请选择人员"
            style="width: 100%">
            <el-option
              v-for="person in personList"
              :key="person.value"
              :label="person.label"
              :value="person.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addTodoVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAddTodo">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加通知公告对话框 -->
    <el-dialog
      title="添加通知公告"
      :visible.sync="addNotificationVisible"
      width="600px"
      @close="resetAddNotificationForm">
      <el-form :model="addNotificationForm" :rules="addNotificationRules" ref="addNotificationForm" label-width="100px">
        <el-form-item label="通知标题" prop="title">
          <el-input v-model="addNotificationForm.title" placeholder="请输入通知标题"></el-input>
        </el-form-item>
        <el-form-item label="通知内容" prop="content">
          <el-input
            type="textarea"
            v-model="addNotificationForm.content"
            placeholder="请输入通知内容"
            :rows="5">
          </el-input>
        </el-form-item>
        <el-form-item label="通知接收人" prop="receivers">
          <el-tree
            ref="receiverTree"
            :data="positionTreeData"
            :props="{ children: 'children', label: 'label' }"
            show-checkbox
            node-key="id"
            style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px; max-height: 200px; overflow-y: auto;">
          </el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addNotificationVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAddNotification">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      activeTab: 'all',
      todos: [
        {
          id: 1,
          title: '人员管理 - 张三的证件过期',
          description: '张三的证件将于2024-02-20过期，请及时办理续期',
          deadline: '2024-02-20',
          type: 'personnel',
          urgency: 'danger',
          actionText: '待处理',
          icon: 'el-icon-user',
          color: '#F56C6C'
        },
        {
          id: 2,
          title: '人员管理 - 李四的证件过期',
          description: '李四的证件将于2024-02-25过期，请及时办理续期',
          deadline: '2024-02-25',
          type: 'personnel',
          urgency: 'warning',
          actionText: '待处理',
          icon: 'el-icon-user',
          color: '#E6A23C'
        },
        {
          id: 3,
          title: '人员管理 - 王五的证件过期',
          description: '王五的证件将于2024-03-01过期，请及时办理续期',
          deadline: '2024-03-01',
          type: 'personnel',
          urgency: 'warning',
          actionText: '待处理',
          icon: 'el-icon-user',
          color: '#E6A23C'
        },
        {
          id: 4,
          title: '人员管理 - 赵六的证件过期',
          description: '赵六的证件将于2024-02-15过期，请及时办理续期',
          deadline: '2024-02-15',
          type: 'personnel',
          urgency: 'danger',
          actionText: '已过期',
          icon: 'el-icon-user',
          color: '#F56C6C'
        },
        {
          id: 5,
          title: '人员管理 - 孙七的证件过期',
          description: '孙七的证件将于2024-02-10过期，请及时办理续期',
          deadline: '2024-02-10',
          type: 'personnel',
          urgency: 'danger',
          actionText: '已过期',
          icon: 'el-icon-user',
          color: '#F56C6C'
        },
        {
          id: 6,
          title: '人员管理 - 周八的证件过期',
          description: '周八的证件将于2024-02-16过期，请及时办理续期',
          deadline: '2024-02-16',
          type: 'personnel',
          urgency: 'danger',
          actionText: '待处理',
          icon: 'el-icon-user',
          color: '#F56C6C'
        },
        {
          id: 7,
          title: '人员管理 - 吴九的证件过期',
          description: '吴九的证件将于2024-02-22过期，请及时办理续期',
          deadline: '2024-02-22',
          type: 'personnel',
          urgency: 'warning',
          actionText: '待处理',
          icon: 'el-icon-user',
          color: '#E6A23C'
        },
        // {
        //   id: 8,
        //   title: '检验检测-管道年度检验计划审核',
        //   description: '2024年度管道检验计划需要审核确认',
        //   deadline: '2024-02-28',
        //   type: 'inspection',
        //   urgency: 'primary',
        //   actionText: '待审核',
        //   icon: 'el-icon-s-check',
        //   color: '#409EFF'
        // }
      ],
      notifications: [
        {
          id: 1,
          title: '关于开展2024年度管道安全检查的通知',
          time: '10分钟前',
          isNew: true
        },
        {
          id: 2,
          title: '系统维护通知：2月20日进行系统升级',
          time: '2小时前',
          isNew: true
        },
        {
          id: 3,
          title: '系统维护通知：2月20日进行系统升级',
          time: '1天前',
          isNew: false
        }
      ],
      addTodoVisible: false,
      addTodoForm: {
        title: '',
        module: '',
        content: '',
        deadline: '',
        assignType: 'position',
        positions: [],
        persons: []
      },
      addTodoRules: {
        title: [
          { required: true, message: '请输入待办标题', trigger: 'blur' }
        ],
        module: [
          { required: true, message: '请选择所属模块', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入待办内容描述', trigger: 'blur' }
        ],
        deadline: [
          { required: true, message: '请选择截止日期', trigger: 'change' }
        ],
        assignType: [
          { required: true, message: '请选择分配方式', trigger: 'change' }
        ]
      },
      positionTreeData: [],
      personList: [],
      addNotificationVisible: false,
      addNotificationForm: {
        title: '',
        content: '',
        receivers: []
      },
      addNotificationRules: {
        title: [
          { required: true, message: '请输入通知标题', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入通知内容', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.initPositionTreeData()
    this.initPersonList()
  },
  computed: {
    filteredTodos() {
      if (this.activeTab === 'all') {
        return this.todos
      }
      return this.todos.filter(todo => todo.type === this.activeTab)
    }
  },
  methods: {
    viewAllTodos() {
      this.$router.push('/todo-detail')
    },
    viewAllNotifications() {
      this.$router.push('/WkbNotificationDetail')
    },
    handleTodo(item) {
      this.$router.push({
        path: '/todo-detail',
        query: { id: item.id }
      })
    },
    handleNotification(item) {
      this.$router.push({
        path: '/WkbNotificationDetail',
        query: { id: item.id }
      })
    },
    loadMoreNotifications() {
      this.$message.info('加载更多通知')
    },
    handleAddTodo() {
      this.addTodoVisible = true
    },
    resetAddTodoForm() {
      this.$refs.addTodoForm.resetFields()
    },
    submitAddTodo() {
      this.$refs.addTodoForm.validate((valid) => {
        if (valid) {
          this.$message.success('待办事项添加成功')
          this.addTodoVisible = false
        } else {
          this.$message.error('请填写完整的信息')
        }
      })
    },
    initPositionTreeData() {
      this.positionTreeData = [
        {
          id: 'management',
          label: '管理人员',
          children: [
            { id: 'manager', label: '单位主要负责人' },
            { id: 'safety_director', label: '安全总监' },
            { id: 'safety_officer', label: '安全员' }
          ]
        },
        {
          id: 'operation',
          label: '运行人员',
          children: [
            { id: 'boiler_operator', label: '电站锅炉司炉' },
            { id: 'water_treatment', label: '锅炉水处理作业' }
          ]
        },
        {
          id: 'technical',
          label: '技术人员'
        }
      ]
    },
    initPersonList() {
      this.personList = [
        { value: 'EMP001', label: '张三（单位主要负责人）' },
        { value: 'EMP002', label: '李四（安全总监）' },
        { value: 'EMP003', label: '王五（安全员）' },
        { value: 'EMP004', label: '赵六（电站锅炉司炉）' },
        { value: 'EMP005', label: '孙七（锅炉水处理作业）' },
        { value: 'EMP006', label: '周八（安全员）' },
        { value: 'EMP007', label: '吴九（电站锅炉司炉）' },
        { value: 'EMP008', label: '郑十（锅炉水处理作业）' },
        { value: 'EMP010', label: '陈十二（电站锅炉司炉）' }
      ]
    },
    handleAddNotification() {
      this.addNotificationVisible = true
    },
    resetAddNotificationForm() {
      this.$refs.addNotificationForm.resetFields()
    },
    submitAddNotification() {
      this.$refs.addNotificationForm.validate((valid) => {
        if (valid) {
          this.$message.success('通知公告添加成功')
          this.addNotificationVisible = false
        } else {
          this.$message.error('请填写完整的信息')
        }
      })
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

/* 顶部问候区域 */
.greeting-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.greeting-content h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
}

.weather-date {
  color: #909399;
  font-size: 14px;
}

.weather-date i {
  margin: 0 5px;
}

.quick-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.left-panel,
.right-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-panel {
  flex: 2;
}

.right-panel {
  flex: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
}

/* 待办事项样式 */
.todo-tabs {
  margin-bottom: 20px;
}

.tab-btn {
  margin-right: 10px;
}

.todo-list {
  max-height: 400px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #EBEEF5;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.todo-item:hover {
  background-color: #f5f7fa;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-icon {
  margin-right: 15px;
  font-size: 20px;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-size: 16px;
  color: #303133;
  margin-bottom: 5px;
  font-weight: 500;
}

.todo-desc {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.todo-time {
  font-size: 12px;
  color: #909399;
}

.todo-time i {
  margin-right: 3px;
}

.todo-action {
  margin-left: 15px;
}

/* 通知公告样式 */
.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #EBEEF5;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item:last-child {
  border-bottom: none;
}

.empty-todo-placeholder,
.empty-notification-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

.notification-indicator {
  margin-right: 15px;
  margin-top: 3px;
}

.notification-indicator i {
  font-size: 16px;
  color: #C0C4CC;
}

.notification-indicator .new-notification {
  color: #409EFF;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.load-more {
  text-align: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #EBEEF5;
}

/* 底部统计卡片 */
.statistics-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.green {
  background-color: #67C23A;
}

.stat-icon.blue {
  background-color: #409EFF;
}

.stat-icon.orange {
  background-color: #E6A23C;
}

.stat-icon.teal {
  background-color: #20B2AA;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-trend {
  font-size: 12px;
}

.stat-trend.positive {
  color: #67C23A;
}

.stat-trend.negative {
  color: #F56C6C;
}

.stat-trend.neutral {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .statistics-section {
    grid-template-columns: repeat(2, 1fr);
  }

  .greeting-section {
    flex-direction: column;
    text-align: center;
  }

  .quick-stats {
    margin-top: 15px;
  }
}
</style>
