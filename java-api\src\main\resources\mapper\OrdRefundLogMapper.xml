<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdRefundLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.OrdRefundLog">
        <id column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="op_name" property="opName" />
        <result column="op_detail" property="opDetail" />
        <result column="op_user_id" property="opUserId" />
        <result column="op_time" property="opTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_sn, op_name, op_detail, op_user_id, op_time
    </sql>


</mapper>
