<template>
  <div class="test-page">
    <h2>字典项编辑功能测试</h2>
    
    <div class="test-section">
      <h3>测试说明</h3>
      <p>1. 编辑模式下，原字典项值变为只读</p>
      <p>2. 编辑模式下，显示"新字典项值"输入框</p>
      <p>3. 如果新字典项值与原值不同，后端会删除旧记录并创建新记录</p>
      <p>4. 如果新字典项值与原值相同或为空，则直接更新记录</p>
    </div>

    <div class="test-section">
      <h3>测试数据</h3>
      <el-table :data="testData" border style="width: 100%">
        <el-table-column prop="categoryId" label="分类ID" width="120"></el-table-column>
        <el-table-column prop="itemValue" label="字典项值" width="120"></el-table-column>
        <el-table-column prop="itemName" label="字典项名称" width="150"></el-table-column>
        <el-table-column prop="sortNum" label="排序号" width="100"></el-table-column>
        <el-table-column prop="color" label="颜色" width="100">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.color }">{{ scope.row.color }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="editItem(scope.row)">编辑</el-button>
            <el-button type="success" size="mini" @click="editItemWithNewValue(scope.row)">编辑(改值)</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog :title="'编辑字典项'" :visible.sync="dialogVisible" width="500px">
      <el-form :model="itemForm" :rules="itemRules" ref="itemForm" label-width="120px">
        <el-form-item label="排序号" prop="sortNum">
          <el-input-number v-model="itemForm.sortNum" :min="0" placeholder="请输入排序号"></el-input-number>
        </el-form-item>
        <el-form-item label="字典项名称" prop="itemName">
          <el-input v-model="itemForm.itemName" placeholder="请输入字典项名称"></el-input>
        </el-form-item>
        <el-form-item label="字典项值" prop="itemValue">
          <el-input v-model="itemForm.itemValue" placeholder="字典项值" disabled></el-input>
        </el-form-item>
        <el-form-item label="新字典项值" prop="newItemValue">
          <el-input v-model="itemForm.newItemValue" placeholder="请输入新字典项值"></el-input>
        </el-form-item>
        <el-form-item label="颜色" prop="color">
          <el-input v-model="itemForm.color" placeholder="请输入颜色值"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveItem">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <div v-if="lastUpdateData" style="margin-top: 20px;">
      <h3>最后提交的数据：</h3>
      <pre>{{ JSON.stringify(lastUpdateData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { updateDictItem } from '@/api/SysDictItem'

export default {
  name: 'SysDictItemEditTest',
  data() {
    return {
      dialogVisible: false,
      testData: [
        {
          categoryId: 'test_category',
          itemValue: 'value1',
          itemName: '测试项1',
          sortNum: 1,
          color: '#409EFF'
        },
        {
          categoryId: 'test_category',
          itemValue: 'value2',
          itemName: '测试项2',
          sortNum: 2,
          color: '#67C23A'
        },
        {
          categoryId: 'test_category',
          itemValue: 'value3',
          itemName: '测试项3',
          sortNum: 3,
          color: '#E6A23C'
        }
      ],
      itemForm: {
        categoryId: '',
        itemValue: '',
        newItemValue: '',
        itemName: '',
        sortNum: 1,
        color: ''
      },
      itemRules: {
        itemName: [
          { required: true, message: '请输入字典项名称', trigger: 'blur' }
        ],
        newItemValue: [
          { required: true, message: '请输入新字典项值', trigger: 'blur' }
        ],
        sortNum: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      },
      lastUpdateData: null
    }
  },
  methods: {
    editItem(item) {
      this.itemForm = {
        categoryId: item.categoryId,
        itemValue: item.itemValue,
        newItemValue: item.itemValue, // 默认与原值相同
        itemName: item.itemName,
        sortNum: item.sortNum,
        color: item.color
      }
      this.dialogVisible = true
    },

    editItemWithNewValue(item) {
      this.itemForm = {
        categoryId: item.categoryId,
        itemValue: item.itemValue,
        newItemValue: item.itemValue + '_new', // 设置不同的新值
        itemName: item.itemName + '(修改)',
        sortNum: item.sortNum,
        color: item.color
      }
      this.dialogVisible = true
    },

    saveItem() {
      this.$refs.itemForm.validate(async (valid) => {
        if (valid) {
          try {
            const updateData = {
              categoryId: this.itemForm.categoryId,
              itemValue: this.itemForm.itemValue,
              newItemValue: this.itemForm.newItemValue,
              itemName: this.itemForm.itemName,
              sortNum: this.itemForm.sortNum,
              color: this.itemForm.color
            }

            console.log('提交的数据:', updateData)
            this.lastUpdateData = updateData

            // 模拟API调用
            // await updateDictItem(updateData)
            
            this.$message.success('保存成功（模拟）')
            this.dialogVisible = false
          } catch (error) {
            console.error('保存失败:', error)
            this.$message.error('保存失败')
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  color: #409EFF;
  margin-bottom: 10px;
}

.test-section p {
  margin: 5px 0;
  color: #666;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
