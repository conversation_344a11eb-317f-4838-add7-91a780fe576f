<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>已出库售后</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="店铺">
          <el-select v-model="formInline.shopIds" placeholder="请选择店铺" clearable multiple style="width: 180px;">
            <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="formInline.orderSn" placeholder="请输入订单号" clearable style="width: 180px;"></el-input>
        </el-form-item>
        <el-form-item label="问题类别">
          <dict-select v-model="formInline.issueType" category-id="AFTERSALE_ISSUE_TYPE" placeholder="问题类别" style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item label="处理办法">
          <dict-select v-model="formInline.closeWay" category-id="AFTERSALE_CLOSE_WAY" placeholder="处理办法"
            style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item label="处理状态">
          <dict-select v-model="formInline.closeStatus" category-id="AFTERSAFE_CLOSE_STATUS" placeholder="处理状态"
            style="width: 120px">
          </dict-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
          <el-button v-permission="['ord-after-sale:insert']" type="primary" icon="el-icon-plus" @click="handleAddAfterSales">添加</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table ref="table" :data="tableData" border style="width: 100%;" v-loading="loading"
        @expand-change="handleExpandChange">
        <!-- 展开列 -->
        <el-table-column type="expand">
          <template slot-scope="scope">
            <div class="order-items-section">
              <el-table :data="scope.row.orderItems" border style="width: 100%; ">
                <el-table-column label="产品图片" width="150" align="center">
                  <template slot-scope="item">
                    <img :src="item.row.itemImage || '/static/img/default-product.png'"
                      style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;"
                      @error="handleImageError" />
                  </template>
                </el-table-column>
                <el-table-column prop="itemName" label="产品名称" min-width="200" show-overflow-tooltip>
                  <template slot-scope="item">
                    <span class="item-name clickable-item-name"
                          @click="copyItemName(item.row.itemName)"
                          :title="'点击复制商品名称: ' + item.row.itemName">
                      {{ item.row.itemName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="itemModelName" label="规格" min-width="150" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="amount" label="数量" width="90" align="center">
                </el-table-column>
                <el-table-column prop="itemPrice" label="单价" width="100" align="center"></el-table-column>
                <el-table-column prop="expressNo" label="快递编号" min-width="150" align="center">
                  <template slot-scope="item">
                    <span v-if="item.row.expressNo" class="express-no clickable-express"
                      @click="copyExpressNo(item.row.expressNo)" :title="'点击复制快递号: ' + item.row.expressNo">
                      {{ item.row.expressNo }}
                    </span>
                    <span v-else class="no-express">未填写</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="shopName" label="店铺名称" width="130" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="orderSn" label="订单号" width="160" align="center">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
              @click="copyOrderSn(scope.row.orderSn)" :title="'点击复制订单号'">
              {{ scope.row.orderSn }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="下单时间" width="150" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <dict-table-column prop="issueType" label="问题类别" align="center" width="140" show-overflow-tooltip
          category-id="AFTERSALE_ISSUE_TYPE">
        </dict-table-column>
        <dict-table-column prop="closeWay" label="处理办法" width="100" align="center" category-id="AFTERSALE_CLOSE_WAY">
        </dict-table-column>
        <el-table-column prop="processResult" label="处理结果" width="180" align="center">
          <template slot-scope="scope">
            <!-- 补寄处理：显示新订单号 -->
            <span v-if="scope.row.closeWay === '1' && scope.row.newOrderSn"
                  style="color: #409EFF; font-weight: bold; cursor: pointer;"
                  @click="copyNewOrderSn(scope.row.newOrderSn)"
                  :title="'点击复制新订单号: ' + scope.row.newOrderSn">
              {{ scope.row.newOrderSn }}
            </span>
            <!-- 退款处理：显示退款金额和结算金额 -->
            <div v-else-if="scope.row.closeWay === '2' && scope.row.refundAmount">
              <div style="color: #F56C6C; font-weight: bold;">
                退款{{ -1 * scope.row.refundAmount }}(台币)
              </div>
              <div v-if="scope.row.settlementAmount" style="color: #67C23A; font-weight: bold; margin-top: 2px;">
                结算{{ scope.row.settlementAmount }}(人民币)
              </div>
            </div>
            <!-- 其他情况或无数据 -->
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="closeStatus" label="处理状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.closeStatus === '1'"
              :content="`确认人：${scope.row.closeUserName || '未知'}, ${formatDate(scope.row.closeTime) || '-'}`"
              placement="top">
              <dict-tag :value="scope.row.closeStatus" category-id="AFTERSAFE_CLOSE_STATUS"
                style="cursor: pointer;"></dict-tag>
            </el-tooltip>
            <dict-tag v-else :value="scope.row.closeStatus" category-id="AFTERSAFE_CLOSE_STATUS"></dict-tag>
          </template>
        </el-table-column>
        <!-- 使用NoteColumn组件 -->
        <NoteColumn :current-scene="currentScene" />
        <!-- <el-table-column prop="issueTime" label="问题录入时间" width="180" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.issueTime) }}
          </template>
        </el-table-column> -->
        <el-table-column label="操作" min-width="280" align="center">
          <template slot-scope="scope">
            <template v-if="scope.row.closeStatus === '0'">
              <el-button v-permission="['ord-after-sale:update']" size="mini" type="text" @click="handleEdit(scope.row)">
                <i class="el-icon-edit"></i> 编辑
              </el-button>
              <el-button v-permission="['ord-after-sale:delete']" size="mini" type="text" @click="handleDelete(scope.row)" style="color: #F56C6C">
                <i class="el-icon-delete"></i> 删除
              </el-button>
              <el-button v-permission="['ord-after-sale:confirm']" size="mini" type="text" @click="handleConfirmComplete(scope.row)"
                :disabled="!canConfirmComplete">
                <i class="el-icon-check"></i> 确认完成
              </el-button>
            </template>
            <!-- 处理状态为"1"（已处理）时不显示操作按钮 -->
            <!-- <template v-else-if="scope.row.closeStatus === '1'">
              <span style="color: #909399;">-</span>
            </template> -->
            <!-- 备注按钮 - 所有状态都显示 -->
            <el-button size="mini" type="text" @click="openNotesDrawer(scope.row)" icon="el-icon-tickets">备注</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-table-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

    <!-- 添加/编辑售后对话框 -->
    <OrdAfterSaleEdit :visible.sync="afterSalesDialogVisible" :editData="currentAfterSaleData" :isEdit="isEditAfterSale"
      @submit="handleAfterSaleSubmit" />

    <!-- 使用EditNoteDialog组件 -->
    <EditNoteDialog
      :visible.sync="noteDialogVisible"
      :orderSn="currentOrderSn"
      :scene="currentScene"
      sceneName="已出库售后"
      @saved="handleNoteSaved" />

    <!-- 备注抽屉 -->
    <el-drawer
      title="订单备注"
      :visible.sync="notesDrawerVisible"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose">
      <div style="padding: 20px;">
        <OrderNotesDrawer
          :notes="currentOrderNotes"
          :orderSn="currentOrderSn"
          :currentScene="currentScene"
          :showDebug="false"
          @note-updated="handleNoteUpdated" />
      </div>
    </el-drawer>

  </div>
</template>

<script>
import { getOrdAfterSalePage, insertOrdAfterSale, updateOrdAfterSale, deleteOrdAfterSale, confirmCompleteOrdAfterSale } from '../../api/ErpOrder'
import { getAllEnabledShops } from '../../api/SysShop'
import Pagination from '../../components/Pagination'
import DictSelect from '../../components/DictSelect'
import DictTableColumn from '../../components/DictTableColumn'
import DictTag from '../../components/DictTag'
import OrdAfterSaleEdit from './OrdAfterSaleEdit'
import NoteColumn from '../../components/NoteColumn'
import noteDrawerMixin from '../../mixins/noteDrawerMixin'
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'OrdAfterSale',
  mixins: [copyMixin, noteDrawerMixin],
  components: {
    Pagination,
    DictSelect,
    DictTableColumn,
    DictTag,
    OrdAfterSaleEdit,
    NoteColumn
  },
  data() {
    return {
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        issueType: '',
        closeWay: '',
        closeStatus: ''
      },
      // 表格数据
      tableData: [],
      // 店铺列表
      shopList: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,

      // 售后对话框
      afterSalesDialogVisible: false,
      currentAfterSaleData: {},
      isEditAfterSale: false,
      currentScene: '07'  // 已出库售后
    }
  },
  computed: {
    // 检查是否可以确认完成（roleId=22时可用）
    canConfirmComplete() {
      // 这里需要从用户信息中获取roleId，假设存储在vuex或localStorage中
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.roleId === 22
    }
  },
  created() {
    // 检查URL参数中是否有orderSn
    if (this.$route.query.orderSn) {
      this.formInline.orderSn = this.$route.query.orderSn;
    }
    this.getPageData()
    this.loadShops()
  },
  methods: {
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = {
          current: this.formInline.current,
          size: this.formInline.size,
          shopIds: this.formInline.shopIds && this.formInline.shopIds.length > 0 ? this.formInline.shopIds.map(id => String(id)) : undefined,
          orderSn: this.formInline.orderSn || undefined,
          issueType: this.formInline.issueType || undefined,
          closeWay: this.formInline.closeWay || undefined,
          closeStatus: this.formInline.closeStatus || undefined
        }
      }

      getOrdAfterSalePage(parameter)
        .then(res => {
          this.loading = false
          if (res.success && res.data) {
            // 直接使用后端返回的数据，包含orderItems
            this.tableData = res.data.records
            this.pageParam.currentPage = res.data.current
            this.pageParam.pageSize = res.data.size
            this.pageParam.total = res.data.total
          }
        })
        .catch(err => {
          this.loading = false
          this.$message.error('获取数据失败：' + err.message)
        })
    },



    // 加载店铺数据
    loadShops() {
      getAllEnabledShops()
        .then(res => {
          if (res.success && res.data && res.data.records) {
            this.shopList = res.data.records
          }
        })
        .catch(err => {
          console.error('加载店铺数据失败：', err)
        })
    },

    // 分页回调
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },

    // 搜索
    onSearch() {
      this.formInline.current = 1
      this.getPageData()
    },

    // 重置
    onReset() {
      this.formInline = {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        issueType: '',
        closeWay: '',
        closeStatus: ''
      }
      this.getPageData()
    },

    // 表格展开变化事件
    handleExpandChange(row, expandedRows) {
      console.log('展开行变化：', row, expandedRows)
    },

    // 切换行展开状态
    toggleRowExpansion(row) {
      this.$refs.table ? this.$refs.table.toggleRowExpansion(row) : null
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png'
    },

    // 添加售后
    handleAddAfterSales() {
      this.currentAfterSaleData = {}
      this.isEditAfterSale = false
      this.afterSalesDialogVisible = true
    },

    // 编辑售后
    handleEdit(row) {
      this.currentAfterSaleData = { ...row }
      this.isEditAfterSale = true
      this.afterSalesDialogVisible = true
    },

    // 删除售后
    handleDelete(row) {
      this.$confirm('确定要删除这条售后记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOrdAfterSale({ idList: [row.id] })
          .then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.getPageData()
            } else {
              this.$message.error('删除失败：' + res.message)
            }
          })
          .catch(err => {
            this.$message.error('删除失败：' + err.message)
          })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 售后提交回调
    handleAfterSaleSubmit(formData) {
      if (this.isEditAfterSale) {
        // 编辑现有记录
        updateOrdAfterSale(formData)
          .then(res => {
            if (res.success) {
              this.$message.success('修改成功')
              this.afterSalesDialogVisible = false
              this.getPageData()
            } else {
              this.$message.error('修改失败：' + res.message)
            }
          })
          .catch(err => {
            this.$message.error('修改失败：' + err.message)
          })
      } else {
        // 添加新记录
        insertOrdAfterSale(formData)
          .then(res => {
            if (res.success) {
              this.$message.success('添加成功')
              this.afterSalesDialogVisible = false
              this.getPageData()
            } else {
              this.$message.error('添加失败：' + res.message)
            }
          })
          .catch(err => {
            this.$message.error('添加失败：' + err.message)
          })
      }
    },

    // 确认完成
    handleConfirmComplete(row) {
      this.$confirm(`确定要将订单号为 ${row.orderSn} 的售后记录标记为完成吗？`, '确认完成', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用确认完成API
        confirmCompleteOrdAfterSale(row.id)
          .then(res => {
            if (res.success) {
              this.$message.success('确认完成成功')
              this.getPageData()
            } else {
              this.$message.error('确认完成失败：' + (res.message || '未知错误'))
            }
          })
          .catch(err => {
            this.$message.error('确认完成失败：' + (err.message || '网络错误'))
          })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    // 复制新订单号
    copyNewOrderSn(newOrderSn) {
      this.copyToClipboard(newOrderSn, '新订单号', 'blue')
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.empty-table-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

/* 订单项展开区域样式 */
.order-items-section {
  background-color: #f8f9fa;
  border-radius: 6px;
}

.order-items-section h4 {
  color: #409EFF;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.price {
  color: #E6A23C;
  font-weight: bold;
}

.express-no {
  color: #67C23A;
  font-weight: bold;
  background-color: #F0F9FF;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #67C23A;
}

.no-express {
  color: #909399;
  font-style: italic;
}

.clickable-express {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-express:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>