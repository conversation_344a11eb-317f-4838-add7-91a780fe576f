import dictService from './dictService'
import DictTableColumn from '../components/DictTableColumn.vue'
import DictTable from '../components/DictTable.vue'
import DictSelect from '../components/DictSelect.vue'
import DictTag from '../components/DictTag.vue'

/**
 * 字典插件
 * 提供全局的字典转换功能
 */
const DictPlugin = {
  install(Vue) {
    // 注册全局组件
    Vue.component('DictTableColumn', DictTableColumn)
    Vue.component('DictTable', DictTable)
    Vue.component('DictSelect', DictSelect)
    Vue.component('DictTag', DictTag)

    // 在Vue原型上添加字典服务方法
    Vue.prototype.$dictService = dictService

    // 添加全局方法：获取字典标签
    Vue.prototype.$getDictLabel = async function(categoryId, value) {
      return await dictService.getDictLabel(categoryId, value)
    }

    // 添加全局方法：同步获取字典标签（仅从缓存获取）
    Vue.prototype.$getDictLabelSync = function(categoryId, value) {
      return dictService.getDictLabelSync(categoryId, value)
    }

    // 添加全局方法：获取字典颜色
    Vue.prototype.$getDictColor = async function(categoryId, value) {
      return await dictService.getDictColor(categoryId, value)
    }

    // 添加全局方法：同步获取字典颜色（仅从缓存获取）
    Vue.prototype.$getDictColorSync = function(categoryId, value) {
      return dictService.getDictColorSync(categoryId, value)
    }

    // 添加全局过滤器：字典转换
    Vue.filter('dictLabel', function(value, categoryId) {
      if (!categoryId) {
        return value || ''
      }
      return dictService.getDictLabelSync(categoryId, value) || value || ''
    })

    // 添加全局混入，提供字典相关方法
    Vue.mixin({
      methods: {
        // 字典转换方法
        dictLabel(categoryId, value) {
          return dictService.getDictLabelSync(categoryId, value)
        },

        // 异步字典转换方法
        async getDictLabel(categoryId, value) {
          return await dictService.getDictLabel(categoryId, value)
        },

        // 字典颜色转换方法
        dictColor(categoryId, value) {
          return dictService.getDictColorSync(categoryId, value)
        },

        // 异步字典颜色转换方法
        async getDictColor(categoryId, value) {
          return await dictService.getDictColor(categoryId, value)
        },

        // 预加载字典数据
        async preloadDicts(categoryIds) {
          await dictService.preloadDicts(categoryIds)
        },

        // 批量转换表格数据的字典字段
        async convertTableDictFields(data, dictMapping) {
          return await dictService.convertDictFields(data, dictMapping)
        }
      }
    })
  }
}

export default DictPlugin
