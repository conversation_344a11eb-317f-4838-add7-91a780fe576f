package com.my.crossborder.controller.vo.stl_purchase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 月度采购结算状态VO
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class MonthlyPurchaseStatusVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结算次数
     */
    private Integer settledCount;
    
    /**
     * 结算金额
     */
    private BigDecimal settledAmount;
    
    /**
     * 未结算次数
     */
    private Integer unsettledCount;
    
    /**
     * 未结算金额
     */
    private Integer unsettledAmount;
    
    /**
     * 结算状态
     */
    private Map<LocalDate, Boolean> purchaseData;


}
