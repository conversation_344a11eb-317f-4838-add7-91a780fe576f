package com.my.crossborder.controller.vo.ord_taiwan;

import java.time.LocalDate;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_台湾上架物品
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdTaiwanPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单项id
     */
    private String orderItemId;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 退货单号
     */
    private String waybillNumber;

    /**
     * 状态字典：TAIWAN_GOODS_STATUS
     */
    private String status;

    /**
     * 台湾仓柜号
     */
    private String warehouseNumber;

    /**
     * 上架时间
     */
    private LocalDate onlineTime;

    /**
     * 下架时间
     */
    private LocalDate offlineTime;

    /**
     * 原订单店铺名称
     */
    private String originalShopName;

    /**
     * 原订单店铺号
     */
    private String originalShopId;

    /**
     * 原订单号
     */
    private String originalOrderNumber;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productSpec;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 商品价格
     */
    private java.math.BigDecimal productPrice;

}
