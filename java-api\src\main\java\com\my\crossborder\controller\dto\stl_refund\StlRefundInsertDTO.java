package com.my.crossborder.controller.dto.stl_refund;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_结算_退款结算表
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class StlRefundInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 退款员工ID
     */
    @NotNull(message = "refundUserId不能为空")
    private Integer refundUserId;


    /**
     * 退款日期_开始
     */
    @NotNull
    private LocalDate refundDateStart;

    /**
     * 退款日期_结束
     */
    @NotNull
    private LocalDate refundDateEnd;

    /**
     * 结算金额
     */
    @NotNull(message = "settlementAmount不能为空")
    private BigDecimal settlementAmount;

    /**
     * 备注（使用常见标签快速填写）
     */
    @NotNull(message = "remark不能为空")
    private String remark;

}
