package com.my.crossborder.controller.dto.sys_log;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import com.my.crossborder.controller.dto.AttachmentIdListDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_操作日志表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysLogInsertDTO extends AttachmentIdListDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 所属模块
     */
	@NotNull(message="moduleName不能为空")
    private String moduleName;

    /**
     * 操作菜单
     */
	@NotNull(message="menuName不能为空")
    private String menuName;

    /**
     * 操作类型（如新增/修改/删除）
     */
	@NotNull(message="operationName不能为空")
    private String operationName;

    /**
     * 操作详情
     */
	@NotNull(message="operationDetail不能为空")
    private String operationDetail;
    
    /**
     * 是否成功
     */
	@NotNull
    private Boolean success;
    
    /**
     * 异常信息
     */
    private String errMsg;
    
    /**
     * 操作用户ID
     */
	@NotNull(message="createUserId不能为空")
    private Integer createUserId;

    /**
     * 操作时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

}
