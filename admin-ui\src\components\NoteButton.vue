<template>
  <el-button 
    size="mini" 
    type="text" 
    @click="handleClick" 
    icon="el-icon-tickets"
    :disabled="disabled">
    {{ text }}
  </el-button>
</template>

<script>
export default {
  name: 'NoteButton',
  props: {
    // 按钮文本
    text: {
      type: String,
      default: '备注'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 订单行数据
    row: {
      type: Object,
      required: true
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.row)
    }
  }
}
</script>

<style scoped>
/* 可以在这里添加按钮的自定义样式 */
</style>
