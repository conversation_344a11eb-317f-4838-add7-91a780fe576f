package com.my.crossborder.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentDeleteDTO;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentInsertDTO;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentPageDTO;
import com.my.crossborder.controller.vo.sys_attachment.SysAttachmentDetailVO;
import com.my.crossborder.controller.vo.sys_attachment.SysAttachmentPageVO;
import com.my.crossborder.mybatis.entity.SysAttachment;
import com.my.crossborder.mybatis.mapper.SysAttachmentMapper;
import com.my.crossborder.service.SysAttachmentService;
import com.my.crossborder.util.ColumnLambda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

/**
 * 附件表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
public class SysAttachmentServiceImpl extends ServiceImpl<SysAttachmentMapper, SysAttachment> implements SysAttachmentService {


	@Transactional
	@Override
	public Integer insertAndReturnId(SysAttachmentInsertDTO insertDTO) {
		SysAttachment entity = BeanUtil.copyProperties(insertDTO, SysAttachment.class);
		entity.setDataSourceId(null);
		this.save(entity);
		return entity.getAttachmentId();
	}

	@Override
	public SysAttachmentDetailVO detail(Integer id) {
		SysAttachment entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SysAttachmentDetailVO.class);
	}

	@Override
	public Page<SysAttachmentPageVO> page(SysAttachmentPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<SysAttachment>().columnsToString(SysAttachment::getAttachmentId)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysAttachmentDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Transactional
	@Override
	public void fillDataSourceId(String dataSourceId, List<Integer> attachmentIdList, String tableName) {
		if (CollectionUtil.isNotEmpty(attachmentIdList)) {
			LambdaUpdateWrapper<SysAttachment> updateWrapper = new LambdaUpdateWrapper<SysAttachment>()
			.set(SysAttachment::getDataSourceId, dataSourceId)
			.set(SysAttachment::getTableName, tableName)
			.in(SysAttachment::getAttachmentId, attachmentIdList)
			;
			this.update(updateWrapper);
		}
	}

	@Transactional
	@Override
	public void clearDatasourceId(String tableName, String dataSourceId) {
		LambdaUpdateWrapper<SysAttachment> updateWrapper = new LambdaUpdateWrapper<SysAttachment>()
				.set(SysAttachment::getDataSourceId, null)
				.eq(SysAttachment::getTableName, tableName)
				.eq(SysAttachment::getDataSourceId, dataSourceId)
				;
		this.update(updateWrapper);
	}
}
