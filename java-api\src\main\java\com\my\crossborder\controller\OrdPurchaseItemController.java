package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.my.crossborder.controller.dto.ord_purchase_item.OrdPurchaseItemDeleteDTO;
import com.my.crossborder.controller.dto.ord_purchase_item.OrdPurchaseItemUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.ord_purchase_item.OrdPurchaseItemDetailVO;
import com.my.crossborder.service.OrdPurchaseItemService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 采购订单明细表 
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/api/ord-purchase-item")
@RequiredArgsConstructor
public class OrdPurchaseItemController {

    private final OrdPurchaseItemService ordPurchaseItemService;

    /**
    * 修改
    */
    @SaCheckPermission("ord-purchase:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody OrdPurchaseItemUpdateDTO updateDTO) {
    	this.ordPurchaseItemService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<OrdPurchaseItemDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.ordPurchaseItemService.detail(id));
    }
	
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("ord-purchase:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody OrdPurchaseItemDeleteDTO deleteDTO) {
    	this.ordPurchaseItemService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
