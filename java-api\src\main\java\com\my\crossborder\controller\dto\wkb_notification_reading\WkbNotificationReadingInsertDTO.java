package com.my.crossborder.controller.dto.wkb_notification_reading;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_通知阅读表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationReadingInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 通知id
     */
	@NotNull(message="notificationId不能为空")
    private Integer notificationId;

    /**
     * 公告的接收用户id
     */
	@NotNull(message="userId不能为空")
    private Integer userId;

    /**
     * 是否已读
     */
	@NotNull(message="read不能为空")
    private Boolean read;

    /**
     * 已读时间
     */
	@NotNull(message="readTime不能为空")
    private LocalDateTime readTime;

}
