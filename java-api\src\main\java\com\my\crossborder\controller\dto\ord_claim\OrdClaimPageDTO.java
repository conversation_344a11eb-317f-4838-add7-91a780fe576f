package com.my.crossborder.controller.dto.ord_claim;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.ord_claim.OrdClaimPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_物流理赔
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdClaimPageDTO 
						extends PageDTO<OrdClaimPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 问题类别 字典claim_issue_type
     */
    private String issueType;

    /**
     * 订单项id
     */
    private String orderItemId;

    /**
     * 处理办法 字典claim_close_way
     */
    private String closeWay;

    /**
     * 理赔进度 字典claim_status
     */
    private String claimStatus;

    /**
     * 录入人id
     */
    private Integer issueUserId;

    /**
     * 处理人id
     */
    private Integer closeUserId;

    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 店铺ID列表
     */
    private List<String> shopIds;

}
