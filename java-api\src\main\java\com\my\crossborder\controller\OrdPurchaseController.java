package com.my.crossborder.controller;

import java.util.List;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchaseDeleteDTO;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchasePageDTO;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchaseUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchaseDetailVO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchasePageVO;
import com.my.crossborder.service.OrdPurchaseService;
import com.my.crossborder.service.SysShopService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 采购订单主表 
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/api/ord-purchase")
@RequiredArgsConstructor
public class OrdPurchaseController {

    private final OrdPurchaseService ordPurchaseService;
    private final SysShopService sysShopService;

    /**
    * 修改
    */
    @SaCheckPermission("ord-purchase:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody OrdPurchaseUpdateDTO updateDTO) {
    	this.ordPurchaseService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<OrdPurchaseDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.ordPurchaseService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<OrdPurchasePageVO>> page(OrdPurchasePageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds);
        }

        Page<OrdPurchasePageVO> page = this.ordPurchaseService.page(pageDTO);
        return StdResp.success(page);
    }
    
//    /**
//    * 批量删除(物理删除)
//    */
//    @DeleteMapping
//    public StdResp<?> delete(@Valid @RequestBody OrdPurchaseDeleteDTO deleteDTO) {
//    	this.ordPurchaseService.delete(deleteDTO);
//		return StdResp.success();
//    }
    
}
