package com.my.crossborder.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_shift.SysShiftDeleteDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftInsertDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftPageDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftQuickCopyDTO;
import com.my.crossborder.controller.dto.sys_shift.SysShiftUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_shift.MonthShift;
import com.my.crossborder.controller.vo.sys_shift.SysShiftPageVO;
import com.my.crossborder.controller.vo.sys_shift.SysShiftQuickCopyVO;
import com.my.crossborder.service.SysShiftService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 排班
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@RestController
@RequestMapping("/api/sys-shift")
@RequiredArgsConstructor
public class SysShiftController {

    private final SysShiftService sysShiftService;

    /**
     * 根据日期查询排班数据
     * @param shiftDay 排班日期（格式：YYYY-MM-DD）
     */
    @GetMapping("/list-by-day")
    public StdResp<List<SysShiftPageVO>> listByShiftDay(@RequestParam String shiftDay) {
        List<SysShiftPageVO> list = this.sysShiftService.listByShiftDay(shiftDay);
        return StdResp.success(list);
    }

    /**
     * 根据日期范围查询排班数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    @GetMapping("/list-by-range")
    public StdResp<List<SysShiftPageVO>> listByDateRange( @RequestParam String startDate,
                                                        @RequestParam String endDate) {
        List<SysShiftPageVO> list = this.sysShiftService.listByDateRange(startDate, endDate);
        return StdResp.success(list);
    }
    
    /**
     * 月度排班
     */
    @GetMapping("/month-shift")
    public StdResp<MonthShift> monthShift(@RequestParam Integer userId, @RequestParam Integer year, @RequestParam Integer month) {
    	MonthShift monthShift = this.sysShiftService.monthShift(userId, year, month);
    	return StdResp.success(monthShift);
    }

    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysShiftPageVO>> page(SysShiftPageDTO pageDTO) {
        Page<SysShiftPageVO> page = this.sysShiftService.page(pageDTO);
        return StdResp.success(page);
    }

    /**
    * 新增
    */
    @SaCheckPermission("sys-shift:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysShiftInsertDTO insertDTO) {
    	this.sysShiftService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("sys-shift:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody SysShiftUpdateDTO updateDTO) {
    	this.sysShiftService.update(updateDTO);
    	return StdResp.success();
    }

    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("sys-shift:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysShiftDeleteDTO deleteDTO) {
    	this.sysShiftService.delete(deleteDTO);
		return StdResp.success();
    }

    /**
     * 快速复制排班
     * 将指定日期的排班安排复制到今天往后7个自然日
     */
    @SaCheckPermission("sys-shift:copy")
    @PostMapping("/quick-copy")
    public StdResp<SysShiftQuickCopyVO> quickCopy(@Valid @RequestBody SysShiftQuickCopyDTO quickCopyDTO) {
        SysShiftQuickCopyVO result = this.sysShiftService.quickCopy(quickCopyDTO);
        return StdResp.success(result);
    }

}
