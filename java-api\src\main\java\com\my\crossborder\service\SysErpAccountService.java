package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountDeleteDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountInsertDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountPageDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountUpdateDTO;
import com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountDetailVO;
import com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountPageVO;
import com.my.crossborder.mybatis.entity.SysErpAccount;

/**
 * 禾宸物流接口账号 服务类
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface SysErpAccountService extends IService<SysErpAccount> {

	/**
	 * 新增
	 */
	void insert(SysErpAccountInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysErpAccountUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysErpAccountDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<SysErpAccountPageVO> page(SysErpAccountPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysErpAccountDeleteDTO deleteDTO);	

}
