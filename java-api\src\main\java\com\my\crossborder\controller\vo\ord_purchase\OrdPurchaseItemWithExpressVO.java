package com.my.crossborder.controller.vo.ord_purchase;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemWithExpressVO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * OrdPurchaseItemWithExpressVO
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseItemWithExpressVO extends ErpOrderItemWithExpressVO {

    /**
     * 采购途径 字典PURCHASE_CHANNEL
     */
    private String purchaseChannel;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 是否结算
     */
    private Boolean settlementFlag;

}
