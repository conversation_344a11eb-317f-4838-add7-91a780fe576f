package com.my.crossborder.controller.dto.ord_purchase_centralized_order;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 新增_集中采购订单
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseCentralizedOrderInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单号
     */
	@NotNull(message="orderSn不能为空")
    private String orderSn;

}
