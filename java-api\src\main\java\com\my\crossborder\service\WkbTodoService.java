package com.my.crossborder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.wkb_todo.WkbTodoDeleteDTO;
import com.my.crossborder.controller.dto.wkb_todo.WkbTodoInsertIfAbsenseDTO;
import com.my.crossborder.controller.dto.wkb_todo.WkbTodoPageDTO;
import com.my.crossborder.controller.vo.wkb_todo.WkbTodoDetailVO;
import com.my.crossborder.controller.vo.wkb_todo.WkbTodoPageVO;
import com.my.crossborder.mybatis.entity.WkbTodo;

/**
 * 通知表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface WkbTodoService extends IService<WkbTodo> {


	/**
	 * 查询详情
	 * @param id 主键
	 */
	WkbTodoDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<WkbTodoPageVO> page(WkbTodoPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(WkbTodoDeleteDTO deleteDTO);

	/**
	 * 对比标题，新增不存在的待办和未读
	 * @param dtoList
	 */
	void saveIfAbsense(List<WkbTodoInsertIfAbsenseDTO> dtoList);	

}
