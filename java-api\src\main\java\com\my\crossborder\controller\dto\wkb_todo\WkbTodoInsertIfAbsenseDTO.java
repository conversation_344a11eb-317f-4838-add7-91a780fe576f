package com.my.crossborder.controller.dto.wkb_todo;

import java.io.Serializable;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_待办
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbTodoInsertIfAbsenseDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 类别
     */
	@NotNull(message="type不能为空")
    private String type;

    /**
     * 标题
     */
	@NotNull(message="title不能为空")
    private String title;

    /**
     * 内容
     */
	@NotNull(message="content不能为空")
    private String content;
	
    /**
     * 截止日期
     */
	@NotNull(message="deadline不能为空")
    private LocalDate deadline;

	/**
	 * 待办接收人id
	 */
	private Integer userId;
}
