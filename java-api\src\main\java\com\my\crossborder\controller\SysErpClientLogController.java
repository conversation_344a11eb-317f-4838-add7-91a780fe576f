package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogDeleteDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogInsertDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogPageDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_erp_client_log.SysErpClientLogDetailVO;
import com.my.crossborder.controller.vo.sys_erp_client_log.SysErpClientLogPageVO;
import com.my.crossborder.service.SysErpClientLogService;

import lombok.RequiredArgsConstructor;

/**
 * erp接口日志表 
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/api/sys-erp-client-log")
@RequiredArgsConstructor
public class SysErpClientLogController {

    private final SysErpClientLogService sysErpClientLogService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysErpClientLogInsertDTO insertDTO) {
    	this.sysErpClientLogService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody SysErpClientLogUpdateDTO updateDTO) {
    	this.sysErpClientLogService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<SysErpClientLogDetailVO> detail(@PathVariable Long id) {
    	return StdResp.success(this.sysErpClientLogService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysErpClientLogPageVO>> page(SysErpClientLogPageDTO pageDTO) {
        Page<SysErpClientLogPageVO> page = this.sysErpClientLogService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysErpClientLogDeleteDTO deleteDTO) {
    	this.sysErpClientLogService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
