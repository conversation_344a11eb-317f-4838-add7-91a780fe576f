<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="handleDialogClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="120px">
      <el-form-item label="订单号">
        <el-input v-model="form.orderSn" style="width: 100%;" readonly="true"></el-input>
      </el-form-item>

      <el-form-item label="订单总金额"> 
        <span style="color: #E6A23C; font-weight: bold; font-size: medium;">{{ formatTotalPrice }}</span>
      </el-form-item>

      <el-form-item label="处理类型" prop="applyType">
        <el-radio-group v-model="form.applyType" @change="handleApplyTypeChange" :disabled="isAppliedRefund">
          <el-radio label="refund">申请退款</el-radio>
          <el-radio label="putIn">不退采买做入库</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="申请退款金额" prop="applyAmount" v-if="form.applyType === 'refund'">
        <el-input-number
          v-model="form.applyAmount"
          :precision="2"
          :min="0.01"
          :max="99999.99"
          placeholder="请输入申请退款金额"
          style="width: 200px;">
        </el-input-number> 元
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ submitting ? '提交中...' : '确 定' }}
      </el-button>
      <el-button @click="handleDialogClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { applyRefund, applyPutIn } from '../../api/OrdRefund'

export default {
  name: 'OrdRefundEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        orderSn: '',
        applyType: 'refund',
        applyAmount: null
      },
      baseRules: {
        applyType: [
          { required: true, message: '请选择申请类型', trigger: 'change' }
        ],
        applyAmount: [
          { required: true, message: '请输入申请退款金额', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '申请退款金额必须大于0', trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return '处理'
    },
    rules() {
      const rules = { ...this.baseRules }

      // 如果选择的是不退采买做入库，则不需要验证金额
      if (this.form.applyType === 'putIn') {
        delete rules.applyAmount
      }

      return rules
    },
    // 判断是否为已申请退款状态
    isAppliedRefund() {
      return this.editData.applyStatus === '1'
    },
    // 格式化订单总金额显示
    formatTotalPrice() {
      if (this.editData.totalPrice) {
        return `${this.editData.totalPrice} ${this.editData.currency || ''}`
      }
      return '暂无数据'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    initForm() {
      // 如果是已申请退款状态，则显示原有数据并禁用处理类型选择
      if (this.editData.applyStatus === '1') {
        this.form = {
          orderSn: this.editData.orderSn || '',
          applyType: 'refund', // 强制设置为申请退款
          applyAmount: this.editData.applyAmount || null // 显示原来的申请退款金额
        }
      } else {
        this.form = {
          orderSn: this.editData.orderSn || '',
          applyType: 'refund',
          applyAmount: null
        }
      }

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },

    handleApplyTypeChange(value) {
      // 当切换申请类型时，清空金额
      if (value === 'putIn') {
        this.form.applyAmount = null
      }
      
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },

    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 确认提交
          const message = this.form.applyType === 'refund' 
            ? `确认申请退款 ${this.form.applyAmount} 元？`
            : '确认不退采买做入库？'
          
          this.$confirm(message, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.submitForm()
          })
        }
      })
    },

    submitForm() {
      this.submitting = true
      
      if (this.form.applyType === 'refund') {
        // 申请退款
        const params = {
          orderSn: this.form.orderSn,
          applyAmount: this.form.applyAmount
        }
        
        applyRefund(params).then(() => {
          this.$message.success('申请退款成功')
          this.$emit('success')
        }).catch(() => {
          this.submitting = false
        })
      } else {
        // 不退采买做入库
        const params = {
          orderSn: this.form.orderSn
        }
        
        applyPutIn(params).then(() => {
          this.$message.success('申请入库成功')
          this.$emit('success')
        }).catch(() => {
          this.submitting = false
        })
      }
    },

    handleDialogClose() {
      this.submitting = false
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
