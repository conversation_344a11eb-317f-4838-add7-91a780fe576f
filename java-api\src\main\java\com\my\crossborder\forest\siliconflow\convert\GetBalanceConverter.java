package com.my.crossborder.forest.siliconflow.convert;

import java.lang.reflect.Type;
import java.math.BigDecimal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.dtflys.forest.converter.text.DefaultTextConverter;
import com.dtflys.forest.utils.StringUtils;
import com.my.crossborder.exception.BusinessException;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 余额接口转换
 *
 */
public class GetBalanceConverter extends DefaultTextConverter {

	
	@SuppressWarnings("unchecked")
	@Override
    public BigDecimal convertToJavaObject(String source, Type targetType) {
    	BusinessException.when(StringUtils.isBlank(source), "接口无数据");
    	
    	// 结果 = 充值余额 + 赠送余额
    	try {
    		UserInfo userInfo = JSON.parseObject(source, UserInfo.class);
    		return userInfo.getData().getTotalBalance();
    	} catch(Exception e) {
    		return null;
    	}
	}

	@NoArgsConstructor @AllArgsConstructor @Builder @Data 
	public static class UserInfo {

	    @JSONField(name = "code", ordinal = 1)
	    String code;

	    @JSONField(name = "data", ordinal = 2)
	    @Builder.Default
	    DataObj data = new DataObj();

	    @JSONField(name = "message", ordinal = 3)
	    String message;

	    @JSONField(name = "status", ordinal = 4)
	    String status;


	    // ======== 以下为内部类 =========
	    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
	    public static class DataObj {

	        @JSONField(name = "balance", ordinal = 1)
	        BigDecimal balance;

	        @JSONField(name = "chargeBalance", ordinal = 2)
	        BigDecimal chargeBalance;

	        @JSONField(name = "email", ordinal = 3)
	        String email;

	        @JSONField(name = "id", ordinal = 4)
	        String id;

	        @JSONField(name = "image", ordinal = 5)
	        String image;

	        @JSONField(name = "introduction", ordinal = 6)
	        String introduction;

	        @JSONField(name = "isAdmin", ordinal = 7)
	        String isAdmin;

	        @JSONField(name = "name", ordinal = 8)
	        String name;

	        @JSONField(name = "role", ordinal = 9)
	        String role;

	        @JSONField(name = "status", ordinal = 10)
	        String status;

	        @JSONField(name = "totalBalance", ordinal = 11)
	        BigDecimal totalBalance;
	    }
	}
}
