package com.my.crossborder.controller.vo.wkb_note;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_工作台_工作笔记表
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNoteDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 笔记内容
     */
    private String content;

    /**
     * 场景
     */
    private String scene;

    /**
     * 场景完成状态：0=待处理，1=已处理
     */
    private Boolean sceneComplete;

    /**
     * 场景完成时间
     */
    private LocalDateTime sceneCompleteTime;

    /**
     * 创建人用户ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
