<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysRole">
        <id column="role_id" property="roleId" />
        <result column="role_name" property="roleName" />
        <result column="description" property="description" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        role_id, role_name, description
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_role.SysRolePageVO">
		SELECT
			role_id, role_name, description
		FROM
			sys_role AS t1
		<where>
        	1=1
	        <if test="roleId != null and roleId != ''">
	           	AND t1.role_id = #{roleId}
            </if>
	        <if test="roleName != null and roleName != ''">
	           	AND t1.role_name = #{roleName}
            </if>
	        <if test="description != null and description != ''">
	           	AND t1.description = #{description}
            </if>
        </where>
    </select>

</mapper>
