package com.my.crossborder.controller.dto.sys_dict_item;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.my.crossborder.controller.dto.AttachmentIdListDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_数据字典-字典项表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictItemUpdateDTO extends AttachmentIdListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类别id (联合主键)
     */
	@NotNull(message="categoryId不能为空")
    private String categoryId;

    /**
     * 字典项值 (联合主键)
     */
	@NotNull(message="itemValue不能为空")
    private String itemValue;

    /**
     * 新字典项值
     */
    private String newItemValue;

    /**
     * 字典项名称
     */
	@NotNull(message="itemName不能为空")
    private String itemName;

    /**
     * 排序编号
     */
	@NotNull(message="sortNum不能为空")
    private Integer sortNum;
	
	/**
	 * 颜色
	 */
	private String color;

}
