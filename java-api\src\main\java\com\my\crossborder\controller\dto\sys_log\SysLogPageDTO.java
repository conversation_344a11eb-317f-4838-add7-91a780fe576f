package com.my.crossborder.controller.dto.sys_log;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_log.SysLogPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_操作日志表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysLogPageDTO 
						extends PageDTO<SysLogPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long logId;

    /**
     * 所属模块
     */
    private String moduleName;

    /**
     * 操作菜单
     */
    private String menuName;

    /**
     * 操作类型（如新增/修改/删除）
     */
    private String operationName;

    /**
     * 操作详情
     */
    private String operationDetail;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 操作用户ID
     */
    private Integer createUserId;

    /**
     * 操作时间（开始时间）
     */
    private LocalDateTime createTimeStart;

    /**
     * 操作时间（结束时间）
     */
    private LocalDateTime createTimeEnd;

}
