package com.my.crossborder.forest.erp990.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 打包记录分页
 * 
 * <AUTHOR>
 * @date 2024
 */
@NoArgsConstructor @AllArgsConstructor @Builder @Data  @EqualsAndHashCode(callSuper=false) 
public class ErpPackageRecordPageVO {

    /** 订单数据行列表 */
    @JSONField(name = "rows", ordinal = 3)
    List<Row> rows;

    /** 总记录数 */
    @JSONField(name = "total", ordinal = 4)
    Integer total;

    
    // ======== 以下为内部类 =========
    
    /**
     * 查询参数类
     */
    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Params {
    	String nothing;
    }
    
    /**
     * 打包记录
     */
    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Row {
    	
    	/** 订单id */
    	@JSONField(name = "orderId", ordinal = 1)
    	String orderId;

    	@JSONField(name = "stickFeeFlag", ordinal = 1)
    	String stickFeeFlag;

        // 202500725. 不能直接使用这个字段名，会覆盖掉下单时间 /** 录入时间 */
        // @JSONField(name = "createTime", ordinal = 2)
        // LocalDateTime createTime;

        /** 结算重量 */
        @JSONField(name = "chargeWeight", ordinal = 3)
        BigDecimal chargeWeight;

        /** 运费 */
        @JSONField(name = "unitFee", ordinal = 4)
        BigDecimal unitFee;

        /** 进店费 */
        @JSONField(name = "inShopeeFee", ordinal = 5)
        BigDecimal inShopeeFee;

        /** 贴标费 */
        @JSONField(name = "stickFee", ordinal = 6)
        BigDecimal stickFee;

    }
    
}