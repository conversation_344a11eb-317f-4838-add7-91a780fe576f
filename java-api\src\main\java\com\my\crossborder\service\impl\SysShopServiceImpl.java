package com.my.crossborder.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.my.crossborder.controller.dto.sys_shop.SysShopInsertDTO;
import com.my.crossborder.controller.dto.sys_shop.SysShopPageDTO;
import com.my.crossborder.controller.dto.sys_shop.SysShopUpdateDTO;
import com.my.crossborder.controller.vo.sys_shop.SysShopPageVO;
import com.my.crossborder.mybatis.entity.SysShop;
import com.my.crossborder.mybatis.entity.SysShopPartner;
import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.mybatis.mapper.SysShopMapper;
import com.my.crossborder.service.SysShopPartnerService;
import com.my.crossborder.service.SysShopService;
import com.my.crossborder.service.SysUserService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;

/**
 * 店铺管理表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
@RequiredArgsConstructor
public class SysShopServiceImpl extends ServiceImpl<SysShopMapper, SysShop> implements SysShopService {

	private final SysShopPartnerService sysShopPartnerService;
	private final SysUserService sysUserService;

	@Transactional
	@Override
	public void insert(SysShopInsertDTO insertDTO) {
		SysShop entity = BeanUtil.copyProperties(insertDTO, SysShop.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(SysShopUpdateDTO updateDTO) {
		// 更新店铺基本信息
		SysShop entity = BeanUtil.copyProperties(updateDTO, SysShop.class);
		this.baseMapper.updateById(entity);

		// 先删除原有的合作人关系
		this.sysShopPartnerService.deleteByShopId(updateDTO.getId());

		// 处理合作人关系
		if (updateDTO.getPartnerIds() != null) {
			// 插入新的合作人关系
			List<SysShopPartner> shopPartnerList = Lists.newLinkedList();
			for (Integer partnerId : updateDTO.getPartnerIds()) {
				SysShopPartner shopPartner = SysShopPartner.builder()
					.shopId(updateDTO.getId())
					.partnerUserId(partnerId)
					.build();
				shopPartnerList.add(shopPartner);
			}
			this.sysShopPartnerService.saveBatch(shopPartnerList);
		}
	}
 
	@Override
	public Page<SysShopPageVO> page(SysShopPageDTO pageDTO) {
		Page<SysShopPageVO> page = this.baseMapper.page(pageDTO);

		// 处理合伙人ID字符串转换为ID数组
		if (page.getRecords() != null) {
			for (SysShopPageVO vo : page.getRecords()) {
				if (vo.getPartnerIdsStr() != null && !vo.getPartnerIdsStr().trim().isEmpty()) {
					try {
						String[] idArray = vo.getPartnerIdsStr().split(",");
						List<Integer> partnerIds = Lists.newArrayList();
						for (String idStr : idArray) {
							if (idStr != null && !idStr.trim().isEmpty()) {
								partnerIds.add(Integer.valueOf(idStr.trim()));
							}
						}
						vo.setPartnerIds(partnerIds);
					} catch (NumberFormatException e) {
						// 如果转换失败，设置为空列表
						vo.setPartnerIds(Lists.newArrayList());
					}
				} else {
					vo.setPartnerIds(Lists.newArrayList());
				}
			}
		}

		return page;
	}
 
	@Override
	public void saveIfAbsense(List<SysShop> shopList) {
		for (SysShop shop : shopList) {
			SysShop entity = this.getById(shop.getId());
			if (entity == null) {
				SysShopInsertDTO insertDTO = SysShopInsertDTO.builder()
						.id(shop.getId())
						.shopName(shop.getShopName())
						.disable(Boolean.FALSE)
						.build();
				SpringUtil.getBean(SysShopService.class).insert(insertDTO);
			}
		}
	}

	@Override
	public List<Integer> myShopIds() {
		// 获取当前登录用户ID
		Integer userId = StpUtil.getLoginIdAsInt();

		// 获取当前用户信息
		SysUser user = this.sysUserService.getById(userId);
		if (user == null) {
			return Lists.newArrayList();
		}

		// 获取用户角色ID
		Integer roleId = user.getRoleId();

		// 如果是合伙人角色(roleId = 12)，查询sys_shop_partner中我的shopId
		if (roleId != null && roleId.intValue() == 12) {
			List<SysShopPartner> partnerList = this.sysShopPartnerService.list(
				new LambdaQueryWrapper<SysShopPartner>()
					.eq(SysShopPartner::getPartnerUserId, userId)
			);

			List<Integer> partnerShopIds = partnerList.stream()
				.map(SysShopPartner::getShopId)
				.collect(java.util.stream.Collectors.toList());
			// 如果合伙人未设置店铺号，则返回-1， 不允许查询数据
			if (CollectionUtil.isEmpty(partnerShopIds)) {
				partnerShopIds = Lists.newArrayList(-1);
			}
			return partnerShopIds;
		}

		// 如果不是合伙人，返回空列表（表示可以查看所有店铺）
		return Lists.newArrayList();
	}
}
