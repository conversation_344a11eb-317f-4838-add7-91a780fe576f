package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryPageDTO;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryPageVO;
import com.my.crossborder.mybatis.entity.SysDictCategory;

/**
 * 数据字典-类别表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface SysDictCategoryMapper extends BaseMapper<SysDictCategory> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysDictCategoryPageVO> page(SysDictCategoryPageDTO pageDTO);
	
}
