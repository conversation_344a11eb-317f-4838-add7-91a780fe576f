<template>
  <el-dialog title="编辑用户" :visible.sync="dialogVisible" width="45%" top="5vh" @close="handleClose">
        <el-form :model="form" :rules="dynamicRules" ref="form" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="form.username" placeholder="请输入用户名" maxlength="16" :disabled="isEdit"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="!isEdit">
        <el-col :span="24">
          <el-form-item label="密码" prop="password">
            <el-input type="password" v-model="form.password" maxlength="16" :disabled="isEdit" :showPassword="true"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="realName">
            <el-input v-model="form.realName" placeholder="请输入姓名" maxlength="16"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色" prop="roleId">
            <el-cascader ref="roleCascader" v-model="roleIdPathModel" :options="treeData" :props="{
              checkStrictly: false,
              children: 'children',
              label: 'label',
              value: 'id',
              expandTrigger: 'hover',
              emitPath: true,
              leaf: 'leaf'
            }" placeholder="请选择角色" clearable style="width: 100%" @change="handlePositionChange">
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备用联系电话" prop="phoneSecondary">
            <el-input v-model="form.phoneSecondary" placeholder="请输入备用联系电话" maxlength="11"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="isEdit">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <dict-select v-model="form.status" category-id="USER_STATUS" placeholder="状态" style="width: 100%">
            </dict-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" :rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      <el-button @click="handleClose">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import DictSelect from '../../components/DictSelect.vue'
import { sysUserInsert, sysUserUpdate } from '@/api/SysUser'

export default {
  name: 'UserEdit',
  components: {
    DictSelect
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      form: {
        userId: null,
        username: '',
        password: '',
        realName: '',
        roleId: '',
        status: '',
        phone: '',
        phoneSecondary: null,
        remark: ''
      },
      treeData: [
        {
          id: '11',
          label: '系统管理员',
          leaf: true
        },
        {
          id: '12',
          label: '合伙人',
          leaf: true
        },
        {
          id: '21',
          label: '客服',
          leaf: true
        },
        {
          id: '22',
          label: '客服主管',
          leaf: true
        }
      ],
      roleIdPathModel: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    isEdit() {
      return !!this.form.userId
    },
    // 动态验证规则
    dynamicRules() {
      const baseRules = {
        username: [
          { required: true, message: '必填', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '必填', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '必填', trigger: 'blur' }
        ],
        roleId: [
          { required: true, message: '必填', trigger: 'change' }
        ],
        phone: [
          { required: true, message: '必填', trigger: 'blur' },
          { pattern: /^\d{11}$/, message: '联系电话必须是11位数字', trigger: 'blur' }
        ],
        phoneSecondary: [
          {
            validator: (_, value, callback) => {
              if (!value || value.trim() === '') {
                // 如果没有填写，则不验证
                callback()
              } else if (!/^\d{11}$/.test(value)) {
                // 如果填写了但格式不正确，则报错
                callback(new Error('备用联系电话必须是11位数字'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
      }

      // 只有编辑时才需要状态字段验证
      if (this.isEdit) {
        baseRules.status = [
          { required: true, message: '必填', trigger: 'change' }
        ]
      }

      return baseRules
    }
  },
  watch: {
    visible(val) {
      if (val && this.editData) {
        this.resetForm();
        // 处理编辑数据
        this.form = {
          userId: this.editData.userId || this.editData.id,
          username: this.editData.username || '',
          realName: this.editData.realName || this.editData.name || '',
          roleId: this.editData.roleId || '',
          status: this.editData.status || '',
          phone: this.editData.phone || '',
          phoneSecondary: this.editData.phoneSecondary || null,
          remark: this.editData.remark || ''
        };

        // 设置角色路径
        this.$nextTick(() => {
          if (this.form.roleId) {
            this.setRolePathBasedOnRoleId();
          }
        });
      } else if (!val) {
        this.roleIdPathModel = [];
      }
    },

    'form.roleId': {
      handler(val) {
        if (val) {
          console.log('roleId变化:', val);
          this.setRolePathBasedOnRoleId();
        }
      }
    }
  },
  methods: {
    // 根据roleId设置级联选择器的值
    setRolePathBasedOnRoleId() {
      const roleId = this.form.roleId;
      if (!roleId) return;

      this.roleIdPathModel = [];
      const roleIdStr = String(roleId);

      setTimeout(() => {
        // 现在所有角色都是一级节点，直接设置
        if (['10', '21', '22', '23'].includes(roleIdStr)) {
          this.roleIdPathModel = [roleIdStr];
        } else {
          this.searchPathInTree(this.treeData, roleIdStr);
        }

        console.log('设置级联选择器的值为:', this.roleIdPathModel);
        this.$forceUpdate();
      }, 100);
    },

    // 递归搜索树结构，寻找目标ID的路径
    searchPathInTree(treeData, targetId, currentPath = []) {
      if (!treeData || !treeData.length) return false;

      for (const node of treeData) {
        const path = [...currentPath, node.id];

        if (String(node.id) === targetId) {
          this.roleIdPathModel = path;
          return true;
        }

        if (node.children && node.children.length) {
          if (this.searchPathInTree(node.children, targetId, path)) {
            return true;
          }
        }
      }

      return false;
    },

    // 处理角色选择变化
    handlePositionChange(value) {
      console.log('级联选择器值变化:', value);
      if (value && value.length > 0) {
        const lastValue = value[value.length - 1];
        console.log('选中的值:', lastValue, typeof lastValue);

        // 现在所有选项都是有效的角色，直接设置
        this.form.roleId = parseInt(lastValue, 10);
        console.log('设置roleId为:', this.form.roleId, typeof this.form.roleId);
      } else {
        this.form.roleId = '';
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.resetForm()
    },

    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true;

          const submitData = JSON.parse(JSON.stringify(this.form));

          // 如果roleId是空字符串，转为null
          if (submitData.roleId === '') {
            submitData.roleId = null;
          }

          // 确保空的phoneSecondary是null
          if (!submitData.phoneSecondary || submitData.phoneSecondary.trim() === '') {
            submitData.phoneSecondary = null;
          }

          console.log('提交数据:', submitData);

          // 根据是否有userId判断是新增还是修改
          const promise = submitData.userId
            ? sysUserUpdate(submitData)
            : sysUserInsert(submitData);

          promise.then(response => {
            this.loading = false;
            console.log('response', response);
            if (response.success) {
              this.$message.success('保存成功');
              this.$emit('success', this.form);
              this.handleClose();
            } else {
              this.$message.error(response.msg || '保存失败');
            }
          }).catch(error => {
            this.loading = false;
          });
        } else {
          this.$message.error('请检查表单填写是否正确');
          return false;
        }
      });
    },

    resetForm() {
      this.$refs.form && this.$refs.form.resetFields();
      this.roleIdPathModel = [];
      this.form = {
        userId: null,
        username: '',
        realName: '',
        roleId: '',
        status: '',
        phone: '',
        phoneSecondary: null,
        remark: ''
      };
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
