package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.OrdPurchaseCentralizedOrder;
import com.my.crossborder.mybatis.mapper.OrdPurchaseCentralizedOrderMapper;
import com.my.crossborder.service.OrdPurchaseCentralizedOrderService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderInsertDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderPageDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderUpdateDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderDeleteDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderDetailVO;
import com.my.crossborder.controller.vo.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 集中采购订单 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
public class OrdPurchaseCentralizedOrderServiceImpl extends ServiceImpl<OrdPurchaseCentralizedOrderMapper, OrdPurchaseCentralizedOrder> implements OrdPurchaseCentralizedOrderService {


	@Transactional
	@Override
	public void insert(OrdPurchaseCentralizedOrderInsertDTO insertDTO) {
		OrdPurchaseCentralizedOrder entity = BeanUtil.copyProperties(insertDTO, OrdPurchaseCentralizedOrder.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(OrdPurchaseCentralizedOrderUpdateDTO updateDTO) {
		OrdPurchaseCentralizedOrder entity = BeanUtil.copyProperties(updateDTO, OrdPurchaseCentralizedOrder.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public OrdPurchaseCentralizedOrderDetailVO detail(Integer id) {
		OrdPurchaseCentralizedOrder entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, OrdPurchaseCentralizedOrderDetailVO.class);
	}

	@Override
	public Page<OrdPurchaseCentralizedOrderPageVO> page(OrdPurchaseCentralizedOrderPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(OrdPurchaseCentralizedOrderDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}
}
