package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.OrdPurchase;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchaseInsertDTO;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchasePageDTO;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchaseUpdateDTO;
import com.my.crossborder.controller.dto.ord_purchase.OrdPurchaseDeleteDTO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchaseDetailVO;
import com.my.crossborder.controller.vo.ord_purchase.OrdPurchasePageVO;

/**
 * 采购订单主表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface OrdPurchaseService extends IService<OrdPurchase> {
 

	/**
	 * 修改
	 */
	void update(OrdPurchaseUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	OrdPurchaseDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<OrdPurchasePageVO> page(OrdPurchasePageDTO pageDTO);	

//	/**
//	 * 批量删除(物理删除)
//	 */
//	void delete(OrdPurchaseDeleteDTO deleteDTO);	

}
