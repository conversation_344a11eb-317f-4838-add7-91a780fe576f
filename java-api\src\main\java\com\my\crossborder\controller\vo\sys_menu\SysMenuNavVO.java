package com.my.crossborder.controller.vo.sys_menu;

import java.io.Serializable;
import java.util.List;

import com.google.common.collect.Lists;
import com.my.crossborder.mybatis.entity.SysMenu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 前端导航菜单VO，与leftnav.vue组件结构匹配
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenuNavVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    private String menuid;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 菜单名称
     */
    private String menuname;

    /**
     * 是否有三级菜单：Y-有 N-没有 null-忽略
     */
    private String hasThird;

    /**
     * 路由URL
     */
    private String url;
    
    /**
     * 外部链接URL
     */
    private String externalLink;
    
    /**
     * 是否为外部链接
     */
    private Boolean isExternal;

    /**
     * 子菜单列表
     */
    private List<SysMenuNavVO> menus;
    
    
    /**
     * 类型转换
     * @param menu
     * @return
     */
    public static SysMenuNavVO convert(SysMenu menu) {
    	SysMenuNavVO vo = SysMenuNavVO.builder()
			.menuid(menu.getId())
			.icon(menu.getIcon())
			.menuname(menu.getMenuName())
			.hasThird(null)
			.url(menu.getRoutePath())
			.menus(Lists.newLinkedList())
			.isExternal(Boolean.FALSE)
			.externalLink(menu.getParentId())
			.build();
    	return vo;
    }
} 