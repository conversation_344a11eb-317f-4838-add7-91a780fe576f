package com.my.crossborder.controller.vo.stl_refund;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_结算_退款结算表
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class StlRefundDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 退款员工ID
     */
    private Integer refundUserId;

    /**
     * 退款年份
     */
    private Integer refundYear;

    /**
     * 退款月份
     */
    private Integer refundMonth;

    /**
     * 退款周次
     */
    private Integer refundWeek;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 结算日期
     */
    private LocalDateTime settlementDate;

    /**
     * 备注（使用常见标签快速填写）
     */
    private String remark;

    /**
     * 结算操作员用户id
     */
    private Integer createUserId;

    /**
     * 结算操作员用户名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
