package com.my.crossborder.controller.dto.sys_user;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_系统用户
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@NotNull(message="userId不能为空")
    private Integer userId;

    /**
     * 岗位(角色id)
     */
	@NotNull(message="roleId不能为空")
    private Integer roleId;

    /**
     * 用户名
     */
	@NotBlank(message="username不能为空")
    private String username;

    /**
     * 姓名
     */
	@NotBlank(message="realName不能为空")
    private String realName;

    /**
     * 人员状态
     */
	@NotBlank(message="status不能为空")
    private String status;

    /**
     * 联系电话
     */
	@Pattern(regexp = "\\d{11}", message = "联系电话必须是11位数字")
	@NotBlank(message="phone不能为空")
    private String phone;

    /**
     * 备用联系电话
     */
	@Pattern(regexp = "\\d{11}", message = "备用联系电话必须是11位数字")
    private String phoneSecondary;

    /**
     * 备注
     */
    private String remark;


}
