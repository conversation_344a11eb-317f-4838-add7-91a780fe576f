package com.my.crossborder.controller;

import java.util.List;
import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedInsertDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedPageDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedUpdateDTO;
import com.my.crossborder.controller.dto.ord_purchase_centralized.OrdPurchaseCentralizedDeleteDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedDetailVO;
import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedPageVO;
import com.my.crossborder.controller.vo.StdResp;

import com.my.crossborder.service.OrdPurchaseCentralizedService;
import com.my.crossborder.service.SysShopService;

import cn.dev33.satoken.annotation.SaCheckPermission;

import org.springframework.web.bind.annotation.RestController;

/**
 * 集中采购 
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@RestController
@RequestMapping("/api/ord-purchase-centralized")
@RequiredArgsConstructor
public class OrdPurchaseCentralizedController {

    private final OrdPurchaseCentralizedService ordPurchaseCentralizedService;
    private final SysShopService sysShopService;

    /**
    * 新增
    */
    @SaCheckPermission("ord-purchase-centralized:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody OrdPurchaseCentralizedInsertDTO insertDTO) {
    	this.ordPurchaseCentralizedService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("ord-purchase-centralized:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody OrdPurchaseCentralizedUpdateDTO updateDTO) {
    	this.ordPurchaseCentralizedService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<OrdPurchaseCentralizedDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.ordPurchaseCentralizedService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<OrdPurchaseCentralizedPageVO>> page(OrdPurchaseCentralizedPageDTO pageDTO) {
        // 先调用SysShopService.myShopIds()，获取当前登录人应该有的shopIds
        List<Integer> myShopIds = this.sysShopService.myShopIds();
        if (myShopIds != null && !myShopIds.isEmpty()) {
            pageDTO.setShopIds(myShopIds);
        }

        Page<OrdPurchaseCentralizedPageVO> page = this.ordPurchaseCentralizedService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("ord-purchase-centralized:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody OrdPurchaseCentralizedDeleteDTO deleteDTO) {
    	this.ordPurchaseCentralizedService.delete(deleteDTO);
		return StdResp.success();
    }

    /**
     * 确认状态
     * @param id 采购记录ID
     * @param status 确认状态
     */
    @SaCheckPermission("ord-purchase-centralized:confirm")
    @PutMapping("/confirm-status/{id}")
    public StdResp<?> confirmStatus(@PathVariable Integer id, @RequestParam String status) {
        this.ordPurchaseCentralizedService.confirmStatus(id, status);
        return StdResp.success();
    }

}
