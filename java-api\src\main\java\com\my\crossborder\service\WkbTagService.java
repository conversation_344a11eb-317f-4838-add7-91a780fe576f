package com.my.crossborder.service;

import com.my.crossborder.mybatis.entity.WkbTag;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagInsertDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagPageDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagUpdateDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagDeleteDTO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagDetailVO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagPageVO;

/**
 * 工作台_订单标签 服务类
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface WkbTagService extends IService<WkbTag> {

	/**
	 * 新增
	 */
	void insert(WkbTagInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(WkbTagUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	WkbTagDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<WkbTagPageVO> page(WkbTagPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(WkbTagDeleteDTO deleteDTO);	

}
