package com.my.crossborder.forest.erp990;

import static org.junit.jupiter.api.Assertions.fail;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.dtflys.forest.callback.OnError;
import com.my.crossborder.forest.erp990.onerror.Erp990OnError;
import com.my.crossborder.forest.erp990.vo.ErpOrderPageVO;
import com.my.crossborder.forest.erp990.vo.ErpPackageRecordPageVO;
import com.my.crossborder.forest.erp990.vo.ShopPageVO;

import lombok.extern.slf4j.Slf4j;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class Erp990ClientTest {
	
	private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	private static final Integer DEFAULT_PAGE_SIZE = 50;
	
	@Autowired
	Erp990Client erp990Client;
	

	@Test
	void testShopPage() {
		Integer pageNum = 1;
		Integer pageSize = 100;
		OnError onError = new Erp990OnError();
		ShopPageVO vo = this.erp990Client.shopPage(pageNum, pageSize, onError);
		log.info(JSON.toJSONString(vo));
	}

	@Test
	void testOrderPage() {
		long daysAgo = 30;
        LocalDateTime endTime = LocalDateTime.now();
		LocalDateTime startTime = endTime.minusDays(daysAgo);
        
        log.info("查询订单数据，时间范围：{} - {}", startTime, endTime);
        
        String startTimeStr = startTime.format(FORMATTER);
        String endTimeStr = endTime.format(FORMATTER);
        Integer pageNum = 1;
		OnError onError = new Erp990OnError();
		ErpOrderPageVO vo = this.erp990Client.orderPage(pageNum, DEFAULT_PAGE_SIZE, 0, 
				                startTimeStr, endTimeStr, 
				                null, null, null, 
				                onError);
		log.info("{}", JSON.toJSONString(vo));
	}

	
	@Test
	void testPackageRecord() {
		String trackid = "250705C2E3W3NH\n250705BKBH28YS";
		ErpPackageRecordPageVO vo = this.erp990Client.packageRecord(1, 10, trackid, new Erp990OnError());
		log.info("{}", JSON.toJSONString(vo));
	}

}
