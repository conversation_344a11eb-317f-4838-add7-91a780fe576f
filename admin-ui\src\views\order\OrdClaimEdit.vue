<template>
  <el-dialog :title="dialogTitle" top="8vh" :visible.sync="dialogVisible" width="650px" @close="handleDialogClose">
    <el-form :model="itemForm" :rules="itemRules" ref="itemForm" label-width="140px">
      <el-form-item label="选择订单商品" prop="selectedOrderItemId">
        <OrderItemSelector v-model="itemForm.selectedOrderItemId" placeholder="请选择订单商品"
          :preset-item-info="currentPresetItemInfo" after-sale-filter="claim" @select="handleOrderItemSelect" />
      </el-form-item>

      <el-form-item label="店铺名称" prop="shopName">
        <el-input v-model="itemForm.shopName" placeholder="选择订单商品后自动填充" readonly></el-input>
      </el-form-item>

      <el-form-item label="订单号" prop="orderSn">
        <el-input v-model="itemForm.orderSn" placeholder="选择订单商品后自动填充" readonly></el-input>
      </el-form-item>

      <el-form-item label="快递号" prop="expressNo">
        <el-input v-model="itemForm.expressNo" placeholder="选择订单商品后自动填充" readonly></el-input>
      </el-form-item>

      <el-form-item label="商品售价" prop="productPrice">
        <el-input v-model="itemForm.productPrice" placeholder="选择订单商品后自动填充" readonly></el-input>
      </el-form-item>

      <hr />

      <el-form-item label="采购成本" prop="purchaseCost">
        <el-input-number v-model="itemForm.purchaseCost" :precision="2" :step="0.01" :min="0" controls-position="right" style="width: 215px;"></el-input-number>
      </el-form-item>

      <el-form-item label="问题描述" prop="issue">
        <el-input type="textarea" :rows="3" v-model="itemForm.issue" placeholder="请输入问题描述" maxlength="500" show-word-limit></el-input>
      </el-form-item>

      <el-form-item label="问题类别" prop="issueType">
        <dict-select v-model="itemForm.issueType" placeholder="请选择问题类别" clearable category-id="claim_issue_type">
        </dict-select>
      </el-form-item>

      <el-form-item label="处理办法" prop="closeWay">
        <dict-select v-model="itemForm.closeWay" placeholder="请选择处理办法" clearable category-id="claim_close_way" @change="handleCloseWayChange">
        </dict-select>
      </el-form-item>

      <!-- 退款截图上传 -->
      <el-form-item v-if="showRefundScreenshot" label="退款截图" prop="attachmentIdList">
        <FileUpload
          ref="refundFileUpload"
          :key="refundUploadKey"
          accept="image/*"
          :max-size="5"
          @upload-success="handleRefundUploadSuccess"
          @file-removed="handleRefundFileRemoved"
        />
      </el-form-item>

      <!-- 补寄单号输入 -->
      <el-form-item v-if="showResendTracking" label="补寄单号" prop="waybillNumber">
        <el-input v-model="itemForm.waybillNumber" placeholder="请输入补寄单号" maxlength="50" style="width: 300px;" @blur="handleWaybillNumberBlur"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import OrderItemSelector from '../../components/OrderItemSelector'
import { insertOrdClaim, updateOrdClaim } from '@/api/OrdClaim'
import DictSelect from '../../components/DictSelect'
import FileUpload from '@/components/FileUpload'

export default {
  name: 'OrdClaimEdit',
  components: {
    OrderItemSelector,
    DictSelect,
    FileUpload
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      itemForm: {
        id: null,
        orderItemId: '',
        selectedOrderItemId: '',
        shopName: '',
        orderSn: '',
        expressNo: '',
        productPrice: '',
        purchaseCost: null,
        issue: '',
        issueType: '',
        closeWay: '',
        waybillNumber: '',
        attachmentIdList: []
      },
      itemRules: {
        selectedOrderItemId: [
          { required: true, message: '请选择订单商品', trigger: 'change' }
        ],
        purchaseCost: [
          { required: true, message: '请输入采购成本', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '采购成本必须大于0', trigger: 'blur' }
        ],
        issue: [
          { required: true, message: '请输入问题描述', trigger: 'blur' }
        ],
        issueType: [
          { required: true, message: '请选择问题类别', trigger: 'change' }
        ],
        closeWay: [
          { required: true, message: '请选择处理办法', trigger: 'change' }
        ],
        attachmentIdList: [
          // { validator: this.validateRefundScreenshot, trigger: 'change' }
        ],
        waybillNumber: [
          // { validator: this.validateWaybillNumber, trigger: 'blur' }
        ]
      },
      currentPresetItemInfo: null,
      refundUploadKey: 0
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return this.isEdit ? '编辑物流理赔' : '添加物流理赔'
    },
    showRefundScreenshot() {
      return this.itemForm.closeWay === '2'
    },
    showResendTracking() {
      return this.itemForm.closeWay === '1'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    },
    editData: {
      handler(newData) {
        if (this.visible && newData) {
          this.initForm()
        }
      },
      deep: true
    }
  },
  methods: {
    initForm() {
      if (this.isEdit && this.editData) {
        this.itemForm = { ...this.editData }
        this.itemForm.selectedOrderItemId = this.editData.orderItemId || ''
        // 修复售价回填问题：如果没有productPrice，使用itemPrice
        if (!this.itemForm.productPrice && this.editData.itemPrice) {
          this.itemForm.productPrice = this.editData.itemPrice
        }
        // 确保 attachmentIdList 存在
        if (!this.itemForm.attachmentIdList) {
          this.itemForm.attachmentIdList = []
        }
        this.currentPresetItemInfo = {
          productName: this.editData.itemName,
          productSpec: this.editData.itemModelName,
          originalOrderNumber: this.editData.orderSn
        }
      } else {
        this.itemForm = {
          id: null,
          orderItemId: '',
          selectedOrderItemId: '',
          shopName: '',
          orderSn: '',
          expressNo: '',
          productPrice: '',
          purchaseCost: null,
          issue: '',
          issueType: '',
          closeWay: '',
          waybillNumber: '',
          attachmentIdList: []
        }
        this.currentPresetItemInfo = null
      }
      this.refundUploadKey++
    },

    handleOrderItemSelect(data) {
      const { orderItem, order } = data
      this.itemForm.orderItemId = orderItem.id
      this.itemForm.shopName = order.shopName || ''
      this.itemForm.orderSn = order.orderSn || ''
      this.itemForm.expressNo = orderItem.expressNo || ''
      // 回填售价 - 在新增和修改时都需要回填
      this.itemForm.productPrice = orderItem.itemPrice || ''

      // // 如果是新增模式，自动设置采购成本为售价
      // if (!this.isEdit && orderItem.itemPrice) {
      //   this.itemForm.purchaseCost = parseFloat(orderItem.itemPrice)
      // }
    },

    handleCloseWayChange() {
      // 清理附件列表
      this.itemForm.attachmentIdList = []
      this.itemForm.waybillNumber = ''
      this.refundUploadKey++

      // 触发相关字段的校验
      this.$nextTick(() => {
        if (this.$refs.itemForm) {
          this.$refs.itemForm.validateField('attachmentIdList')
          this.$refs.itemForm.validateField('waybillNumber')
        }
      })
    },

    handleRefundUploadSuccess(data) {
      // 将文件ID添加到附件列表中
      if (data.id && !this.itemForm.attachmentIdList.includes(data.id)) {
        this.itemForm.attachmentIdList.push(data.id)
      }
      // 触发校验
      this.$nextTick(() => {
        if (this.$refs.itemForm) {
          this.$refs.itemForm.validateField('attachmentIdList')
        }
      })
    },

    handleRefundFileRemoved(fileId) {
      // 从附件列表中移除对应的文件ID
      if (fileId) {
        const index = this.itemForm.attachmentIdList.indexOf(fileId)
        if (index > -1) {
          this.itemForm.attachmentIdList.splice(index, 1)
        }
      }
      // 触发校验
      this.$nextTick(() => {
        if (this.$refs.itemForm) {
          this.$refs.itemForm.validateField('attachmentIdList')
        }
      })
    },

    // 退款截图校验
    validateRefundScreenshot(rule, value, callback) {
      if (this.itemForm.closeWay === '2' && (!this.itemForm.attachmentIdList || this.itemForm.attachmentIdList.length === 0)) {
        callback(new Error('选择退款时必须上传退款截图'))
      } else {
        callback()
      }
    },

    // 补寄单号校验
    validateWaybillNumber(rule, value, callback) {
      if (this.itemForm.closeWay === '1' && !this.itemForm.waybillNumber) {
        callback(new Error('选择补寄时必须填写补寄单号'))
      } else {
        callback()
      }
    },

    // 补寄单号失焦事件
    handleWaybillNumberBlur() {
      this.$nextTick(() => {
        if (this.$refs.itemForm) {
          this.$refs.itemForm.validateField('waybillNumber')
        }
      })
    },

    handleSubmit() {
      this.$refs.itemForm.validate((valid) => {
        if (valid) {
          const submitData = {
            orderItemId: String(this.itemForm.selectedOrderItemId || this.itemForm.orderItemId),
            orderSn: this.itemForm.orderSn,
            issue: this.itemForm.issue,
            issueType: this.itemForm.issueType,
            purchaseCost: this.itemForm.purchaseCost,
            closeWay: this.itemForm.closeWay,
            waybillNumber: this.itemForm.waybillNumber,
            refundScreenshotId: this.itemForm.refundScreenshotId,
            attachmentIdList: this.itemForm.attachmentIdList
          }

          if (this.isEdit && this.itemForm.id) {
            submitData.id = this.itemForm.id
          }

          const apiCall = this.isEdit ? updateOrdClaim(submitData) : insertOrdClaim(submitData)

          apiCall
            .then(response => {
              if (response.success) {
                this.$message.success('保存成功')
                this.$emit('submit', submitData)
                this.dialogVisible = false
              } else {
                this.$message.error('保存失败：' + response.message)
              }
            })
            .catch(error => {
              this.$message.error('保存失败，请稍后重试')
            })
        } else {
          this.$message.warning('请完善表单信息')
        }
      })
    },

    handleCancel() {
      this.dialogVisible = false
    },

    handleDialogClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
hr {
  border: none;
  border-top: 1px solid #e4e7ed;
  margin: 20px 0;
}
</style>
