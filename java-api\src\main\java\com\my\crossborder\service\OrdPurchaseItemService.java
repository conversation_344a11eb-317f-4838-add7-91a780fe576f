package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.ord_purchase_item.OrdPurchaseItemDeleteDTO;
import com.my.crossborder.controller.dto.ord_purchase_item.OrdPurchaseItemUpdateDTO;
import com.my.crossborder.controller.vo.ord_purchase_item.OrdPurchaseItemDetailVO;
import com.my.crossborder.mybatis.entity.OrdPurchaseItem;

/**
 * 采购订单明细表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface OrdPurchaseItemService extends IService<OrdPurchaseItem> {
 
	/**
	 * 修改
	 */
	void update(OrdPurchaseItemUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	OrdPurchaseItemDetailVO detail(String id);
	/**
	 * 批量删除(物理删除)
	 */
	void delete(OrdPurchaseItemDeleteDTO deleteDTO);	

}
