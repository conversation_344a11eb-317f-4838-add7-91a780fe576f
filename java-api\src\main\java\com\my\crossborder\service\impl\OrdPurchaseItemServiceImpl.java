package com.my.crossborder.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.ord_purchase_item.OrdPurchaseItemDeleteDTO;
import com.my.crossborder.controller.dto.ord_purchase_item.OrdPurchaseItemUpdateDTO;
import com.my.crossborder.controller.vo.ord_purchase_item.OrdPurchaseItemDetailVO;
import com.my.crossborder.mybatis.entity.ErpOrderItem;
import com.my.crossborder.mybatis.entity.ErpOrderItemExpress;
import com.my.crossborder.mybatis.entity.OrdPurchaseItem;
import com.my.crossborder.mybatis.mapper.OrdPurchaseItemMapper;
import com.my.crossborder.service.ErpOrderItemExpressService;
import com.my.crossborder.service.ErpOrderItemService;
import com.my.crossborder.service.OrdPurchaseItemService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 采购订单明细表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@RequiredArgsConstructor
public class OrdPurchaseItemServiceImpl extends ServiceImpl<OrdPurchaseItemMapper, OrdPurchaseItem> implements OrdPurchaseItemService {

	private final ErpOrderItemExpressService erpOrderItemExpressService;
	private final ErpOrderItemService erpOrderItemService;


	@Transactional
	@Override
	public void update(OrdPurchaseItemUpdateDTO updateDTO) {
		OrdPurchaseItem entity = BeanUtil.copyProperties(updateDTO, OrdPurchaseItem.class);
		if (!"1".equals(entity.getPurchaseChannel())
			&& !"2".equals(entity.getPurchaseChannel())
			&& !"3".equals(entity.getPurchaseChannel())
			&& !"4".equals(entity.getPurchaseChannel())){
			entity.setPurchaseAmount(new BigDecimal("0"));
		}
		// 检查数据库中是否已存在该记录
		String orderItemId = updateDTO.getOrderItemId();
		OrdPurchaseItem existingEntity = this.baseMapper.selectById(orderItemId);
		if (existingEntity == null) {
			// 如果记录不存在，当作新增处理
			entity.setPurchaseUserId(StpUtil.getLoginIdAsInt());
			// 设置采购日期为今天
			entity.setPurchaseDate(LocalDate.now());
			entity.setCreateTime(LocalDateTime.now());
			entity.setUpdateTime(LocalDateTime.now());
			this.save(entity);
		} else {
			// 如果记录存在，只更新updateTime
			entity.setUpdateTime(LocalDateTime.now());
			this.baseMapper.updateById(entity);
		}
		// 更新快递单号
		// if (StrUtil.isNotBlank(updateDTO.getExpressNo())) {
		// 	// 根据orderItemId查询erp_order_item_express表，获取id
		// 	ErpOrderItemExpress expressEntity = this.erpOrderItemExpressService.getById(orderItemId);
		// 	if (expressEntity == null) {
		// 		// 根据orderItemId查询erp_order_item表，获取orderId
		// 		ErpOrderItem erpOrderItem = this.erpOrderItemService.getById(orderItemId);
		// 		String orderId = erpOrderItem.getOrderId();
		// 		expressEntity = ErpOrderItemExpress.builder()
		// 			.id(orderItemId + "" + updateDTO.getExpressNo())
		// 			.orderItemId(orderItemId)
		// 			.expressNo(updateDTO.getExpressNo())
		// 			.putCreateTime(LocalDateTime.now())
		// 			.putInTime(LocalDateTime.now())
		// 			.expressinFlag("")
		// 			.orderId(orderId)
		// 			.build();
		// 		this.erpOrderItemExpressService.save(expressEntity);
		// 	} else {
		// 		expressEntity = new ErpOrderItemExpress();
		// 		expressEntity.setId(orderItemId + "" + updateDTO.getExpressNo());
		// 		expressEntity.setExpressNo(updateDTO.getExpressNo());
		// 		this.erpOrderItemExpressService.updateById(expressEntity);
		// 	}
		// }
	}

	@Override
	public OrdPurchaseItemDetailVO detail(String id) {
		OrdPurchaseItem entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, OrdPurchaseItemDetailVO.class);
	}

	@Transactional
	@Override
	public void delete(OrdPurchaseItemDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}
}
