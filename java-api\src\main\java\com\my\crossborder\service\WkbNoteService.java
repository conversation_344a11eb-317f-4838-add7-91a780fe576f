package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteByOrderDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteDeleteDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteInsertDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNotePageDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteUpdateDTO;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;
import com.my.crossborder.controller.vo.wkb_note.WkbNotePageVO;
import com.my.crossborder.mybatis.entity.WkbNote;

/**
 * 工作台_工作笔记表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface WkbNoteService extends IService<WkbNote> {

	/**
	 * 新增
	 */
	void insert(WkbNoteInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(WkbNoteUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	WkbNoteDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<WkbNotePageVO> page(WkbNotePageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(WkbNoteDeleteDTO deleteDTO);	

	/**
	 * 标记为已完成
	 * @param id 笔记ID
	 */
	void markAsCompleted(Integer id);

	/**
	 * 根据订单号和场景查询工作笔记
	 * @param orderSnAndScene 订单号和场景
	 * @return 工作笔记详情，如果不存在则返回null
	 */
	WkbNoteDetailVO getByOrderSnAndScene(WkbNoteByOrderDTO orderSnAndScene);

}
