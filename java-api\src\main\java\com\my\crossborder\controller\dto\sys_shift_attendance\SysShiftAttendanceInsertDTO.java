package com.my.crossborder.controller.dto.sys_shift_attendance;

import java.io.Serializable;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_考勤表
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftAttendanceInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 排班日期
     */
    @NotNull(message="shiftDay不能为空")
    private LocalDate shiftDay;

    /**
     * 用户ID
     */
	@NotNull(message="userId不能为空")
    private Integer userId;

}
