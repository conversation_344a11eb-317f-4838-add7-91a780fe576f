package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.stl_refund.StlRefundPageDTO;
import com.my.crossborder.controller.vo.stl_refund.StlRefundPageVO;
import com.my.crossborder.mybatis.entity.StlRefund;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 结算_退款结算表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface StlRefundMapper extends BaseMapper<StlRefund> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<StlRefundPageVO> page(StlRefundPageDTO pageDTO);
	
}
