import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

// 分页查询集中采购
export function getOrdPurchaseCentralizedPage(params) {
  return reqGet('/ord-purchase-centralized/page', params)
}

// 新增集中采购
export function insertOrdPurchaseCentralized(data) {
  return reqPost('/ord-purchase-centralized', data)
}

// 修改集中采购
export function updateOrdPurchaseCentralized(data) {
  return reqPut('/ord-purchase-centralized', data)
}

// 查询集中采购详情
export function getOrdPurchaseCentralizedDetail(id) {
  return reqGet(`/ord-purchase-centralized/${id}`)
}

// 删除集中采购
export function deleteOrdPurchaseCentralized(data) {
  return reqDelete('/ord-purchase-centralized', data)
}

// 确认采购状态
export function confirmStatusOrdPurchaseCentralized(id, status) {
  return reqPut(`/ord-purchase-centralized/confirm-status/${id}?status=${status}`)
}
