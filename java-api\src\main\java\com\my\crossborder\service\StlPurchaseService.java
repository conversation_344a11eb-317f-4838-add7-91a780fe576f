package com.my.crossborder.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.stl_purchase.StlPurchaseDeleteDTO;
import com.my.crossborder.controller.dto.stl_purchase.StlPurchaseInsertDTO;
import com.my.crossborder.controller.vo.stl_purchase.DailyPurchaseDetail;
import com.my.crossborder.controller.vo.stl_purchase.MonthlyPurchaseDataVO;
import com.my.crossborder.controller.vo.stl_purchase.StlPurchaseDetailVO;
import com.my.crossborder.mybatis.entity.StlPurchase;

/**
 * 结算_采购结算表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface StlPurchaseService extends IService<StlPurchase> {

	/**
	 * 新增
	 */
	void insert(StlPurchaseInsertDTO insertDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	StlPurchaseDetailVO detail(Integer id);

//	/**
//	 * 批量删除(物理删除)
//	 */
//	void delete(StlPurchaseDeleteDTO deleteDTO);

	/**
	 * 获取用户月度采购结算数据
	 * @param userId 用户ID
	 * @param year 年份
	 * @param month 月份
	 * @return 月度采购数据
	 */
	List<MonthlyPurchaseDataVO> monthlyPurchaseData(Integer userId, Integer year, Integer month);

	/**
	 * 获取用户指定日期的采购详情
	 * @param userId 用户ID
	 * @param purchaseDate 采购日期
	 * @return 采购详情列表
	 */
	DailyPurchaseDetail dailyPurchaseDetails(Integer userId, LocalDate purchaseDate);

}
