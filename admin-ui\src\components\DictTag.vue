<template>
  <span>
    <el-tag
      v-if="loading"
      size="small"
      type="info"
    >
      {{ '-' }}
    </el-tag>
    <el-tag
      v-else-if="colorValue"
      :type="colorValue"
      size="small"
    >
      {{ labelText }}
    </el-tag>
    <span v-else>{{ labelText }}</span>
  </span>
</template>

<script>
import dictService from '../utils/dictService'

export default {
  name: 'DictTag',
  props: {
    // 字典分类ID
    categoryId: {
      type: String,
      required: true
    },
    // 字典值
    value: {
      type: [String, Number],
      required: false,
      default: null
    }
  },
  data() {
    return {
      labelText: '',
      colorValue: '',
      loading: true
    }
  },
  created() {
    this.loadDictLabel()
  },
  watch: {
    value() {
      this.loadDictLabel()
    },
    categoryId() {
      this.loadDictLabel()
    }
  },
  methods: {
    async loadDictLabel() {
      if (!this.categoryId || this.value === undefined || this.value === null) {
        this.labelText = '-'
        this.colorValue = ''
        this.loading = false
        return
      }

      try {
        this.loading = true
        // 同时获取标签文本和颜色
        const [label, color] = await Promise.all([
          dictService.getDictLabel(this.categoryId, this.value),
          dictService.getDictColor(this.categoryId, this.value)
        ])
        this.labelText = label
        this.colorValue = color
      } catch (error) {
        console.error('获取字典信息失败:', error)
        this.labelText = String(this.value)
        this.colorValue = ''
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
