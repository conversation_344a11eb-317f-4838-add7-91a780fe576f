package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.erp_order.ErpOrderPageDTO;
import com.my.crossborder.controller.dto.erp_order.ErpOrderStatusSummaryPageDTO;

import com.my.crossborder.controller.vo.erp_order.ErpOrderPageVO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderStatusSummaryPageVO;
import com.my.crossborder.controller.vo.erp_order.RepurchaseErpOrderPageVO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemWithExpressVO;
import com.my.crossborder.mybatis.entity.ErpOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Set;

/**
 * 订单主表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ErpOrderMapper extends BaseMapper<ErpOrder> {

	/**
	 * (未完整填写物流编号)分页查询（包含订单项）
	 * @param pageDTO 查询参数
	 */
	Page<ErpOrderPageVO> incompleteLogisticsPageWithItems(ErpOrderPageDTO pageDTO);

	/**
	 * 已填物流编号未入库订单分页查询（包含订单项）
	 * @param pageDTO 查询参数
	 */
	Page<ErpOrderPageVO> logisticsDoneNotWarehoused(ErpOrderPageDTO pageDTO);

	/**
	 * 已入库未出库订单分页查询（包含订单项）
	 * @param pageDTO 查询参数
	 */
	Page<ErpOrderPageVO> warehousedNotOutbound(ErpOrderPageDTO pageDTO);

//	/**
//	 * 已采购但出库前取消订单分页查询（包含订单项）
//	 * @param pageDTO 查询参数
//	 */
//	Page<ErpOrderPageVO> purchaseDoneOrderCancelled(ErpOrderPageDTO pageDTO);

	/**
	 * 根据订单ID查询订单项（包含快递信息）
	 * @param orderId 订单ID
	 */
	List<ErpOrderItemWithExpressVO> selectOrderItemsWithExpress(@Param("orderId") String orderId);



	/**
	 * 重新采购订单分页查询（包含订单项）
	 * @param pageDTO 查询参数
	 */
	Page<RepurchaseErpOrderPageVO> repurchasePageWithItems(ErpOrderPageDTO pageDTO);

	/**
	 * 订单选择器专用分页查询（包含订单项）
	 * 用于OrderSelector组件的通用订单选择功能
	 * @param pageDTO 查询参数
	 */
	Page<ErpOrderPageVO> selectorPageWithItems(ErpOrderPageDTO pageDTO);

	/**
	 * 订单状态汇总分页查询
	 * @param pageDTO 查询参数
	 */
	Page<ErpOrderStatusSummaryPageVO> orderStatusSummaryPage(ErpOrderStatusSummaryPageDTO pageDTO);

	/**
	 * 查询物流待完成订单分页
	 *
	 * @param page 分页参数
	 * @param pageDTO 查询条件
	 * @return 分页结果
	 */
	Page<ErpOrderPageVO> incompleteLogisticsPageWithItems(Page<ErpOrderPageVO> page, @Param("query") ErpOrderPageDTO pageDTO);
	
	/**
	 * 查询物流完成未入库订单分页
	 *
	 * @param page 分页参数
	 * @param pageDTO 查询条件
	 * @return 分页结果
	 */
	Page<ErpOrderPageVO> logisticsDoneNotWarehoused(Page<ErpOrderPageVO> page, @Param("query") ErpOrderPageDTO pageDTO);
	
	/**
	 * 查询已入库未出库订单分页
	 *
	 * @param page 分页参数
	 * @param pageDTO 查询条件
	 * @return 分页结果
	 */
	Page<ErpOrderPageVO> warehousedNotOutbound(Page<ErpOrderPageVO> page, @Param("query") ErpOrderPageDTO pageDTO);
	
	/**
	 * 查询采购完成订单取消分页
	 *
	 * @param page 分页参数
	 * @param pageDTO 查询条件
	 * @return 分页结果
	 */
	Page<ErpOrderPageVO> purchaseDoneOrderCancelled(Page<ErpOrderPageVO> page, @Param("query") ErpOrderPageDTO pageDTO);
	
	/**
	 * 批量查询订单项与快递信息
	 *
	 * @param orderIds 订单ID集合
	 * @return 订单项列表
	 */
	List<ErpOrderItemWithExpressVO> selectOrderItemListWithExpress(@Param("orderIds") Set<String> orderIds);
	
}
