package com.my.crossborder.service.check.impl;

import java.lang.reflect.Field;

import com.my.crossborder.service.check.AbstractCheck;
import com.my.crossborder.service.check.result.CheckResult;

import cn.hutool.core.util.StrUtil;

/**
 * AI超时检测器
 * <AUTHOR>
 */
public class AiTimeoutCheck extends AbstractCheck {

    @Override
    protected CheckResult doCheck(Object fieldValue, Field field, Object obj) {
    	String str = (String) fieldValue;
    	if (StrUtil.isBlank(str)) {
    		return fail(this.fieldLabel + "不能为空");
    	}
    	
        Integer aiTimeout = new Integer(str);
        if (aiTimeout <= 0) {
            return fail(this.fieldLabel + "必须大于零");
        }
        return success();
    }
    
}
