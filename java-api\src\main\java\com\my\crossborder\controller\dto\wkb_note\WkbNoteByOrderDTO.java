package com.my.crossborder.controller.dto.wkb_note;

import javax.validation.constraints.NotBlank;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 根据订单号和场景查询工作笔记DTO
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNoteByOrderDTO {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderSn;

    /**
     * 场景
     */
    @NotBlank(message = "场景不能为空")
    private String scene;
} 