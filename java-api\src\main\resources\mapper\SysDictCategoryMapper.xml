<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysDictCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysDictCategory">
        <id column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="description" property="description" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        category_id, category_name, description
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryPageVO">
		SELECT
			category_id, category_name, description
		FROM
			sys_dict_category AS t1
		<where>
        	1=1
	        <if test="categoryId != null and categoryId != ''">
	           	AND t1.category_id = #{categoryId}
            </if>
	        <if test="categoryName != null and categoryName != ''">
	           	AND t1.category_name like concat('%', #{categoryName}, '%')
            </if>
	        <if test="description != null and description != ''">
	           	AND t1.description like concat('%', #{description}, '%')
            </if>
        </where>
    </select>

</mapper>
