package com.my.crossborder.controller.dto.sys_role;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_role.SysRolePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_系统角色表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRolePageDTO 
						extends PageDTO<SysRolePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer roleId;

    private String roleName;

    private String description;

}
