<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.WkbTodoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.WkbTodo">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="deadline" property="deadline" />
        <result column="publish_time" property="publishTime" />
        <result column="publish_user_id" property="publishUserId" />
        <result column="receive_user_id" property="receiveUserId" />
        <result column="is_read" property="read" />
        <result column="read_time" property="readTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, title, content, deadline, publish_time, publish_user_id, receive_user_id, is_read, readTime
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.wkb_todo.WkbTodoPageVO">
		SELECT	
			t1.id,
			t1.type,
			t1.title,
			t1.content,
			t1.publish_time,
			t1.publish_user_id,
			t1.deadline,
			t1.receive_user_id as user_id, 
			t1.is_read as 'read', 
			t1.read_time,
			tUser.real_name as publishUserName
		FROM
			wkb_todo AS t1 
			LEFT JOIN sys_user AS tUser ON t1.publish_user_id = tUser.user_id
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="type != null and type != ''">
	           	AND t1.type = #{type}
            </if>
	        <if test="title != null and title != ''">
	           	AND t1.title = #{title}
            </if>
	        <if test="content != null and content != ''">
	           	AND t1.content = #{content}
            </if>
	        <if test="publishTime != null and publishTime != ''">
	           	AND t1.publish_time = #{publishTime}
            </if>
	        <if test="publishUserId != null and publishUserId != ''">
	           	AND t1.publish_user_id = #{publishUserId}
            </if>
	        <if test="receiveUserId != null">
	           	AND t1.receive_user_id = #{receiveUserId}
            </if>
	        <if test="read != null">
	           	AND t1.is_read = #{read}
            </if>
        </where>
    </select>

</mapper>
