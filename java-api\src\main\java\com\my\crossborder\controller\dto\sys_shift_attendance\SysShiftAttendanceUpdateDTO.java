package com.my.crossborder.controller.dto.sys_shift_attendance;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 修改_考勤表
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftAttendanceUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排班日期
     */
	@NotNull(message="shiftDay不能为空")
    private LocalDate shiftDay;

    /**
     * 用户ID
     */
	@NotNull(message="userId不能为空")
    private Integer userId;

    /**
     * 打卡时间
     */
	@NotNull(message="clockTime不能为空")
    private LocalDateTime clockTime;

    /**
     * 打卡状态：0=待打卡/1=已打卡/2=缺勤
     */
	@NotNull(message="closeStatus不能为空")
    private Integer closeStatus;

}
