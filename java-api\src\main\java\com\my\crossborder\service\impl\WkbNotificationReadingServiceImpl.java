package com.my.crossborder.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingDeleteDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingInsertDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingPageDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingUpdateDTO;
import com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingDetailVO;
import com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingPageVO;
import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.mybatis.entity.WkbNotificationReading;
import com.my.crossborder.mybatis.mapper.WkbNotificationReadingMapper;
import com.my.crossborder.service.SysUserService;
import com.my.crossborder.service.WkbNotificationReadingService;
import com.my.crossborder.util.ColumnLambda;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;

/**
 * 通知阅读表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
@RequiredArgsConstructor
public class WkbNotificationReadingServiceImpl extends ServiceImpl<WkbNotificationReadingMapper, WkbNotificationReading> implements WkbNotificationReadingService {

	private final SysUserService sysUserService;
	

	@Transactional
	@Override
	public void insert(WkbNotificationReadingInsertDTO insertDTO) {
		WkbNotificationReading entity = BeanUtil.copyProperties(insertDTO, WkbNotificationReading.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(WkbNotificationReadingUpdateDTO updateDTO) {
		WkbNotificationReading entity = BeanUtil.copyProperties(updateDTO, WkbNotificationReading.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public WkbNotificationReadingDetailVO detail(Integer id) {
		WkbNotificationReading entity = this.baseMapper.selectById(id);
		WkbNotificationReadingDetailVO ret = BeanUtil.copyProperties(entity, WkbNotificationReadingDetailVO.class);
		return ret;
	}

	@Override
	public Page<WkbNotificationReadingPageVO> page(WkbNotificationReadingPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<WkbNotificationReading>().columnsToString(WkbNotificationReading::getId)));
		}
		pageDTO.setUserId(StpUtil.getLoginIdAsInt());
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void deleteMy(WkbNotificationReadingDeleteDTO deleteDTO) {
		LambdaQueryWrapper<WkbNotificationReading> wrapper = new LambdaQueryWrapper<WkbNotificationReading>()
				.in(WkbNotificationReading::getId, deleteDTO.getIdList())
				.eq(WkbNotificationReading::getUserId, StpUtil.getLoginId())
				;
		this.baseMapper.delete(wrapper);
	}

	@Transactional
	@Override
	public void insertNotRead(Integer notificationId, List<Integer> roleIdList) {
		List<SysUser> userEntityList = this.sysUserService.listByRoleIdList(roleIdList);
		if (CollectionUtil.isEmpty(userEntityList)) {
			return;
		}
		List<WkbNotificationReading> entityList = userEntityList.stream()
			.map(t -> WkbNotificationReading.builder()
					.userId(t.getUserId())
					.read(Boolean.FALSE)
					.notificationId(notificationId)
					.build())
			.collect(Collectors.toList());
		this.saveBatch(entityList);
	}

	@Transactional
	@Override
	public void deleteByNotificatinId(List<Integer> notificationIdList) {
		LambdaQueryWrapper<WkbNotificationReading> wrapper = new LambdaQueryWrapper<WkbNotificationReading>()
				.in(WkbNotificationReading::getNotificationId, notificationIdList)
				;
		this.baseMapper.delete(wrapper);
	}

	// 本人已读
	@Transactional
	@Override
	public void markRead(Integer notificationId) {
		LambdaUpdateWrapper<WkbNotificationReading> wrapper = new LambdaUpdateWrapper<WkbNotificationReading>()
				.set(WkbNotificationReading::getRead, Boolean.TRUE)
				.set(WkbNotificationReading::getReadTime, LocalDateTime.now())
				.eq(WkbNotificationReading::getUserId, StpUtil.getLoginIdAsInt())
				.eq(WkbNotificationReading::getNotificationId, notificationId)
				.eq(WkbNotificationReading::getRead, Boolean.FALSE)
				;
		this.update(wrapper);
	}	
}
