package com.my.crossborder.controller.vo.wkb_notification;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_通知表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知id
     */
    private Integer id;

    /**
     * 类别
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布用户id
     */
    private Integer publishUserId;

    /**
     * 接收人角色ID列表，逗号分隔
     */
    private String receiveRoleIds;

}
