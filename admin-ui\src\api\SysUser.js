import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 修改我的密码
export const sysUserChangeMyPassword = (params) => { return reqPut("/sys-user/change-my-password", params) };

// 修改我的资料
export const sysUserChangeMyProfile = (params) => { return reqPut("/sys-user/change-my-profile", params) };

// 重置密码
export const sysUserResetPassword = (params) => { return reqPut("/sys-user/reset-password", params) };

// 重置密码(新接口)
export const sysUserResetPwd = (params) => { return reqPut("/sys-user/reset-pwd", params) };

// 分页
export const sysUserPage = (params) => { return reqGet("/sys-user/page", params) };

// 物理删除
export const sysUserDelete = (params) => { return reqDelete("/sys-user", params) };

// 新增
export const sysUserInsert = (params) => { return reqPost("/sys-user", params) };

// 修改
export const sysUserUpdate = (params) => { return reqPut("/sys-user", params) };

// 修改资料
export const sysUserChangeProfile = (params) => { return reqPut("/sys-user/change-profile", params) };

// 停用、启用
export const sysUserChangeEnable = (params) => { return reqPut("/sys-user/change-enable", params) };

// 查看我的资料
export const sysUserProfile = (params) => { return reqGet("/sys-user/profile", params) };

// 批量查询用户列表
export const sysUserList = (params) => { return reqGet("/sys-user/list", params) };

/**
 * 用户管理API
 */

// 分页查询用户列表
export function pageSysUser(params) {
  return reqGet('/sys-user/page', params)
}

// 获取所有启用的用户
export function getAllEnabledUsers() {
  return reqGet('/sys-user/page', {
    disable: false,
    size: 1000,  // 获取所有用户，使用size而不是pageSize
    current: 1
  })
}

// 获取所有客服人员（角色ID为21）
export function getAllStaffUsers() {
  return reqGet('/sys-user/page', {
    disable: false,
    roleId: 21,  // 客服角色
    size: 9999,  // 设置足够大的分页大小，使用size而不是pageSize
    current: 1
  })
}

// 获取所有客服主管（角色ID为22）
export function getAllSupervisorUsers() {
  return reqGet('/sys-user/page', {
    disable: false,
    roleId: 22,  // 客服主管角色
    size: 9999,  // 设置足够大的分页大小，使用size而不是pageSize
    current: 1
  })
}

// 根据ID查询用户详情
export function getSysUserById(id) {
  return reqGet(`/sys-user/${id}`)
}

// 根据ID列表查询用户
export function getSysUserList(idList) {
  return reqGet('/sys-user/list', {
    idList: idList
  })
}

// 新增用户
export function insertSysUser(data) {
  return reqPost('/sys-user', data)
}

// 修改用户
export function updateSysUser(data) {
  return reqPut('/sys-user', data)
}

// 删除用户
export function deleteSysUser(data) {
  return reqDelete('/sys-user', data)
}

// 重置密码
export function resetPassword(data) {
  return reqPut('/sys-user/reset-pwd', data)
}

// 获取排班可用的员工（排除客服主管角色22）
export function getAvailableStaffForSchedule() {
  return reqGet('/sys-user/page', {
    disable: false,
    excludeRoleId: 22,  // 排除客服主管角色
    size: 1000,  // 使用size而不是pageSize
    current: 1
  })
}


