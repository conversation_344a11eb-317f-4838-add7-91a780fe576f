package com.my.crossborder.controller.dto.erp_order_item_express;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_订单项_快递信息
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ErpOrderItemExpressUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键(order_item_id + express_no)
     */
    private String id;

    /**
     * 快递单号
     */
	@NotNull(message="expressNo不能为空")
    private String expressNo;

    /**
     * 快递入仓状态 (400:已入仓)
     */
	@NotNull(message="expressinFlag不能为空")
    private String expressinFlag;

    /**
     * 入仓时间
     */
	@NotNull(message="putInTime不能为空")
    private LocalDateTime putInTime;

    /**
     * 数据创建时间
     */
	@NotNull(message="putCreateTime不能为空")
    private LocalDateTime putCreateTime;

    /**
     * 订单项id
     */
	@NotNull(message="orderItemId不能为空")
    private String orderItemId;
    
    /**
     * 订单id
     */
    private String orderId;

}
