package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.SysErpClientLog;
import com.my.crossborder.mybatis.mapper.SysErpClientLogMapper;
import com.my.crossborder.service.SysErpClientLogService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogInsertDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogPageDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogUpdateDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogDeleteDTO;
import com.my.crossborder.controller.vo.sys_erp_client_log.SysErpClientLogDetailVO;
import com.my.crossborder.controller.vo.sys_erp_client_log.SysErpClientLogPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * erp接口日志表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class SysErpClientLogServiceImpl extends ServiceImpl<SysErpClientLogMapper, SysErpClientLog> implements SysErpClientLogService {


	@Transactional
	@Override
	public void insert(SysErpClientLogInsertDTO insertDTO) {
		SysErpClientLog entity = BeanUtil.copyProperties(insertDTO, SysErpClientLog.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(SysErpClientLogUpdateDTO updateDTO) {
		SysErpClientLog entity = BeanUtil.copyProperties(updateDTO, SysErpClientLog.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public SysErpClientLogDetailVO detail(Long id) {
		SysErpClientLog entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SysErpClientLogDetailVO.class);
	}

	@Override
	public Page<SysErpClientLogPageVO> page(SysErpClientLogPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysErpClientLogDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Override
	public void deleteDaysAgo(Integer days) {
		// 计算days天前的时间点
		LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);

		// 使用条件构造器删除指定时间点之前的日志
		LambdaQueryWrapper<SysErpClientLog> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.lt(SysErpClientLog::getCreateTime, cutoffTime);
		
		this.baseMapper.delete(queryWrapper);
	}	
}
