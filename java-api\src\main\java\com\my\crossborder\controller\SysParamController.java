package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.my.crossborder.controller.dto.sys_param.SysParamUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_param.SysParamCheckVO;
import com.my.crossborder.controller.vo.sys_param.SysParamVO;
import com.my.crossborder.service.SysParamService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 系统参数
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/api/sys-param")
@RequiredArgsConstructor
public class SysParamController {

    private final SysParamService sysParamService;

    
	/**
	 * 修改
	 */
    @SaCheckPermission("sys-param:update")
	@PutMapping
	public StdResp<?> update(@Valid @RequestBody SysParamUpdateDTO updateDTO) {
		this.sysParamService.update(updateDTO);
		return StdResp.success();
	}

	/**
	 * 根据主键查询
	 * 
	 * @param id 主键
	 */
	@GetMapping("detail")
	public StdResp<SysParamVO> detail() {
		return StdResp.success(this.sysParamService.get());
	}

	/**
	 * 检查参数
	 */
	@GetMapping("/check")
	public StdResp<SysParamCheckVO> check() {
		SysParamVO sysParamVO = this.sysParamService.get();
		return StdResp.success(sysParamVO.check());
	}
}