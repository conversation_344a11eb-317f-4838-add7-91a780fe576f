package com.my.crossborder.controller.vo.ord_after_sale;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_售后表
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdAfterSaleDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 问题类别
     */
    private String issueType;

    /**
     * 处理办法 字典参数aftersale_close_way
     */
    private String closeWay;

    /**
     * 是否已处理
     */
    private String closeStatus;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 新订单编号
     */
    private String newOrderSn;

    /**
     * 问题录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理完成时间
     */
    private LocalDateTime closeTime;

    /**
     * 录入人id
     */
    private Integer createUserId;

    /**
     * 处理人id
     */
    private Integer closeUserId;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

}
