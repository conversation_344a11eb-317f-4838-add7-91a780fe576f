package com.my.crossborder.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 集中采购订单
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ord_purchase_centralized_order")
public class OrdPurchaseCentralizedOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批量采购id
     */
    @TableId(value = "purchase_id", type = IdType.INPUT)
    private Integer purchaseId;

    /**
     * 订单号
     */
    private String orderSn;


}
