package com.my.crossborder.controller;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentDeleteDTO;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentInsertDTO;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentPageDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_attachment.SysAttachmentDetailVO;
import com.my.crossborder.controller.vo.sys_attachment.SysAttachmentPageVO;
import com.my.crossborder.controller.vo.sys_param.SysParamVO;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.mybatis.entity.SysAttachment;
import com.my.crossborder.service.SysAttachmentService;
import com.my.crossborder.service.SysParamService;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 附件表 
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@RestController
@RequestMapping("/api/sys-attachment")
@RequiredArgsConstructor
@Slf4j
public class SysAttachmentController {

    private final SysAttachmentService sysAttachmentService;
    private final SysParamService sysParamService;
    
    
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<SysAttachmentDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.sysAttachmentService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysAttachmentPageVO>> page(SysAttachmentPageDTO pageDTO) {
        Page<SysAttachmentPageVO> page = this.sysAttachmentService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysAttachmentDeleteDTO deleteDTO) {
    	this.sysAttachmentService.delete(deleteDTO);
		return StdResp.success();
    }
    
    /**
     * 文件上传接口
     * 将文件保存到系统参数配置的上传路径中，路径为:/文件类别/日期+时间戳+原文件后缀
     */
    @PostMapping("/upload")
    public StdResp<Integer> upload(@RequestParam("file") MultipartFile file) {
        BusinessException.when(file.isEmpty(), "上传的文件不能为空");
        try {
            // 获取系统参数
            SysParamVO sysParam = this.sysParamService.get();
            String baseUploadPath = sysParam.getFileUploadPath();
            String fileAllowExt = sysParam.getFileAllowExt();
            
            BusinessException.when(StringUtils.isBlank(baseUploadPath), "系统未配置文件上传路径，请先配置");
            BusinessException.when(StringUtils.isBlank(fileAllowExt), "系统未配置允许的文件扩展名，请先配置");
            
            // 获取文件原始名称和扩展名
            String fileOriginalName = file.getOriginalFilename();
            String fileExtension = "";
            if (fileOriginalName != null && fileOriginalName.contains(".")) {
                fileExtension = fileOriginalName.substring(fileOriginalName.lastIndexOf(".") + 1).toLowerCase();
            }
            
            // 检查文件扩展名是否在禁止列表中
            String[] allowedExts = fileAllowExt.toLowerCase().split(",");
            BusinessException.when(!ArrayUtils.contains(allowedExts, "." + fileExtension), "不允许上传{}格式的文件", fileExtension);
            
            // 生成日期文件夹 (yyyy-MM-dd格式)
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String dateFolder = dateFormat.format(new Date());
            
            // 生成新的文件名（时间戳.原始文件后缀）
            String newFileName = System.currentTimeMillis() + "." + fileExtension;
            
            // 构建文件存储路径：/原来的后缀/yyyy-MM-dd/时间戳.原来的后缀
            String folderPath = baseUploadPath + File.separator + fileExtension + File.separator + dateFolder;
            String fullFilePath = folderPath + File.separator + newFileName;
            
            // 确保目录存在
            File directory = new File(folderPath);
            if (!directory.exists()) {
                directory.mkdirs();
            }
            
            // 写文件
            Path targetLocation = Paths.get(fullFilePath);
            Files.copy(file.getInputStream(), targetLocation);
            
            // 创建附件记录
            SysAttachmentInsertDTO insertDTO = SysAttachmentInsertDTO.builder()
                .fileName(newFileName)
                .path(fileExtension + File.separator + dateFolder + File.separator + newFileName)
                .tableName(null)
                .dataSourceId("upload")
                .fileOriginalName(fileOriginalName)
                .createTime(LocalDateTime.now())
                .build();
            
            // 调用service新增记录并返回主键
            Integer attachmentId = this.sysAttachmentService.insertAndReturnId(insertDTO);
            
            // 日志文件信息
            Map<String, String> fileInfo = new HashMap<>();
            fileInfo.put("attachmentId", String.valueOf(attachmentId));
            fileInfo.put("fileName", newFileName);
            fileInfo.put("fileOriginalName", fileOriginalName);
            fileInfo.put("fileSize", String.valueOf(file.getSize()));
            fileInfo.put("filePath", insertDTO.getPath());
            log.info("文件上传成功: {}", fileInfo);
            
            // 返回新增的主键
            return StdResp.success(attachmentId);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 文件下载接口
     * 根据文件名从系统配置的上传路径中下载文件
     */
    @SaIgnore
    @GetMapping("/download/{attachmentId}")
    public void download(@PathVariable("attachmentId") Integer attachmentId, HttpServletResponse response) {
        try {
            SysAttachment entity = this.sysAttachmentService.getById(attachmentId);
            BusinessException.when(entity == null, "附件id有误，未找到文件");
            
            // 文件完整路径
            String uploadPath = this.getUploadPath();
			String fullPath = uploadPath + File.separator + entity.getPath();
            File file = new File(fullPath);
            if (!file.exists()) {
            	log.error("服务器磁盘上未找到此文件: {}", fullPath);
            }
            BusinessException.when(!file.exists(), "服务器磁盘上未找到此文件");
            
            // 设置响应头信息
            String fileName = entity.getFileName();
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.setContentLengthLong(file.length());
            
            // 将文件内容写入响应流
            try (InputStream inputStream = new FileInputStream(file);
                 OutputStream outputStream = response.getOutputStream()) {
                
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                
                outputStream.flush();
            }
//            log.info("文件下载成功: {}", fileName);
        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw new BusinessException("文件下载失败: " + e.getMessage());
        }
    }

    /**
     * 读取系统配置:文件上传路径
     * @return
     */
	private String getUploadPath() {
		SysParamVO sysParam = this.sysParamService.get();
		String uploadPath = sysParam.getFileUploadPath();
		BusinessException.when(StringUtils.isBlank(uploadPath), "系统未配置文件上传路径，请先配置");
		
		// 确保生成该目录
        File directory = new File(uploadPath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
		return uploadPath;
	}
    
    /**
     * 获取文件预览接口
     * 根据文件名从系统配置的上传路径中读取文件并直接显示
     */
    @SaIgnore
    @GetMapping("/view/{attachmentId}")
    public void view(@PathVariable("attachmentId") Integer attachmentId, HttpServletResponse response) {    	
        try {
            SysAttachment entity = this.sysAttachmentService.getById(attachmentId);
            BusinessException.when(entity == null, "附件id有误，未找到文件");
            
            // 文件完整路径
            String uploadPath = this.getUploadPath();
			String fullPath = uploadPath + File.separator + entity.getPath();
            File file = new File(fullPath);
            log.error("服务器磁盘上未找到此文件: {}", fullPath);
            BusinessException.when(!file.exists(), "服务器磁盘上未找到此文件");
            
            String fileName = entity.getFileName();
            // 设置响应头信息（根据文件类型设置合适的Content-Type）
            String contentType = determineContentType(fileName);
            response.setContentType(contentType);
            
            // 将文件内容写入响应流
            try (InputStream inputStream = new FileInputStream(file);
                 OutputStream outputStream = response.getOutputStream()) {
                
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                
                outputStream.flush();
            }
            
            log.info("文件预览成功: {}", fileName);
        } catch (IOException e) {
            log.error("文件预览失败", e);
            throw new BusinessException("文件预览失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据文件名确定内容类型
     */
    private String determineContentType(String fileName) {
        if (fileName == null) {
            return MediaType.APPLICATION_OCTET_STREAM_VALUE;
        }
        
        String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        
        switch (fileExtension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "pdf":
                return "application/pdf";
            case "txt":
                return "text/plain";
            case "html":
                return "text/html";
            case "mp4":
                return "video/mp4";
            case "mp3":
                return "audio/mpeg";
            default:
                return MediaType.APPLICATION_OCTET_STREAM_VALUE;
        }
    }
}