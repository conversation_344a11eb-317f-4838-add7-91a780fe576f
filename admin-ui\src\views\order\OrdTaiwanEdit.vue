<template>
  <el-dialog :title="dialogTitle" top="8vh" :visible.sync="dialogVisible" width="650px" @close="handleDialogClose">
    <el-form :model="itemForm" :rules="itemRules" ref="itemForm" label-width="140px">
      <el-form-item label="选择订单商品" prop="selectedOrderItemId">
        <OrderItemSelector v-model="itemForm.selectedOrderItemId" placeholder="请选择订单商品"
          :preset-item-info="currentPresetItemInfo" after-sale-filter="taiwan" @select="handleOrderItemSelect" />
      </el-form-item>

      <el-form-item label="原订单店铺名称" prop="originalShopName">
        <el-input v-model="itemForm.originalShopName" placeholder="选择订单商品后自动填充" readonly></el-input>
      </el-form-item>

      <el-form-item label="原订单店铺号" prop="originalShopId">
        <el-input v-model="itemForm.originalShopId" placeholder="选择订单商品后自动填充" readonly></el-input>
      </el-form-item>

      <el-form-item label="原订单号" prop="originalOrderNumber">
        <el-input v-model="itemForm.originalOrderNumber" placeholder="选择订单商品后自动填充" readonly></el-input>
      </el-form-item>

      <hr />

      <el-form-item label="商品数量" prop="amount">
        <el-input-number v-model="itemForm.amount" :min="1" :step="1" controls-position="right"></el-input-number>
      </el-form-item>

      <el-form-item label="退货单号" prop="waybillNumber">
        <el-input v-model="itemForm.waybillNumber" placeholder="请输入退货单号" maxlength="16" style="width: 215px;"></el-input>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <dict-select v-model="itemForm.status" placeholder="请选择状态" clearable category-id="TAIWAN_GOODS_STATUS">
        </dict-select>
      </el-form-item>

      <el-form-item label="台湾仓柜号" prop="warehouseNumber">
        <el-input v-model="itemForm.warehouseNumber" placeholder="请输入台湾仓柜号" maxlength="16" style="width: 215px;"></el-input>
      </el-form-item>

      <el-form-item label="上架时间" prop="onlineTime">
        <el-date-picker v-model="itemForm.onlineTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="选择上架时间">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="下架时间" prop="offlineTime">
        <el-date-picker v-model="itemForm.offlineTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="选择下架时间">
        </el-date-picker>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import OrderItemSelector from '../../components/OrderItemSelector'
import { insertOrdTaiwan, updateOrdTaiwan } from '@/api/OrdTaiwan'
import DictSelect from '../../components/DictSelect'

export default {
  name: 'OrdTaiwanEdit',
  components: {
    OrderItemSelector,
    DictSelect
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 自定义校验函数 - 上架状态相关字段验证
    const validateShelfFields = (rule, value, callback) => {
      if (this.itemForm.status === '2' && !value) {
        callback(new Error('已上架状态下此字段为必填项'))
      } else {
        callback()
      }
    }

    return {
      itemForm: {
        orderItemId: '', // 订单项ID（主键）
        selectedOrderItemId: '', // 选中的订单项ID（用于前端选择）
        originalShopName: '',
        originalShopId: '',
        originalOrderNumber: '',
        amount: 1, // 数量
        waybillNumber: '', // 退货单号
        status: '1', // 状态字典：TAIWAN_GOODS_STATUS
        warehouseNumber: '', // 台湾仓柜号
        onlineTime: '', // 上架时间
        offlineTime: '' // 下架时间
      },
      itemRules: {
        selectedOrderItemId: [
          { required: true, message: '请选择订单商品', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入商品数量', trigger: 'blur' }
        ],
        waybillNumber: [
          { required: true, message: '请输入退货单号', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        warehouseNumber: [
          { validator: validateShelfFields, trigger: 'blur' }
        ],
        onlineTime: [
          { validator: validateShelfFields, trigger: 'blur' }
        ]
      },
      // 当前预设的商品信息（用于编辑时传递给OrderItemSelector）
      currentPresetItemInfo: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return this.isEdit ? '编辑台湾上架物品' : '添加台湾上架物品'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    },
    editData: {
      handler(newData) {
        if (this.visible && newData) {
          this.initForm()
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.editData) {
        // 编辑模式
        this.itemForm = { ...this.editData }
        this.itemForm.selectedOrderItemId = this.editData.orderItemId || ''

        // 设置预设的商品信息，让OrderItemSelector直接显示
        this.currentPresetItemInfo = {
          productName: this.editData.productName,
          productSpec: this.editData.productSpec,
          originalOrderNumber: this.editData.originalOrderNumber
        }
      } else {
        // 添加模式
        this.itemForm = {
          orderItemId: '',
          selectedOrderItemId: '',
          originalShopName: '',
          originalShopId: '',
          originalOrderNumber: '',
          amount: 1,
          waybillNumber: '',
          status: '1',
          warehouseNumber: '',
          onlineTime: '',
          offlineTime: ''
        }
        this.currentPresetItemInfo = null
      }
    },

    // 选择订单项后的处理
    handleOrderItemSelect(data) {
      const { orderItem, order } = data

      // 设置订单项ID（这是主键）
      this.itemForm.orderItemId = orderItem.id
      this.itemForm.selectedOrderItemId = orderItem.id

      // 自动填充前三项信息
      this.itemForm.originalShopName = order.shopName
      this.itemForm.originalShopId = order.shopId
      this.itemForm.originalOrderNumber = order.orderSn

      // 设置商品数量（如果订单项有数量信息）
      if (orderItem.amount) {
        this.itemForm.amount = orderItem.amount
      }

      console.log('选择的订单项:', orderItem)
      console.log('所属订单:', order)
    },

    // 提交表单
    handleSubmit() {
      this.$refs.itemForm.validate((valid) => {
        if (valid) {
          // 验证上架相关字段
          if (this.itemForm.status === '2') {
            if (!this.itemForm.warehouseNumber) {
              this.$message.warning('已上架状态下必须填写台湾仓柜号')
              return false
            }
            if (!this.itemForm.onlineTime) {
              this.$message.warning('已上架状态下必须填写上架时间')
              return false
            }
          }

          // 准备提交的数据
          const submitData = {
            orderItemId: String(this.itemForm.selectedOrderItemId || this.itemForm.orderItemId),
            amount: parseInt(this.itemForm.amount) || 1,
            status: String(this.itemForm.status),
            onlineTime: this.itemForm.onlineTime,
            offlineTime: this.itemForm.offlineTime
          }

          // // 处理日期字段 - 转换为 yyyy-MM-dd 格式
          // if (this.itemForm.onlineTime) {
          //   submitData.onlineTime = this.formatDateTime(this.itemForm.onlineTime)
          // }

          // if (this.itemForm.offlineTime) {
          //   submitData.offlineTime = this.formatDateTime(this.itemForm.offlineTime)
          // }

          // 添加可选字段
          if (this.itemForm.waybillNumber && this.itemForm.waybillNumber.trim()) {
            submitData.waybillNumber = String(this.itemForm.waybillNumber.trim())
          }

          if (this.itemForm.warehouseNumber && this.itemForm.warehouseNumber.trim()) {
            submitData.warehouseNumber = String(this.itemForm.warehouseNumber.trim())
          }

          console.log('提交的数据:', submitData)

          // 调用后端API
          const apiCall = this.isEdit ? updateOrdTaiwan(submitData) : insertOrdTaiwan(submitData)

          apiCall
            .then(response => {
              if (response.success) {
                this.$message.success('保存成功')
                this.$emit('submit', submitData)
                this.dialogVisible = false
              } else {
                this.$message.error('保存失败：' + response.message)
              }
            })
            .catch(error => {
              console.error('保存失败:', error)
              this.$message.error('保存失败，请稍后重试')
            })
        } else {
          this.$message.warning('请完善表单信息')
          return false
        }
      })
    },

    // 格式化日期
    formatDateTime(dateTime) {
      const date = new Date(dateTime)
      return date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0')
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    },

    // 对话框关闭处理
    handleDialogClose() {
      if (this.$refs.itemForm) {
        this.$refs.itemForm.resetFields()
      }
    }
  }
}
</script>

<style scoped></style>
