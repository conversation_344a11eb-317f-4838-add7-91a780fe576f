package com.my.crossborder.controller.dto.sys_exchange_rate_day_range;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_汇率日期区间
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysExchangeRateDayRangeInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 开始日期
     */
	@NotNull(message="startDay不能为空")
    private LocalDate startDay;

    /**
     * 结束日期
     */
	@NotNull(message="endDay不能为空")
    private LocalDate endDay;

    /**
     * 汇率（CNY/TWD的值，比如：4.0814）
     */
	@NotNull(message="exchangeRate不能为空")
    private BigDecimal exchangeRate;

}
