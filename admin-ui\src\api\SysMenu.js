import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 后台管理获取菜单树结构
export const sysMenuTreeNodes = () => { return reqGet("/sys-menu/tree-nodes") };

// 新增菜单
export const sysMenuInsert = (params) => { return reqPost("/sys-menu", params) };

// 修改菜单
export const sysMenuUpdate = (params) => { return reqPut("/sys-menu", params) };

// 查询菜单详情
export const sysMenuDetail = (id) => { return reqGet(`/sys-menu/${id}`) };

// 菜单分页
export const sysMenuPage = (params) => { return reqGet("/sys-menu/page", params) };

// 删除菜单
export const sysMenuDelete = (params) => { return reqDelete("/sys-menu", params) };

// 角色授权菜单
export const sysMenuGrant = (params) => { return reqPost("/sys-menu/grant", params) };

// 根据角色ID查询菜单ID列表
export const sysMenuListByRoleId = (roleId) => { return reqGet(`/sys-menu/list-by-roleid?roleId=${roleId}`) };

// 获取当前用户的菜单树
export const sysMenuTree = () => { return reqGet("/sys-menu/tree") };

// 获取测试菜单树 - 仅用于调试
export const sysMenuTestTree = () => { return reqGet("/sys-menu/test-tree") };

/**
 * 获取用户权限列表
 */
export const getPermissions = () => {
    return reqGet('/sys-menu/permissions');
};
