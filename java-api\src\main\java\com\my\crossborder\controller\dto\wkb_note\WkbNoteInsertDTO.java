package com.my.crossborder.controller.dto.wkb_note;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_工作台_工作笔记表
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNoteInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单号
     */
	@NotNull(message="orderSn不能为空")
    private String orderSn;

    /**
     * 笔记内容
     */
	@NotNull(message="content不能为空")
    private String content;

    /**
     * 场景
     */
	@NotNull(message="scene不能为空")
    private String scene;




}
