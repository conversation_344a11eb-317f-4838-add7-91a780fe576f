package com.my.crossborder.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.wkb_todo.WkbTodoDeleteDTO;
import com.my.crossborder.controller.dto.wkb_todo.WkbTodoInsertIfAbsenseDTO;
import com.my.crossborder.controller.dto.wkb_todo.WkbTodoPageDTO;
import com.my.crossborder.controller.vo.wkb_todo.WkbTodoDetailVO;
import com.my.crossborder.controller.vo.wkb_todo.WkbTodoPageVO;
import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.mybatis.entity.WkbTodo;
import com.my.crossborder.mybatis.mapper.WkbTodoMapper;
import com.my.crossborder.service.SysUserService;
import com.my.crossborder.service.WkbTodoService;
import com.my.crossborder.util.ColumnLambda;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;

/**
 * 待办表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
@RequiredArgsConstructor
public class WkbTodoServiceImpl extends ServiceImpl<WkbTodoMapper, WkbTodo> implements WkbTodoService {

	private final SysUserService sysUserService;
	

	@Transactional
	@Override
	public WkbTodoDetailVO detail(Integer id) {
		WkbTodoPageDTO pageDTO = new WkbTodoPageDTO(1, 1);
		pageDTO.setId(id);
		WkbTodoPageVO pageVO = this.page(pageDTO).getRecords().get(0);
		WkbTodoDetailVO vo = BeanUtil.copyProperties(pageVO, WkbTodoDetailVO.class);
		SysUser sysUser = this.sysUserService.getById(vo.getPublishUserId());
		vo.setPublishUserName(sysUser.getRealName());
		// 标记已读
		this.markRead(id, StpUtil.getLoginIdAsInt());
		return vo;
	}

	@Override
	public Page<WkbTodoPageVO> page(WkbTodoPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<WkbTodo>().columnsToString(WkbTodo::getId)));
		}
		pageDTO.setReceiveUserId(StpUtil.getLoginIdAsInt());
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(WkbTodoDeleteDTO deleteDTO) {
		List<Integer> idList = deleteDTO.getIdList();
		Wrapper<WkbTodo> wrapper = Wrappers.lambdaQuery(WkbTodo.class)
				.in(WkbTodo::getId, idList)
				.eq(WkbTodo::getReceiveUserId, StpUtil.getLoginIdAsInt());
		this.baseMapper.delete(wrapper);
	}

	@Transactional
	@Override
	public void saveIfAbsense(List<WkbTodoInsertIfAbsenseDTO> dtoList) {
		dtoList.forEach(t -> {
			Wrapper<WkbTodo> wrapper = Wrappers.lambdaUpdate(WkbTodo.class)
					.set(WkbTodo::getRead, Boolean.FALSE)
					.set(WkbTodo::getReadTime, null)
					.eq(WkbTodo::getTitle, t.getTitle())
					.eq(WkbTodo::getReceiveUserId, t.getUserId());
			boolean boolSuccess = this.update(wrapper);
			if (!boolSuccess) {
				WkbTodo entity = BeanUtil.copyProperties(t, WkbTodo.class);
				entity.setPublishUserId(StpUtil.getLoginIdAsInt());
				entity.setPublishTime(LocalDateTime.now());
				entity.setRead(Boolean.FALSE);
				entity.setReceiveUserId(t.getUserId());
				this.save(entity);
			}
		});
	}	
	
	// 本人已读
	private void markRead(Integer todoId, int userId) {
		LambdaUpdateWrapper<WkbTodo> wrapper = new LambdaUpdateWrapper<WkbTodo>()
			.set(WkbTodo::getRead, Boolean.TRUE)
			.set(WkbTodo::getReadTime, LocalDateTime.now())
			.eq(WkbTodo::getReceiveUserId, userId)
			.eq(WkbTodo::getId, todoId)
			.eq(WkbTodo::getRead, Boolean.FALSE)
			;
		this.update(wrapper);
	}
}
