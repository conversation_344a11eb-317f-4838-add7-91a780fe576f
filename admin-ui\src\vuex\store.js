import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);
// 登录验证
export default new Vuex.Store({
    state: {
        user: false,
        userEntity: null,
        // 未读通知缓存
        unreadNotifications: [],
        // 未读待办缓存
        unreadTodos: [],
        // 用户权限列表
        permissions: JSON.parse(localStorage.getItem("userPermissions") || '[]')
    },
    getters: {
        // 未读通知数量
        unreadNotificationCount: state => state.unreadNotifications.length,
        // 未读待办数量
        unreadTodoCount: state => state.unreadTodos.length,
        // 获取权限列表
        permissions: state => state.permissions
    },
    mutations: {
        // 登录
        login(state, user) {
            state.user = user;
            localStorage.setItem("userInfo", user);
        },
        // 退出
        logout(state, user) {
            state.user = "";
            localStorage.setItem("userInfo", "");
            // 清空缓存
            state.unreadNotifications = [];
            state.unreadTodos = [];
            state.permissions = [];
            // 清除localStorage中的权限
            localStorage.removeItem("userPermissions");
        },
        // 设置用户实体
        setUserEntity(state, userEntity) {
            state.userEntity = userEntity;
        },
        // 设置未读通知
        setUnreadNotifications(state, notifications) {
            state.unreadNotifications = notifications || [];
        },
        // 设置未读待办
        setUnreadTodos(state, todos) {
            state.unreadTodos = todos || [];
        },
        // 从缓存中移除通知（通过notificationId）
        removeNotificationFromCache(state, notificationId) {
            state.unreadNotifications = state.unreadNotifications.filter(
                notification => notification.notificationId !== notificationId
            );
        },
        // 从缓存中移除待办（通过todoId）
        removeTodoFromCache(state, todoId) {
            state.unreadTodos = state.unreadTodos.filter(
                todo => todo.id !== todoId
            );
        },
        // 设置权限列表
        SET_PERMISSIONS: (state, permissions) => {
            console.log("设置权限列表", permissions);
            state.permissions = permissions || [];
            // 将权限保存到localStorage以便页面刷新后恢复
            localStorage.setItem("userPermissions", JSON.stringify(permissions || []));
        }
    },
    actions: {
        // 刷新未读数据
        async refreshUnreadData({ commit }) {
            try {
                // 导入API函数
                const { notificationReadingAllUnread } = await import('../api/WkbNotificationReading');
                const { todoAllUnread } = await import('../api/WkbTodo');

                // 并行获取未读通知和待办
                const [notificationRes, todoRes] = await Promise.all([
                    notificationReadingAllUnread(),
                    todoAllUnread()
                ]);

                if (notificationRes.success) {
                    commit('setUnreadNotifications', notificationRes.data);
                }

                if (todoRes.success) {
                    commit('setUnreadTodos', todoRes.data);
                }
            } catch (error) {
                console.error('刷新未读数据失败:', error);
            }
        },
        // 获取用户权限列表
        getPermissions({ commit }) {
            console.log("开始获取权限列表");
            return new Promise((resolve, reject) => {
                // 这里需要导入权限API
                import('../api/SysMenu').then(({ getPermissions }) => {
                    getPermissions().then(response => {
                        if (response.success) {
                            console.log("获取权限列表成功", response.data);
                            commit('SET_PERMISSIONS', response.data);
                            resolve(response.data);
                        } else {
                            console.error("获取权限列表失败", response.message);
                            reject(response.message || '获取权限失败');
                        }
                    }).catch(error => {
                        console.error("获取权限API调用出错", error);
                        reject(error);
                    });
                });
            });
        }
    }
})
