package com.my.crossborder.mybatis.mapper;

import java.time.LocalDate;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.my.crossborder.controller.vo.stl_purchase.DailyPurchaseDetail.Purchase;
import com.my.crossborder.controller.vo.stl_purchase.MonthlyPurchaseDataVO;
import com.my.crossborder.mybatis.entity.StlPurchase;

/**
 * 结算_采购结算表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface StlPurchaseMapper extends BaseMapper<StlPurchase> {


	/**
	 * 获取用户月度采购结算数据
	 * @param userId 用户ID
	 * @param year 年份
	 * @param month 月份
	 * @return 月度采购数据列表
	 */
	List<MonthlyPurchaseDataVO> monthlyPurchaseData(@Param("userId") Integer userId,
	                                                @Param("year") Integer year,
	                                                @Param("month") Integer month);
	
	/**
	 * 单笔采购详情
	 * @param userId 用户ID
	 * @param purchaseDate 采购日期
	 * @return 采购详情列表
	 */
    List<Purchase> dailyPurchaseDetails(@Param("userId") Integer userId,
                                   		@Param("purchaseDate") LocalDate purchaseDate);


}
