import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 退款结算API
 */

// 分页查询退款结算列表
export function pageStlRefund(params) {
  return reqGet('/stl-refund/page', params)
}

// 根据ID查询退款结算详情
export function getStlRefundById(id) {
  return reqGet(`/stl-refund/${id}`)
}

// 新增退款结算
export function insertStlRefund(data) {
  return reqPost('/stl-refund', data)
}

// 修改退款结算
export function updateStlRefund(data) {
  return reqPut('/stl-refund', data)
}

// 删除退款结算
export function deleteStlRefund(data) {
  return reqDelete('/stl-refund', data)
}

/**
 * 根据周期查询退款结算详情
 * @param {Object} params - 查询参数
 * @param {number} params.refundUserId - 用户ID
 * @param {number} params.refundYear - 年份
 * @param {number} params.refundMonth - 月份
 * @param {number} params.refundWeek - 周次
 */
export function getStlRefundByWeek(params) {
  return reqGet('/stl-refund/by-week', params)
}
