<template>
  <div class="user-selector">
    <el-input v-model="displayText" placeholder="请选择人员" readonly @focus="openUserDialog" :disabled="disabled">
      <el-button slot="append" icon="el-icon-search" @click="openUserDialog" :disabled="disabled"></el-button>
    </el-input>

    <!-- 用户选择对话框 -->
    <el-dialog title="选择人员" :visible="showUserSelectDialog" width="850px" append-to-body @close="closeUserDialog">
      <div>
        <!-- 用户搜索区域 -->
        <el-form :inline="true" :model="userSearchForm">
          <el-form-item label="姓名">
            <el-input v-model="userSearchForm.realName" style="width:160px" placeholder="请输入姓名" clearable></el-input>
          </el-form-item>
          <!-- <el-form-item label="工号">
            <el-input v-model="userSearchForm.workNumber" style="width:160px" placeholder="请输入工号" clearable></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="岗位">
            <el-input v-model="userSearchForm.position" placeholder="请输入岗位" clearable></el-input>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="searchUsers">查询</el-button>
            <el-button @click="resetUserSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 用户表格 -->
        <el-table ref="userTable" v-loading="userLoading" :data="userTableData" style="width: 100%" height="350px"
          border @selection-change="handleSelectionChange" @row-click="handleRowClick">
          <el-table-column v-if="multiple" type="selection" width="55"></el-table-column>
          <el-table-column v-else type="index" label="序号" width="55"></el-table-column>
          <el-table-column prop="username" label="用户名" width="150"></el-table-column>
          <el-table-column prop="realName" label="姓名" width="210"></el-table-column>
          <dict-table-column prop="roleId" label="角色" category-id="ROLE_ID" width="180" align="center"></dict-table-column>
          <dict-table-column prop="status" label="人员状态" category-id="USER_STATUS" min-width="100" align="center"></dict-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination @size-change="handleUserSizeChange" @current-change="handleUserCurrentChange"
            :current-page="userSearchForm.current" :page-sizes="[10, 20, 50, 100]" :page-size="userSearchForm.size"
            layout="total, sizes, prev, pager, next, jumper" :total="userTotal">
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="multiple" type="primary" @click="confirmSelection">确定选择</el-button>
        <el-button @click="closeUserDialog">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { sysUserPage } from '../api/SysUser'

export default {
  name: 'UserSelector',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      selectedUsers: this.value || [],
      showUserSelectDialog: false,
      userSearchForm: {
        realName: '',
        position: '',
        workNumber: '',
        current: 1,
        size: 10
      },
      userTableData: [],
      userTotal: 0,
      userLoading: false,
      selectedTableUsers: [] // 当前表格中选中的用户
    }
  },
  computed: {
    displayText() {
      if (this.selectedUsers && this.selectedUsers.length > 0) {
        return this.selectedUsers.map(user => user.realName).join(', ');
      }
      return '';
    }
  },
  watch: {
    value(newVal) {
      this.selectedUsers = newVal || [];
    },
    selectedUsers: {
      deep: true,
      handler(newVal) {
        this.$emit('input', newVal);
      }
    }
  },
  methods: {
    // 搜索用户
    searchUsers() {
      this.userSearchForm.current = 1;
      this.loadUserData();
    },

    // 重置用户搜索
    resetUserSearch() {
      this.userSearchForm = {
        realName: '',
        position: '',
        workNumber: '',
        current: 1,
        size: 10
      };
      this.loadUserData();
    },

    // 加载用户数据
    loadUserData() {
      this.userLoading = true;
      sysUserPage(this.userSearchForm)
        .then(res => {
          this.userLoading = false;
          if (res.success) {
            this.userTableData = res.data.records || [];
            this.userTotal = res.data.total || 0;
            // 设置已选中的行
            this.$nextTick(() => {
              this.setSelectedRows();
            });
          } else {
            this.$message.error(res.message || '加载用户数据失败');
          }
        })
        .catch(error => {
          this.userLoading = false;
          this.$message.error('加载用户数据失败: ' + error.message);
        });
    },

    // 设置已选中的行
    setSelectedRows() {
      if (this.multiple && this.$refs.userTable && this.selectedUsers.length > 0) {
        this.userTableData.forEach(row => {
          const isSelected = this.selectedUsers.some(selected => selected.userId === row.userId);
          if (isSelected) {
            this.$refs.userTable.toggleRowSelection(row, true);
          }
        });
      }
    },

    // 用户分页大小变化
    handleUserSizeChange(val) {
      this.userSearchForm.size = val;
      this.loadUserData();
    },

    // 用户分页页码变化
    handleUserCurrentChange(val) {
      this.userSearchForm.current = val;
      this.loadUserData();
    },

    // 打开用户选择对话框
    openUserDialog() {
      if (this.disabled) return;
      this.showUserSelectDialog = true;
      this.selectedTableUsers = [...this.selectedUsers]; // 复制当前选中的用户
      // 打开对话框时加载数据
      this.loadUserData();
    },

    // 表格选择变化（多选模式）
    handleSelectionChange(selection) {
      if (this.multiple) {
        this.selectedTableUsers = selection;
      }
    },

    // 行点击事件（单选模式）
    handleRowClick(row) {
      if (!this.multiple) {
        // 单选模式：直接选择并关闭对话框
        this.selectedUsers = [row];
        this.closeUserDialog();
        // 触发事件，通知父组件选择了用户
        this.$emit('select', this.selectedUsers);
      } else {
        // 多选模式：切换复选框状态
        this.$refs.userTable.toggleRowSelection(row);
      }
    },

    // 确认选择
    confirmSelection() {
      this.selectedUsers = [...this.selectedTableUsers];
      this.closeUserDialog();
      // 触发事件，通知父组件选择了用户
      this.$emit('select', this.selectedUsers);
    },

    // 关闭用户对话框
    closeUserDialog() {
      this.showUserSelectDialog = false;
      this.selectedTableUsers = [];
    }
  }
}
</script>

<style scoped>
.user-selector {
  width: 100%;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  /* justify-content: flex-end; */
}
</style>
