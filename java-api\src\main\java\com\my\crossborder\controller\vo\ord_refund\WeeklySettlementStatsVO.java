package com.my.crossborder.controller.vo.ord_refund;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 员工周结算统计结果VO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WeeklySettlementStatsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 周序号（当月第几周）
     */
    private Integer weekNumber;

    /**
     * 周开始日期
     */
    private LocalDate startDate;

    /**
     * 周结束日期
     */
    private LocalDate endDate;

    /**
     * 日期范围描述（如：6月30日 - 7月6日）
     */
    private String dateRange;

    /**
     * 整体结算状态
     * -1: 无需结算
     * 0: 待结算
     * 1: 已结算
     */
    private String settlementStatus;

    /**
     * 是否为当前周
     */
    private Boolean isCurrentWeek;

    /**
     * 总退款金额
     */
    private BigDecimal totalAmount;

    /**
     * 已结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 未结算金额
     */
    private BigDecimal unsettledAmount;

    /**
     * 退款记录总数
     */
    private Integer totalCount;

    /**
     * 已结算记录数
     */
    private Integer settledCount;

    /**
     * 未结算记录数
     */
    private Integer unsettledCount;

}
