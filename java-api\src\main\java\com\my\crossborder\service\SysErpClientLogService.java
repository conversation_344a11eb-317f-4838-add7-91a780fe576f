package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogDeleteDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogInsertDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogPageDTO;
import com.my.crossborder.controller.dto.sys_erp_client_log.SysErpClientLogUpdateDTO;
import com.my.crossborder.controller.vo.sys_erp_client_log.SysErpClientLogDetailVO;
import com.my.crossborder.controller.vo.sys_erp_client_log.SysErpClientLogPageVO;
import com.my.crossborder.mybatis.entity.SysErpClientLog;

/**
 * erp接口日志表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface SysErpClientLogService extends IService<SysErpClientLog> {

	/**
	 * 新增
	 */
	void insert(SysErpClientLogInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysErpClientLogUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SysErpClientLogDetailVO detail(Long id);

	/**
	 * 分页
	 */
	Page<SysErpClientLogPageVO> page(SysErpClientLogPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysErpClientLogDeleteDTO deleteDTO);

	/**
	 * 删除N天前的日志
	 * @param days
	 */
	void deleteDaysAgo(Integer days);	

}
