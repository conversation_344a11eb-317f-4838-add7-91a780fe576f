import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 重新采购分页查询
 * @param {Object} params 查询参数
 */
export function getOrdRepurchasePage(params) {
  return reqGet('/ord-repurchase/page', params)
}

/**
 * 新增重新采购
 * @param {Object} data 重新采购数据
 */
export function insertOrdRepurchase(data) {
  return reqPost('/ord-repurchase', data)
}

/**
 * 修改重新采购
 * @param {Object} data 重新采购数据
 */
export function updateOrdRepurchase(data) {
  return reqPut('/ord-repurchase', data)
}

/**
 * 删除重新采购
 * @param {Object} data 删除参数
 */
export function deleteOrdRepurchase(data) {
  return reqDelete('/ord-repurchase', data)
}

/**
 * 根据ID查询重新采购详情
 * @param {String} id 重新采购ID
 */
export function getOrdRepurchaseDetail(id) {
  return reqGet(`/ord-repurchase/${id}`)
}

/**
 * 确认完成重新采购
 * @param {Number} id 重新采购ID
 */
export function confirmCompleteOrdRepurchase(id) {
  return reqPost(`/ord-repurchase/confirm/${id}`)
}
