package com.my.crossborder.controller.dto.wkb_notification;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_通知表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 类别
     */
	@NotNull(message="type不能为空")
    private String type;

    /**
     * 标题
     */
	@NotNull(message="title不能为空")
    private String title;

    /**
     * 内容
     */
	@NotNull(message="content不能为空")
    private String content;

	/**
	 * 通知接收人
	 */
	@NotEmpty(message="通知接收人不能为空")
	private List<Integer> roleIdList;

}
