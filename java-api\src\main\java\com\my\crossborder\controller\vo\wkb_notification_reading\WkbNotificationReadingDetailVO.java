package com.my.crossborder.controller.vo.wkb_notification_reading;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_通知阅读表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationReadingDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 通知id
     */
    private Integer notificationId;

    /**
     * 公告的接收用户id
     */
    private Integer userId;

    /**
     * 是否已读
     */
    private Boolean read;

    /**
     * 已读时间
     */
    private LocalDateTime readTime;

}
