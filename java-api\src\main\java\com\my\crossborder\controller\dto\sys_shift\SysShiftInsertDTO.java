package com.my.crossborder.controller.dto.sys_shift;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_排班表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 排班日期
     */
    private LocalDate shiftDay;
    
    /**
     * 排班列表
     */
    @NotEmpty
    private List<ShiftObj> shiftList;
    
    
    @Data
    public static class ShiftObj {
    	
        /**
         * 店铺ID
         */
        private Integer shopId;

        /**
         * 客服ID
         */
        private Integer serviceUserId;

        /**
         * 主管ID
         */
        private Integer supervisorUserId;
    }
    

    
}
