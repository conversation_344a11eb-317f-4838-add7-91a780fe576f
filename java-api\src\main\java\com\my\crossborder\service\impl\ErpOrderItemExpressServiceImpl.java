package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.ErpOrderItemExpress;
import com.my.crossborder.mybatis.mapper.ErpOrderItemExpressMapper;
import com.my.crossborder.service.ErpOrderItemExpressService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressInsertDTO;
import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressPageDTO;
import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressUpdateDTO;
import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressDeleteDTO;
import com.my.crossborder.controller.vo.erp_order_item_express.ErpOrderItemExpressDetailVO;
import com.my.crossborder.controller.vo.erp_order_item_express.ErpOrderItemExpressPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单项_快递信息 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
public class ErpOrderItemExpressServiceImpl extends ServiceImpl<ErpOrderItemExpressMapper, ErpOrderItemExpress> implements ErpOrderItemExpressService {


	@Transactional
	@Override
	public void insert(ErpOrderItemExpressInsertDTO insertDTO) {
		ErpOrderItemExpress entity = BeanUtil.copyProperties(insertDTO, ErpOrderItemExpress.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(ErpOrderItemExpressUpdateDTO updateDTO) {
		ErpOrderItemExpress entity = BeanUtil.copyProperties(updateDTO, ErpOrderItemExpress.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public ErpOrderItemExpressDetailVO detail(String id) {
		ErpOrderItemExpress entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, ErpOrderItemExpressDetailVO.class);
	}

	@Override
	public Page<ErpOrderItemExpressPageVO> page(ErpOrderItemExpressPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(ErpOrderItemExpressDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
