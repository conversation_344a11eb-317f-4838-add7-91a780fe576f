# 调度的配置
com:
  my:
    crossborder:
      job:
        Erp990Job:
          fetchShop: "0 0/5 * * * ? "  # 每5分钟更新店铺信息
          fetchOrder: "0 0/5 * * * ? " # 每5分钟更新订单信息
          fetchPackageRecord: "0 0/8 * * * ? " # 每8分钟更新打包信息
          clearClintLog: "0 0 1 * * ? "  # 每天清理erp990Client的日志
          clientLogStoreDays: 2         # erp990Client的日志最大留存天数
        AttendanceJob:
          processAbsentStatus: "1 0 0 * * ?"  # 每天凌晨，将前一天未打卡的记录标记为缺勤
        SysParamCheckJob:
          check: "0 0/30 * * * ? "  # 每30分钟检查系统参数sysParam