package com.my.crossborder.controller;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.my.crossborder.controller.dto.stl_purchase.StlPurchaseDeleteDTO;
import com.my.crossborder.controller.dto.stl_purchase.StlPurchaseInsertDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.stl_purchase.DailyPurchaseDetail;
import com.my.crossborder.controller.vo.stl_purchase.MonthlyPurchaseDataVO;
import com.my.crossborder.controller.vo.stl_purchase.MonthlyPurchaseStatusVO;
import com.my.crossborder.controller.vo.stl_purchase.StlPurchaseDetailVO;
import com.my.crossborder.service.StlPurchaseService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 结算_采购结算表 
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/api/stl-purchase")
@RequiredArgsConstructor
public class StlPurchaseController {

    private final StlPurchaseService stlPurchaseService;

    /**
    * 新增
    */
    @SaCheckPermission("stl-purchase:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody StlPurchaseInsertDTO insertDTO) {
    	this.stlPurchaseService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<StlPurchaseDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.stlPurchaseService.detail(id));
    }
    
//    /**
//    * 批量删除(物理删除)
//    */
//    @DeleteMapping
//    public StdResp<?> delete(@Valid @RequestBody StlPurchaseDeleteDTO deleteDTO) {
//    	this.stlPurchaseService.delete(deleteDTO);
//		return StdResp.success();
//    }

    /**
     * 获取用户月度采购结算数据
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份
     */
    @GetMapping(value = "monthly-purchase-data")
    public StdResp<List<MonthlyPurchaseDataVO>> monthlyPurchaseData(@RequestParam Integer userId,
													            @RequestParam Integer year,
													            @RequestParam Integer month) {
    	List<MonthlyPurchaseDataVO> data = this.stlPurchaseService.monthlyPurchaseData(userId, year, month);
        return StdResp.success(data);
    }

    /**
     * 获取用户指定日期的采购详情
     * @param userId 用户ID
     * @param purchaseDate 采购日期
     */
    @GetMapping(value = "daily-purchase-details")
    public StdResp<DailyPurchaseDetail> dailyPurchaseDetails(@RequestParam Integer userId,
													            @RequestParam LocalDate purchaseDate) {
    	DailyPurchaseDetail data = this.stlPurchaseService.dailyPurchaseDetails(userId, purchaseDate);
        return StdResp.success(data);
    }

}
