package com.my.crossborder.controller.vo.sys_erp_client_log;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_erp接口日志表
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysErpClientLogDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long logId;

    /**
     * 禾宸用户名
     */
    private String username;

    /**
     * 接口名称
     */
    private String operationName;

    /**
     * 接口详情
     */
    private String operationContent;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 异常信息
     */
    private String errMsg;

    /**
     * 日志时间
     */
    private LocalDateTime createTime;

}
