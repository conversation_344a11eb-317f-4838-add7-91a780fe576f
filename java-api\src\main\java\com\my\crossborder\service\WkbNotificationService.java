package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationDeleteDTO;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationInsertDTO;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationPageDTO;
import com.my.crossborder.controller.vo.wkb_notification.WkbNotificationDetailVO;
import com.my.crossborder.controller.vo.wkb_notification.WkbNotificationPageVO;
import com.my.crossborder.mybatis.entity.WkbNotification;

/**
 * 通知表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface WkbNotificationService extends IService<WkbNotification> {

	/**
	 * 新增
	 */
	void insert(WkbNotificationInsertDTO insertDTO);

//	/**
//	 * 修改
//	 */
//	void update(WkbNotificationUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	WkbNotificationDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<WkbNotificationPageVO> page(WkbNotificationPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(WkbNotificationDeleteDTO deleteDTO);	

}
