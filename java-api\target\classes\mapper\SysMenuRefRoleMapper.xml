<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysMenuRefRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysMenuRefRole">
        <id column="role_id" property="roleId" />
        <result column="menu_id" property="menuId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        role_id, menu_id
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_menu_ref_role.SysMenuRefRolePageVO">
		SELECT
			role_id, menu_id
		FROM
			sys_menu_ref_role AS t1
		<where>
        	1=1
	        <if test="roleId != null and roleId != ''">
	           	AND t1.role_id = #{roleId}
            </if>
	        <if test="menuId != null and menuId != ''">
	           	AND t1.menu_id = #{menuId}
            </if>
        </where>
    </select>

	<!-- menuId和permission -->    
    <select id="menuIdAndPermission" resultType="com.my.crossborder.controller.vo.sys_menu_ref_role.SysMenuRefRolePermissionVO">
		SELECT
			tRef.role_id,
			tRef.menu_id,
			tMenu.is_menu as menu,
			tMenu.permission 
		FROM
			sys_menu AS tMenu
			INNER JOIN sys_menu_ref_role AS tRef ON tMenu.id = tRef.menu_id
    </select>

</mapper>
