package com.my.crossborder.service.check;

import java.lang.reflect.Field;

import com.my.crossborder.service.check.result.CheckResult;

/**
 * 字段检测接口
 * <AUTHOR>
 *
 */
public interface ICheck {

    /**
     * 检测字段值
     * @param fieldValue 字段值
     * @param field 字段反射对象
     * @param obj 包含该字段的对象
     * @return 检测结果
     */
    CheckResult check(Object fieldValue, Field field, Object obj);
    
    /**
     * 获取字段名称
     * @param field 字段反射对象
     * @return 字段名称
     */
    String getFieldName(Field field);
    
    /**
     * 获取字段的中文标签
     * @param field 字段反射对象
     * @return 字段的中文标签
     */
    String getFieldLabel(Field field);
}
