package com.my.crossborder.controller.dto.sys_shop_partner;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_shop_partner.SysShopPartnerPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_店铺合伙人表
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShopPartnerPageDTO 
						extends PageDTO<SysShopPartnerPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 店铺ID（关联 sys_shop.id）
     */
    private Integer shopId;

    /**
     * 合伙人用户ID（关联 sys_user.user_id）
     */
    private Integer partnerUserId;

}
