package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_role.SysRolePageDTO;
import com.my.crossborder.controller.vo.sys_role.SysRolePageVO;
import com.my.crossborder.mybatis.entity.SysRole;

/**
 * 系统角色表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface SysRoleMapper extends BaseMapper<SysRole> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysRolePageVO> page(SysRolePageDTO pageDTO);
	
}
