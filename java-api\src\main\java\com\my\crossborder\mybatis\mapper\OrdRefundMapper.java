package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.ord_refund.OrdRefundPageDTO;
import com.my.crossborder.controller.vo.ord_refund.OrdRefundPageVO;
import com.my.crossborder.mybatis.entity.OrdRefund;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 退款表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface OrdRefundMapper extends BaseMapper<OrdRefund> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<OrdRefundPageVO> page(OrdRefundPageDTO pageDTO);
	
}
