package com.my.crossborder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_shop.SysShopInsertDTO;
import com.my.crossborder.controller.dto.sys_shop.SysShopPageDTO;
import com.my.crossborder.controller.dto.sys_shop.SysShopUpdateDTO;
import com.my.crossborder.controller.vo.sys_shop.SysShopPageVO;
import com.my.crossborder.mybatis.entity.SysShop;

/**
 * 店铺管理表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface SysShopService extends IService<SysShop> {

	/**
	 * 新增
	 */
	void insert(SysShopInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysShopUpdateDTO updateDTO);
 
	/**
	 * 分页
	 */
	Page<SysShopPageVO> page(SysShopPageDTO pageDTO);	
 
	/**
	 * 不存在则新增
	 * @param shopList
	 */
	void saveIfAbsense(List<SysShop> shopList);

	/**
	 * 查询登录人应该有的shopIds
	 * 如果是合伙人，查询sys_shop_partner中我的shopId
	 * @return 店铺ID列表
	 */
	List<Integer> myShopIds();

}
