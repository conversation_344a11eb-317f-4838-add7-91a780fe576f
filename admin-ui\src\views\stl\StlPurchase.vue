<template>
  <div class="stl-purchase-container">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">结算管理</el-breadcrumb-item>
      <el-breadcrumb-item>采购结算</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="main-content">
      <!-- 左侧员工列表 -->
      <div class="left-panel">
        <div class="employee-section">
          <div class="section-header">
            <h3>{{ canEditSettlement ? '人员列表' : '我的结算' }}</h3>
            <el-select
              v-if="canEditSettlement"
              v-model="roleFilter"
              placeholder="全部角色"
              size="small"
              clearable
              style="width: 120px;">
              <el-option label="全部角色" value=""></el-option>
              <el-option label="客服" value="客服"></el-option>
              <el-option label="客服主管" value="客服主管"></el-option>
            </el-select>
          </div>
          <!-- 添加筛选框 -->
          <div class="filter-box">
            <el-input
              v-model="employeeFilter"
              placeholder="搜索用户..."
              prefix-icon="el-icon-search"
              size="small"
              clearable
              style="margin-bottom: 15px;">
            </el-input>
          </div>
          <div class="employee-list">
            <div
              v-for="employee in filteredEmployeeList"
              :key="employee.id"
              :class="['employee-item', { active: isEmployeeSelected(employee) }]"
              @click="selectEmployee(employee)">
              <div class="employee-avatar">
                <i class="el-icon-user"></i>
              </div>
              <div class="employee-info">
                <div class="employee-name">{{ employee.name }}</div>
                <div class="employee-dept">{{ employee.roleName }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间采购结算记录 -->
      <div class="middle-panel">
        <div v-if="selectedEmployee" class="purchase-settlement-section">
          <h3 style="text-align: center;">{{ selectedEmployee.name }} {{ currentMonth }}月采购结算</h3>

          <div class="month-stats">
              <div v-if="selectedEmployee && selectedEmployee.loading" class="loading-stats">
                <el-icon class="is-loading"><i class="el-icon-loading"></i></el-icon>
                <span>加载中...</span>
              </div>
              <template v-else>
                <div class="settlement-cards">
                  <div class="settlement-card success-settlement">
                    <div class="card-number">{{ getPurchaseStats().settledCount }}</div>
                    <div class="card-label">已结算</div>
                    <div class="card-amount">¥ {{ getPurchaseStats().settledAmount }}</div>
                  </div>
                  <div class="settlement-card warning-settlement">
                    <div class="card-number">{{ getPurchaseStats().unsettledCount }}</div>
                    <div class="card-label">未结算</div>
                    <div class="card-amount">¥ {{ getPurchaseStats().unsettledAmount }}</div>
                  </div>
                </div>
              </template>
            </div>

          <!-- 每日采购结算记录列表 -->
          <div class="daily-records-list">
            <div
              v-for="record in getDailyPurchaseRecords()"
              :key="record.date"
              :class="['record-row', record.status, { 'no-edit-permission': record.status === 'unsettled' && !canEditSettlement }]"
              @click="handleRecordClick(record)"
              :title="getRecordTooltip(record)">

              <!-- 左侧：日期和状态 -->
              <div class="record-left">
                <div class="record-date">{{ formatDate(record.date) }}</div>
                <div class="record-status">
                  <span v-if="record.status === 'settled'" class="status-settled">
                    <i class="el-icon-circle-check"></i>
                    <span class="status-text">已结算</span>
                  </span>
                  <span v-else-if="record.status === 'unsettled'" class="status-unsettled">
                    <i class="el-icon-warning"></i>
                    <span class="status-text">未结算</span>
                  </span>
                  <span v-else class="status-none">
                    <i class="el-icon-minus"></i>
                    <span class="status-text">无采购</span>
                  </span>
                </div>
              </div>

              <!-- 右侧：采购金额和结算信息 -->
              <div class="record-right">
                <div class="purchase-amount">
                  <div v-if="record.amount > 0" class="amount-info">
                    <div class="amount-row">
                      <span class="amount-label">采购金额：</span>
                      <span class="amount-value">¥{{ record.amount.toFixed(2) }}</span>
                    </div>
                    <div v-if="record.status === 'settled'" class="settlement-info">
                      <div class="amount-row">
                        <span class="amount-label">结算金额：</span>
                        <span class="settlement-value">¥{{ record.settlementAmount.toFixed(2) }}</span>
                      </div>
                      <!-- <div class="settlement-remark">备注：{{ record.remark }}</div>
                      <div class="settlement-date">{{ record.settlementDate }}</div> -->
                    </div>
                  </div>
                  <div v-else class="no-purchase">无采购记录</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-selection">
          <i class="el-icon-user"></i>
          <p>请选择员工查看采购结算记录</p>
        </div>
      </div>

      <!-- 右侧采购日历 -->
      <div class="right-panel">
        <div v-if="selectedEmployee" class="detail-section">
        <!-- 状态说明图例 -->
        <div class="status-legend">
            <div class="legend-item">
              <span class="legend-icon status-settled">
                <i class="el-icon-circle-check"></i>
              </span>
              <span class="legend-text">已结算</span>
            </div>
            <div class="legend-number">
              <span class="number-value number-settled">{{ getPurchaseStats().settledCount }}</span>
            </div>
            <div class="legend-item" style="margin-left: 100px;">
              <span class="legend-icon status-unsettled">
                <i class="el-icon-warning"></i>
              </span>
              <span class="legend-text">未结算</span>
            </div>
            <div class="legend-number">
              <span class="number-value number-unsettled">{{ getPurchaseStats().unsettledCount }}</span>
            </div>
          </div>

          <!-- 采购日历 -->
          <div class="calendar-section">
            <el-calendar
              v-model="calendarDate"
              @pick="onCalendarDateChange"
              :key="calendarKey"
            >
              <template slot="dateCell" slot-scope="{date, data}">
                <div class="custom-calendar-cell" @click="onCalendarCellClick(data.day)">
                  <div class="cell-date">{{ data.day.split('-').slice(2).join('-') }}</div>
                  <div class="cell-status">
                    <span v-if="getEmployeePurchaseStatus(data.day) === 'settled'" class="status-settled">
                      <i class="el-icon-circle-check"></i>
                    </span>
                    <span v-else-if="getEmployeePurchaseStatus(data.day) === 'unsettled'" class="status-unsettled">
                      <i class="el-icon-warning"></i>
                    </span>
                    <span v-else-if="data.day === getCurrentDate()" class="status-today">
                      <i class="el-icon-time"></i>
                    </span>
                  </div>
                </div>
              </template>
            </el-calendar>
          </div>
        </div>

        <div v-else class="no-selection">
          <i class="el-icon-user"></i>
          <p>请选择员工查看采购日历</p>
        </div>
      </div>
    </div>



    <!-- 结算编辑弹窗 -->
    <StlPurchaseEdit
      :visible.sync="editDialogVisible"
      :purchase-data="editPurchaseData"
      @success="handleSettlementSuccess"
      @close="handleEditClose" />

    <!-- 采购详情查看弹窗 -->
    <StlPurchaseView
      :visible.sync="viewDialogVisible"
      :purchase-detail-data="viewPurchaseDetailData"
      :selected-date="selectedViewDate"
      @close="handleViewClose" />
  </div>
</template>

<script>
import { getAllStaffUsers, getAllSupervisorUsers } from '@/api/SysUser'
import { getMonthlyPurchaseData, getDailyPurchaseDetails } from '@/api/StlPurchase'
import StlPurchaseEdit from './StlPurchaseEdit.vue'
import StlPurchaseView from './StlPurchaseView.vue'

export default {
  name: 'StlPurchase',
  components: {
    StlPurchaseEdit,
    StlPurchaseView
  },
  data() {
    return {
      selectedEmployee: null,
      calendarDate: new Date(),
      employeeList: [],
      // 员工采购数据
      employeePurchaseData: {},
      employeeFilter: '',
      roleFilter: '', // 角色过滤器
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      calendarKey: 0, // 用于强制重新渲染日历
      // 结算编辑弹窗相关
      editDialogVisible: false,
      editPurchaseData: {
        date: '',
        amount: 0,
        userId: null
      },
      // 采购详情查看弹窗相关
      viewDialogVisible: false,
      selectedViewDate: '',
      viewPurchaseDetailData: {
        purchaseData: [],
        purchaseCentralizedData: []
      }
    }
  },
  computed: {
    // 检查当前用户角色权限
    currentUserRole() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.roleId
    },

    // 获取当前用户ID
    currentUserId() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.userId || userEntity.id
    },

    // 检查是否可以编辑结算（roleId=11时可用）
    canEditSettlement() {
      return this.currentUserRole === 11
    },

    filteredEmployeeList() {
      let filtered = this.employeeList

      // 如果不是roleId=11的用户，只能查看自己的结算
      if (this.currentUserRole !== 11) {
        filtered = filtered.filter(employee => {
          const userId = employee.userId || employee.id
          return userId === this.currentUserId
        })
      }

      // 按角色过滤
      if (this.roleFilter) {
        filtered = filtered.filter(employee => employee.roleName === this.roleFilter)
      }

      // 按搜索关键字过滤
      if (this.employeeFilter) {
        filtered = filtered.filter(employee =>
          employee.name.toLowerCase().includes(this.employeeFilter.toLowerCase()) ||
          employee.roleName.toLowerCase().includes(this.employeeFilter.toLowerCase())
        )
      }

      return filtered
    }
  },
  created() {
    this.loadEmployeeList()
  },
  watch: {
    roleFilter() {
      // 当角色过滤器变化时，检查当前选中的员工是否仍在过滤结果中
      if (this.selectedEmployee) {
        const isStillVisible = this.filteredEmployeeList.some(emp =>
          this.isEmployeeSelected(emp)
        )

        // 如果当前选中的员工不在过滤结果中，清除选中状态
        if (!isStillVisible) {
          this.selectedEmployee = null
        }
      }
    },

    // 监听日历日期变化（包括翻页）
    calendarDate: {
      handler(newDate, oldDate) {
        if (oldDate) {
          // 检查年月是否发生变化
          const oldYear = oldDate.getFullYear()
          const oldMonth = oldDate.getMonth() + 1
          const newYear = newDate.getFullYear()
          const newMonth = newDate.getMonth() + 1

          if (oldYear !== newYear || oldMonth !== newMonth) {
            console.log(`日历月份变化: ${oldYear}-${oldMonth} -> ${newYear}-${newMonth}`)
            this.handleMonthChange(newYear, newMonth)
          }
        }
      },
      immediate: false
    }
  },
  methods: {
        // 检查员工是否被选中 - 解决ID数据类型匹配问题
    isEmployeeSelected(employee) {
      if (!this.selectedEmployee) return false

      // 获取所有可能的ID字段进行比较
      const employeeId = employee.id || employee.userId || employee.sysUserId || employee.employeeId
      const selectedId = this.selectedEmployee.id || this.selectedEmployee.userId || this.selectedEmployee.sysUserId || this.selectedEmployee.employeeId

      // 转换为字符串进行比较，避免数据类型不匹配问题
      const isSelected = String(employeeId) === String(selectedId)

      return isSelected
    },

    async loadEmployeeList() {
      try {
        // 如果不是roleId=11的用户，只加载当前用户信息
        if (!this.canEditSettlement) {
          const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
          const currentUser = {
            id: userEntity.userId || userEntity.id,
            userId: userEntity.userId || userEntity.id,
            name: userEntity.realName || userEntity.name || userEntity.username || '当前用户',
            roleName: userEntity.roleId === 21 ? '客服' : userEntity.roleId === 22 ? '客服主管' : '其他'
          }
          this.employeeList = [currentUser]
          console.log('当前用户信息:', currentUser)

          // 自动选择当前用户
          if (this.employeeList.length > 0) {
            this.selectEmployee(this.employeeList[0])
          }
          return
        }

        // 管理员用户：加载所有员工列表
        // 根据记忆中的角色映射关系：21-客服，22-客服主管
        const [staffRes, supervisorRes] = await Promise.all([
          getAllStaffUsers(),
          getAllSupervisorUsers()
        ])

        const allEmployees = []

        // 处理客服人员 - 分页结果在data.records中
        if (staffRes.success && staffRes.data && staffRes.data.records) {
          const staffUsers = staffRes.data.records.map(user => ({
            ...user,
            name: user.name || user.userName || user.realName || user.nickName || user.username || `用户${user.id || user.userId}`,
            roleName: '客服'
          }))
          allEmployees.push(...staffUsers)
          console.log('客服用户数据结构:', staffUsers.length > 0 ? staffUsers[0] : 'empty')
        }

        // 处理客服主管 - 分页结果在data.records中
        if (supervisorRes.success && supervisorRes.data && supervisorRes.data.records) {
          const supervisorUsers = supervisorRes.data.records.map(user => ({
            ...user,
            name: user.name || user.userName || user.realName || user.nickName || user.username || `用户${user.id || user.userId}`,
            roleName: '客服主管'
          }))
          allEmployees.push(...supervisorUsers)
          console.log('客服主管用户数据结构:', supervisorUsers.length > 0 ? supervisorUsers[0] : 'empty')
        }

        this.employeeList = allEmployees
        console.log('所有员工列表:', allEmployees)

        // 修复：不自动选择第一个员工，让用户手动选择
        // if (this.employeeList.length > 0) {
        //   this.selectEmployee(this.employeeList[0])
        // }
      } catch (error) {
        console.error('加载员工列表失败:', error)
        this.$message.error('加载员工列表失败')
      }
    },

    async selectEmployee(employee) {
      console.log('选中的员工对象:', employee)

      // 尝试不同的ID字段，并在employee对象上标准化为realUserId
      const userId = employee.id || employee.userId || employee.sysUserId || employee.employeeId
      console.log('实际使用的userId:', userId)

      if (userId) {
        // 立即清空当前选择，避免显示错误数据和错误的高亮状态
        this.selectedEmployee = null

        // 检查是否已有缓存数据，如果没有则先加载
        if (!this.employeePurchaseData[userId]) {
          // 显示加载状态
          this.selectedEmployee = { ...employee, realUserId: userId, loading: true }

          // 加载采购数据
          await this.loadEmployeePurchaseData(userId)
        }

        // 数据加载完成后设置选中员工
        this.selectedEmployee = { ...employee, realUserId: userId, loading: false }

        // 强制重新渲染日历
        this.calendarKey++

        // 强制更新视图，确保日历重新渲染
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      } else {
        console.error('无法找到用户ID字段')
        this.$message.error('无法获取用户ID')
      }
    },

    async loadEmployeePurchaseData(userId) {
      try {
        // 调用月度采购数据接口
        console.log(`调用月度采购数据接口: /stl-purchase/monthly-purchase-data?userId=${userId}&year=${this.currentYear}&month=${this.currentMonth}`)

        // 调用真实的API接口
        const res = await getMonthlyPurchaseData({
          userId: userId,
          year: this.currentYear,
          month: this.currentMonth
        })

        if (res.success && res.data) {
          const purchaseRecords = res.data || []

          // 转换为前端需要的格式
          const purchaseData = {}
          let settledCount = 0
          let unsettledCount = 0
          let settledAmount = 0
          let unsettledAmount = 0

          purchaseRecords.forEach(record => {
            const dateStr = record.purchaseDate
            if (record && dateStr) {
              purchaseData[dateStr] = {
                status: record.settlementFlag ? 'settled' : 'unsettled',
                amount: record.purchaseAmount || 0,
                settlementAmount: record.settlementAmount || 0,
                settlementDate: record.settlementDate,
                remark: record.remark,
                hasPurchase: true
              }

              if (record.settlementFlag) {
                settledCount++
                settledAmount += record.settlementAmount || 0
              } else {
                unsettledCount++
                unsettledAmount += record.purchaseAmount || 0
              }
            }
          })

          this.employeePurchaseData[userId] = {
            purchaseData: purchaseData,
            stats: {
              settledCount: settledCount,
              unsettledCount: unsettledCount,
              settledAmount: settledAmount,
              unsettledAmount: unsettledAmount
            }
          }
        }
      } catch (error) {
        console.error('加载采购数据失败:', error)
        this.$message.error('加载采购数据失败')
      }
    },

    getCurrentDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    getEmployeePurchaseStatus(dateStr) {
      if (!this.selectedEmployee) {
        return 'none'
      }
      const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
      const employeeData = this.employeePurchaseData[userId]

      if (employeeData && employeeData.purchaseData && employeeData.purchaseData[dateStr]) {
        const purchaseInfo = employeeData.purchaseData[dateStr]
        // 调用月度结算状态接口
        console.log(`调用月度结算状态接口: /stl-purchase/monthly-purchase-status?userId=${userId}&year=${this.currentYear}&month=${this.currentMonth}`)
        return purchaseInfo.status || 'none'
      }

      return 'none'
    },



    // 处理月份变化
    async handleMonthChange(newYear, newMonth) {
      console.log(`处理月份变化: ${this.currentYear}-${this.currentMonth} -> ${newYear}-${newMonth}`)
      console.log(`当前选中员工:`, this.selectedEmployee)

      // 更新当前年月
      this.currentYear = newYear
      this.currentMonth = newMonth

      // 如果有选中的员工，重新加载该员工的数据
      if (this.selectedEmployee) {
        const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
        if (userId) {
          // 清空缓存的数据，强制重新加载
          delete this.employeePurchaseData[userId]

          // 显示加载状态
          this.selectedEmployee.loading = true

          try {
            // 重新加载数据
            await this.loadEmployeePurchaseData(userId)
          } catch (error) {
            console.error('重新加载数据失败:', error)
          } finally {
            // 隐藏加载状态
            this.selectedEmployee.loading = false

            // 强制重新渲染日历
            this.calendarKey++
          }
        }
      }
    },

    async onCalendarDateChange(date) {
      this.calendarDate = date

      // 检查年月是否发生变化
      const newYear = date.getFullYear()
      const newMonth = date.getMonth() + 1

      if (newYear !== this.currentYear || newMonth !== this.currentMonth) {
        await this.handleMonthChange(newYear, newMonth)
      }
    },

    getDailyPurchaseRecords() {
      if (!this.selectedEmployee) return []

      const currentDateStr = this.getCurrentDate()
      const records = []
      const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
      const employeeData = this.employeePurchaseData[userId]
      const purchaseData = employeeData && employeeData.purchaseData ? employeeData.purchaseData : {}

      // 获取所有有采购记录的日期
      const allDates = Object.keys(purchaseData)

      // 转换为数组并排序（最新的在前）
      const sortedDates = allDates.sort((a, b) => new Date(b) - new Date(a))

      sortedDates.forEach(dateStr => {
        const purchaseInfo = purchaseData[dateStr]

        const record = {
          date: dateStr,
          status: purchaseInfo.status || 'none',
          amount: purchaseInfo.amount || 0,
          settlementAmount: purchaseInfo.settlementAmount || 0,
          settlementDate: purchaseInfo.settlementDate || '',
          remark: purchaseInfo.remark || '',
          isToday: dateStr === currentDateStr
        }

        records.push(record)
      })

      return records
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    },

    getPurchaseStats() {
      if (!this.selectedEmployee) return { settledCount: 0, unsettledCount: 0, settledAmount: 0, unsettledAmount: 0 }

      const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
      const employeeData = this.employeePurchaseData[userId]

      // 使用后端统计的数据
      if (employeeData && employeeData.stats) {
        return {
          settledCount: employeeData.stats.settledCount,
          unsettledCount: employeeData.stats.unsettledCount,
          settledAmount: employeeData.stats.settledAmount.toFixed(2),
          unsettledAmount: employeeData.stats.unsettledAmount.toFixed(2)
        }
      }

      return { settledCount: 0, unsettledCount: 0, settledAmount: 0, unsettledAmount: 0 }
    },

    // 日历单元格点击事件
    async onCalendarCellClick(dateStr) {
      if (!this.selectedEmployee) {
        return
      }

      // 检查是否有采购记录
      const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
      const employeeData = this.employeePurchaseData[userId]
      const hasPurchase = employeeData && employeeData.purchaseData && employeeData.purchaseData[dateStr]

      if (!hasPurchase) {
        return
      }

      // 使用新的采购详情查看弹窗
      this.selectedViewDate = this.formatDate(dateStr)

      // 加载当日采购详情
      await this.loadDailyPurchaseDetails(dateStr)

      this.viewDialogVisible = true
    },

    // 加载当日采购详情
    async loadDailyPurchaseDetails(dateStr) {
      try {
        const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
        const res = await getDailyPurchaseDetails({
          userId: userId,
          purchaseDate: dateStr
        })

        if (res.success && res.data) {
          // 为查看弹窗准备数据
          this.viewPurchaseDetailData = {
            purchaseData: res.data.purchaseData || [],
            purchaseCentralizedData: res.data.purchaseCentralizedData || []
          }
        }
      } catch (error) {
        console.error('加载采购详情失败:', error)
        this.$message.error('加载采购详情失败')
      }
    },



    // 获取记录的提示信息
    getRecordTooltip(record) {
      if (record.status === 'settled') {
        return '点击查看采购详情'
      } else if (record.status === 'unsettled') {
        const today = this.getCurrentDate()
        if (record.date === today) {
          return '不允许结算今日的采购'
        } else if (!this.canEditSettlement) {
          return '您没有权限进行结算操作，点击查看采购详情'
        } else {
          return '点击进行结算'
        }
      } else {
        return '无采购记录'
      }
    },

    // 处理记录点击事件
    async handleRecordClick(record) {
      // 检查是否是今日，不允许结算今日的采购
      const today = this.getCurrentDate()
      if (record.date === today && record.status === 'unsettled') {
        this.$message.warning('不允许结算今日的采购')
        return
      }

      if (record.status === 'unsettled') {
        // 检查权限：如果不是roleId=11的用户，不允许唤起编辑弹窗
        if (!this.canEditSettlement) {
          this.$message.warning('您没有权限进行结算操作')
          return
        }

        // 未结算记录，打开编辑弹窗
        this.editPurchaseData = {
          date: record.date,
          amount: record.amount,
          userId: this.selectedEmployee.realUserId || this.selectedEmployee.id
        }
        this.editDialogVisible = true
      } else if (record.status === 'settled' || record.status === 'unsettled') {
        // 有采购记录，打开采购详情查看弹窗（所有用户都可以查看）
        this.selectedViewDate = this.formatDate(record.date)

        // 加载当日采购详情
        await this.loadDailyPurchaseDetails(record.date)

        this.viewDialogVisible = true
      }
    },

    // 结算成功回调
    async handleSettlementSuccess() {
      // 重新加载当前员工的采购数据
      if (this.selectedEmployee) {
        const userId = this.selectedEmployee.realUserId || this.selectedEmployee.id
        await this.loadEmployeePurchaseData(userId)

        // 强制重新渲染日历和视图
        this.calendarKey++
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      }
    },

    // 编辑弹窗关闭回调
    handleEditClose() {
      this.editDialogVisible = false
    },

    // 查看弹窗关闭回调
    handleViewClose() {
      this.viewDialogVisible = false
      this.selectedViewDate = ''
      this.viewPurchaseDetailData = {
        purchaseData: [],
        purchaseCentralizedData: []
      }
    }
  }
}
</script>

<style scoped>
/* 修复样式问题 */
.record-date {
  font-weight: bold !important;
  color: #333 !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
  margin-top: 4px !important;
}

/* 修改高度对齐问题 */
.main-content {
  margin-top: 20px !important;
  padding-top: 20px !important;
  display: flex !important;
  gap: 20px !important;
  max-width: 1400px !important;
  margin: 0 auto !important;
  height: calc(100vh - 160px) !important;
  align-items: stretch !important; /* 让所有子元素高度一致 */
}

/* 采购相关样式 */
.status-settled {
  color: #67C23A;
}

.status-unsettled {
  color: #E6A23C; /* 改为warning色 */
}

.number-settled {
  color: #67C23A; /* 与已结算图标颜色一致 */
}

.number-unsettled {
  color: #E6A23C; /* 改为warning色 */
}

/* 结算卡片样式 */
.settlement-cards {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin: 15px 0;
}

.settlement-card {
  width: 120px;
  /* height: 90px; */
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.success-settlement {
  background-color: #f0f9ff;
  border: 2px solid #67C23A;
}

.warning-settlement {
  background-color: #fdf6ec;
  border: 2px solid #E6A23C;
}

.card-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
  line-height: 1;
}

.success-settlement .card-number {
  color: #67C23A;
}

.warning-settlement .card-number {
  color: #E6A23C;
}

.card-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1;
}

.card-amount {
  font-size: 15px;
  font-weight: bold;
  line-height: 1;
}

.success-settlement .card-amount {
  color: #67C23A;
}

.warning-settlement .card-amount {
  color: #E6A23C;
}



/* 采购金额显示 */
.purchase-amount {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount-info {
  color: #333;
  font-size: 13px;
  padding: 2px 0;
}

.amount-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.amount-label {
  color: #666;
  font-size: 12px;
  margin-right: 8px;
  min-width: 60px;
}

.amount-value {
  font-weight: bold;
  color: #409EFF;
}

.settlement-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
}

.settlement-value {
  font-weight: bold;
  color: #67C23A;
}

.settlement-date {
  color: #999;
  font-size: 11px;
  margin-top: 4px;
  text-align: right;
}

.settlement-remark {
  color: #666;
  font-size: 11px;
  margin-top: 2px;
  font-style: italic;
}

.no-purchase {
  color: #999;
  font-size: 12px;
  font-style: italic;
}

/* 采购详情弹窗样式 */
.purchase-detail-header h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
  text-align: center;
}

.purchase-table-container {
  max-height: 400px;
  overflow-y: auto;
}

.custom-calendar-cell {
  cursor: pointer;
}

.custom-calendar-cell:hover {
  background-color: #f0f9ff;
}

.left-panel {
  flex: 0 0 300px !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 20px !important;
  height: 100% !important; /* 确保高度占满 */
}

.middle-panel {
  flex: 0 0 400px !important;
  background: white !important;
  border-radius: 8px !important;
  padding: 20px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  overflow-y: auto !important;
  height: 100% !important; /* 确保高度占满 */
  box-sizing: border-box !important;
}

.right-panel {
  flex: 1 !important;
  background: white !important;
  border-radius: 8px !important;
  padding: 20px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  overflow-y: auto !important;
  height: 100% !important; /* 确保高度占满 */
  box-sizing: border-box !important;
}

.employee-section {
  background: white !important;
  border-radius: 8px !important;
  padding: 20px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  height: 100% !important; /* 确保高度占满 */
  display: flex !important;
  flex-direction: column !important;
  box-sizing: border-box !important;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.employee-section h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.employee-list {
  max-height: calc(100% - 50px) !important;
  overflow-y: auto !important;
  flex: 1 !important; /* 让列表区域占据剩余空间 */
}

.employee-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.employee-item:hover {
  background: #f8f9fa;
  border-color: #409EFF;
}

.employee-item.active {
  background: #e8f4ff;
  border-color: #409EFF;
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.employee-dept {
  color: #666;
  font-size: 12px;
}

.month-stats {
  display: flex;
  gap: 20px;
  /* margin-bottom: 25px; */
  padding: 0 10px;
  justify-content: space-around;
}

.stat-card {
  text-align: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 6px;
  min-width: 60px;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 12px;
}

.calendar-section {
  margin-top: 20px;
}

.status-legend {
  display: flex;
  justify-content: space-around;
  margin: 15px 0 20px 0;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.legend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
}

.legend-icon {
  margin-bottom: 4px;
  font-size: 16px;
}

.legend-text {
  color: #666;
  white-space: nowrap;
}

.legend-number {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px; /* 与legend-item高度保持一致 */
}

.number-value {
  font-size: 30px;
  font-weight: bold;
  min-width: 40px;
  text-align: center;
}

.number-present {
  color: #67C23A; /* 与已打卡图标颜色一致 */
}

.number-absent {
  color: #F56C6C; /* 与缺勤图标颜色一致 */
}

.calendar-section .el-calendar {
  --el-calendar-cell-width: 50px;
}

.calendar-section .el-calendar__header {
  padding: 8px 20px;
}

.calendar-section .el-calendar__body {
  padding: 5px 20px 10px;
}

.calendar-section .el-calendar-table td {
  height: 22px;
  text-align: center;
}

.calendar-section .el-calendar-day {
  height: 22px;
  padding: 0;
}

.custom-calendar-cell {
  text-align: center;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.cell-date {
  font-size: 11px;
  line-height: 1;
}

.cell-status {
  margin-top: 3px;
}

.cell-status i {
  font-size: 32px;
  font-weight: bold;
}

.status-present {
  color: #67C23A;
}

.status-absent {
  color: #F56C6C;
}

.status-today {
  color: #409EFF;
}

/* 排班圆点 */
.schedule-dot {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  background-color: #409EFF;
  border-radius: 50%;
}

.no-selection {
  text-align: center;
  color: #999;
  padding: 80px 0;
}

.no-selection i {
  font-size: 48px;
  margin-bottom: 20px;
}

.purchase-settlement-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
}

.daily-records-list {
  max-height: calc(100% - 120px);
  overflow-y: auto;
}

.record-row {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 8px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #eee;
  border-left: 4px solid #ddd;
  transition: all 0.3s;
}

.record-row:hover {
  background: #f8f9fa;
  border-color: #c6e2ff;
}

.record-row.settled {
  border-left-color: #67C23A;
}

.record-row.unsettled {
  border-left-color: #E6A23C; /* 改为warning色 */
}

.record-row.today {
  border-left-color: #409EFF;
}

/* 记录行点击样式 */
.record-row.settled,
.record-row.unsettled {
  cursor: pointer;
  transition: background-color 0.2s;
}

.record-row.settled:hover,
.record-row.unsettled:hover {
  background-color: #f5f7fa;
}

.record-row.settled:active,
.record-row.unsettled:active {
  background-color: #e4e7ed;
}

/* 无编辑权限的记录样式 */
.record-row.no-edit-permission {
  opacity: 0.7;
  cursor: default !important;
}

.record-row.no-edit-permission:hover {
  background-color: #f9f9f9 !important;
  border-color: #ddd !important;
}

.record-row.no-edit-permission .status-unsettled {
  color: #909399 !important;
}

.record-left {
  flex: 0 0 110px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.record-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.record-status i {
  font-size: 16px;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.record-right {
  flex: 1;
  margin-left: 20px;
}

.schedule-shops {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shop-item {
  color: #333;
  font-size: 13px;
  padding: 2px 0;
}

.no-schedule {
  color: #999;
  font-size: 12px;
  font-style: italic;
}

.status-none {
  color: #909399;
}

.loading-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #409EFF;
  font-size: 14px;
  gap: 10px;
}

.loading-stats .el-icon {
  font-size: 24px;
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    height: auto;
  }

  .left-panel {
    flex: none;
    order: 1;
  }

  .middle-panel {
    flex: none;
    order: 2;
  }

  .right-panel {
    flex: none;
    order: 3;
  }

  .month-stats {
    justify-content: center;
  }

  .records-grid {
    grid-template-columns: 1fr;
  }

  .record-card {
    padding: 10px;
  }

  .status-legend {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}
</style>