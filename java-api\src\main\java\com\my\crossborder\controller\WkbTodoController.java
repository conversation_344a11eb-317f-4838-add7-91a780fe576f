package com.my.crossborder.controller;


import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_todo.WkbTodoDeleteDTO;
import com.my.crossborder.controller.dto.wkb_todo.WkbTodoPageDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.wkb_todo.WkbTodoDetailVO;
import com.my.crossborder.controller.vo.wkb_todo.WkbTodoPageVO;
import com.my.crossborder.service.WkbTodoService;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;

/**
 * 待办表 
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/api/wkb-todo")
@RequiredArgsConstructor
public class WkbTodoController {

    private final WkbTodoService wkbTodoService;


    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<WkbTodoDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.wkbTodoService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<WkbTodoPageVO>> page(WkbTodoPageDTO pageDTO) {
        Page<WkbTodoPageVO> page = this.wkbTodoService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
     * 所有未读
     */
    @GetMapping(value = "all-unread")
    public StdResp<List<WkbTodoPageVO>> allUnread() {
    	WkbTodoPageDTO pageDTO = new WkbTodoPageDTO(1L, 10000L);
    	pageDTO.setReceiveUserId(StpUtil.getLoginIdAsInt());
    	pageDTO.setRead(Boolean.FALSE);
		Page<WkbTodoPageVO> page = this.wkbTodoService.page(pageDTO);
		List<WkbTodoPageVO> data = page.getRecords();
    	return StdResp.success(data);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody WkbTodoDeleteDTO deleteDTO) {
    	this.wkbTodoService.delete(deleteDTO);
		return StdResp.success();
    }
    
}