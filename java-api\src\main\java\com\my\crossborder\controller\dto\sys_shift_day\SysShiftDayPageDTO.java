package com.my.crossborder.controller.dto.sys_shift_day;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_shift_day.SysShiftDayPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_排班日期表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftDayPageDTO 
						extends PageDTO<SysShiftDayPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排班日期
     */
    private LocalDate shiftDay;

    /**
     * 当日应排店铺总数（固化值，避免店铺变化影响历史数据）
     */
    private Integer shopCount;

    /**
     * 创建人ID（关联用户表）
     */
    private Integer createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
