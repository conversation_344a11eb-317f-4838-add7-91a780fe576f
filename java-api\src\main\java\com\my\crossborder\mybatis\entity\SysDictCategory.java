package com.my.crossborder.mybatis.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数据字典-类别表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_dict_category")
public class SysDictCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类别id
     */
    @TableId(value = "category_id", type = IdType.INPUT)
    private String categoryId;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 描述
     */
    private String description;


}
