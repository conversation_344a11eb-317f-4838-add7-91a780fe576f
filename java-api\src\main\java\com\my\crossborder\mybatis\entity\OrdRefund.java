package com.my.crossborder.mybatis.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 退款表
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ord_refund")
public class OrdRefund implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @TableId(value = "order_sn", type = IdType.INPUT)
    private String orderSn;

    /**
     * 申请状态 (0:待申请 1:已申请 2:不退采买改为入库)
     */
    private String applyStatus;

    /**
     * 申请退款金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请人id
     */
    private Integer applyUserId;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 结果状态 (1:退款成功 2:退款失败 3:确认已入库)
     */
    private String resultStatus;

    /**
     * 结果时间
     */
    private LocalDateTime resultTime;

    /**
     * 退款失败备注
     */
    private String refundFailReason;

    /**
     * 退款成功金额
     */
    private BigDecimal refundSuccessAmount;

    /**
     * 结果填写人
     */
    private Integer resultUserId;

    /**
     * 结算状态 (-1:无需结算 0:待结算 1:已结算)
     */
    private String settlementStatus;


}
