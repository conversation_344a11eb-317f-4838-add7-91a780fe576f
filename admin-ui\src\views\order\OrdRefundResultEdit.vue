<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="handleDialogClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="120px">
      <el-form-item label="订单号">
        <el-input v-model="form.orderSn" disabled style="width: 100%;"></el-input>
      </el-form-item>

      <el-form-item label="处理类型">
        <el-input v-model="applyTypeText" disabled style="width: 100%;"></el-input>
      </el-form-item>

      <el-form-item v-if="isRefundType" label="申请退款金额">
        <span style="font-size: medium;">{{ formatApplyAmount }}</span>
      </el-form-item>

      <el-form-item v-if="isRefundType" label="处理结果" prop="success">
        <el-radio-group v-model="form.success" @change="handleResultChange">
          <el-radio :label="true">退款成功</el-radio>
          <el-radio :label="false">退款失败</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="isRefundType && form.success === true" label="退款成功金额" prop="refundSuccessAmount">
        <el-input-number 
          v-model="form.refundSuccessAmount" 
          :precision="2" 
          :min="0.01" 
          :max="99999.99"
          placeholder="请输入退款成功金额"
          style="width: 200px;">
        </el-input-number> 元
      </el-form-item>

      <el-form-item v-if="isRefundType && form.success === false" label="退款失败备注" prop="refundFailReason">
        <el-input 
          v-model="form.refundFailReason" 
          type="textarea" 
          :rows="3"
          placeholder="请输入退款失败原因"
          style="width: 100%;">
        </el-input>
      </el-form-item>

      <el-form-item v-if="!isRefundType" label="确认操作">
        <el-alert
          title="确认商品已入库"
          type="info"
          :closable="false">
        </el-alert>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ submitting ? '提交中...' : '确 定' }}
      </el-button>
      <el-button @click="handleDialogClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { refundResult, confirmPutIn } from '../../api/OrdRefund'

export default {
  name: 'OrdRefundResultEdit',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        orderSn: '',
        success: null,
        refundSuccessAmount: null,
        refundFailReason: ''
      },
      baseRules: {
        success: [
          { required: true, message: '请选择处理结果', trigger: 'change' }
        ],
        refundSuccessAmount: [
          { required: true, message: '请输入退款成功金额', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '退款成功金额必须大于0', trigger: 'blur' }
        ],
        refundFailReason: [
          { required: true, message: '请输入退款失败原因', trigger: 'blur' },
          { min: 1, max: 500, message: '失败原因长度在 1 到 500 个字符', trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return this.isRefundType ? '退款结果' : '确认入库'
    },
    isRefundType() {
      // 判断是否为退款类型（申请状态为1-已申请）
      return this.editData.applyStatus === '1'
    },
    applyTypeText() {
      if (this.editData.applyStatus === '1') {
        return '申请退款'
      } else if (this.editData.applyStatus === '2') {
        return '不退采买做入库'
      }
      return '未知'
    },
    formatApplyAmount() {
      if (this.editData.applyAmount) {
        // return `${this.editData.applyAmount} 元`
        const num = parseFloat(this.editData.applyAmount)
        if (isNaN(num)) return '-'
        return '￥ ' + num.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
      }
      return '暂无数据'
    },
    rules() {
      const rules = { ...this.baseRules }
      
      if (!this.isRefundType) {
        // 如果是确认入库，不需要验证退款相关字段
        delete rules.success
        delete rules.refundSuccessAmount
        delete rules.refundFailReason
      } else {
        // 根据选择的结果类型动态调整验证规则
        if (this.form.success === true) {
          delete rules.refundFailReason
        } else if (this.form.success === false) {
          delete rules.refundSuccessAmount
        } else {
          delete rules.refundSuccessAmount
          delete rules.refundFailReason
        }
      }
      
      return rules
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    initForm() {
      this.form = {
        orderSn: this.editData.orderSn || '',
        success: null,
        refundSuccessAmount: null,
        refundFailReason: ''
      }
      
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },

    handleResultChange(value) {
      // 当切换结果类型时，清空相关字段
      if (value === true) {
        this.form.refundFailReason = ''
        // 当选择退款成功时，默认设置为申请退款金额
        this.form.refundSuccessAmount = this.editData.applyAmount || null
      } else if (value === false) {
        this.form.refundSuccessAmount = null
      }

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },

    handleSubmit() {
      if (!this.isRefundType) {
        // 确认入库，直接确认
        this.$confirm('确认商品已入库？', '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.submitConfirmPutIn()
        })
        return
      }

      this.$refs.form.validate((valid) => {
        if (valid) {
          // 确认提交
          const message = this.form.success 
            ? `确认退款成功，金额：${this.form.refundSuccessAmount} 元？`
            : '确认退款失败？'
          
          this.$confirm(message, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.submitRefundResult()
          })
        }
      })
    },

    submitRefundResult() {
      this.submitting = true
      
      const params = {
        orderSn: this.form.orderSn,
        success: this.form.success,
        refundSuccessAmount: this.form.success ? this.form.refundSuccessAmount : null,
        refundFailReason: !this.form.success ? this.form.refundFailReason : null
      }
      
      refundResult(params).then(() => {
        this.$message.success('退款结果提交成功')
        this.$emit('success')
      }).catch(() => {
        this.submitting = false
      })
    },

    submitConfirmPutIn() {
      this.submitting = true
      
      const params = {
        orderSn: this.form.orderSn
      }
      
      confirmPutIn(params).then(() => {
        this.$message.success('确认入库成功')
        this.$emit('success')
      }).catch(() => {
        this.submitting = false
      })
    },

    handleDialogClose() {
      this.submitting = false
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
