Manifest-Version: 1.0
Implementation-Title: CrossBorder-Business-CloudOps-System
Implementation-Version: 1.0.0
Class-Path: lib/fastjson-1.2.58.jar lib/forest-spring-boot-starter-1.6
 .4.jar lib/forest-core-1.6.4.jar lib/juniversalchardet-1.0.3.jar lib/
 httpclient-4.5.10.jar lib/commons-codec-1.13.jar lib/httpclient-cache
 -4.5.10.jar lib/commons-logging-1.2.jar lib/httpcore-4.4.13.jar lib/h
 ttpmime-4.5.10.jar lib/okhttp-3.14.6.jar lib/okio-1.17.2.jar lib/comm
 ons-io-2.18.0.jar lib/hutool-cache-5.8.35.jar lib/hutool-core-5.8.35.
 jar lib/forest-spring-1.6.4.jar lib/protobuf-java-3.25.5.jar lib/jul-
 to-slf4j-1.7.30.jar lib/slf4j-api-1.7.30.jar lib/spring-cloud-context
 -2.1.0.RELEASE.jar lib/spring-security-crypto-5.2.1.RELEASE.jar lib/h
 utool-all-5.8.27.jar lib/commons-lang3-3.9.jar lib/guava-29.0-jre.jar
  lib/failureaccess-1.0.1.jar lib/listenablefuture-9999.0-empty-to-avo
 id-conflict-with-guava.jar lib/jsr305-3.0.2.jar lib/checker-qual-2.11
 .1.jar lib/error_prone_annotations-2.3.4.jar lib/j2objc-annotations-1
 .3.jar lib/spring-boot-starter-2.2.4.RELEASE.jar lib/spring-boot-2.2.
 4.RELEASE.jar lib/spring-context-5.2.3.RELEASE.jar lib/spring-boot-au
 toconfigure-2.2.4.RELEASE.jar lib/spring-boot-starter-logging-2.2.4.R
 ELEASE.jar lib/logback-classic-1.2.3.jar lib/logback-core-1.2.3.jar l
 ib/log4j-to-slf4j-2.12.1.jar lib/log4j-api-2.12.1.jar lib/jakarta.ann
 otation-api-1.3.5.jar lib/spring-core-5.2.3.RELEASE.jar lib/spring-jc
 l-5.2.3.RELEASE.jar lib/snakeyaml-1.25.jar lib/mysql-connector-java-8
 .0.19.jar lib/druid-spring-boot-starter-1.1.23.jar lib/druid-1.1.23.j
 ar lib/mybatis-plus-boot-starter-3.4.3.1.jar lib/mybatis-plus-3.4.3.1
 .jar lib/mybatis-plus-extension-3.4.3.1.jar lib/mybatis-plus-core-3.4
 .3.1.jar lib/mybatis-plus-annotation-3.4.3.1.jar lib/jsqlparser-4.0.j
 ar lib/mybatis-3.5.7.jar lib/mybatis-spring-2.0.6.jar lib/spring-boot
 -starter-jdbc-2.2.4.RELEASE.jar lib/HikariCP-3.4.2.jar lib/spring-jdb
 c-5.2.3.RELEASE.jar lib/spring-tx-5.2.3.RELEASE.jar lib/sa-token-spri
 ng-boot-starter-1.38.0.jar lib/sa-token-servlet-1.38.0.jar lib/sa-tok
 en-core-1.38.0.jar lib/sa-token-spring-boot-autoconfig-1.38.0.jar lib
 /spring-boot-starter-aop-2.2.4.RELEASE.jar lib/spring-aop-5.2.3.RELEA
 SE.jar lib/spring-beans-5.2.3.RELEASE.jar lib/aspectjweaver-1.9.5.jar
  lib/spring-boot-starter-web-2.2.4.RELEASE.jar lib/spring-boot-starte
 r-json-2.2.4.RELEASE.jar lib/jackson-databind-2.10.2.jar lib/jackson-
 annotations-2.10.2.jar lib/jackson-core-2.10.2.jar lib/jackson-dataty
 pe-jdk8-2.10.2.jar lib/jackson-datatype-jsr310-2.10.2.jar lib/jackson
 -module-parameter-names-2.10.2.jar lib/spring-boot-starter-tomcat-2.2
 .4.RELEASE.jar lib/tomcat-embed-core-9.0.30.jar lib/tomcat-embed-el-9
 .0.30.jar lib/tomcat-embed-websocket-9.0.30.jar lib/spring-boot-start
 er-validation-2.2.4.RELEASE.jar lib/jakarta.validation-api-2.0.2.jar 
 lib/hibernate-validator-6.0.18.Final.jar lib/jboss-logging-3.4.1.Fina
 l.jar lib/classmate-1.5.1.jar lib/spring-web-5.2.3.RELEASE.jar lib/sp
 ring-webmvc-5.2.3.RELEASE.jar lib/spring-expression-5.2.3.RELEASE.jar
  lib/lombok-1.18.10.jar lib/spring-boot-starter-test-2.2.4.RELEASE.ja
 r lib/spring-boot-test-2.2.4.RELEASE.jar lib/spring-boot-test-autocon
 figure-2.2.4.RELEASE.jar lib/json-path-2.4.0.jar lib/json-smart-2.3.j
 ar lib/accessors-smart-1.2.jar lib/asm-5.0.4.jar lib/jakarta.xml.bind
 -api-2.3.2.jar lib/jakarta.activation-api-1.2.1.jar lib/junit-jupiter
 -5.5.2.jar lib/junit-jupiter-api-5.5.2.jar lib/opentest4j-1.2.0.jar l
 ib/junit-platform-commons-1.5.2.jar lib/junit-jupiter-params-5.5.2.ja
 r lib/junit-jupiter-engine-5.5.2.jar lib/junit-vintage-engine-5.5.2.j
 ar lib/apiguardian-api-1.1.0.jar lib/junit-platform-engine-1.5.2.jar 
 lib/junit-4.12.jar lib/mockito-junit-jupiter-3.1.0.jar lib/assertj-co
 re-3.13.2.jar lib/hamcrest-2.1.jar lib/mockito-core-3.1.0.jar lib/byt
 e-buddy-1.10.6.jar lib/byte-buddy-agent-1.10.6.jar lib/objenesis-2.6.
 jar lib/jsonassert-1.5.0.jar lib/android-json-0.0.20131108.vaadin1.ja
 r lib/spring-test-5.2.3.RELEASE.jar lib/xmlunit-core-2.6.3.jar lib/sp
 ring-boot-configuration-processor-2.2.4.RELEASE.jar lib/easyexcel-3.1
 .0.jar lib/easyexcel-core-3.1.0.jar lib/easyexcel-support-3.1.0.jar l
 ib/poi-4.1.2.jar lib/commons-collections4-4.4.jar lib/commons-math3-3
 .6.1.jar lib/SparseBitSet-1.2.jar lib/poi-ooxml-4.1.2.jar lib/commons
 -compress-1.19.jar lib/curvesapi-1.06.jar lib/poi-ooxml-schemas-4.1.2
 .jar lib/xmlbeans-3.1.0.jar lib/commons-csv-1.8.jar lib/ehcache-3.8.1
 .jar lib/jaxb-runtime-2.3.2.jar lib/txw2-2.3.2.jar lib/istack-commons
 -runtime-3.0.8.jar lib/stax-ex-1.8.1.jar lib/FastInfoset-1.2.16.jar l
 ib/h2-1.4.200.jar
Build-Jdk-Spec: 1.8
Created-By: Maven Integration for Eclipse
Main-Class: com.my.crossborder.App

