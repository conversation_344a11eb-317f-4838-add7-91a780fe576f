package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_user.SysUserPageDTO;
import com.my.crossborder.controller.vo.sys_user.SysUserPageVO;
import com.my.crossborder.mybatis.entity.SysUser;

/**
 * system user Mapper 接口
 *
 * @date 2024-05-20
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysUserPageVO> page(SysUserPageDTO pageDTO);
	

	/**
	 * 查询用户
	 * @param username
	 * @param password
	 * @return
	 */
	default SysUser getByUsernameAndPassword(String username, String password) {
		LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<SysUser>()
				.eq(SysUser::getUsername, username)
	            .eq(SysUser::getPassword, password)
	            .eq(SysUser::getDisable, Boolean.FALSE);
        return this.selectOne(wrapper);
	}

	/**
	 * 更新密码
	 * @param userId
	 * @param password 
	 */
	default void updatePassword(Integer userId, String password) {
		Wrapper<SysUser> wrapper = new LambdaUpdateWrapper<SysUser>()
				.set(SysUser::getPassword, password)
				.eq(SysUser::getUserId, userId);
		SysUser entity = SysUser.builder().userId(userId).build();
		this.update(entity, wrapper);
	}

	/**
	 * 逻辑删除
	 * @param userId
	 */
	default void logicDelete(Integer userId) {
		Wrapper<SysUser> wrapper = new LambdaUpdateWrapper<SysUser>()
				.set(SysUser::getDisable, Boolean.TRUE)
				.eq(SysUser::getUserId, userId);
		this.update(null, wrapper);
	}

	/**
	 * 用户名是否存在
	 * @param username
	 * @return
	 */
	default Boolean exists(String username) {
		LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<SysUser>()
				.eq(SysUser::getUsername, username)
				.eq(SysUser::getDisable, Boolean.FALSE);
		return this.selectCount(wrapper) > 0;
	}
	
	
}
