package com.my.crossborder.mybatis.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_menu_ref_role.SysMenuRefRolePageDTO;
import com.my.crossborder.controller.vo.sys_menu_ref_role.SysMenuRefRolePageVO;
import com.my.crossborder.controller.vo.sys_menu_ref_role.SysMenuRefRolePermissionVO;
import com.my.crossborder.mybatis.entity.SysMenuRefRole;

/**
 * 菜单_角色_关联表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface SysMenuRefRoleMapper extends BaseMapper<SysMenuRefRole> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysMenuRefRolePageVO> page(SysMenuRefRolePageDTO pageDTO);
	
	/**
	 * 菜单Id和权限
	 */
	List<SysMenuRefRolePermissionVO> menuIdAndPermission();
	
}
