package com.my.crossborder.controller.dto.sys_user;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import com.my.crossborder.exception.BusinessException;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改我的密码
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserChangeMyPasswordDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 密码
     */
	@NotBlank(message="password不能为空")
    private String password;
	
	/**
	 * 二次输入密码
	 */
	@NotBlank(message="passwordConfirm不能为空")
	private String passwordConfirm;
	
	
	/**
	 * 校验输入项
	 */
	public void validate() {
		BusinessException.when(!this.password.equals(this.passwordConfirm), "两次输入的密码不一致!");
	}
}
