package com.my.crossborder.service.order;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;
import com.my.crossborder.forest.erp990.Erp990Client;
import com.my.crossborder.forest.erp990.context.Erp990Context;
import com.my.crossborder.forest.erp990.onerror.Erp990OnError;
import com.my.crossborder.forest.erp990.vo.ErpOrderPageVO;
import com.my.crossborder.forest.erp990.vo.ErpPackageRecordPageVO;
import com.my.crossborder.mybatis.entity.ErpOrder;
import com.my.crossborder.mybatis.entity.ErpOrderItem;
import com.my.crossborder.mybatis.entity.ErpOrderItemExpress;
import com.my.crossborder.service.ErpOrderItemExpressService;
import com.my.crossborder.service.ErpOrderItemService;
import com.my.crossborder.service.ErpOrderService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单数据同步服务
 * 定期从ERP990获取订单数据并同步到本地数据库
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ErpOrderSyncService {
	

    private final Erp990Client erp990Client;
    private final ErpOrderService erpOrderService;
    private final ErpOrderItemService erpOrderItemService;
    private final ErpOrderItemExpressService erpOrderItemExpressService;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final int DEFAULT_PAGE_SIZE = 100;

    /**
     * 全量同步最近订单数据
     * @param days 同步最近几天的数据
     */
    public void syncRecentOrders(int days) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days);
        this.syncOrdersByTimeRange(startTime, endTime);
    }

    /**
     * 按时间范围同步订单数据
     */
    public void syncOrdersByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("开始同步订单数据，时间范围：{} - {}", startTime, endTime);
        
        String startTimeStr = startTime.format(FORMATTER);
        String endTimeStr = endTime.format(FORMATTER);
        
        int orderItemSynced = 0;
        int orderSynced = 0;
        try {
            // 先查询总数，用于计算总页数
            ErpOrderPageVO firstResponse = erp990Client.orderPage(
                1, 1, 0, 
                startTimeStr, endTimeStr, 
                null, null, null, 
                new Erp990OnError()
            );
            
            if (firstResponse == null || firstResponse.getTotal() == null || firstResponse.getTotal() == 0) {
                log.info("查询时间范围内无数据，同步结束");
                return;
            }
            
            int total = firstResponse.getTotal();
            int pageCount = (int) Math.ceil((double) total / DEFAULT_PAGE_SIZE);
            log.info("查询到总数据量：{}，计算总页数：{}", total, pageCount);
            
            // 按页数循环同步
            for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
                try {
                    // 从ERP990获取数据
                    ErpOrderPageVO response = erp990Client.orderPage(
                        pageNum, DEFAULT_PAGE_SIZE, 0, 
                        startTimeStr, endTimeStr, 
                        null, null, null, 
                        new Erp990OnError()
                    );
                    
                    if (response == null || CollectionUtils.isEmpty(response.getRows())) {
                        log.info("第{}页无数据，跳过", pageNum);
                        continue;
                    }
                    
                    // 将 List<List<Row>> 展平为 List<Row>
                    List<ErpOrderPageVO.Row> flatRows = response.getRows().stream()
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
                    
                    // 转换数据
                    List<ErpOrder> orders = this.convertToErpOrders(flatRows);
                    List<ErpOrderItem> orderItems = this.convertToErpOrderItems(flatRows);
                    List<ErpOrderItemExpress> orderItemExpresses = this.convertToErpOrderItemExpresses(flatRows);
                    
                    // 保存到数据库
                    this.erpOrderService.saveOrUpdateBatch(orders);
                    this.erpOrderItemService.saveOrUpdateBatch(orderItems);
                    if (!CollectionUtils.isEmpty(orderItemExpresses)) {
                        this.erpOrderItemExpressService.saveOrUpdateBatch(orderItemExpresses);
                    }
                    
                    orderItemSynced += orders.size();
                    orderSynced += response.getRows().size();
                    log.info("同步第{}页完成，本页订单项：{}，累计：{}", pageNum, orders.size(), orderItemSynced);
                    
                } catch (Exception e) {
                    log.error("同步第{}页数据失败", pageNum, e);
                    // 继续处理下一页，不中断整个同步过程
                }
            }
            
        } catch (Exception e) {
            log.error("获取数据总数失败", e);
            return;
        }
        
        log.info("订单数据同步完成，总计同步 order：{}条, orderItem：{}条", orderSynced, orderItemSynced);
    }

    
    private List<ErpOrder> convertToErpOrders(List<ErpOrderPageVO.Row> rows) {
        return rows.stream().map(t -> BeanUtil.copyProperties(t, ErpOrder.class)).collect(Collectors.toList());
    }

    private List<ErpOrderItem> convertToErpOrderItems(List<ErpOrderPageVO.Row> rows) {
        List<ErpOrderItem> items = Lists.newLinkedList();
        for (ErpOrderPageVO.Row row : rows) {
        	ErpOrderItem item = BeanUtil.copyProperties(row, ErpOrderItem.class);
            item.setId(row.getOrderId() + "" +  row.getProductIdx());
            items.add(item);
        }
        return items;
    }

    private List<ErpOrderItemExpress> convertToErpOrderItemExpresses(List<ErpOrderPageVO.Row> rows) {
        List<ErpOrderItemExpress> expressItems = Lists.newLinkedList();
        
        for (ErpOrderPageVO.Row row : rows) {
            if (!CollectionUtils.isEmpty(row.getExpressDto())) {
                for (ErpOrderPageVO.ExpressDto expressDto : row.getExpressDto()) {
                    if (StringUtils.hasText(expressDto.getExpressNo())) {
                        ErpOrderItemExpress express = ErpOrderItemExpress.builder()
                            .id(row.getOrderId() + "" +  row.getProductIdx() + "" + expressDto.getExpressNo())
                            .expressNo(expressDto.getExpressNo())
                            .expressinFlag(expressDto.getExpressInFlag())
                            .putInTime(expressDto.getPutInTime())
                            .putCreateTime(expressDto.getPutCreateTime())
                            .orderItemId(row.getOrderId() + "" +  row.getProductIdx())
                            .orderId(row.getOrderId())
                            .build();
                        expressItems.add(express);
                    }
                }
            }
        }
        
        return expressItems;
    }

    /**
     * 同步打包信息
     */
	public void syncPackageRecord() {
        String username = Erp990Context.getUsername();
        log.info("[{}]开始同步打包信息", username);

        int totalSynced = 0;
        int pageNum = 1;

        try {
            while (true) {
                // 分页查询 stickFeeFlag 为空的订单
                List<ErpOrder> ordersToSync = this.erpOrderService.lambdaQuery()
                    .select(ErpOrder::getOrderSn)
                    .and(wrapper -> wrapper.eq(ErpOrder::getStickFeeFlag, StrUtil.EMPTY))
                    .last("LIMIT " + (pageNum - 1) * DEFAULT_PAGE_SIZE + ", " + DEFAULT_PAGE_SIZE)
                    .list();

                if (CollectionUtils.isEmpty(ordersToSync)) {
                    log.info("[{}]第{}页无数据，同步结束", username, pageNum);
                    break;
                }

                log.info("[{}]第{}页查询到{}条待同步订单", username, pageNum, ordersToSync.size());

                // 提取订单号列表，用换行符连接
                String trackidList = ordersToSync.stream()
                    .map(ErpOrder::getOrderSn)
                    .collect(Collectors.joining("\n"));

                // 调用ERP990接口获取打包信息
                ErpPackageRecordPageVO response = erp990Client.packageRecord(
                    1, 100, trackidList, new Erp990OnError()
                );

                if (response == null || CollectionUtils.isEmpty(response.getRows())) {
                    log.info("[{}]第{}页ERP订单接口返回无数据", username, pageNum);
                    pageNum++;
                    continue;
                }

                // 更新订单打包信息
                List<ErpOrder> ordersToUpdate = BeanUtil.copyToList(response.getRows(), ErpOrder.class);

                log.info("[{}]第{}页ERP接口返回{}条打包信息", username, pageNum, response.getRows().size());

                // 批量更新到数据库
                if (!CollectionUtils.isEmpty(ordersToUpdate)) {
                    this.erpOrderService.updateBatchById(ordersToUpdate);
                    totalSynced += ordersToUpdate.size();
                    log.info("[{}]第{}页成功更新{}条订单打包信息", username, pageNum, ordersToUpdate.size());
                } else {
                    log.info("[{}]第{}页无可更新的订单打包信息", username, pageNum);
                }

                pageNum++;

                // 防止无限循环，设置最大页数限制
                if (pageNum > 1000) {
                    log.warn("[{}]达到最大页数限制，停止同步", username);
                    break;
                }
            }

        } catch (Exception e) {
            log.error("[{}]同步打包信息失败", username, e);
            throw e;
        }

        log.info("[{}]打包信息同步完成，总计更新{}条订单", username, totalSynced);
	}

}