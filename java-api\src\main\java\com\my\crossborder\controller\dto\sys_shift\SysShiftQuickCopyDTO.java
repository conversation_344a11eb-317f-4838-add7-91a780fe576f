package com.my.crossborder.controller.dto.sys_shift;

import java.io.Serializable;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 快速复制排班DTO
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftQuickCopyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 源日期（要复制的排班日期）
     */
    @NotNull(message = "源日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate sourceDate;

}
