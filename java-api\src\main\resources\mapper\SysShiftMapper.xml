<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysShiftMapper">


    <!-- 联表查询结果列 -->
    <sql id="Enhanced_Column_List">
	    tShift.shift_day AS date,
	    tShift.shift_day AS shiftDay,
	    tShift.shop_id AS shopId,
	    tShop.shop_name AS shopName,
	    tShopDay.shop_count AS shopCount,
	    tShift.service_user_id AS serviceUserId,
	    tUserService.real_name AS serviceUserName,
	    tShift.supervisor_user_id AS supervisorUserId,
	    tUserSupervisor.real_name AS supervisorUserName,
	    tAttendanceService.clock_status AS serviceShiftStatus,
	    tAttendanceSupervisor.clock_status AS supervisorShiftStatus,
	    tAttendanceService.clock_time AS serviceClockTime,
	    tAttendanceSupervisor.clock_time AS supervisorClockTime,
	    tShift.create_time AS createTime,
	    tShift.update_time AS updateTime
    </sql>

    <!-- 联表查询FROM子句 -->
    <sql id="Enhanced_From_Tables">
	    FROM sys_shift tShift
	    LEFT JOIN sys_shift_day tShopDay ON tShift.shift_day = tShopDay.shift_day
		LEFT JOIN sys_shop tShop ON tShift.shop_id = tShop.id
		LEFT JOIN sys_user tUserService ON tShift.service_user_id = tUserService.user_id
		LEFT JOIN sys_user tUserSupervisor ON tShift.supervisor_user_id = tUserSupervisor.user_id
		LEFT JOIN sys_shift_attendance tAttendanceService ON tShift.shift_day = tAttendanceService.shift_day  AND tShift.service_user_id = tAttendanceService.user_id
		LEFT JOIN sys_shift_attendance tAttendanceSupervisor ON tShift.shift_day = tAttendanceSupervisor.shift_day AND tShift.supervisor_user_id = tAttendanceSupervisor.user_id
		LEFT JOIN sys_shift_day tShiftDay ON tShift.shift_day = tShiftDay.shift_day
    </sql>

	<!-- 分页查询（增强版，包含联表查询） -->
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_shift.SysShiftPageVO">
		SELECT
			<include refid="Enhanced_Column_List" />
		    <include refid="Enhanced_From_Tables" />
		<where>
	        <if test="shiftDay != null and shiftDay != ''">
	           	AND tShift.shift_day = #{shiftDay}
            </if>
	        <if test="shopId != null and shopId != ''">
	           	AND tShift.shop_id = #{shopId}
            </if>
	        <if test="serviceUserId != null and serviceUserId != ''">
	           	AND tShift.service_user_id = #{serviceUserId}
            </if>
	        <if test="serviceClockTime != null and serviceClockTime != ''">
	           	AND tShift.service_clock_time = #{serviceClockTime}
            </if>
	        <!-- <if test="serviceShiftStatus != null and serviceShiftStatus != ''">
	           	AND tShift.service_shift_status = #{serviceShiftStatus}
            </if> -->
	        <if test="supervisorUserId != null and supervisorUserId != ''">
	           	AND tShift.supervisor_user_id = #{supervisorUserId}
            </if>
	        <if test="supervisorClockTime != null and supervisorClockTime != ''">
	           	AND tShift.supervisor_clock_time = #{supervisorClockTime}
            </if>
	        <!-- <if test="supervisorShiftStatus != null and supervisorShiftStatus != ''">
	           	AND tShift.supervisor_shift_status = #{supervisorShiftStatus}
            </if> -->
	        <if test="createTime != null and createTime != ''">
	           	AND tShift.create_time = #{createTime}
            </if>
	        <if test="updateTime != null and updateTime != ''">
	           	AND tShift.update_time = #{updateTime}
            </if>
            <!-- 新增日期范围查询条件 -->
            <if test="startDate != null and startDate != ''">
                AND tShift.shift_day &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND tShift.shift_day &lt;= #{endDate}
            </if>
        </where>
        ORDER BY tShift.shift_day DESC, tShop.shop_name ASC
    </select>

    <!-- 按排班日期查询 -->
    <select id="listByShiftDay" resultType="com.my.crossborder.controller.vo.sys_shift.SysShiftPageVO">
        SELECT
            <include refid="Enhanced_Column_List" />
        <include refid="Enhanced_From_Tables" />
        WHERE tShift.shift_day = #{shiftDay}
        ORDER BY tShop.shop_name ASC
    </select>

    <!-- 按日期范围查询 -->
    <select id="listByDateRange" resultType="com.my.crossborder.controller.vo.sys_shift.SysShiftPageVO">
        SELECT
            <include refid="Enhanced_Column_List" />
        <include refid="Enhanced_From_Tables" />
        <where> 
	        <if test="startDate != null and startDate != ''">
	            AND tShift.shift_day &gt;= #{startDate}
	        </if>
	        <if test="endDate != null and endDate != ''">
	            AND tShift.shift_day &lt;= #{endDate}
	        </if>
        </where>
        ORDER BY tShift.shift_day DESC, tShop.shop_name ASC
    </select>


</mapper>
