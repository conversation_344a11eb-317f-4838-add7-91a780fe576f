package com.my.crossborder.controller.dto.erp_order;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.erp_order.ErpOrderStatusSummaryPageVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页_订单状态汇总
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ErpOrderStatusSummaryPageDTO
						extends PageDTO<ErpOrderStatusSummaryPageVO>
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺ID列表
     */
    private List<Integer> shopIds;

    /**
     * 订单号
     */
    private String orderSn;

}
