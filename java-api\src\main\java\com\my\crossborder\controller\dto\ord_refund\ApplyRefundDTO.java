package com.my.crossborder.controller.dto.ord_refund;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 申请退款DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ApplyRefundDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private String orderSn;

    /**
     * 申请退款金额
     */
    @NotNull(message = "申请退款金额不能为空")
    @DecimalMin(value = "0.01", message = "申请退款金额必须大于0")
    private BigDecimal applyAmount;

}
