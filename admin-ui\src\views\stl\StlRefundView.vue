<template>
  <el-dialog
    :title="'退款详情 (' + refundData.dateRange + ')'"
    :visible.sync="visible"
    width="1200px"
    top="5vh"
    :before-close="handleClose"
    @close="handleClose">

    <!-- <div class="refund-detail-header" v-if="refundData">
       <h3> ({{ refundData.dateRange }}) 退款列表</h3> 
      <div class="total-amount-section">
        <span class="total-label">退款总金额：</span>
        <span class="total-amount">￥{{ totalAmount.toFixed(2) }}</span>
      </div>
    </div> -->

    <!-- 数据表格 -->
    <el-table
      ref="table"
      :data="tableData"
      border
      style="width: 100%; margin-top: 10px;"
      v-loading="loading"
      :row-key="getRowKey"
      :expand-row-keys="expandedRows"
      @expand-change="handleExpandChange">

      <!-- 展开行 -->
      <el-table-column type="expand">
        <template slot-scope="scope">
          <div class="order-items-section">
            <div v-if="scope.row.itemsLoading" style="text-align: center; padding: 20px;">
              <i class="el-icon-loading"></i> 加载中...
            </div>
            <el-table v-else :data="scope.row.orderItems" border style="width: 100%;">
              <el-table-column label="产品图片" width="150" align="center">
                <template slot-scope="item">
                  <img :src="item.row.itemImage || '/static/img/default-product.png'"
                    style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;"
                    @error="handleImageError" />
                </template>
              </el-table-column>
              <el-table-column prop="itemName" label="产品名称" min-width="200" show-overflow-tooltip>
                <template slot-scope="item">
                  <span class="item-name clickable-item-name"
                        @click="copyItemName(item.row.itemName)"
                        :title="'点击复制商品名称: ' + item.row.itemName">
                    {{ item.row.itemName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="itemModelName" label="规格" min-width="150" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="amount" label="数量" width="90" align="center">
              </el-table-column>
              <el-table-column prop="itemPrice" label="单价" width="100" align="center">
                <template slot-scope="item">
                  ￥{{ item.row.itemPrice ? item.row.itemPrice.toFixed(2) : '0.00' }}
                </template>
              </el-table-column>
              <el-table-column prop="expressNo" label="快递编号" min-width="150" align="center">
                <template slot-scope="item">
                  <span v-if="item.row.expressNo" class="express-no clickable-express"
                    @click="copyExpressNo(item.row.expressNo)" :title="'点击复制快递号: ' + item.row.expressNo">
                    {{ item.row.expressNo }}
                  </span>
                  <span v-else class="no-express">未填写</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="shopName" label="店铺名称" width="110" align="center" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="orderSn" label="订单号" width="160" align="center">
        <template slot-scope="scope">
          <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
            @click="copyOrderSn(scope.row.orderSn)"
            :title="'点击复制订单号'">
            {{ scope.row.orderSn }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="下单时间" width="140" align="center">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column prop="totalPrice" label="订单总金额" width="110" align="center">
        <template slot-scope="scope">
          <span style="">
            {{ scope.row.totalPrice ? scope.row.totalPrice.toFixed(2) : '0.00' }}
          </span>
        </template>
      </el-table-column>

      <!-- <dict-table-column prop="applyStatus" label="处理状态" align="center" width="120" category-id="REFUND_APPLY_STATUS">
      </dict-table-column> -->

      <el-table-column prop="applyDetail" label="处理详情" width="160" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.applyStatus === '1'" style="text-align: center; line-height: 1.6;">
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <div>申请金额：{{ formatAmount(scope.row.applyAmount) }}</div>
                <div>填写时间：{{ formatDate(scope.row.applyTime) }}</div>
                <div>填写人：{{ scope.row.applyUserName }}</div>
              </div>
              <div style="cursor: pointer; ">
                申请金额： ￥{{ scope.row.applyAmount ? scope.row.applyAmount.toFixed(2) : '0.00' }}
              </div>
            </el-tooltip>
          </div>
          <span v-else style="color: #909399;">-</span>
        </template>
      </el-table-column>

      <!-- <dict-table-column prop="resultStatus" label="处理结果" align="center" width="100" category-id="REFUND_RESULT_STATUS">
      </dict-table-column> -->

      <el-table-column prop="resultDetail" label="结果详情" width="160" align="center">
        <template slot-scope="scope">
          <!-- 退款成功 -->
          <div v-if="scope.row.resultStatus === '1'" style="text-align: center; line-height: 1.6;">
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <div>填写时间：{{ formatDate(scope.row.resultTime) }}</div>
                <div>填写人：{{ scope.row.resultUserName }}</div>
              </div>
              <div>
                成功退款： <span style="color: #409EFF; font-weight: bold; cursor: pointer;">￥{{ scope.row.refundSuccessAmount ? scope.row.refundSuccessAmount.toFixed(2) : '0.00' }} </span>
              </div>
            </el-tooltip>
          </div>
          <!-- 退款失败 -->
          <div v-else-if="scope.row.resultStatus === '2'" style="text-align: center; line-height: 1.6;">
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <div>填写时间：{{ formatDate(scope.row.resultTime) }}</div>
                <div>填写人：{{ scope.row.resultUserName }}</div>
              </div>
              <div style="color: #F56C6C; font-weight: bold; cursor: pointer;">
                {{ scope.row.refundFailReason || '退款失败' }}
              </div>
            </el-tooltip>
          </div>
          <!-- 已入库 -->
          <div v-else-if="scope.row.resultStatus === '3'" style="text-align: center; line-height: 1.6;">
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <div>确认时间：{{ formatDate(scope.row.resultTime) }}</div>
                <div>确认人：{{ scope.row.resultUserName }}</div>
              </div>
              <div style="color: #4CA0FF; font-weight: bold; cursor: pointer;">已入库</div>
            </el-tooltip>
          </div>
          <!-- 无结果状态 -->
          <span v-else style="color: #909399;">-</span>
        </template>
      </el-table-column>

      <!-- 备注列 -->
      <NoteColumn :current-scene="currentScene" width="80" />

      <!-- 结算状态 -->
      <el-table-column prop="settlementFlag" label="结算详情" min-width="80" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.resultStatus == null">
            -
          </span>
          <span v-else-if="scope.row.settlementFlag === '-1'">
            <el-tag type="info">无需结算</el-tag>
          </span>
          <span v-else-if="scope.row.settlementFlag === '0'">
            <el-tag type="warning">待结算</el-tag>
          </span>
          <span v-else-if="scope.row.settlementFlag === '1'">
            <el-tag type="success">已结算</el-tag>
          </span>
          <span v-else>
            <el-tag type="info">无需结算</el-tag>
          </span>
        </template>
      </el-table-column>

    </el-table>

    <!-- 总记录数显示 -->
    <div class="total-records">
      共 {{ pageParam.total }} 条记录， 总金额：<span class="total-amount">￥{{ totalAmount.toFixed(2) }}</span>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getOrdRefundPage } from '../../api/OrdRefund'
import DictTableColumn from '../../components/DictTableColumn'
import NoteColumn from '../../components/NoteColumn'
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'StlRefundView',
  mixins: [copyMixin],
  components: {
    DictTableColumn,
    NoteColumn
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    refundData: {
      type: Object,
      default: () => ({
        week: 1,
        dateRange: '',
        amount: 0,
        userId: null,
        year: null,
        month: null
      })
    }
  },
  data() {
    return {
      tableData: [],
      loading: false,
      totalAmount: 0,
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        applyUserId: null,
        resultStatus: '1' // 只查询退款成功的记录
      },
      currentScene: '05',  // 已采购但出库前取消
      expandedRows: []  // 展开的行
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadRefundData()
      } else {
        this.resetData()
      }
    }
  },
  methods: {
    async loadRefundData() {
      if (!this.refundData.userId || !this.refundData.year || !this.refundData.month || !this.refundData.week) {
        return
      }

      this.loading = true
      try {
        const params = {
          ...this.formInline,
          applyUserId: this.refundData.userId
        }

        const response = await getOrdRefundPage(params)
        if (response.success && response.data) {
          // 获取所有数据后，在前端过滤该周的数据
          const allData = response.data.records || []
          this.tableData = this.filterDataByWeek(allData)
          this.pageParam.total = this.tableData.length
          this.pageParam.currentPage = 1
          this.pageParam.pageSize = 10

          // 计算总金额
          this.calculateTotalAmount()
        } else {
          this.$message.error('获取退款数据失败')
        }
      } catch (error) {
        console.error('加载退款数据失败:', error)
        this.$message.error('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },

    calculateWeekDates() {
      // 根据年、月、周计算该周的开始和结束日期
      const year = this.refundData.year
      const month = this.refundData.month
      const week = this.refundData.week

      // 获取该月第一天
      const firstDay = new Date(year, month - 1, 1)
      const firstDayOfWeek = firstDay.getDay() // 0是周日，1是周一

      // 计算第一周的开始日期（从周一开始）
      const firstMondayDate = new Date(firstDay)
      if (firstDayOfWeek === 0) {
        // 如果第一天是周日，则往前推6天到周一
        firstMondayDate.setDate(firstDay.getDate() - 6)
      } else if (firstDayOfWeek !== 1) {
        // 如果第一天不是周一，则往前推到周一
        firstMondayDate.setDate(firstDay.getDate() - (firstDayOfWeek - 1))
      }

      // 计算指定周的开始日期
      const weekStartDate = new Date(firstMondayDate)
      weekStartDate.setDate(firstMondayDate.getDate() + (week - 1) * 7)

      // 计算指定周的结束日期
      const weekEndDate = new Date(weekStartDate)
      weekEndDate.setDate(weekStartDate.getDate() + 6)
      weekEndDate.setHours(23, 59, 59, 999)

      return {
        startDate: this.formatDateForAPI(weekStartDate),
        endDate: this.formatDateForAPI(weekEndDate)
      }
    },

    formatDateForAPI(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    filterDataByWeek(allData) {
      if (!allData || allData.length === 0) return []

      // 计算该周的开始和结束日期
      const { startDate, endDate } = this.calculateWeekDates()
      const startTime = new Date(startDate).getTime()
      const endTime = new Date(endDate).getTime()

      return allData.filter(item => {
        if (!item.resultTime) return false
        const resultTime = new Date(item.resultTime).getTime()
        return resultTime >= startTime && resultTime <= endTime
      })
    },

    calculateTotalAmount() {
      this.totalAmount = this.tableData.reduce((total, item) => {
        return total + (item.refundSuccessAmount || 0)
      }, 0)
    },

    resetData() {
      this.tableData = []
      this.totalAmount = 0
      this.pageParam = {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
      this.formInline.current = 1
    },



    // 格式化金额，添加千分位逗号
    formatAmount(amount) {
      if (!amount && amount !== 0) return '-'
      const num = parseFloat(amount)
      if (isNaN(num)) return '-'
      return '￥ ' + num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      const dateObj = new Date(date)
      const year = dateObj.getFullYear()
      const month = String(dateObj.getMonth() + 1).padStart(2, '0')
      const day = String(dateObj.getDate()).padStart(2, '0')
      const hours = String(dateObj.getHours()).padStart(2, '0')
      const minutes = String(dateObj.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 获取行key
    getRowKey(row) {
      return row.orderSn
    },

    // 展开行变化
    handleExpandChange(row, expandedRows) {
      this.expandedRows = expandedRows.map(r => r.orderSn)
      // 如果行被展开且还没有加载订单子项
      if (expandedRows.includes(row) && !row.orderItems && !row.itemsLoading) {
        this.loadOrderItems(row)
      }
    },



    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyTextToClipboard(text)
        })
      } else {
        this.fallbackCopyTextToClipboard(text)
      }
    },

    // 兼容性复制方法
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    },

    // 复制快递号
    copyExpressNo(expressNo) {
      this.copyToClipboard(expressNo)
    },

    // 复制订单号
    copyOrderSn(orderSn) {
      this.copyToClipboard(orderSn)
    },

    // 加载订单项
    async loadOrderItems(row) {
      row.itemsLoading = true
      this.$set(row, 'itemsLoading', true)

      try {
        // 这里应该调用API获取订单项，暂时使用模拟数据
        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟

        // 模拟订单项数据
        const mockOrderItems = [
          {
            itemName: '示例商品1',
            itemModelName: '红色/L',
            amount: 2,
            itemPrice: 99.00,
            itemImage: '/static/img/default-product.png',
            expressNo: 'SF1234567890'
          },
          {
            itemName: '示例商品2',
            itemModelName: '蓝色/M',
            amount: 1,
            itemPrice: 159.00,
            itemImage: '/static/img/default-product.png',
            expressNo: ''
          }
        ]

        this.$set(row, 'orderItems', mockOrderItems)
      } catch (error) {
        console.error('加载订单项失败:', error)
        this.$message.error('加载订单明细失败')
      } finally {
        this.$set(row, 'itemsLoading', false)
      }
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png'
    }
  }
}
</script>

<style scoped>
.refund-detail-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.refund-detail-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.total-amount-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.total-label {
  color: #666;
  font-size: 14px;
}

.total-amount {
  color: #409EFF;
  font-weight: bold;
  font-size: 18px;
}

.dialog-footer {
  text-align: center;
}

.total-records {
  text-align: center;
  margin: 15px 0;
  color: #666;
  font-size: 14px;
}

/* 订单项展开区域样式 */
.order-items-section {
  padding: 10px 20px;
  background-color: #fafafa;
  border-radius: 4px;
  margin: 10px 0;
}

.order-items-section .el-table {
  margin-top: 10px;
}

.order-items-section .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

/* 快递号样式 */
.express-no {
  color: #409EFF;
  cursor: pointer;
  text-decoration: underline;
}

.express-no:hover {
  color: #66b1ff;
}

.no-express {
  color: #c0c4cc;
  font-style: italic;
}

.clickable-express {
  transition: color 0.3s;
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>
