package com.my.crossborder.mybatis.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 订单项表
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("erp_order_item")
public class ErpOrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 订单ID，关联erp_order.order_id
     */
    private String orderId;

    /**
     * 商品索引，同一订单中商品的序号
     */
    private Integer productIdx;

    /**
     * 商品ID
     */
    private String itemId;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品图片URL
     */
    private String itemImage;

    /**
     * 商品价格
     */
    private BigDecimal itemPrice;

    /**
     * 订单中该商品价格
     */
    private BigDecimal orderPrice;

    /**
     * 商品数量
     */
    private Integer amount;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 规格ID
     */
    private String modelId;

    /**
     * 商品规格名称
     */
    private String itemModelName;

    /**
     * 商品规格SKU
     */
    private String itemModelSku;

    /**
     * 商品SKU
     */
    private String productSku;

    /**
     * 优惠前价格
     */
    private BigDecimal priceBeforeBundle;

    /**
     * 折扣前价格
     */
    private BigDecimal priceBeforeDiscount;

    /**
     * 首个商品数量
     */
    private Integer firstItemCount;

    /**
     * 首个商品是否批发
     */
    private String firstItemIsWholesale;

    /**
     * 首个商品型号
     */
    private String firstItemModel;

    /**
     * 首个商品名称
     */
    private String firstItemName;

    /**
     * 首个商品退货状态
     */
    private String firstItemReturn;

    /**
     * 商品状态
     */
    private String goodsStates;

    /**
     * 商品类型
     */
    private String goodsType;

    /**
     * 即时买家取消发货
     */
    private String instantBuyercancelToship;

    /**
     * 是否买家取消发货
     */
    private String isBuyercancelToship;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


}
