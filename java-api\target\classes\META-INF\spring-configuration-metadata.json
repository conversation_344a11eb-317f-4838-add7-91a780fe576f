{"groups": [{"name": "com.my.forest.erp990.address", "type": "com.my.crossborder.forest.erp990.address.Erp990Address", "sourceType": "com.my.crossborder.forest.erp990.address.Erp990Address"}, {"name": "com.my.forest.siliconflow.address", "type": "com.my.crossborder.forest.siliconflow.address.SiliconflowAddress", "sourceType": "com.my.crossborder.forest.siliconflow.address.SiliconflowAddress"}], "properties": [{"name": "com.my.forest.erp990.address.host", "type": "java.lang.String", "description": "IP", "sourceType": "com.my.crossborder.forest.erp990.address.Erp990Address"}, {"name": "com.my.forest.erp990.address.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "com.my.crossborder.forest.erp990.address.Erp990Address"}, {"name": "com.my.forest.erp990.address.scheme", "type": "java.lang.String", "description": "协议(http或https)", "sourceType": "com.my.crossborder.forest.erp990.address.Erp990Address"}, {"name": "com.my.forest.siliconflow.address.host", "type": "java.lang.String", "description": "IP", "sourceType": "com.my.crossborder.forest.siliconflow.address.SiliconflowAddress"}, {"name": "com.my.forest.siliconflow.address.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "com.my.crossborder.forest.siliconflow.address.SiliconflowAddress"}, {"name": "com.my.forest.siliconflow.address.scheme", "type": "java.lang.String", "description": "协议(http或https)", "sourceType": "com.my.crossborder.forest.siliconflow.address.SiliconflowAddress"}], "hints": []}