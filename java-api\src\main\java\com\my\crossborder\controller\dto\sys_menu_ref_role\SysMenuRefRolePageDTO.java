package com.my.crossborder.controller.dto.sys_menu_ref_role;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_menu_ref_role.SysMenuRefRolePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_菜单_角色_关联表
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenuRefRolePageDTO 
						extends PageDTO<SysMenuRefRolePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色id
     */
    private Integer roleId;

    /**
     * 菜单id
     */
    private String menuId;

}
