package com.my.crossborder.service;

import java.time.LocalDate;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.ord_refund.ApplyPutInDTO;
import com.my.crossborder.controller.dto.ord_refund.ApplyRefundDTO;
import com.my.crossborder.controller.dto.ord_refund.ConfirmPutInDTO;
import com.my.crossborder.controller.dto.ord_refund.OrdRefundDeleteDTO;
import com.my.crossborder.controller.dto.ord_refund.OrdRefundPageDTO;
import com.my.crossborder.controller.dto.ord_refund.RefundResultDTO;
import com.my.crossborder.controller.dto.ord_refund.WeeklySettlementStatsDTO;
import com.my.crossborder.controller.vo.ord_refund.OrdRefundPageVO;
import com.my.crossborder.controller.vo.ord_refund.WeeklySettlementStatsVO;
import com.my.crossborder.mybatis.entity.OrdRefund;

import java.util.List;

/**
 * 退款表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface OrdRefundService extends IService<OrdRefund> {

	/**
	 * 分页
	 */
	Page<OrdRefundPageVO> page(OrdRefundPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(OrdRefundDeleteDTO deleteDTO);

	/**
	 * 申请退款
	 */
	void applyRefund(ApplyRefundDTO applyRefundDTO);

	/**
	 * 申请入库
	 */
	void applyPutIn(ApplyPutInDTO applyPutInDTO);

	/**
	 * 退款结果
	 */
	void refundResult(RefundResultDTO refundResultDTO);

	/**
	 * 确认已入库
	 */
	void confirmPutIn(ConfirmPutInDTO confirmPutInDTO);

	/**
	 * 员工周结算统计
	 */
	List<WeeklySettlementStatsVO> getWeeklySettlementStats(WeeklySettlementStatsDTO dto);

	/**
	 * 结算完毕
	 * @param userId 用户ID
	 */
	void settlementDone(Integer userId, LocalDate dateStart, LocalDate dateEnd);

}
