<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdPurchaseCentralizedOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.OrdPurchaseCentralizedOrder">
        <id column="purchase_id" property="purchaseId" />
        <result column="order_sn" property="orderSn" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        purchase_id, order_sn
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderPageVO">
		SELECT
			purchase_id, order_sn
		FROM
			ord_purchase_centralized_order AS t1
		<where>
        	1=1
	        <if test="purchaseId != null and purchaseId != ''">
	           	AND t1.purchase_id = #{purchaseId}
            </if>
	        <if test="orderSn != null and orderSn != ''">
	           	AND t1.order_sn = #{orderSn}
            </if>
        </where>
    </select>

</mapper>
