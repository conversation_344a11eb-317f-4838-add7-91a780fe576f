package com.my.crossborder.controller.dto.sys_log;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_操作日志表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysLogDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<Long> idList;
	
}
