package com.my.crossborder.job;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class AttendanceJobTest {
	
	@Autowired
	AttendanceJob attendanceJob;

	
	@Test
	void testAutoAbsentStatus() {
		this.attendanceJob.autoAbsentStatus();
	}

}
