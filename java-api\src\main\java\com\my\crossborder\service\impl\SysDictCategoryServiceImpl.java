package com.my.crossborder.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryDeleteDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryInsertDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryPageDTO;
import com.my.crossborder.controller.dto.sys_dict_category.SysDictCategoryUpdateDTO;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryDetailVO;
import com.my.crossborder.controller.vo.sys_dict_category.SysDictCategoryPageVO;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.mybatis.entity.SysDictCategory;
import com.my.crossborder.mybatis.mapper.SysDictCategoryMapper;
import com.my.crossborder.service.SysDictCategoryService;
import com.my.crossborder.service.SysDictItemService;
import com.my.crossborder.util.ColumnLambda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 数据字典-类别表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
@RequiredArgsConstructor
public class SysDictCategoryServiceImpl extends ServiceImpl<SysDictCategoryMapper, SysDictCategory> implements SysDictCategoryService {

	private final SysDictItemService sysDictItemService;
	

	@Transactional
	@Override
	public void insert(SysDictCategoryInsertDTO insertDTO) {
		SysDictCategory entity = BeanUtil.copyProperties(insertDTO, SysDictCategory.class);
		// 处理categoryId：如果前端传入了则使用并转大写，否则自动生成
		if (entity.getCategoryId() != null && !entity.getCategoryId().trim().isEmpty()) {
			entity.setCategoryId(entity.getCategoryId().toUpperCase());
		} else {
			String categoryId = IdUtil.getSnowflakeNextIdStr();
			entity.setCategoryId(categoryId.toUpperCase());
		}
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(SysDictCategoryUpdateDTO updateDTO) {
		String categoryId = updateDTO.getCategoryId();
		String newCategoryId = updateDTO.getNewCategoryId();
		String categoryName = updateDTO.getCategoryName();
		String description = updateDTO.getDescription();
		// 确保categoryId大写
		newCategoryId = !StrUtil.isEmpty(newCategoryId) ? newCategoryId.toUpperCase() : newCategoryId.toUpperCase();
		Wrapper<SysDictCategory> wrapper = Wrappers.lambdaUpdate(SysDictCategory.class)
			.set(!StrUtil.isEmpty(newCategoryId), SysDictCategory::getCategoryId, newCategoryId)
			.set(!StrUtil.isEmpty(updateDTO.getCategoryName()), SysDictCategory::getCategoryName, categoryName)
			.set(!StrUtil.isEmpty(updateDTO.getDescription()), SysDictCategory::getDescription, description)
			.eq(SysDictCategory::getCategoryId, categoryId);
		this.update(wrapper);
		
		// 同时更新子表
		this.sysDictItemService.updateCategory(categoryId, newCategoryId);
	}

	@Override
	public SysDictCategoryDetailVO detail(String id) {
		SysDictCategory entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SysDictCategoryDetailVO.class);
	}

	@Override
	public List<SysDictCategoryPageVO> listAll() {
		List<SysDictCategory> entities = this.list();
		return entities.stream()
				.map(entity -> BeanUtil.copyProperties(entity, SysDictCategoryPageVO.class))
				.collect(Collectors.toList());
	}

	@Override
	public Page<SysDictCategoryPageVO> page(SysDictCategoryPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<SysDictCategory>().columnsToString(SysDictCategory::getCategoryId)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysDictCategoryDeleteDTO deleteDTO) {
		int itemCount = this.sysDictItemService.listByCategoryId(deleteDTO.getIdList().get(0)).size();
		BusinessException.when(itemCount > 0, "删除失败：包含字典项");
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
