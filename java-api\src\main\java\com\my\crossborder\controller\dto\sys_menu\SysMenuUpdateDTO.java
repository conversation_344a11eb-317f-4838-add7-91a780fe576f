package com.my.crossborder.controller.dto.sys_menu;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_系统菜单表
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenuUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID（主键）
     */
	@NotNull(message="id不能为空")
    private String id;

    /**
     * 菜单名称
     */
	@NotNull(message="menuName不能为空")
    private String menuName;

    /**
     * 父级菜单ID（0表示一级菜单）
     */
    private String parentId;

    /**
     * 菜单类型：1-菜单 0-按钮
     */
	@NotNull(message="menu不能为空")
    private Boolean menu;

    /**
     * 路由路径（前端跳转用，按钮可空）
     */
    private String routePath;

    /**
     * 权限标识（按钮必填，如 dict:add）
     */
    private String permission;

    /**
     * 排序值（同层级排序）
     */
	@NotNull(message="sortNum不能为空")
    private Integer sortNum;

    /**
     * 状态：1-启用 0-禁用
     */
	@NotNull(message="enable不能为空")
    private Boolean enable;

    /**
     * 菜单图标（可选，前端展示用）
     */
    private String icon;

}
