package com.my.crossborder.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemDeleteDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemInsertDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemPageDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemUpdateDTO;
import com.my.crossborder.controller.vo.sys_dict_item.SysDictItemDetailVO;
import com.my.crossborder.controller.vo.sys_dict_item.SysDictItemPageVO;
import com.my.crossborder.mybatis.entity.SysDictItem;

/**
 * 数据字典-字典项表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface SysDictItemService extends IService<SysDictItem> {

	/**
	 * 新增
	 */
	void insert(SysDictItemInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysDictItemUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param categoryId 字典类别id
	 * @param itemValue 字典项值
	 */
	SysDictItemDetailVO detail(String categoryId, String itemValue);

	/**
	 * 分页
	 */
	Page<SysDictItemPageVO> page(SysDictItemPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysDictItemDeleteDTO deleteDTO);

	/**
	 * 查询字典项
	 * @param categoryId
	 * @return
	 */
	List<SysDictItem> listByCategoryId(String categoryId);

	/**
	 * 更新类别id
	 * @param categoryId
	 * @param newCategoryId
	 */
	void updateCategory(String categoryId, String newCategoryId);

	/**
	 * 按类别和值查询
	 * @param categoryId
	 * @param itemValue
	 * @return
	 */
	SysDictItem get(String categoryId, String itemValue);

}
