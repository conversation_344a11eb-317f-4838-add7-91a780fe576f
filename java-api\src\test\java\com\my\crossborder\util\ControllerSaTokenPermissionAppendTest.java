package com.my.crossborder.util;

import java.io.IOException;

import org.junit.jupiter.api.Test;

import com.my.crossborder.tmp.ControllerSaTokenPermissionAppend;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ControllerSaTokenPermissionAppendTest {

	/**
	 * 给所有controller的方法添加权限
	 */
	@Test
	void testProcessControllers() {
		try {
			ControllerSaTokenPermissionAppend.processControllers();
            log.info("SaToken权限注解添加完成！");
        } catch (IOException e) {
            log.error("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
	}

}
