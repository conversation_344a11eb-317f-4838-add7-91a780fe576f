package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.WkbTag;
import com.my.crossborder.mybatis.mapper.WkbTagMapper;
import com.my.crossborder.service.WkbTagService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagInsertDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagPageDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagUpdateDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagDeleteDTO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagDetailVO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工作台_订单标签 服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class WkbTagServiceImpl extends ServiceImpl<WkbTagMapper, WkbTag> implements WkbTagService {


	@Transactional
	@Override
	public void insert(WkbTagInsertDTO insertDTO) {
		WkbTag entity = BeanUtil.copyProperties(insertDTO, WkbTag.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(WkbTagUpdateDTO updateDTO) {
		WkbTag entity = BeanUtil.copyProperties(updateDTO, WkbTag.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public WkbTagDetailVO detail(Integer id) {
		WkbTag entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, WkbTagDetailVO.class);
	}

	@Override
	public Page<WkbTagPageVO> page(WkbTagPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(WkbTagDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
