package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.WkbTag;
import com.my.crossborder.mybatis.mapper.WkbTagMapper;
import com.my.crossborder.service.WkbTagService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagInsertDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagPageDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagUpdateDTO;
import com.my.crossborder.controller.dto.wkb_tag.WkbTagDeleteDTO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagDetailVO;
import com.my.crossborder.controller.vo.wkb_tag.WkbTagPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 工作台_订单标签 服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class WkbTagServiceImpl extends ServiceImpl<WkbTagMapper, WkbTag> implements WkbTagService {


	@Transactional
	@Override
	public void insert(WkbTagInsertDTO insertDTO) {
		WkbTag entity = BeanUtil.copyProperties(insertDTO, WkbTag.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(WkbTagUpdateDTO updateDTO) {
		WkbTag entity = BeanUtil.copyProperties(updateDTO, WkbTag.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public WkbTagDetailVO detail(Integer id) {
		WkbTag entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, WkbTagDetailVO.class);
	}

	@Override
	public Page<WkbTagPageVO> page(WkbTagPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(WkbTagDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Transactional
	@Override
	public void updateOrderTags(String orderSn, String scene, List<String> tagList) {
		// 1. 查询当前订单的所有标签
		LambdaQueryWrapper<WkbTag> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(WkbTag::getOrderSn, orderSn)
					.eq(WkbTag::getScene, scene);
		List<WkbTag> existingTags = this.baseMapper.selectList(queryWrapper);

		// 2. 获取现有标签值集合
		Set<String> existingTagValues = existingTags.stream()
				.map(WkbTag::getTag)
				.collect(Collectors.toSet());

		// 3. 找出需要删除的标签（现有的但不在新列表中的）
		List<Integer> tagsToDelete = existingTags.stream()
				.filter(tag -> !tagList.contains(tag.getTag()))
				.map(WkbTag::getId)
				.collect(Collectors.toList());

		// 4. 找出需要添加的标签（新列表中但不在现有的）
		List<String> tagsToAdd = tagList.stream()
				.filter(tag -> !existingTagValues.contains(tag))
				.collect(Collectors.toList());

		// 5. 删除不需要的标签
		if (!tagsToDelete.isEmpty()) {
			this.baseMapper.deleteBatchIds(tagsToDelete);
		}

		// 6. 添加新标签
		for (String tagValue : tagsToAdd) {
			WkbTag newTag = new WkbTag();
			newTag.setOrderSn(orderSn);
			newTag.setScene(scene);
			newTag.setTag(tagValue);
			this.baseMapper.insert(newTag);
		}
	}
}
