import { getByOrderSnAndScene } from '../api/WkbNote'
import OrderNotesDrawer from '../views/order/OrderNotesDrawer'
import EditNoteDialog from '../components/EditNoteDialog'

/**
 * 备注抽屉功能Mixin
 * 提供统一的备注抽屉功能，包括打开抽屉、关闭抽屉、处理备注保存和更新事件
 */
export default {
  components: {
    OrderNotesDrawer,
    EditNoteDialog
  },
  
  data() {
    return {
      // 备注弹窗
      noteDialogVisible: false,
      currentOrderSn: '',
      currentOrder: null,

      // 备注抽屉
      notesDrawerVisible: false,
      currentOrderNotes: []
    }
  },

  methods: {
    /**
     * 打开备注抽屉
     * @param {Object} row - 订单行数据
     */
    openNotesDrawer(row) {
      this.currentOrder = row
      this.currentOrderSn = row.orderSn
      this.currentOrderNotes = row.notes || []
      this.notesDrawerVisible = true
    },

    /**
     * 关闭备注抽屉
     */
    handleDrawerClose() {
      this.notesDrawerVisible = false
      this.currentOrderNotes = []
    },

    /**
     * 处理备注保存成功事件
     * @param {Object} noteData - 保存的备注数据
     */
    handleNoteSaved(noteData) {
      console.log('备注已保存:', noteData)

      // 如果有当前订单对象，更新其备注数据
      if (this.currentOrder) {
        // 查询该订单的指定场景的备注
        getByOrderSnAndScene({
          orderSn: this.currentOrder.orderSn,
          scene: this.currentScene
        }).then(res => {
          if (res.success && res.data) {
            // 初始化notes数组（如果不存在）
            if (!this.currentOrder.notes) {
              this.currentOrder.notes = [];
            }

            // 查找是否已有相同场景的备注
            const existingNoteIndex = this.currentOrder.notes.findIndex(note => note.scene === this.currentScene);
            if (existingNoteIndex !== -1) {
              // 替换已有的备注
              this.currentOrder.notes[existingNoteIndex] = res.data;
            } else {
              // 添加新的备注
              this.currentOrder.notes.push(res.data);
            }

            // 强制刷新视图
            this.$forceUpdate();
          }
        });
      }

      // 刷新列表数据（可选，取决于业务需求）
      setTimeout(() => {
        this.getPageData()
      }, 500)
    },

    /**
     * 处理备注更新事件（从OrderNotes组件触发）
     */
    handleNoteUpdated() {
      console.log('备注已更新，刷新页面数据')

      // 刷新列表数据
      setTimeout(() => {
        this.getPageData()

        // 如果抽屉是打开状态，在数据刷新后更新抽屉中的备注数据
        if (this.notesDrawerVisible && this.currentOrder && this.currentOrderSn) {
          // 从刷新后的表格数据中找到当前订单并更新备注
          const updatedOrder = this.tableData.find(order => order.orderSn === this.currentOrderSn)
          if (updatedOrder) {
            this.currentOrder = updatedOrder
            this.currentOrderNotes = updatedOrder.notes || []
          }
        }
      }, 500)
    },

    /**
     * 获取特定场景的备注
     * @param {Array} notes - 备注数组
     * @param {String} scene - 场景代码
     * @returns {Array} 过滤后的备注数组
     */
    getSceneNotes(notes, scene) {
      if (!notes || !Array.isArray(notes)) return []
      return notes.filter(note => note.scene === scene)
    }
  },

  /**
   * 生成备注抽屉模板
   * @param {String} sceneName - 场景名称，用于EditNoteDialog的sceneName属性
   * @returns {String} 备注抽屉的HTML模板
   */
  noteDrawerTemplate(sceneName = '订单备注') {
    return `
    <!-- 使用EditNoteDialog组件 -->
    <EditNoteDialog
      :visible.sync="noteDialogVisible"
      :orderSn="currentOrderSn"
      :scene="currentScene"
      sceneName="${sceneName}"
      @saved="handleNoteSaved" />

    <!-- 备注抽屉 -->
    <el-drawer
      title="订单备注"
      :visible.sync="notesDrawerVisible"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose">
      <div style="padding: 20px;">
        <OrderNotesDrawer
          :notes="currentOrderNotes"
          :orderSn="currentOrderSn"
          :currentScene="currentScene"
          :showDebug="false"
          @note-updated="handleNoteUpdated" />
      </div>
    </el-drawer>
    `
  }
}
