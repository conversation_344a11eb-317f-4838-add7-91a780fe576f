package com.my.crossborder.controller.dto.sys_shift;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 批量保存排班数据DTO
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftBatchSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排班日期
     */
    @NotNull(message = "排班日期不能为空")
    private LocalDate shiftDay;

    /**
     * 客服用户ID列表
     */
    @NotEmpty(message = "客服用户ID列表不能为空")
    private List<Integer> serviceUserIds;

    /**
     * 客服主管用户ID
     */
    @NotNull(message = "客服主管用户ID不能为空")
    private Integer supervisorUserId;

    /**
     * 备注
     */
    private String remark;

}
