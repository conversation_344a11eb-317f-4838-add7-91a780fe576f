<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysExchangeRateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysExchangeRate">
        <id column="day" property="day" />
        <result column="exchange_rate" property="exchangeRate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        day, exchange_rate
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_exchange_rate.SysExchangeRatePageVO">
		SELECT
			day, exchange_rate
		FROM
			sys_exchange_rate AS t1
		<where>
        	1=1
	        <if test="day != null and day != ''">
	           	AND t1.day = #{day}
            </if>
	        <if test="exchangeRate != null and exchangeRate != ''">
	           	AND t1.exchange_rate = #{exchangeRate}
            </if>
        </where>
    </select>

</mapper>
