package com.my.crossborder.controller;


import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingDeleteDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingInsertDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingPageDTO;
import com.my.crossborder.controller.dto.wkb_notification_reading.WkbNotificationReadingUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingDetailVO;
import com.my.crossborder.controller.vo.wkb_notification_reading.WkbNotificationReadingPageVO;
import com.my.crossborder.service.WkbNotificationReadingService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 通知阅读表 
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/api/wkb-notification-reading")
@RequiredArgsConstructor
public class WkbNotificationReadingController {

    private final WkbNotificationReadingService wkbNotificationReadingService;

    /**
    * 新增
    */
    @SaCheckPermission("wkb-notification:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody WkbNotificationReadingInsertDTO insertDTO) {
    	this.wkbNotificationReadingService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("wkb-notification:update")
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody WkbNotificationReadingUpdateDTO updateDTO) {
    	this.wkbNotificationReadingService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<WkbNotificationReadingDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.wkbNotificationReadingService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<WkbNotificationReadingPageVO>> page(WkbNotificationReadingPageDTO pageDTO) {
        Page<WkbNotificationReadingPageVO> page = this.wkbNotificationReadingService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
     * 所有未读
     */
    @GetMapping(value = "all-unread")
    public StdResp<List<WkbNotificationReadingPageVO>> allUnread() {
    	WkbNotificationReadingPageDTO pageDTO = new WkbNotificationReadingPageDTO(1L, 10000L);
    	pageDTO.setRead(Boolean.FALSE);
		Page<WkbNotificationReadingPageVO> page = this.wkbNotificationReadingService.page(pageDTO);
		List<WkbNotificationReadingPageVO> data = page.getRecords();
    	return StdResp.success(data);
    }
    
    /**
    * 删除我的已读
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody WkbNotificationReadingDeleteDTO deleteDTO) {
    	this.wkbNotificationReadingService.deleteMy(deleteDTO);
		return StdResp.success();
    }
    
}