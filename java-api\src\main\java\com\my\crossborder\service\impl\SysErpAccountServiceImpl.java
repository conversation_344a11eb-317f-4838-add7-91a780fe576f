package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.SysErpAccount;
import com.my.crossborder.mybatis.mapper.SysErpAccountMapper;
import com.my.crossborder.service.SysErpAccountService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountInsertDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountPageDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountUpdateDTO;
import com.my.crossborder.controller.dto.sys_erp_account.SysErpAccountDeleteDTO;
import com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountDetailVO;
import com.my.crossborder.controller.vo.sys_erp_account.SysErpAccountPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.my.crossborder.exception.BusinessException;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 禾宸物流接口账号 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class SysErpAccountServiceImpl extends ServiceImpl<SysErpAccountMapper, SysErpAccount> implements SysErpAccountService {


	@Transactional
	@Override
	public void insert(SysErpAccountInsertDTO insertDTO) {
		// 检查用户名是否已存在
		String username = insertDTO.getUsername();
		LambdaQueryWrapper<SysErpAccount> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(SysErpAccount::getUsername, username);
		boolean exists = this.baseMapper.selectCount(queryWrapper) > 0;
		BusinessException.when(exists, "用户名【{}】已存在", username);
		
		SysErpAccount entity = BeanUtil.copyProperties(insertDTO, SysErpAccount.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(SysErpAccountUpdateDTO updateDTO) {
		SysErpAccount entity = BeanUtil.copyProperties(updateDTO, SysErpAccount.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public SysErpAccountDetailVO detail(Integer id) {
		SysErpAccount entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SysErpAccountDetailVO.class);
	}

	@Override
	public Page<SysErpAccountPageVO> page(SysErpAccountPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysErpAccountDeleteDTO deleteDTO) {
		// 检查是否只剩余一条记录
		long totalCount = this.baseMapper.selectCount(null);
		BusinessException.when(totalCount <= 1, "至少保留一个账号");
		
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
