import { reqUpload, reqGet } from './axiosFun';

// 文件上传
export const sysAttachmentUpload = (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return reqUpload("/sys-attachment/upload", formData);
};

// 文件下载
export const sysAttachmentDownload = (attachmentId) => {
  window.open('/api/sys-attachment/download/' + attachmentId);
  // return reqGet("/sys-attachment/download/" + id );
};
