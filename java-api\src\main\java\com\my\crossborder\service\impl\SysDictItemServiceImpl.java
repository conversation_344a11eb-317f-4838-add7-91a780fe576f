package com.my.crossborder.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.cache.SysDictItemCache;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemDeleteDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemInsertDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemPageDTO;
import com.my.crossborder.controller.dto.sys_dict_item.SysDictItemUpdateDTO;
import com.my.crossborder.controller.vo.sys_dict_item.SysDictItemDetailVO;
import com.my.crossborder.controller.vo.sys_dict_item.SysDictItemPageVO;
import com.my.crossborder.mybatis.entity.SysDictItem;
import com.my.crossborder.mybatis.mapper.SysDictItemMapper;
import com.my.crossborder.service.SysDictItemService;
import com.my.crossborder.util.ColumnLambda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;

/**
 * 数据字典-字典项表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
@RequiredArgsConstructor
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements SysDictItemService {

	private final SysDictItemCache sysDictItemCache;


	@Transactional
	@Override
	public void insert(SysDictItemInsertDTO insertDTO) {
		SysDictItem entity = BeanUtil.copyProperties(insertDTO, SysDictItem.class);
		this.save(entity);
		// 重置缓存
		this.sysDictItemCache.reloadCache();
	}

	@Transactional
	@Override
	public void update(SysDictItemUpdateDTO updateDTO) {
		String categoryId = updateDTO.getCategoryId();
		String oldItemValue = updateDTO.getItemValue();
		String newItemValue = updateDTO.getNewItemValue();

		// 更新
		Wrapper<SysDictItem> wrapper = Wrappers.lambdaUpdate(SysDictItem.class)
				.set(SysDictItem::getItemName, updateDTO.getItemName())
				.set(SysDictItem::getColor, updateDTO.getColor())
				.set(SysDictItem::getSortNum, updateDTO.getSortNum())
				.set(SysDictItem::getItemValue, newItemValue)
				.eq(SysDictItem::getCategoryId, categoryId)
				.eq(SysDictItem::getItemValue, oldItemValue);
		this.update(wrapper);

		// 重置缓存
		this.sysDictItemCache.reloadCache();
	}

	@Override
	public SysDictItemDetailVO detail(String categoryId, String itemValue) {
		// 从缓存查询
		SysDictItem entity = this.sysDictItemCache.get(categoryId, itemValue);
		return BeanUtil.copyProperties(entity, SysDictItemDetailVO.class);
	}

	@Override
	public Page<SysDictItemPageVO> page(SysDictItemPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.asc(new ColumnLambda<SysDictItem>().columnsToString(SysDictItem::getSortNum)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysDictItemDeleteDTO deleteDTO) {
		// 使用联合主键删除
		for (SysDictItemDeleteDTO.CompositeKey key : deleteDTO.getKeyList()) {
			Wrapper<SysDictItem> wrapper = Wrappers.lambdaQuery(SysDictItem.class)
					.eq(SysDictItem::getCategoryId, key.getCategoryId())
					.eq(SysDictItem::getItemValue, key.getItemValue());
			this.baseMapper.delete(wrapper);
		}
		// 重置缓存
		this.sysDictItemCache.reloadCache();
	}

	@Override
	public List<SysDictItem> listByCategoryId(String categoryId) {
		// 从缓存查询
		return this.sysDictItemCache.listByCategoryId(categoryId);
	}

	@Override
	public void updateCategory(String categoryId, String newCategoryId) {
		Wrapper<SysDictItem> wrapper = Wrappers.lambdaUpdate(SysDictItem.class)
			.set(SysDictItem::getCategoryId, newCategoryId)
			.eq(SysDictItem::getCategoryId, categoryId);
		this.update(wrapper);
		// 重置缓存
		this.sysDictItemCache.reloadCache();
	}
	
	@Override
	public SysDictItem get(String categoryId, String itemValue) {
		return this.sysDictItemCache.get(categoryId, itemValue);
	}
}
