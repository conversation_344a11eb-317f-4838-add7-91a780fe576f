package com.my.crossborder.forest.erp990.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 订单分页查询返回对象
 * 用于跨境电商ERP系统的订单管理
 * 
 * <AUTHOR>
 * @date 2024
 */
@NoArgsConstructor @AllArgsConstructor @Builder @Data  @EqualsAndHashCode(callSuper=false) 
public class ErpOrderPageVO {

    /** 订单数据行列表 */
    @JSONField(name = "rows", ordinal = 3)
    List<List<Row>> rows;

    /** 总记录数 */
    @JSONField(name = "total", ordinal = 4)
    Integer total;

    
    // ======== 以下为内部类 =========
    
    /**
     * 查询参数类
     */
    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Params {
    	String nothing;
    }
    
    /**
     * 订单行数据
     * 包含订单基本信息、商品信息、物流信息等
     */
    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Row {

        /** 实际承运商 */
        @JSONField(name = "actualCarrier", ordinal = 1)
        String actualCarrier;

        /** 附加交易ID */
        @JSONField(name = "addOnDealId", ordinal = 2)
        String addOnDealId;

        /** 商品数量 */
        @JSONField(name = "amount", ordinal = 3)
        Integer amount;

        /** 申请邮寄标识 */
        @JSONField(name = "applyMailFlag", ordinal = 4)
        String applyMailFlag;

        /** 安排取件截止日期 */
        @JSONField(name = "arrangePickupByDate", ordinal = 5)
        String arrangePickupByDate;

        /** 自动取消3PL确认日期 */
        @JSONField(name = "autoCancel3plAckDate", ordinal = 6)
        String autoCancel3plAckDate;

        /** 自动取消安排发货日期 */
        @JSONField(name = "autoCancelArrangeShipDate", ordinal = 7)
        String autoCancelArrangeShipDate;

        /** 保险标识 */
        @JSONField(name = "baoFlag", ordinal = 8)
        String baoFlag;

        /** 买家地址姓名 */
        @JSONField(name = "buyerAddressName", ordinal = 9)
        String buyerAddressName;

        /** 买家地址电话 */
        @JSONField(name = "buyerAddressPhone", ordinal = 10)
        String buyerAddressPhone;

        /** 买家取消原因 */
        @JSONField(name = "buyerCancelReason", ordinal = 11)
        String buyerCancelReason;

        /** 买家是否已评价 */
        @JSONField(name = "buyerIsRated", ordinal = 12)
        String buyerIsRated;

        /** 买家最后修改地址时间 */
        @JSONField(name = "buyerLastChangeAddressTime", ordinal = 13)
        LocalDateTime buyerLastChangeAddressTime;

        /** 买家交易手续费 */
        @JSONField(name = "buyerTxnFee", ordinal = 14)
        BigDecimal buyerTxnFee;

        /** 取消原因扩展 */
        @JSONField(name = "cancelReasonExt", ordinal = 15)
        String cancelReasonExt;

        /** 取消用户ID */
        @JSONField(name = "cancelUserid", ordinal = 16)
        String cancelUserid;

        /** 包装箱尺寸 */
        @JSONField(name = "cartonSize", ordinal = 17)
        String cartonSize;

        /** 销售渠道 */
        @JSONField(name = "channel", ordinal = 18)
        String channel;

        /** 清理标识 */
        @JSONField(name = "cleanFlag", ordinal = 19)
        String cleanFlag;

        /** 清理时间 */
        @JSONField(name = "cleanTime", ordinal = 20)
        String cleanTime;

        /** 硬币抵扣 */
        @JSONField(name = "coinOffset", ordinal = 21)
        BigDecimal coinOffset;

        /** 完成时间 */
        @JSONField(name = "completeTime", ordinal = 22)
        String completeTime;

        /** 关联标识 */
        @JSONField(name = "correlationFlag", ordinal = 23)
        String correlationFlag;

        /** 创建人 */
        @JSONField(name = "createBy", ordinal = 24)
        String createBy;

        /** 创建时间 */
        @JSONField(name = "createTime", ordinal = 25)
        String createTime;

        /** 货币类型 */
        @JSONField(name = "currency", ordinal = 26)
        String currency;

        /** 删除标识 */
        @JSONField(name = "deleteFlag", ordinal = 27)
        String deleteFlag;

        /** 发货标识 */
        @JSONField(name = "deliverFlag", ordinal = 28)
        String deliverFlag;

        /** 配送标识 */
        @JSONField(name = "deliveryFlag", ordinal = 29)
        String deliveryFlag;

        /** 配送时间 */
        @JSONField(name = "deliveryTime", ordinal = 30)
        String deliveryTime;

        /** 部门ID */
        @JSONField(name = "deptId", ordinal = 31)
        Long deptId;

        /** 部门名称 */
        @JSONField(name = "deptName", ordinal = 32)
        String deptName;

        /** 商品描述 */
        @JSONField(name = "description", ordinal = 33)
        String description;

        /** 第三方托管价格 */
        @JSONField(name = "escrowPrice", ordinal = 34)
        BigDecimal escrowPrice;

        /** 验货状态 */
        @JSONField(name = "examineGoodsStatus", ordinal = 35)
        Integer examineGoodsStatus;

        /** 换货ID */
        @JSONField(name = "exchangeId", ordinal = 36)
        String exchangeId;

        @JSONField(name = "expressDto", ordinal = 37)
        List<ExpressDto> expressDto;

        /** 首个商品数量 */
        @JSONField(name = "firstItemCount", ordinal = 38)
        Integer firstItemCount;

        /** 首个商品是否批发 */
        @JSONField(name = "firstItemIsWholesale", ordinal = 39)
        String firstItemIsWholesale;

        /** 首个商品型号 */
        @JSONField(name = "firstItemModel", ordinal = 40)
        String firstItemModel;

        /** 首个商品名称 */
        @JSONField(name = "firstItemName", ordinal = 41)
        String firstItemName;

        /** 首个商品退货状态 */
        @JSONField(name = "firstItemReturn", ordinal = 42)
        String firstItemReturn;

        /** Go信标识 */
        @JSONField(name = "goXin", ordinal = 43)
        String goXin;

        /** 商品状态 */
        @JSONField(name = "goodsStates", ordinal = 44)
        String goodsStates;

        /** 商品类型 */
        @JSONField(name = "goodsType", ordinal = 45)
        String goodsType;

        /** 手工订单标识 */
        @JSONField(name = "handOrder", ordinal = 46)
        String handOrder;

        /** 记录ID */
        @JSONField(name = "id", ordinal = 47)
        String id;

        /** 即时买家取消发货 */
        @JSONField(name = "instantBuyercancelToship", ordinal = 48)
        String instantBuyercancelToship;

        /** 是否买家取消发货 */
        @JSONField(name = "isBuyercancelToship", ordinal = 49)
        String isBuyercancelToship;

        /** 隔离标识 */
        @JSONField(name = "isolateFlag", ordinal = 50)
        String isolateFlag;

        /** 商品ID */
        @JSONField(name = "itemId", ordinal = 51)
        String itemId;

        /** 商品图片 */
        @JSONField(name = "itemImage", ordinal = 52)
        String itemImage;

        /** 商品规格名称 */
        @JSONField(name = "itemModelName", ordinal = 53)
        String itemModelName;

        /** 商品规格SKU */
        @JSONField(name = "itemModelSku", ordinal = 54)
        String itemModelSku;

        /** 商品价格 */
        @JSONField(name = "itemPrice", ordinal = 55)
        BigDecimal itemPrice;

        /** 快递100打印标识 */
        @JSONField(name = "kuaidi100PrintFlag", ordinal = 56)
        String kuaidi100PrintFlag;

        /** 快递100打印时间 */
        @JSONField(name = "kuaidi100PrintTime", ordinal = 57)
        LocalDateTime kuaidi100PrintTime;

        /** 列表类型 */
        @JSONField(name = "listType", ordinal = 58)
        String listType;

        /** 物流状态 */
        @JSONField(name = "logisticsStatus", ordinal = 59)
        String logisticsStatus;

        /** 消息 */
        @JSONField(name = "message", ordinal = 60)
        String message;

        /** 规格ID */
        @JSONField(name = "modelId", ordinal = 61)
        String modelId;

        /** 商品名称 */
        @JSONField(name = "name", ordinal = 62)
        String itemName;  // TODO 接口字段name, 本地字段itemName

        /** 用户昵称 */
        @JSONField(name = "nickName", ordinal = 63)
        String nickName;

        /** 一对多标识 */
        @JSONField(name = "oneManyFlag", ordinal = 64)
        String oneManyFlag;

        /** 操作类型 */
        @JSONField(name = "operateType", ordinal = 65)
        String operateType;

        /** 订单详情 */
        @JSONField(name = "orderDetail", ordinal = 66)
        String orderDetail;

        /** 订单表单 */
        @JSONField(name = "orderForm", ordinal = 67)
        String orderForm;

        /** 订单ID */
        @JSONField(name = "orderId", ordinal = 68)
        String orderId;

        /** 订单ID1 */
        @JSONField(name = "orderId1", ordinal = 69)
        String orderId1;

        /** 订单价格 */
        @JSONField(name = "orderPrice", ordinal = 70)
        BigDecimal orderPrice;

        /** 订单编号 */
        @JSONField(name = "orderSn", ordinal = 71)
        String orderSn;

        /** 订单状态码 */
        @JSONField(name = "orderStates", ordinal = 72)
        String orderStates;

        /** 订单状态名称 */
        @JSONField(name = "orderStatesName", ordinal = 73)
        String orderStatesName;

        /** 订单类型 */
        @JSONField(name = "orderType", ordinal = 74)
        String orderType;

        /** 出库标识 */
        @JSONField(name = "outFlag", ordinal = 75)
        String outFlag;

        /** 出库时间 */
        @JSONField(name = "outTime", ordinal = 76)
        String outTime;

        /** 超额金额 */
        @JSONField(name = "over", ordinal = 77)
        String over;

        /** 打包标识 */
        @JSONField(name = "packFlag", ordinal = 78)
        String packFlag;

        /** 包裹编号 */
        @JSONField(name = "packageNumber", ordinal = 79)
        String packageNumber;

        /** 平台替代标识 */
        @JSONField(name = "palt", ordinal = 80)
        String palt;

        /** 查询参数 */
        @JSONField(name = "params", ordinal = 81)
        @Builder.Default
        Params params = new Params();

        /** 父级ID */
        @JSONField(name = "parentId", ordinal = 82)
        Long parentId;

        /** 部分发货标识 */
        @JSONField(name = "partFlag", ordinal = 83)
        String partFlag;

        /** 付款截止日期 */
        @JSONField(name = "paybyDate", ordinal = 84)
        String paybyDate;

        /** 付款方式 */
        @JSONField(name = "paymentMethod", ordinal = 85)
        String paymentMethod;

        /** 平台标识 */
        @JSONField(name = "pf", ordinal = 86)
        String pf;

        /** 拣货标识 */
        @JSONField(name = "pickFlag", ordinal = 87)
        String pickFlag;

        /** 取件尝试次数 */
        @JSONField(name = "pickupAttempts", ordinal = 88)
        String pickupAttempts;

        /** 取件截止时间 */
        @JSONField(name = "pickupCutoffTime", ordinal = 89)
        String pickupCutoffTime;

        /** 取件时间 */
        @JSONField(name = "pickupTime", ordinal = 90)
        String pickupTime;

        /** 平台申请邮寄日期 */
        @JSONField(name = "platApplyMailDate", ordinal = 91)
        String platApplyMailDate;

        /** 平台申请邮寄时间 */
        @JSONField(name = "platApplyMailTime", ordinal = 92)
        String platApplyMailTime;

        /** 平台配送方式 */
        @JSONField(name = "platShipping", ordinal = 93)
        String platShipping;

        /** 平台配送方法 */
        @JSONField(name = "platShippingMethod", ordinal = 94)
        String platShippingMethod;

        /** 平台用户ID */
        @JSONField(name = "platUserId", ordinal = 95)
        String platUserId;

        /** 预售标识 */
        @JSONField(name = "preFlag", ordinal = 96)
        String preFlag;

        /** 优惠前价格 */
        @JSONField(name = "priceBeforeBundle", ordinal = 97)
        BigDecimal priceBeforeBundle;

        /** 折扣前价格 */
        @JSONField(name = "priceBeforeDiscount", ordinal = 98)
        BigDecimal priceBeforeDiscount;

        /** 商品索引 */
        @JSONField(name = "productIdx", ordinal = 99)
        Integer productIdx;

        /** 商品SKU */
        @JSONField(name = "productSku", ordinal = 100)
        String productSku;

        /** 促销活动ID */
        @JSONField(name = "promotionId", ordinal = 101)
        String promotionId;

        /** 入库标识 */
        @JSONField(name = "putInFalg", ordinal = 102)
        String putInFalg;

        /** 评价截止日期 */
        @JSONField(name = "rateByDate", ordinal = 103)
        String rateByDate;

        /** 实际出库标识 */
        @JSONField(name = "realOutFlag", ordinal = 104)
        String realOutFlag;

        /** 实际出库时间 */
        @JSONField(name = "realOutTime", ordinal = 105)
        String realOutTime;

        /** 地区 */
        @JSONField(name = "region", ordinal = 106)
        String region;

        /** 备注 */
        @JSONField(name = "remark", ordinal = 107)
        String remark;

        /** 角色 */
        @JSONField(name = "role", ordinal = 108)
        String role;

        /** 搜索值 */
        @JSONField(name = "searchValue", ordinal = 109)
        String searchValue;

        /** 收货地址 */
        @JSONField(name = "shippingAddress", ordinal = 110)
        String shippingAddress;

        /** 发货确认时间 */
        @JSONField(name = "shippingConfirmTime", ordinal = 111)
        String shippingConfirmTime;

        /** 配送方式 */
        @JSONField(name = "shippingMethod", ordinal = 112)
        String shippingMethod;

        /** 店铺ID */
        @JSONField(name = "shopId", ordinal = 113)
        String shopId;

        /** 店铺登录类型 */
        @JSONField(name = "shopLoginType", ordinal = 114)
        String shopLoginType;

        /** 店铺名称 */
        @JSONField(name = "shopName", ordinal = 115)
        String shopName;

        /** 店铺其他名称 */
        @JSONField(name = "shopOtherName", ordinal = 116)
        String shopOtherName;

        /** Shopee申请邮寄标识 */
        @JSONField(name = "shopeeApplyMailFlag", ordinal = 117)
        String shopeeApplyMailFlag;

        /** Shopee申请邮寄时间 */
        @JSONField(name = "shopeeApplyMailTime", ordinal = 118)
        String shopeeApplyMailTime;

        /** Shopee入库标识 */
        @JSONField(name = "shopeeInShopeFlag", ordinal = 119)
        String shopeeInShopeFlag;

        /** Shopee入库时间 */
        @JSONField(name = "shopeeInShopeTime", ordinal = 120)
        String shopeeInShopeTime;

        /** Shopee备注 */
        @JSONField(name = "shopeeRemark", ordinal = 121)
        String shopeeRemark;

        /** 快照ID */
        @JSONField(name = "snapshotId", ordinal = 122)
        String snapshotId;

        /** 状态 */
        @JSONField(name = "status", ordinal = 123)
        String status;

        /** 状态扩展 */
        @JSONField(name = "statusExt", ordinal = 124)
        String statusExt;

        /** 系统用户ID */
        @JSONField(name = "sysUserId", ordinal = 125)
        Long sysUserId;

        /** 第三方单号 */
        @JSONField(name = "thirdNo", ordinal = 126)
        String thirdNo;

        /** 第三方交易号 */
        @JSONField(name = "thirdPartyTn", ordinal = 127)
        String thirdPartyTn;

        /** 订单总价 */
        @JSONField(name = "totalPrice", ordinal = 128)
        BigDecimal totalPrice;

        /** 更新人 */
        @JSONField(name = "updateBy", ordinal = 129)
        String updateBy;

        /** 更新时间 */
        @JSONField(name = "updateTime", ordinal = 130)
        String updateTime;

        /** 用户ID */
        @JSONField(name = "userId", ordinal = 131)
        String userId;

        /** 用户消息 */
        @JSONField(name = "userMessage", ordinal = 132)
        String userMessage;

        /** 用户名 */
        @JSONField(name = "userName", ordinal = 133)
        String userName;

        @Builder.Default
        String stickFeeFlag = "";
    }
 
    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class ExpressDto {
    	
    	/** 快递单号/运单号 */
    	String expressNo;
    	
    	/** 快递入库状态标志 */
    	String expressInFlag;
    	
    	/** 入库时间 */
    	LocalDateTime putInTime;
    	
    	/** 录入时间 */
    	LocalDateTime putCreateTime;
    }
    
}