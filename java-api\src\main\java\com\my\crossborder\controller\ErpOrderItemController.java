package com.my.crossborder.controller;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemDeleteDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemInsertDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemPageDTO;
import com.my.crossborder.controller.dto.erp_order_item.ErpOrderItemUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemDetailVO;
import com.my.crossborder.controller.vo.erp_order_item.ErpOrderItemPageVO;
import com.my.crossborder.service.ErpOrderItemService;

import lombok.RequiredArgsConstructor;

/**
 * 订单项表 
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/api/erp-order-item")
@RequiredArgsConstructor
public class ErpOrderItemController {

    private final ErpOrderItemService erpOrderItemService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody ErpOrderItemInsertDTO insertDTO) {
    	this.erpOrderItemService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody ErpOrderItemUpdateDTO updateDTO) {
    	this.erpOrderItemService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询订单项详情
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<ErpOrderItemDetailVO> detail(@PathVariable String id) {
        try {
            Long longId = Long.parseLong(id);
            return StdResp.success(this.erpOrderItemService.detail(longId));
        } catch (NumberFormatException e) {
            return StdResp.fail("INVALID_ID", "订单项ID格式错误");
        }
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<ErpOrderItemPageVO>> page(ErpOrderItemPageDTO pageDTO) {
        Page<ErpOrderItemPageVO> page = this.erpOrderItemService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody ErpOrderItemDeleteDTO deleteDTO) {
    	this.erpOrderItemService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
