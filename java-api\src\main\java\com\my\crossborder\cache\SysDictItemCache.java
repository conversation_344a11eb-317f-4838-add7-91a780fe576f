package com.my.crossborder.cache;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.my.crossborder.mybatis.entity.SysDictItem;
import com.my.crossborder.mybatis.mapper.SysDictItemMapper;

import lombok.RequiredArgsConstructor;

/**
 * 字典缓存
 * <AUTHOR>
 * @date 2023年4月22日
 */
@Component
@RequiredArgsConstructor
public class SysDictItemCache {
	
	private final SysDictItemMapper sysDictItemMapper;

	private List<SysDictItem> entityList;
	
	/**
	 * 初始缓存
	 */
	@PostConstruct
	public void reloadCache() {
		Wrapper<SysDictItem> wrapper = Wrappers.lambdaQuery();
		this.entityList = this.sysDictItemMapper.selectList(wrapper);
	}
	
    /**
     * 按类别查询
     * @param categoryId 
     * @return
     */
	public List<SysDictItem> listByCategoryId(String categoryId) {
		return this.entityList.stream()
				.filter(t -> t.getCategoryId().equalsIgnoreCase(categoryId))
				.sorted((t1, t2) -> t1.getSortNum().compareTo(t2.getSortNum()))
				.collect(Collectors.toList());
	}
	
	/**
	 * 根据联合主键查询
	 * @param categoryId
	 * @param itemValue
	 * @return
	 */
	public SysDictItem get(String categoryId, String itemValue) {
		return this.entityList.stream()
				.filter(t -> t.getCategoryId().equals(categoryId) && t.getItemValue().equals(itemValue))
				.findFirst()
				.orElse(null);
	}
	
}
