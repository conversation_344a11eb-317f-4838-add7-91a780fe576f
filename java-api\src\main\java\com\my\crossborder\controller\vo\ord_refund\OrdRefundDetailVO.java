package com.my.crossborder.controller.vo.ord_refund;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_退款表
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdRefundDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 申请状态 (0:待申请 1:已申请 2:不退采买改为入库)
     */
    private String applyStatus;

    /**
     * 申请退款金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请人id
     */
    private Integer applyUserId;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 结果状态 (1:退款成功 2:退款失败 3:确认已入库)
     */
    private String resultStatus;

    /**
     * 结果时间
     */
    private LocalDateTime resultTime;

    /**
     * 退款失败备注
     */
    private String refundFailReason;

    /**
     * 退款成功金额
     */
    private BigDecimal refundSuccessAmount;

    /**
     * 结果填写人
     */
    private Integer resultUserId;

}
