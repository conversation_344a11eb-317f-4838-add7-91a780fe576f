package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteByOrderDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteDeleteDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteInsertDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNotePageDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;
import com.my.crossborder.controller.vo.wkb_note.WkbNotePageVO;
import com.my.crossborder.service.WkbNoteService;

import lombok.RequiredArgsConstructor;

/**
 * 工作台_工作笔记表 
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/api/wkb-note")
@RequiredArgsConstructor
public class WkbNoteController {

    private final WkbNoteService wkbNoteService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody WkbNoteInsertDTO insertDTO) {
    	this.wkbNoteService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody WkbNoteUpdateDTO updateDTO) {
    	this.wkbNoteService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<WkbNoteDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.wkbNoteService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<WkbNotePageVO>> page(WkbNotePageDTO pageDTO) {
        Page<WkbNotePageVO> page = this.wkbNoteService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody WkbNoteDeleteDTO deleteDTO) {
    	this.wkbNoteService.delete(deleteDTO);
		return StdResp.success();
    }
    
    /**
     * 状态变更：将待处理改为已处理
     * @param id 笔记ID
     */
    @PutMapping("/{id}/complete")
    public StdResp<?> markAsCompleted(@PathVariable Integer id) {
        this.wkbNoteService.markAsCompleted(id);
        return StdResp.success();
    }
    
    /**
     * 根据订单号和场景查询工作笔记
     * @param orderSn 订单号
     * @param scene 场景
     * @return 工作笔记详情，如果不存在则返回null
     */
    @GetMapping("/by-order")
    public StdResp<WkbNoteDetailVO> getByOrderSnAndScene(@Valid WkbNoteByOrderDTO orderSnAndScene) {
        WkbNoteDetailVO noteDetailVO = this.wkbNoteService.getByOrderSnAndScene(orderSnAndScene);
        return StdResp.success(noteDetailVO);
    }
}
