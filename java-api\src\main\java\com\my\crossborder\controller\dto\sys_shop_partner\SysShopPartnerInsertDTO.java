package com.my.crossborder.controller.dto.sys_shop_partner;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_店铺合伙人表
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShopPartnerInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 店铺ID（关联 sys_shop.id）
     */
	@NotNull(message="shopId不能为空")
    private Integer shopId;

    /**
     * 合伙人用户ID（关联 sys_user.user_id）
     */
	@NotNull(message="partnerUserId不能为空")
    private Integer partnerUserId;

}
