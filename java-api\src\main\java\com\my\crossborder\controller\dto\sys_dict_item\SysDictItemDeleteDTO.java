package com.my.crossborder.controller.dto.sys_dict_item;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 批量删除_数据字典-字典项表
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDictItemDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 联合主键数组
	*/
	@NotEmpty(message = "keyList不能为空")
	private List<CompositeKey> keyList;

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class CompositeKey {
		private String categoryId;
		private String itemValue;
	}
	
}
