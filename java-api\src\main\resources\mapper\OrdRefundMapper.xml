<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.OrdRefundMapper">

	<!-- 分页 -->
    <select id="page" resultType="com.my.crossborder.controller.vo.ord_refund.OrdRefundPageVO">
		SELECT
			t2.order_sn,
			t2.total_price,
			IFNULL(t1.apply_status, '0') as apply_status,
			t1.apply_amount,
			t1.apply_user_id,
			t1.apply_time,
			t1.result_status,
			t1.result_time,
			t1.refund_fail_reason,
			t1.refund_success_amount,
			t1.result_user_id,
			(select real_name from sys_user where user_id = t1.apply_user_id) as apply_user_name,
			(select real_name from sys_user where user_id = t1.result_user_id) as result_user_name,
			COALESCE ( t2.shop_name, '未知店铺' ) AS shop_name,
			t2.shop_id,
			t2.order_id,
			t2.create_time,
			t1.settlement_status AS settlement_flag
		FROM
			ord_refund AS t1
			RIGHT JOIN erp_order AS t2 ON t1.order_sn = t2.order_sn
		<where>
        	1=1
        	 <!-- 不成立 -->
        	 AND t2.order_states = '003'
        	 <!-- 已采购 -->
        	 AND EXISTS (
        	 	SELECT 1 FROM erp_order_item t3
        	 	INNER JOIN ord_purchase_item t4 ON t3.id = t4.order_item_id
        	 	WHERE t3.order_id = t2.order_id
        	 )
	        <if test="shopIds != null and shopIds.size() > 0">
	           	AND t2.shop_id IN
	           	<foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
	           		#{shopId}
	           	</foreach>
            </if>
	        <if test="orderSn != null and orderSn != ''">
	           	AND t1.order_sn = #{orderSn}
            </if>
	        <if test="applyStatus != null and applyStatus != ''">
	           	AND t1.apply_status = #{applyStatus}
            </if>
	        <if test="applyAmount != null and applyAmount != ''">
	           	AND t1.apply_amount = #{applyAmount}
            </if>
	        <if test="applyUserId != null and applyUserId != ''">
	           	AND t1.apply_user_id = #{applyUserId}
            </if>
	        <if test="applyTime != null and applyTime != ''">
	           	AND t1.apply_time = #{applyTime}
            </if>
	        <if test="resultStatus != null and resultStatus != ''">
	           	AND t1.result_status = #{resultStatus}
            </if>
	        <if test="resultTime != null and resultTime != ''">
	           	AND t1.result_time = #{resultTime}
            </if>
	        <if test="refundFailReason != null and refundFailReason != ''">
	           	AND t1.refund_fail_reason = #{refundFailReason}
            </if>
	        <if test="refundSuccessAmount != null and refundSuccessAmount != ''">
	           	AND t1.refund_success_amount = #{refundSuccessAmount}
            </if>
	        <if test="resultUserId != null and resultUserId != ''">
	           	AND t1.result_user_id = #{resultUserId}
            </if>
        </where>
        ORDER BY t2.create_time DESC
    </select>

</mapper>
