package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRateDeleteDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRateInsertDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRatePageDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate.SysExchangeRateUpdateDTO;
import com.my.crossborder.controller.vo.sys_exchange_rate.SysExchangeRatePageVO;
import com.my.crossborder.mybatis.entity.SysExchangeRate;

/**
 * 汇率表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface SysExchangeRateService extends IService<SysExchangeRate> {


	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysExchangeRateDeleteDTO deleteDTO);	

}
