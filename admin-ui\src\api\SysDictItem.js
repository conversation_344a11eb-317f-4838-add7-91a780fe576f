import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// ============== 字典分类接口 ==============

// 获取全部字典分类列表
export const dictCategoryList = (params) => { return reqGet("/sys-dict-category/list", params) };

// 分页查询字典分类
export const dictCategoryPage = (params) => { return reqGet("/sys-dict-category/page", params) };

// 获取字典分类详情
export const dictCategoryDetail = (params) => { return reqGet("/sys-dict-category/" + params.id) };

// 创建字典分类
export const createDictCategory = (params) => { return reqPost("/sys-dict-category", params) };

// 更新字典分类
export const updateDictCategory = (params) => { return reqPut("/sys-dict-category", params) };

// 删除字典分类
export const deleteDictCategory = (params) => { return reqDelete("/sys-dict-category", params) };

// 获取字典分类项
export const dictCategoryItems = (params) => { return reqGet("/sys-dict-category/items", params) };

// ============== 字典项接口 ==============

// 分页查询字典项
export const dictItemPage = (params) => { return reqGet("/sys-dict-item/page", params) };

// 创建字典项
export const createDictItem = (params) => { return reqPost("/sys-dict-item", params) };

// 更新字典项
export const updateDictItem = (params) => { return reqPut("/sys-dict-item", params) };

// 删除字典项
export const deleteDictItem = (params) => { return reqDelete("/sys-dict-item", params) };