<template>
  <el-dialog
    title="添加公告"
    :visible.sync="dialogVisible"
    width="600px"
    top="10vh"
    @close="handleClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-form-item label="公告标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入公告标题"></el-input>
      </el-form-item>
      <el-form-item label="公告类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择公告类型" style="width: 100%">
          <el-option
            v-for="item in notificationTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公告内容" prop="content">
        <el-input
          type="textarea"
          v-model="form.content"
          placeholder="请输入公告内容"
          :rows="5">
        </el-input>
      </el-form-item>
      <el-form-item label="接收人" prop="roleIdList">
        <el-tree
          :default-checked-keys="form.roleIdList"
          ref="receiverTree"
          :data="positionTreeData"
          :props="{ children: 'children', label: 'label' }"
          show-checkbox
          node-key="id"
          @check="handleTreeCheck"
          @node-click="handleNodeClick"
          style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px; max-height: 200px; overflow-y: auto;">
        </el-tree>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { dictCategoryItems } from '../../api/SysDictItem'
import { createNotification } from '../../api/WkbNotification'

export default {
  name: 'SysNotificationAdd',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {
        title: '',
        type: '',
        content: '',
        roleIdList: []
      },
      rules: {
        title: [
          { required: true, message: '请输入公告标题', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择公告类型', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入公告内容', trigger: 'blur' }
        ],
        roleIdList: [
          { required: true, message: '请选择接收人', trigger: 'change' }
        ]
      },
      notificationTypes: [],
      positionTreeData: [
        {
          id: 11,
          label: '系统管理员'
        },
        {
          id: 12,
          label: '合伙人'
        },
        {
          id: 21,
          label: '客服'
        },
        {
          id: 22,
          label: '客服主管'
        }
      ]
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  created() {
    this.loadNotificationTypes()
  },
  methods: {
    // 加载公告类型字典
    loadNotificationTypes() {
      dictCategoryItems({ categoryId: 'NOTIFICATION_TYPE' })
        .then(res => {
          if (res.success && res.data) {
            this.notificationTypes = res.data
          }
        })
        .catch(err => {
          console.error('获取公告类型字典失败:', err)
        })
    },
    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.$refs.form.resetFields()
      this.form = {
        title: '',
        type: '',
        content: '',
        roleIdList: []
      }
      // 清空树节点的选中状态
      if (this.$refs.receiverTree) {
        this.$refs.receiverTree.setCheckedKeys([])
      }
    },
    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          // 获取选中的接收人ID列表
          const roleIdList = this.$refs.receiverTree.getCheckedKeys()
            .filter(id => typeof id === 'number') // 只获取数字类型的ID

          const formData = {
            title: this.form.title,
            type: this.form.type,
            content: this.form.content,
            roleIdList: roleIdList
          }

          createNotification(formData)
            .then(res => {
              this.loading = false
              this.$message({
                message: '保存成功',
                type: 'success'
              });
              this.$emit('success');
              this.handleClose()
            })
        }
      })
    },
    // 添加树形控件选择处理方法
    handleTreeCheck(data, checked) {
      this.form.roleIdList = this.$refs.receiverTree.getCheckedKeys()
    },
    // 处理节点点击事件
    handleNodeClick(data) {
      // 获取当前节点的选中状态
      const isChecked = this.$refs.receiverTree.getCheckedKeys().includes(data.id)

      // 切换选中状态
      this.$refs.receiverTree.setChecked(data.id, !isChecked)

      // 更新表单数据
      this.form.roleIdList = this.$refs.receiverTree.getCheckedKeys()
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
