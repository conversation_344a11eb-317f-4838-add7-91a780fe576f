package com.my.crossborder.mybatis.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 汇率表
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_exchange_rate")
public class SysExchangeRate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
    @TableId(value = "day", type = IdType.INPUT)
    private LocalDate day;

    /**
     * 汇率（CNY/TWD的值，比如：4.0814）
     */
    private BigDecimal exchangeRate;


}
