package com.my.crossborder.controller.vo.sys_shift_attendance;

import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ShiftShopGroupByDay {

	LocalDate shiftDay;

	List<Shop> shopList;

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Shop {

		Integer shopId;

		String shopName;
	}
	
}