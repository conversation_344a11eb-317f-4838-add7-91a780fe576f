<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.4.RELEASE</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.my</groupId>
	<artifactId>CrossBorder-Business-CloudOps-System</artifactId>
	<version>1.0.0</version>
	<name>CrossBorder-Business-CloudOps-System</name>
	<description>跨境电商运营管理系统</description>

    <properties>
        <java.version>1.8</java.version>
        <smart-doc.version>3.1.0</smart-doc.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    </properties>
    
    <!-- 添加阿里云Maven仓库配置 -->
    <repositories>
        <repository>
            <id>aliyun-central</id>
            <name>Aliyun Central Repository</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyun-public</id>
            <name>Aliyun Public Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyun-spring</id>
            <name>Aliyun Spring Repository</name>
            <url>https://maven.aliyun.com/repository/spring</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!-- 添加插件仓库配置 -->
    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-central</id>
            <name>Aliyun Central Repository</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>aliyun-public</id>
            <name>Aliyun Public Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    
	<dependencies>
		<!-- fastjson -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.58</version>
		</dependency>
		
		<!-- forrest  20250621 -->
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot-starter</artifactId>
            <version>1.6.4</version>
        </dependency>	
        
		<!-- bootstrap.yml loader -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-context</artifactId>
			<version>2.1.0.RELEASE</version>
		</dependency>
		
		<!-- hutool -->
		<dependency>
		    <groupId>cn.hutool</groupId>
		    <artifactId>hutool-all</artifactId>
		    <version>5.8.27</version>
		</dependency>		
		
		<!-- lang3 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>	

		<!-- guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>29.0-jre</version>
        </dependency>		

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		
        <!-- Mysql Driver -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>	
		<!-- druid -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.23</version>
        </dependency>
        
		<!-- mybatis-plus -->
		<dependency>
		  <groupId>com.baomidou</groupId>
		  <artifactId>mybatis-plus-boot-starter</artifactId>
		  <version>3.4.3.1</version>
		</dependency>

		<!-- Sa-Token：https://sa-token.cc -->
		<dependency>
		    <groupId>cn.dev33</groupId>
		    <artifactId>sa-token-spring-boot-starter</artifactId>
		    <version>1.38.0</version>
		</dependency>
		
		<!-- Spring AOP -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		
		<!-- web -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		
		<!-- lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<!-- test -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		
		<!-- Bean封装 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		
		<!-- 参考：https://github.com/alibaba/easyexcel.git -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
        </dependency>	
        
       <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
		
	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<!-- build时跳过junit测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>		
	 		<!--  build为精简版jar, 并设定manifest的参数 -->
	        <plugin>
	          <groupId>org.apache.maven.plugins</groupId>
	          <artifactId>maven-jar-plugin</artifactId>
	          <configuration>
	            <archive>
	              <manifest>
	                <addClasspath>true</addClasspath>
	                <classpathPrefix>lib/</classpathPrefix>
	                <mainClass>com.my.crossborder.App</mainClass>
	              </manifest>
	            </archive>
	          </configuration>
	        </plugin>
	        <!-- 复制依赖的jar包到lib目录  -->
	        <plugin>
	          <groupId>org.apache.maven.plugins</groupId>
	          <artifactId>maven-dependency-plugin</artifactId>
	          <executions>
	            <execution>
	              <id>copy</id>
	              <phase>package</phase>
	              <goals>
	                <goal>copy-dependencies</goal>
	              </goals>
	              <configuration>
	                <outputDirectory>${project.build.directory}/lib</outputDirectory>
	              </configuration>
	            </execution>
	          </executions>
	        </plugin>	
			<!-- smart-doc 修复插件配置 -->
		    <plugin>
		        <groupId>com.ly.smart-doc</groupId>
		        <artifactId>smart-doc-maven-plugin</artifactId>
		        <version>${smart-doc.version}</version>
		        <configuration>
		            <configFile>smart-doc.json</configFile>
		            <projectName>CrossBorder-Business-CloudOps-System</projectName>
		        </configuration>
		    </plugin>
            <!-- 胖包 -->
<!--             <plugin> -->
<!--                 <groupId>org.springframework.boot</groupId> -->
<!--                 <artifactId>spring-boot-maven-plugin</artifactId> -->
<!--                 <configuration> -->
<!--                     <includeSystemScope>true</includeSystemScope> -->
<!--                 </configuration> -->
<!--             </plugin> -->
		</plugins>
	</build>

</project>
