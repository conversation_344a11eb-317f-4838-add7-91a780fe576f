package com.my.crossborder.controller.vo.sys_shift;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_排班表
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 排班日期
     */
    private LocalDate shiftDay;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 客服ID（仅允许【客服】角色）
     */
    private Integer serviceUserId;

    /**
     * 客服打卡时间
     */
    private LocalDateTime serviceClockTime;

    /**
     * 客服状态：0=待打卡/1=已打卡/2=缺勤
     */
    private Integer serviceShiftStatus;

    /**
     * 主管ID（仅允许【主管】角色）
     */
    private Integer supervisorUserId;

    /**
     * 主管打卡时间
     */
    private LocalDateTime supervisorClockTime;

    /**
     * 主管状态：0=待打卡/1=已打卡/2=缺勤
     */
    private Integer supervisorShiftStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
