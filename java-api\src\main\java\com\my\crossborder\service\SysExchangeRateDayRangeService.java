package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeDeleteDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeInsertDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangePageDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeUpdateDTO;
import com.my.crossborder.controller.vo.sys_exchange_rate_day_range.SysExchangeRateDayRangePageVO;
import com.my.crossborder.mybatis.entity.SysExchangeRateDayRange;

/**
 * 汇率日期区间 服务类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface SysExchangeRateDayRangeService extends IService<SysExchangeRateDayRange> {

	/**
	 * 新增
	 */
	void insert(SysExchangeRateDayRangeInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SysExchangeRateDayRangeUpdateDTO updateDTO);

	/**
	 * 分页
	 */
	Page<SysExchangeRateDayRangePageVO> page(SysExchangeRateDayRangePageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SysExchangeRateDayRangeDeleteDTO deleteDTO);	

}
