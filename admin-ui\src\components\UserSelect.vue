<template>
  <el-select
    v-model="currentValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :disabled="disabled"
    :multiple="multiple"
    :filterable="filterable"
    @change="handleChange"
  >
    <el-option
      v-for="user in userList"
      :key="user.id"
      :label="user.realName"
      :value="user.id"
    />
  </el-select>
</template>

<script>
import { getAllEnabledUsers } from '../api/SysUser'

export default {
  name: 'UserSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: null
    },
    placeholder: {
      type: String,
      default: '请选择用户'
    },
    clearable: {
      type: <PERSON>olean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      userList: [],
      currentValue: this.value
    }
  },
  watch: {
    value(newVal) {
      this.currentValue = newVal
    }
  },
  created() {
    this.fetchUsers()
  },
  methods: {
    async fetchUsers() {
      try {
        const response = await getAllEnabledUsers()
        this.userList = response.data.records || []
      } catch (error) {
        console.error('获取用户列表失败:', error)
        this.userList = []
      }
    },
    handleChange(value) {
      this.currentValue = value
      this.$emit('input', value)
      this.$emit('change', value)
    }
  }
}
</script>
