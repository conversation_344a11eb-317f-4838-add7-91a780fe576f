import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun.js'

/**
 * 考勤API
 */

// 获取今日考勤状态
export function getTodayAttendanceStatus(params) {
  return reqGet('/sys-shift-attendance/today-attendance-status', params)
}

// 执行打卡
export function punchIn(data) {
  return reqPost('/sys-shift-attendance/punch-in', data)
}

// 获取本月考勤统计
export function getMonthlyAttendanceStats(params) {
  return reqGet('/sys-shift-attendance/monthly-attendance-stats', params)
}

// 获取本月考勤数据（用于日历显示）
export function getMonthlyAttendanceData(params) {
  return reqGet('/sys-shift-attendance/monthly-attendance-data', params)
}

// // 获取指定日期的排班详情
// export function getDateScheduleDetail(params) {
//   return reqGet('/sys-shift-attendance/date-schedule-detail', params)
// }

// 获取用户考勤统计
export function getUserAttendanceStats(params) {
  return reqGet('/sys-shift-attendance/user-attendance-stats', params)
}

// // 获取员工的排班数据（用于考勤管理显示）
// export function getEmployeeScheduleData(params) {
//   return reqGet('/sys-shift-attendance/employee-schedule-data', params)
// }