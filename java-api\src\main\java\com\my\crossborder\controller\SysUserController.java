package com.my.crossborder.controller;


import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.assertj.core.util.Lists;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_user.SysUserChangeMyPasswordDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserDeleteDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserInsertDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserListDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserPageDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserResetPwdDTO;
import com.my.crossborder.controller.dto.sys_user.SysUserUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_user.SysUserDetailVO;
import com.my.crossborder.controller.vo.sys_user.SysUserPageVO;
import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.service.SysUserService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;

/**
 * 系统用户
 * 
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/api/sys-user")
@RequiredArgsConstructor
public class SysUserController {

    private final SysUserService sysUserService;

    
    /**
     * 查询我的资料
     */
    @GetMapping(value = "profile")
    public StdResp<SysUser> profile() {
    	Integer userId = StpUtil.getLoginIdAsInt();
		SysUser entity = this.sysUserService.getById(userId);
		entity.setPassword(null);
    	return StdResp.success(entity);
    }
    
    /**
     * 修改我的密码
     */
    @PutMapping("/change-my-password")
    public StdResp<?> changeMyPassword(@Valid @RequestBody SysUserChangeMyPasswordDTO dto) {
    	this.sysUserService.changeMyPassword(dto);
    	return StdResp.success();
    }
    
    /**
     * 新增
     */
     @SaCheckPermission("sys-user:insert")
     @PostMapping
     public StdResp<?> insert(@Valid @RequestBody SysUserInsertDTO insertDTO) {
     	this.sysUserService.insert(insertDTO);
     	return StdResp.success();
     }

     /**
     * 修改
     */
     @SaCheckPermission("sys-user:update")
     @PutMapping
     public StdResp<?> update(@Valid @RequestBody SysUserUpdateDTO updateDTO) {
     	this.sysUserService.update(updateDTO);
     	return StdResp.success();
     }
     
     /**
      * 重置密码
      */
     @SaCheckPermission("sys-user:reset-pwd")
     @PutMapping("/reset-pwd")
     public StdResp<?> resetPwd(@Valid @RequestBody SysUserResetPwdDTO dto) {
    	 this.sysUserService.resetPwd(dto);
    	 return StdResp.success();
     }
             
     /**
     * 根据主键查询
     * @param id 主键
     */
     @SaCheckPermission("sys-user:view")
     @GetMapping("/{id}")
     public StdResp<SysUserDetailVO> detail(@PathVariable Integer id) {
     	return StdResp.success(this.sysUserService.detail(id));
     }
 	
     /**
      * 分页
      */
     @SaCheckPermission("sys-user:view")
     @GetMapping(value = "page")
     public StdResp<Page<SysUserPageVO>> page(SysUserPageDTO pageDTO) {
         Page<SysUserPageVO> page = this.sysUserService.page(pageDTO);
         return StdResp.success(page);
     }

    /**
     * 根据id查询一批用户
    */
    @GetMapping(value = "list")
    public StdResp<List<SysUser>> list(SysUserListDTO listDTO) {
        List<SysUser> data = this.sysUserService.list(listDTO.getIdList());
        return StdResp.success(data);
    }
     
     /**
     * 逻辑删除
     */
     @SaCheckPermission("sys-user:delete")
     @DeleteMapping
     public StdResp<?> delete(@Valid @RequestBody SysUserDeleteDTO deleteDTO) {
     	this.sysUserService.delete(deleteDTO);
 		return StdResp.success();
     }
     
     /**
      * 查询合伙人
      */
     @GetMapping(value = "partners")
     public StdResp<List<SysUser>> getPartners() {
    	 List<Integer> roleIdList = Lists.newArrayList(12);
         List<SysUser> partners = this.sysUserService.listByRoleIdList(roleIdList);
         return StdResp.success(partners);
     }
}