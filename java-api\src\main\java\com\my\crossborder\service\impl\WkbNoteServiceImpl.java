package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.mybatis.entity.WkbNote;
import com.my.crossborder.mybatis.mapper.WkbNoteMapper;
import com.my.crossborder.service.WkbNoteService;
import com.my.crossborder.util.ColumnLambda;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteInsertDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNotePageDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteUpdateDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteDeleteDTO;
import com.my.crossborder.controller.dto.wkb_note.WkbNoteByOrderDTO;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;
import com.my.crossborder.controller.vo.wkb_note.WkbNotePageVO;
import com.my.crossborder.exception.BusinessException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

import org.springframework.transaction.annotation.Transactional;

/**
 * 工作台_工作笔记表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
public class WkbNoteServiceImpl extends ServiceImpl<WkbNoteMapper, WkbNote> implements WkbNoteService {


	@Transactional
	@Override
	public void insert(WkbNoteInsertDTO insertDTO) {
		WkbNote checkEntity = this.baseMapper.selectOne(new LambdaQueryWrapper<WkbNote>()
			.eq(WkbNote::getOrderSn, insertDTO.getOrderSn())
			.eq(WkbNote::getScene, insertDTO.getScene())
			.eq(WkbNote::getCreateUserId, StpUtil.getLoginIdAsInt()));
		BusinessException.when(checkEntity != null, "该订单该场景下已存在备注");
		WkbNote entity = BeanUtil.copyProperties(insertDTO, WkbNote.class);
		entity.setCreateUserId(StpUtil.getLoginIdAsInt());
		entity.setSceneComplete(Boolean.FALSE);
		entity.setCreateTime(LocalDateTime.now());
		entity.setUpdateTime(LocalDateTime.now());
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(WkbNoteUpdateDTO updateDTO) {
		WkbNote entity = BeanUtil.copyProperties(updateDTO, WkbNote.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public WkbNoteDetailVO detail(Integer id) {
		WkbNote entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, WkbNoteDetailVO.class);
	}

	@Override
	public Page<WkbNotePageVO> page(WkbNotePageDTO pageDTO) {
		pageDTO.setCreateUserId(StpUtil.getLoginIdAsInt());
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<WkbNote>().columnsToString(WkbNote::getId)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(WkbNoteDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Transactional
	@Override
	public void markAsCompleted(Integer id) {
		WkbNote entity = new WkbNote();
		entity.setId(id);
		entity.setSceneComplete(Boolean.TRUE);
		this.baseMapper.updateById(entity);
	}

	@Override
	public WkbNoteDetailVO getByOrderSnAndScene(WkbNoteByOrderDTO orderSnAndScene) {
		// 构建查询条件：订单号、场景、当前登录用户
		LambdaQueryWrapper<WkbNote> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(WkbNote::getOrderSn, orderSnAndScene.getOrderSn())
					.eq(WkbNote::getScene, orderSnAndScene.getScene())
					.eq(WkbNote::getCreateUserId, StpUtil.getLoginIdAsInt());

		// 查询记录
		WkbNote entity = this.baseMapper.selectOne(queryWrapper);

		// 如果没有找到记录，返回null
		if (entity == null) {
			return null;
		}

		// 转换为VO对象并返回
		return BeanUtil.copyProperties(entity, WkbNoteDetailVO.class);
	}
}
