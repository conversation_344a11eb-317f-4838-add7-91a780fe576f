import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 分页查询通知公告
export const notificationPage = (params) => { return reqGet("/wkb-notification/page", params) };

// 创建通知公告
export const createNotification = (params) => { return reqPost("/sys-notification", params) };

// 更新通知公告
export const updateNotification = (params) => { return reqPut("/wkb-notification", params) };

// 删除通知公告
export const deleteNotification = (params) => { return reqDelete("/wkb-notification", params) };

// 标记通知为已读
export const notificationDetail = (id) => { return reqGet("/wkb-notification/" + id) };