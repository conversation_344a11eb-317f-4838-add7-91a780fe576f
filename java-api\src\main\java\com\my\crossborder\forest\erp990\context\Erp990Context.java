package com.my.crossborder.forest.erp990.context;

/**
 * Erp990上下文
 */
public class Erp990Context {
	
    private static final ThreadLocal<String> USERNAME_LOCAL = new ThreadLocal<String>();
    private static final ThreadLocal<String> PASSWORD_LOCAL = new ThreadLocal<String>();

    
    /**
     * 设置username, password
     * @param username
     * @param password
     */
    public static void set(String username, String password) {
    	USERNAME_LOCAL.set(username);
    	PASSWORD_LOCAL.set(password);
    }    
    
    
    /**
     * 清除(防止内存泄漏)
     */
    public static void remove() {
    	USERNAME_LOCAL.remove();
    	PASSWORD_LOCAL.remove();
    }

    /**
     * 获取用户名
     * @return
     */
    public static String getUsername() {
    	return USERNAME_LOCAL.get();
    }

    /**
     * 获取密码
     * @return
     */
    public static String getPassword() {
    	return PASSWORD_LOCAL.get();
    }

}
