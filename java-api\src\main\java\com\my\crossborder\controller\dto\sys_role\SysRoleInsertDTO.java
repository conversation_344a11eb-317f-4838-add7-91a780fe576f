package com.my.crossborder.controller.dto.sys_role;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_系统角色表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRoleInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


	@NotNull(message="roleName不能为空")
    private String roleName;

	@NotNull(message="description不能为空")
    private String description;

}
