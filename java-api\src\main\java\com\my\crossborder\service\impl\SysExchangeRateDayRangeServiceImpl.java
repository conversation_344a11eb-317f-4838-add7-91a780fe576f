package com.my.crossborder.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeDeleteDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeInsertDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangePageDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeUpdateDTO;
import com.my.crossborder.controller.vo.sys_exchange_rate_day_range.SysExchangeRateDayRangePageVO;
import com.my.crossborder.exception.BusinessException;
import com.my.crossborder.mybatis.entity.SysExchangeRate;
import com.my.crossborder.mybatis.entity.SysExchangeRateDayRange;
import com.my.crossborder.mybatis.mapper.SysExchangeRateDayRangeMapper;
import com.my.crossborder.service.SysExchangeRateDayRangeService;
import com.my.crossborder.service.SysExchangeRateService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * 汇率日期区间 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class SysExchangeRateDayRangeServiceImpl extends ServiceImpl<SysExchangeRateDayRangeMapper, SysExchangeRateDayRange> implements SysExchangeRateDayRangeService {

	@Autowired
	private SysExchangeRateService sysExchangeRateService;

	@Transactional
	@Override
	public void insert(SysExchangeRateDayRangeInsertDTO insertDTO) {
		// 1. 判断开始日期与结束日期会不会与其他数据日期重合， 重合了，则抛出BusinessException
		LocalDate startDay = insertDTO.getStartDay();
		LocalDate endDay = insertDTO.getEndDay();

		// 验证日期范围的合理性
		BusinessException.when(startDay.isAfter(endDay), "开始日期不能晚于结束日期");

		// 检查日期范围是否与现有区间数据重合
		boolean hasConflict = checkDateRangeConflict(startDay, endDay, null, null);
		BusinessException.when(hasConflict, "日期范围 {} 至 {} 与现有汇率区间数据存在重合，请检查后重新设置", startDay, endDay);

		// 2. 不重合，则新增， 同时批量保存到SysExchangeRate表
		// 2.1 保存到区间表
		SysExchangeRateDayRange dayRangeEntity = BeanUtil.copyProperties(insertDTO, SysExchangeRateDayRange.class);
		dayRangeEntity.setUpdateTime(LocalDateTime.now());
		dayRangeEntity.setUpdateUserId(StpUtil.getLoginIdAsInt());
		this.save(dayRangeEntity);

		// 2.2 批量保存到明细表
		List<SysExchangeRate> exchangeRateList = new ArrayList<>();
		LocalDate currentDay = startDay;
		while (!currentDay.isAfter(endDay)) {
			SysExchangeRate exchangeRate = SysExchangeRate.builder()
					.day(currentDay)
					.exchangeRate(insertDTO.getExchangeRate())
					.build();
			exchangeRateList.add(exchangeRate);
			currentDay = currentDay.plusDays(1);
		}
		this.sysExchangeRateService.saveBatch(exchangeRateList);
	}

	@Transactional
	@Override
	public void update(SysExchangeRateDayRangeUpdateDTO updateDTO) {
		// 1. 忽略旧的开始日期、旧的结束日期、判断新的开始日期与新的结束日期会不会与其他数据日期重合， 重合了，则抛出BusinessException
		LocalDate oldStartDay = updateDTO.getStartDay();
		LocalDate oldEndDay = updateDTO.getEndDay();
		LocalDate newStartDay = updateDTO.getNewStartDay();
		LocalDate newEndDay = updateDTO.getNewEndDay();

		// 验证日期范围的合理性
		BusinessException.when(newStartDay.isAfter(newEndDay), "新的开始日期不能晚于新的结束日期");

		// 检查新的日期范围是否与现有区间数据重合（排除旧的日期范围）
		boolean hasConflict = checkDateRangeConflict(newStartDay, newEndDay, oldStartDay, oldEndDay);
		BusinessException.when(hasConflict, "新的日期范围 {} 至 {} 与现有汇率区间数据存在重合，请检查后重新设置", newStartDay, newEndDay);

		// 2. 不重合，则更新； 同时批量删除SysExchangeRate表旧数据、再批量新增
		// 2.1 删除旧的区间记录
		LambdaQueryWrapper<SysExchangeRateDayRange> deleteWrapper = new LambdaQueryWrapper<SysExchangeRateDayRange>()
				.eq(SysExchangeRateDayRange::getStartDay, oldStartDay)
				.eq(SysExchangeRateDayRange::getEndDay, oldEndDay);
		this.remove(deleteWrapper);

		// 2.2 新增新的区间记录
		SysExchangeRateDayRange newDayRangeEntity = SysExchangeRateDayRange.builder()
				.startDay(newStartDay)
				.endDay(newEndDay)
				.exchangeRate(updateDTO.getExchangeRate())
				.updateTime(LocalDateTime.now())
				.updateUserId(StpUtil.getLoginIdAsInt())
				.build();
		this.save(newDayRangeEntity);

		// 2.3 删除旧日期范围的明细数据
		LambdaQueryWrapper<SysExchangeRate> deleteDetailWrapper = new LambdaQueryWrapper<SysExchangeRate>()
				.ge(SysExchangeRate::getDay, oldStartDay)
				.le(SysExchangeRate::getDay, oldEndDay);
		sysExchangeRateService.remove(deleteDetailWrapper);

		// 2.4 批量新增新日期范围的明细数据
		List<SysExchangeRate> exchangeRateList = new ArrayList<>();
		LocalDate currentDay = newStartDay;

		while (!currentDay.isAfter(newEndDay)) {
			SysExchangeRate exchangeRate = SysExchangeRate.builder()
					.day(currentDay)
					.exchangeRate(updateDTO.getExchangeRate())
					.build();
			exchangeRateList.add(exchangeRate);
			currentDay = currentDay.plusDays(1);
		}

		// 批量保存到明细表
		sysExchangeRateService.saveBatch(exchangeRateList);
	}

	@Override
	public Page<SysExchangeRateDayRangePageVO> page(SysExchangeRateDayRangePageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysExchangeRateDayRangeDeleteDTO deleteDTO) {
		// 检查要删除的记录是否存在
		LocalDate startDay = deleteDTO.getStartDay();
		LocalDate endDay = deleteDTO.getEndDay();

		// 根据复合主键查询记录是否存在
		LambdaQueryWrapper<SysExchangeRateDayRange> queryWrapper = new LambdaQueryWrapper<SysExchangeRateDayRange>()
				.eq(SysExchangeRateDayRange::getStartDay, startDay)
				.eq(SysExchangeRateDayRange::getEndDay, endDay);

		SysExchangeRateDayRange existingRecord = this.getOne(queryWrapper);
		BusinessException.when(existingRecord == null, "日期区间记录不存在，开始日期：{}，结束日期：{}", startDay, endDay);

		// 删除明细表中对应日期范围的数据
		LambdaQueryWrapper<SysExchangeRate> deleteDetailWrapper = new LambdaQueryWrapper<SysExchangeRate>()
				.ge(SysExchangeRate::getDay, startDay)
				.le(SysExchangeRate::getDay, endDay);
		this.sysExchangeRateService.remove(deleteDetailWrapper);

		// 删除区间表记录
		this.remove(queryWrapper);
	}

	/**
	 * 检查日期范围是否与现有数据重合
	 * @param startDay 开始日期
	 * @param endDay 结束日期
	 * @param excludeStartDay 排除的开始日期（更新时使用）
	 * @param excludeEndDay 排除的结束日期（更新时使用）
	 * @return true表示有重合，false表示无重合
	 */
	private boolean checkDateRangeConflict(LocalDate startDay, LocalDate endDay,
			LocalDate excludeStartDay, LocalDate excludeEndDay) {

		LambdaQueryWrapper<SysExchangeRateDayRange> queryWrapper = new LambdaQueryWrapper<SysExchangeRateDayRange>()
				// 检查是否有重合：新区间的开始日期 <= 现有区间的结束日期 AND 新区间的结束日期 >= 现有区间的开始日期
				.and(wrapper -> wrapper
						.le(SysExchangeRateDayRange::getStartDay, endDay)
						.ge(SysExchangeRateDayRange::getEndDay, startDay)
				);

		// 如果是更新操作，排除旧的日期范围
		if (excludeStartDay != null && excludeEndDay != null) {
			queryWrapper.not(wrapper -> wrapper
					.eq(SysExchangeRateDayRange::getStartDay, excludeStartDay)
					.eq(SysExchangeRateDayRange::getEndDay, excludeEndDay)
			);
		}

		long conflictCount = this.count(queryWrapper);
		return conflictCount > 0;
	}
}
