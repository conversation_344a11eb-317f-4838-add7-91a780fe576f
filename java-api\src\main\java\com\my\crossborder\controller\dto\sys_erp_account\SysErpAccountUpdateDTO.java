package com.my.crossborder.controller.dto.sys_erp_account;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_禾宸物流接口账号
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysErpAccountUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
	@NotNull(message="id不能为空")
    private Integer id;

    /**
     * 禾宸用户名
     */
	@NotNull(message="username不能为空")
    private String username;

    /**
     * 禾宸密码(明文)
     */
	@NotNull(message="password不能为空")
    private String password;

}
