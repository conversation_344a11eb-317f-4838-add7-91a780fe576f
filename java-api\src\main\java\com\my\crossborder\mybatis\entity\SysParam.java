package com.my.crossborder.mybatis.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 系统参数配置表
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_param")
public class SysParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 键
     */
    @TableId(value = "k", type = IdType.ASSIGN_ID)
    private String k;

    /**
     * 值
     */
    private String v;

}
