package com.my.crossborder.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.crossborder.controller.dto.stl_refund.StlRefundInsertDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundPageDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundUpdateDTO;
import com.my.crossborder.controller.dto.stl_refund.StlRefundDeleteDTO;
import com.my.crossborder.controller.vo.stl_refund.StlRefundDetailVO;
import com.my.crossborder.controller.vo.stl_refund.StlRefundPageVO;
import com.my.crossborder.controller.vo.StdResp;

import com.my.crossborder.service.StlRefundService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算_退款结算表 
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/api/stl-refund")
@RequiredArgsConstructor
public class StlRefundController {

    private final StlRefundService stlRefundService;

    /**
    * 新增退款结算
    */
    @SaCheckPermission("stl-refund:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody StlRefundInsertDTO insertDTO) {
    	this.stlRefundService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody StlRefundUpdateDTO updateDTO) {
    	this.stlRefundService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<StlRefundDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.stlRefundService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<StlRefundPageVO>> page(StlRefundPageDTO pageDTO) {
        Page<StlRefundPageVO> page = this.stlRefundService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody StlRefundDeleteDTO deleteDTO) {
    	this.stlRefundService.delete(deleteDTO);
		return StdResp.success();
    }

    /**
     * 根据周期查询退款结算详情
     * @param refundUserId 用户ID
     * @param refundYear 年份
     * @param refundMonth 月份
     * @param refundWeek 周次
     */
    @GetMapping("/by-week")
    public StdResp<StlRefundDetailVO> getByWeek(@RequestParam Integer refundUserId,
                                                @RequestParam Integer refundYear,
                                                @RequestParam Integer refundMonth,
                                                @RequestParam Integer refundWeek) {
        StlRefundDetailVO detail = this.stlRefundService.getByWeek(refundUserId, refundYear, refundMonth, refundWeek);
        return StdResp.success(detail);
    }

}
