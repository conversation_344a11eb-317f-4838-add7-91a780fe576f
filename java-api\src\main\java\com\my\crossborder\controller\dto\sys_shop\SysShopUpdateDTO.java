package com.my.crossborder.controller.dto.sys_shop;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_店铺管理表
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShopUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺ID
     */
	@NotNull(message="id不能为空")
    private Integer id;

    /**
     * 起店时间
     */
	@NotNull(message="openingDate不能为空")
    private LocalDate openingDate;

    /**
     * 合作人用户ID列表
     */
    private List<Integer> partnerIds;

}
