package com.my.crossborder.controller.dto.ord_taiwan;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.ord_taiwan.OrdTaiwanPageVO;
import java.time.LocalDate;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_台湾上架物品
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdTaiwanPageDTO 
						extends PageDTO<OrdTaiwanPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单项id
     */
    private String orderItemId;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 店铺id
     */
    private Integer shopId;

    /**
     * 退货单号
     */
    private String waybillNumber;

    /**
     * 状态字典：TAIWAN_GOODS_STATUS
     */
    private String status;

    /**
     * 台湾仓柜号
     */
    private String warehouseNumber;

    /**
     * 上架时间
     */
    private LocalDate onlineTime;

    /**
     * 下架时间
     */
    private LocalDate offlineTime;

    /**
     * 原订单店铺名称（搜索用）
     */
    private String originalShopName;

    /**
     * 原订单号（搜索用）
     */
    private String originalOrderNumber;

    /**
     * 商品名称（搜索用）
     */
    private String productName;

    /**
     * 店铺ID列表（用于权限过滤）
     */
    private List<String> shopIds;

}
