package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeDeleteDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeInsertDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangePageDTO;
import com.my.crossborder.controller.dto.sys_exchange_rate_day_range.SysExchangeRateDayRangeUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_exchange_rate_day_range.SysExchangeRateDayRangePageVO;
import com.my.crossborder.service.SysExchangeRateDayRangeService;

import lombok.RequiredArgsConstructor;

/**
 * 汇率日期区间 
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/api/sys-exchange-rate-day-range")
@RequiredArgsConstructor
public class SysExchangeRateDayRangeController {

    private final SysExchangeRateDayRangeService sysExchangeRateDayRangeService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysExchangeRateDayRangeInsertDTO insertDTO) {
    	this.sysExchangeRateDayRangeService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody SysExchangeRateDayRangeUpdateDTO updateDTO) {
    	this.sysExchangeRateDayRangeService.update(updateDTO);
    	return StdResp.success();
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysExchangeRateDayRangePageVO>> page(SysExchangeRateDayRangePageDTO pageDTO) {
        Page<SysExchangeRateDayRangePageVO> page = this.sysExchangeRateDayRangeService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysExchangeRateDayRangeDeleteDTO deleteDTO) {
    	this.sysExchangeRateDayRangeService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
