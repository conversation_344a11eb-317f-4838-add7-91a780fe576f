package com.my.crossborder.mybatis.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 物流理赔
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ord_claim")
public class OrdClaim implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 问题描述
     */
    private String issue;

    /**
     * 问题类别 字典claim_issue_type
     */
    private String issueType;

    /**
     * 订单项id
     */
    private String orderItemId;

    /**
     * 采购金额
     */
    private BigDecimal purchaseCost;

    /**
     * 补寄单号
     */
    private String waybillNumber;

    /**
     * 处理办法 字典claim_close_way
     */
    private String closeWay;

    /**
     * 理赔进度 字典claim_status
     */
    private String claimStatus;

    /**
     * 录入人id
     */
    private Integer issueUserId;

    /**
     * 录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理人id
     */
    private Integer closeUserId;

    /**
     * 处理时间
     */
    private LocalDateTime closeTime;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

}
