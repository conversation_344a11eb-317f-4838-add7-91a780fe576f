<template>
  <el-dialog :title="dialogTitle" top="8vh" :visible.sync="dialogVisible" width="650px" @close="handleDialogClose">
    <el-form :model="itemForm" :rules="itemRules" ref="itemForm" label-width="140px">
      <!-- 隐藏字段：订单明细ID -->
      <el-form-item prop="orderItemId" style="display: none;">
        <el-input v-model="itemForm.orderItemId"></el-input>
      </el-form-item>

      <el-form-item label="产品图片" prop="itemImage">
        <div class="product-image-container">
          <img v-if="itemForm.itemImage" :src="itemForm.itemImage" class="product-image" />
          <span v-else class="no-image">暂无图片</span>
        </div>
      </el-form-item>

      <el-form-item label="产品名称" prop="itemName">
        <el-input v-model="itemForm.itemName" placeholder="产品名称" readonly></el-input>
      </el-form-item>

      <el-form-item label="规格" prop="itemModelName">
        <el-input v-model="itemForm.itemModelName" placeholder="规格" readonly></el-input>
      </el-form-item>

      <el-form-item label="数量" prop="amount">
        <el-input v-model="itemForm.amount" placeholder="数量" readonly>
          <template slot="append">件</template>
        </el-input>
      </el-form-item>

      <el-form-item label="快递单号" prop="trackingNumber">
        <el-input v-model="itemForm.trackingNumber" placeholder="快递单号" readonly></el-input>
      </el-form-item>

      <hr style="margin: 20px 0; border: none; border-top: 1px solid #e4e7ed;" />

      <el-form-item label="采购途径" prop="purchaseChannel">
        <dict-select v-model="itemForm.purchaseChannel" category-id="PURCHASE_CHANNEL" placeholder="请选择采购途径"
          style="width: 200px;">
        </dict-select>
      </el-form-item>

      <el-form-item v-if="shouldShowPurchaseAmount" label="采购金额" prop="purchaseAmount">
        <el-input-number v-model="itemForm.purchaseAmount" :precision="2" :step="0.01" :min="0"
          controls-position="right" style="width: 200px;">
        </el-input-number>
        <span style="margin-left: 10px; color: #909399;">元</span>
      </el-form-item>

      <el-form-item v-if="shouldShowExpressNo" label="新快递单号" prop="expressNo">
        <el-input v-model="itemForm.expressNo"
                  :placeholder="isExpressNoReadonly ? '自动填充快递单号' : '请输入新快递单号'"
                  :readonly="isExpressNoReadonly"
                  style="width: 200px;">
        </el-input>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateOrdPurchaseItem } from '../../api/OrdPurchase'
import DictSelect from '../../components/DictSelect'

export default {
  name: 'OrdPurchaseItemEdit',
  components: {
    DictSelect
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      itemForm: {
        orderItemId: '',
        orderSn: '',
        itemImage: '',
        itemName: '',
        itemModelName: '',
        amount: 0,
        trackingNumber: '',
        purchaseChannel: '',
        purchaseAmount: 0,
        expressNo: ''
      },
      itemRules: {
        orderItemId: [
          { required: true, message: '订单明细ID不能为空', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              if (!value || value.trim() === '') {
                callback(new Error('订单明细ID不能为空'))
              } else {
                callback()
              }
            }, trigger: 'blur'
          }
        ],
        purchaseChannel: [
          { required: true, message: '请选择采购途径', trigger: 'change' }
        ],
        purchaseAmount: [
          { required: true, message: '请输入采购金额', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              if (value <= 0) {
                callback(new Error('采购金额必须大于0'))
              } else {
                callback()
              }
            }, trigger: 'blur'
          }
        ],
        expressNo: []
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return this.isEdit ? '编辑采购明细' : '添加采购明细'
    },
    // 判断是否显示采购金额字段
    shouldShowPurchaseAmount() {
      return this.itemForm.purchaseChannel === '1'
        || this.itemForm.purchaseChannel === '2'
        || this.itemForm.purchaseChannel === '3'
        || this.itemForm.purchaseChannel === '4'
    },
    // 判断是否显示新快递单号字段
    shouldShowExpressNo() {
      return this.itemForm.purchaseChannel === '4'
    },
    // 判断新快递单号字段是否只读
    isExpressNoReadonly() {
      return this.itemForm.purchaseChannel === '4' 
          && this.itemForm.trackingNumber !== null
          && this.itemForm.trackingNumber.trim() !== '' 
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    },
    editData: {
      handler(newData) {
        if (this.visible && newData) {
          this.initForm()
        }
      },
      deep: true
    },
    // 监听采购途径变化
    'itemForm.purchaseChannel'(newVal) {
      this.updateValidationRules()
      // 如果从需要采购金额的途径切换到不需要的途径，清空采购金额
      if (!this.shouldShowPurchaseAmount) {
        this.itemForm.purchaseAmount = 0
      }
      // 如果不是采购途径4，清空新快递单号
      if (!this.shouldShowExpressNo) {
        this.itemForm.expressNo = ''
      } else if (this.shouldShowExpressNo && this.isExpressNoReadonly) {
        // 如果是采购途径4且trackingNumber不为空，将trackingNumber赋值给expressNo
        this.itemForm.expressNo = this.itemForm.trackingNumber
      }
      // 清除字段的验证错误
      if (this.$refs.itemForm) {
        this.$refs.itemForm.clearValidate('purchaseAmount')
        this.$refs.itemForm.clearValidate('expressNo')
      }
    }
  },
  methods: {
    // 动态更新验证规则
    updateValidationRules() {
      // 更新采购金额验证规则
      if (this.shouldShowPurchaseAmount) {
        // 需要显示采购金额时，设置为必填
        this.itemRules.purchaseAmount = [
          { required: true, message: '请输入采购金额', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              if (value <= 0) {
                callback(new Error('采购金额必须大于0'))
              } else {
                callback()
              }
            }, trigger: 'blur'
          }
        ]
      } else {
        // 不需要显示采购金额时，移除验证规则
        this.itemRules.purchaseAmount = []
      }

      // 更新新快递单号验证规则
      if (this.shouldShowExpressNo) {
        // 采购途径为4时，新快递单号必填
        this.itemRules.expressNo = [
          { required: true, message: '请输入新快递单号', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              if (!value || value.trim() === '') {
                callback(new Error('新快递单号不能为空'))
              } else {
                callback()
              }
            }, trigger: 'blur'
          }
        ]
      } else {
        // 不是采购途径4时，移除验证规则
        this.itemRules.expressNo = []
      }
    },

    // 初始化表单
    initForm() {
      if (this.isEdit && this.editData) {
        // 编辑模式
        console.log('OrdPurchaseItemEdit 编辑数据:', this.editData)
        this.itemForm = {
          orderItemId: this.editData.orderItemId || this.editData.id || '',
          orderSn: this.editData.orderSn || '',
          itemImage: this.editData.itemImage || '',
          itemName: this.editData.itemName || '',
          itemModelName: this.editData.itemModelName || '',
          amount: this.editData.amount || 0,
          trackingNumber: this.editData.trackingNumber || this.editData.expressNo || '',
          purchaseChannel: this.editData.purchaseChannel || '',
          purchaseAmount: this.editData.purchaseAmount || 0,
          expressNo: this.editData.expressNo || ''
        }
        console.log('OrdPurchaseItemEdit 表单数据:', this.itemForm)
      } else {
        // 添加模式
        this.itemForm = {
          orderItemId: '',
          orderSn: '',
          itemImage: '',
          itemName: '',
          itemModelName: '',
          amount: 0,
          trackingNumber: '',
          purchaseChannel: '',
          purchaseAmount: 0,
          expressNo: ''
        }
      }

      // 初始化后更新验证规则和处理新快递单号逻辑
      this.$nextTick(() => {
        this.updateValidationRules()
        // 如果是采购途径4且trackingNumber不为空，将trackingNumber赋值给expressNo
        if (this.shouldShowExpressNo && this.isExpressNoReadonly && !this.itemForm.expressNo) {
          this.itemForm.expressNo = this.itemForm.trackingNumber
        }
      })
    },

    // 提交表单
    handleSubmit() {
      this.$refs.itemForm.validate((valid) => {
        if (valid) {
          // 检查 orderItemId 是否为空
          if (!this.itemForm.orderItemId || this.itemForm.orderItemId.trim() === '') {
            this.$message.error('订单明细ID不能为空')
            return false
          }

          // 准备提交的数据
          const submitData = {
            orderItemId: this.itemForm.orderItemId.trim(),
            purchaseChannel: this.itemForm.purchaseChannel,
            purchaseAmount: this.shouldShowPurchaseAmount ? this.itemForm.purchaseAmount : 0,
            expressNo: this.shouldShowExpressNo ? this.itemForm.expressNo : ''
          }

          console.log('提交的数据:', submitData)

          // 调用后端API（新增和编辑都使用update接口）
          const apiCall = updateOrdPurchaseItem(submitData)

          apiCall
            .then(response => {
              if (response.success) {
                this.$message.success('保存成功')
                this.$emit('submit', submitData)
                this.dialogVisible = false
              } else {
                this.$message.error('保存失败：' + response.message)
              }
            })
            .catch(error => {
              console.error('保存失败:', error)
              this.$message.error('保存失败，请稍后重试')
            })
        } else {
          this.$message.warning('请完善表单信息')
          return false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    },

    // 对话框关闭时重置表单
    handleDialogClose() {
      this.$refs.itemForm.resetFields()
    }
  }
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 22px;
}

.el-input-number {
  width: 100%;
}

.product-image-container {
  display: flex;
  align-items: center;
  height: 60px;
}

.product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.no-image {
  color: #909399;
  font-size: 14px;
}

hr {
  margin: 20px 0;
  border: none;
  border-top: 1px solid #e4e7ed;
}
</style>
