package com.my.crossborder.controller.vo.wkb_notification_reading;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_通知阅读表
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class WkbNotificationReadingPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * read表主键
     */
    private Integer id;

    /**
     * 通知id
     */
    private Integer notificationId;

    /**
     * 公告的接收用户id
     */
    private Integer userId;

    /**
     * 是否已读
     */
    private Boolean read;

    /**
     * 通知类型
     */
    private String type;
    
    /**
     * 通知类型
     */
    private String title;
    
    /**
     * 通知内容
     */
    private String content;
    
    /**
     * 通知类型
     */
    private String publishTime;
    
    /**
     * 发布人id
     */
    private String publishUserId;
    
    /**
     * 发布人姓名
     */
    private String publishUserName;

}
