package com.my.crossborder.controller.vo.stl_purchase;

import java.math.BigDecimal;
import java.util.List;

import com.my.crossborder.controller.vo.ord_purchase_centralized.OrdPurchaseCentralizedPageVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class DailyPurchaseDetail {

	/**
	 * 单笔采购数据
	 */
	List<Purchase> purchaseData;
	
	/**
	 * 集中采购数据
	 */
	List<OrdPurchaseCentralizedPageVO> purchaseCentralizedData;
	
	
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	@Data
	@EqualsAndHashCode(callSuper = false)
	public static class Purchase {
		String shopName;
		
		String orderSn;
		
		String itemName;
		
		String itemImage;
		
		BigDecimal itemPrice;
		
		BigDecimal amount;
		
		String itemModelName;
		
		String expressNo;
		
		String purchaseChannel;
		
		BigDecimal purchaseAmount;
	}
	
}
