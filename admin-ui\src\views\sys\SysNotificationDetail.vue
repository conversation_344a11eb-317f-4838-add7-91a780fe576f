<template>
  <el-dialog title="查看公告详情" :visible.sync="dialogVisible" width="600px" top="10vh" @close="handleClose">
    <el-form :model="form" ref="form" label-width="100px">
      <el-form-item label="公告标题">
        <el-input v-model="form.title" placeholder="公告标题" readonly></el-input>
      </el-form-item>
      <el-form-item label="公告类型">
        <el-input v-model="form.typeName" placeholder="公告类型" readonly></el-input>
      </el-form-item>
      <el-form-item label="公告内容">
        <el-input type="textarea" v-model="form.content" placeholder="公告内容" :rows="5" readonly>
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="发布人">
        <el-input v-model="form.publishUserName" placeholder="发布人" readonly></el-input>
      </el-form-item> -->
      <el-form-item label="发布时间">
        <el-input v-model="form.publishTime" placeholder="发布时间" readonly></el-input>
      </el-form-item>
      <el-form-item label="接收人">
        <el-tree ref="receiverTree" :data="positionTreeData" :props="{ children: 'children', label: 'label' }"
          show-checkbox node-key="id" :default-checked-keys="form.roleIdList" :check-strictly="false" :disabled="true"
          class="readonly-tree"
          style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px; max-height: 200px; overflow-y: auto;">
        </el-tree>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { dictCategoryItems } from '../../api/SysDictItem'
import { sysUserList } from '../../api/SysUser'

export default {
  name: 'SysNotificationDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    notificationData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        title: '',
        type: '',
        typeName: '',
        content: '',
        publishUserName: '',
        publishTime: '',
        roleIdList: []
      },
      notificationTypes: [],
      userMap: {},
      positionTreeData: [
        {
          id: 11,
          label: '系统管理员'
        },
        {
          id: 12,
          label: '合伙人'
        },
        {
          id: 21,
          label: '客服'
        },
        {
          id: 22,
          label: '客服主管'
        }
      ]
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.notificationData) {
        this.loadNotificationData()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  created() {
    this.loadNotificationTypes()
  },
  methods: {
    // 加载公告类型字典
    loadNotificationTypes() {
      dictCategoryItems({ categoryId: 'NOTIFICATION_TYPE' })
        .then(res => {
          if (res.success && res.data) {
            this.notificationTypes = res.data
          }
        })
        .catch(err => {
          console.error('获取公告类型字典失败:', err)
        })
    },

        // 加载公告数据
    loadNotificationData() {
      const data = this.notificationData

      // 设置基本信息
      this.form.title = data.title || ''
      this.form.type = data.type || ''
      this.form.content = data.content || ''
      this.form.publishTime = data.publishTime || ''

      // 处理接收人角色ID列表
      if (data.receiveRoleIds) {
        // 从逗号分隔的字符串转换为数组
        this.form.roleIdList = data.receiveRoleIds.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id))
      } else {
        this.form.roleIdList = data.roleIdList || []
      }

      // 获取类型名称
      this.form.typeName = this.getNotificationTypeName(data.type)

      // 获取发布人姓名
      if (data.publishUserId) {
        this.loadUserInfo(data.publishUserId)
      }

      // 设置树的选中状态
      this.$nextTick(() => {
        if (this.$refs.receiverTree && this.form.roleIdList.length > 0) {
          this.$refs.receiverTree.setCheckedKeys(this.form.roleIdList)
          // 禁用所有复选框的交互事件
          this.disableTreeInteraction()
        }
      })
    },

    // 加载用户信息
    loadUserInfo(userId) {
      sysUserList({ idList: [userId] })
        .then(res => {
          if (res.success && res.data && res.data.length > 0) {
            this.form.publishUserName = res.data[0].realName || '-'
          } else {
            this.form.publishUserName = '-'
          }
        })
        .catch(err => {
          console.error('获取用户信息失败:', err)
          this.form.publishUserName = '-'
        })
    },

    // 获取通知类型名称
    getNotificationTypeName(type) {
      if (!type || !this.notificationTypes.length) return type || ''
      const item = this.notificationTypes.find(t => t.value === type)
      return item ? item.label : type
    },

    // 禁用树的交互事件
    disableTreeInteraction() {
      this.$nextTick(() => {
        if (this.$refs.receiverTree) {
          const treeEl = this.$refs.receiverTree.$el
          if (treeEl) {
            // 移除所有事件监听器
            const checkboxes = treeEl.querySelectorAll('.el-checkbox')
            checkboxes.forEach(checkbox => {
              checkbox.style.pointerEvents = 'none'
              checkbox.style.cursor = 'default'
            })

            // 移除节点的点击事件
            const nodes = treeEl.querySelectorAll('.el-tree-node__content')
            nodes.forEach(node => {
              node.style.cursor = 'default'
            })
          }
        }
      })
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.form = {
        title: '',
        type: '',
        typeName: '',
        content: '',
        publishUserName: '',
        publishTime: '',
        roleIdList: []
      }
      // 清空树节点的选中状态
      if (this.$refs.receiverTree) {
        this.$refs.receiverTree.setCheckedKeys([])
      }
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

/* 禁用树形控件的样式 */
.readonly-tree {
  pointer-events: none !important;
}

.readonly-tree .el-tree-node__content {
  cursor: default !important;
  color: #606266 !important;
}

.readonly-tree .el-checkbox {
  cursor: default !important;
  pointer-events: none !important;
}

.readonly-tree .el-checkbox__input {
  cursor: default !important;
  pointer-events: none !important;
}

.readonly-tree .el-checkbox__input .el-checkbox__inner {
  cursor: default !important;
  pointer-events: none !important;
}

.readonly-tree .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.readonly-tree .el-checkbox__input.is-checked .el-checkbox__inner::after {
  border-color: #fff !important;
}

/* 禁用hover效果 */
.readonly-tree .el-tree-node__content:hover {
  background-color: transparent !important;
}

.readonly-tree .el-checkbox__inner:hover {
  border-color: #dcdfe6 !important;
}

.readonly-tree .el-checkbox__input.is-checked .el-checkbox__inner:hover {
  background-color: #409eff !important;
  border-color: #409eff !important;
}
</style>
