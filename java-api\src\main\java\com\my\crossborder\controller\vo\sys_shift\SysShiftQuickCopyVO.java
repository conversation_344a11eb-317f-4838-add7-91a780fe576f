package com.my.crossborder.controller.vo.sys_shift;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 快速复制排班响应VO
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShiftQuickCopyVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 源日期
     */
    private LocalDate sourceDate;

    /**
     * 目标日期列表
     */
    private List<LocalDate> targetDates;

    /**
     * 复制的排班记录数量
     */
    private Integer copiedShiftCount;

    /**
     * 影响的总记录数（排班记录数 × 目标日期数）
     */
    private Integer totalAffectedRecords;

    /**
     * 操作结果描述
     */
    private String message;

}
