<template>
  <div class="multi-order-selector">
    <!-- 只读文本框模式 -->
    <div v-if="readonlyMode" class="readonly-mode">
      <div class="readonly-input-container">
        <el-input
          :value="displayText"
          readonly
          :placeholder="placeholder"
          class="readonly-input"
        >
          <el-button
            slot="append"
            type="primary"
            @click="openOrderDialog"
            :disabled="disabled"
          >
            选择
          </el-button>
        </el-input>
      </div>
    </div>

    <!-- 原有模式 -->
    <template v-else>
      <!-- 选择订单按钮 -->
      <el-button type="primary" icon="el-icon-plus" @click="openOrderDialog" :disabled="disabled">
        选择订单
      </el-button>

      <!-- 紧凑模式显示 -->
      <div v-if="compact" class="compact-display">
        <span v-if="selectedOrderSnList && selectedOrderSnList.length > 0" class="selected-count">
          已选择 {{ selectedOrderSnList.length }} 个订单
        </span>
        <span v-else class="no-selection">
          请选择订单
        </span>
      </div>

      <!-- 完整模式显示 -->
      <template v-else>
        <!-- 已选订单标签显示区域 -->
        <div v-if="selectedOrderSnList && selectedOrderSnList.length > 0" class="selected-orders-container">
          <div class="selected-orders-header">
            <span class="orders-count">已选择 {{ selectedOrderSnList.length }} 个订单：</span>
          </div>
          <div class="order-tags">
            <el-tag
              v-for="(orderSn, index) in selectedOrderSnList"
              :key="index"
              closable
              @close="removeOrderSn(index)"
              style="margin: 4px;"
              type="info"
            >
              {{ orderSn }}
            </el-tag>
          </div>
        </div>
        <div v-else class="no-orders">
          暂无关联订单
        </div>
      </template>
    </template>

    <!-- 订单选择对话框 -->
    <el-dialog title="选择订单" top="5vh" :visible="showOrderSelectDialog" width="1200px" append-to-body @close="closeOrderDialog" :close-on-click-modal="false">
      <div>
        <!-- 订单搜索区域 -->
        <el-form :inline="true" :model="orderSearchForm" class="demo-form-inline">
          <el-form-item label="店铺">
            <el-select v-model="orderSearchForm.shopId" placeholder="请选择店铺" clearable style="width: 140px;">
              <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="orderSearchForm.orderSn" style="width:180px" placeholder="订单号" clearable></el-input>
          </el-form-item>
          <el-form-item label="订单状态">
            <dict-select v-model="orderSearchForm.orderStates" category-id="ERP_ORDER_STATUS" placeholder="请选择订单状态" clearable style="width: 140px;"></dict-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadOrderData">查询</el-button>
            <el-button @click="resetOrderSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 订单表格 -->
        <el-table ref="orderTable" v-loading="orderLoading" :data="orderTableData" style="width: 100%" height="530px"
          border @selection-change="handleSelectionChange" @row-click="handleRowClick" :row-key="getRowKey">
          <!-- 复选框列 -->
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="shopName" label="店铺名称" min-width="130" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="orderSn" label="订单号" min-width="180" align="center">
            <template slot-scope="scope">
              <!-- <span class="clickable-order-sn" @click="copyOrderSn(scope.row.orderSn)"
                    :title="'点击复制订单号: ' + scope.row.orderSn"> -->
                {{ scope.row.orderSn }}
              <!-- </span> -->
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="下单时间" min-width="140" align="center">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <dict-table-column prop="orderStates" category-id="ERP_ORDER_STATUS" label="订单状态" width="120" align="center" />
          <!-- <el-table-column prop="orderState" label="订单状态" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.orderState }}
            </template>
          </el-table-column> -->
          <el-table-column prop="totalPrice" label="订单金额" width="120" align="center"></el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleOrderSizeChange"
            @current-change="handleOrderCurrentChange"
            :current-page="orderSearchForm.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="orderSearchForm.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="orderTotal">
          </el-pagination>
        </div>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="confirmOrderSelection" 
          :disabled="selectedOrders.length === 0">
          确认选择{{ selectedOrders.length > 0 ? `(${selectedOrders.length})` : '' }}
        </el-button>
        <el-button @click="closeOrderDialog">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { selectorPageWithItems } from '../api/ErpOrder'
import { getAllEnabledShops } from '../api/SysShop'
import DictSelect from './DictSelect'
import copyMixin from '../mixins/copyMixin'

export default {
  name: 'MultiOrderSelector',
  mixins: [copyMixin],
  components: {
    DictSelect
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    compact: {
      type: Boolean,
      default: false
    },
    readonlyMode: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择订单号'
    }
  },
  data() {
    return {
      selectedOrderSnList: [],
      showOrderSelectDialog: false,
      orderSearchForm: {
        shopId: '',
        orderSn: '',
        orderStates: '',
        current: 1,
        size: 10
      },
      shopList: [],
      orderTableData: [],
      orderTotal: 0,
      orderLoading: false,
      selectedOrders: [],
      isRestoringSelection: false // 标记是否正在恢复选中状态
    }
  },
  computed: {
    displayText() {
      if (!this.selectedOrderSnList || this.selectedOrderSnList.length === 0) {
        return '';
      }
      return this.selectedOrderSnList.join(', ');
    }
  },
  watch: {
    value: {
      handler(newVal) {
        const newList = Array.isArray(newVal) ? [...newVal] : [];
        // 简单比较数组长度和内容，避免JSON.stringify的性能问题
        if (newList.length !== this.selectedOrderSnList.length ||
            !newList.every((item, index) => item === this.selectedOrderSnList[index])) {
          this.selectedOrderSnList = newList;
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取行的key
    getRowKey(row) {
      return row.orderId;
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '';
      return new Date(dateTime).toLocaleString('zh-CN');
    },

    // 重置订单搜索
    resetOrderSearch() {
      this.orderSearchForm = {
        shopId: '',
        orderSn: '',
        orderStates: '',
        current: 1,
        size: 10
      };
      this.loadOrderData();
    },

    // 加载店铺列表
    loadShopList() {
      getAllEnabledShops()
        .then(res => {
          if (res.success) {
            this.shopList = res.data.records || [];
          } else {
            this.$message.error(res.message || '加载店铺列表失败');
          }
        })
        .catch(error => {
          this.$message.error('加载店铺列表失败: ' + error.message);
        });
    },

    // 加载订单数据
    loadOrderData() {
      this.orderLoading = true;
      selectorPageWithItems(this.orderSearchForm)
        .then(res => {
          this.orderLoading = false;
          if (res.success) {
            this.orderTableData = res.data.records || [];
            this.orderTotal = res.data.total || 0;
            
            // 恢复已选中的订单
            this.$nextTick(() => {
              this.restoreSelection();
            });
          } else {
            this.$message.error(res.message || '加载订单数据失败');
          }
        })
        .catch(error => {
          this.orderLoading = false;
          this.$message.error('加载订单数据失败: ' + error.message);
        });
    },

    // 恢复选中状态
    restoreSelection() {
      if (this.selectedOrderSnList.length > 0 && this.$refs.orderTable) {
        this.isRestoringSelection = true;

        // 先清除所有选中状态
        this.$refs.orderTable.clearSelection();

        // 然后恢复应该选中的行
        this.orderTableData.forEach(row => {
          if (this.selectedOrderSnList.includes(row.orderSn)) {
            this.$refs.orderTable.toggleRowSelection(row, true);
          }
        });

        this.$nextTick(() => {
          this.isRestoringSelection = false;
        });
      }
    },

    // 订单分页大小变化
    handleOrderSizeChange(val) {
      this.orderSearchForm.size = val;
      this.loadOrderData();
    },

    // 订单分页页码变化
    handleOrderCurrentChange(val) {
      this.orderSearchForm.current = val;
      this.loadOrderData();
    },

    // 打开订单选择对话框
    openOrderDialog() {
      if (this.disabled) return;
      this.showOrderSelectDialog = true;
      this.selectedOrders = [];
      // 打开对话框时加载数据
      this.loadOrderData();
    },

    // 多选模式下的选择变化事件
    handleSelectionChange(selection) {
      // 防止在恢复选中状态时触发不必要的更新
      if (!this.isRestoringSelection && Array.isArray(selection)) {
        this.selectedOrders = selection;
      }
    },

    // 行点击事件，切换选中状态
    handleRowClick(row, column) {
      // 如果点击的是复选框列，不处理（让复选框自己处理）
      if (column && column.type === 'selection') {
        return;
      }

      // 切换行的选中状态
      this.$refs.orderTable.toggleRowSelection(row);
    },

    // 确认选择订单
    confirmOrderSelection() {
      if (this.selectedOrders.length === 0) {
        this.$message.warning('请先选择订单');
        return;
      }

      // 获取新选择的订单号
      const newOrderSnList = this.selectedOrders.map(order => order.orderSn);

      // 合并到现有的订单列表中，去重
      const allOrderSnList = [...this.selectedOrderSnList];
      newOrderSnList.forEach(orderSn => {
        if (!allOrderSnList.includes(orderSn)) {
          allOrderSnList.push(orderSn);
        }
      });

      // 直接触发 input 事件，不修改内部状态
      this.$emit('input', allOrderSnList);

      // 关闭对话框
      this.closeOrderDialog();

      this.$message.success(`已添加 ${newOrderSnList.length} 个订单`);
    },

    // 移除订单号
    removeOrderSn(index) {
      const newList = [...this.selectedOrderSnList];
      newList.splice(index, 1);
      this.$emit('input', newList);
    },

    // 关闭订单对话框
    closeOrderDialog() {
      this.showOrderSelectDialog = false;
      this.selectedOrders = [];
      // 清除表格选中状态
      if (this.$refs.orderTable) {
        this.$refs.orderTable.clearSelection();
      }
    }
  },
  mounted() {
    // 组件挂载时加载店铺列表
    this.loadShopList();
  }
}
</script>

<style scoped>
.multi-order-selector {
  width: 100%;
}

/* 只读模式样式 */
.readonly-mode {
  width: 100%;
}

.readonly-input-container {
  width: 100%;
}

.readonly-input {
  width: 100%;
}

.readonly-input >>> .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: default;
}

.readonly-input >>> .el-input-group__append {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.readonly-input >>> .el-input-group__append .el-button {
  background-color: transparent;
  border: none;
  color: white;
  padding: 0 15px;
}

.readonly-input >>> .el-input-group__append .el-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.selected-orders-container {
  margin-top: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.selected-orders-header {
  margin-bottom: 10px;
}

.orders-count {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.order-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.no-orders {
  margin-top: 15px;
  padding: 20px;
  text-align: center;
  color: #909399;
  border: 1px dashed #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

.clickable-order-sn {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}

.clickable-order-sn:hover {
  color: #66b1ff;
}

.compact-display {
  display: inline-block;
  margin-left: 10px;
  font-size: 14px;
}

.selected-count {
  color: #409eff;
  font-weight: 500;
}

.no-selection {
  color: #909399;
}
</style>
