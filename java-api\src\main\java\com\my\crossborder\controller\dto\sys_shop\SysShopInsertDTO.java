package com.my.crossborder.controller.dto.sys_shop;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_店铺管理表
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysShopInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 店铺号
     */
    @NotNull(message="shopId不能为空")
    private Integer id;
    
    /**
     * 店铺名称
     */
	@NotBlank(message="shopName不能为空")
    private String shopName;


    /**
     * 逻辑删除（1-已删除，0-未删除）
     */
	@NotNull(message="disable不能为空")
    private Boolean disable;


}
