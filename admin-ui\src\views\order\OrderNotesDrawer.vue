<template>
  <div class="service-notes">
    <!-- <h3>订单备注</h3> -->
    <!-- 显示订单号 -->
    <div v-if="orderSn" class="order-number">
      <span class="order-label">订单号：</span>
      <span class="order-value">{{ orderSn }}</span>
    </div>
    <pre v-if="showDebug"
      style="font-size: 12px; max-height: 100px; overflow: auto; background: #f5f5f5; padding: 8px; margin-bottom: 10px;">{{ JSON.stringify(notes, null, 2) }}</pre>
    <div class="notes-container">
      <el-tabs tab-position="left" style="height: 400px;" v-model="activeTab">
        <el-tab-pane v-for="scene in sceneList" :key="scene.code" :name="scene.code">
          <span slot="label" class="tab-label-wrapper">
            <span class="tab-label-text">{{ scene.name }}</span>
            <span v-if="getSceneNotes(scene.code).length > 0" class="red-dot">●</span>
          </span>
          <!-- 显示已有备注 -->
          <div v-if="getSceneNotes(scene.code).length > 0">
            <div class="service-note-item" v-for="(note, index) in getSceneNotes(scene.code)" :key="index">
              <div class="note-header">
                <div class="note-header-left">
                  <span class="service-name">{{ note.createUserName }}</span>
                  <el-tag :type="getStatusType(note.sceneComplete)" size="small"
                    @click="handleStatusClick(note)"
                    :style="canModifyStatus(note) ? 'cursor: pointer; margin-left: 8px;' : 'margin-left: 8px;'">
                    {{ getStatusText(note.sceneComplete) }}
                  </el-tag>
                </div>
                <span class="note-time">{{ formatDate(note.updateTime) }}</span>
              </div>
              <!-- 编辑状态 -->
              <div v-if="editingNoteId === note.id" class="note-edit-container">
                <el-input type="textarea" :rows="4" v-model="editingContent" placeholder="请输入备注内容" maxlength="1000"
                  show-word-limit>
                </el-input>

                <!-- 场景02的快速填写标签 -->
                <div v-if="scene.code === '02'" class="quick-note-tags" v-show="quickNoteOptions.length > 0">
                  <div class="tags-label">快速填写：</div>
                  <div class="tags-container">
                    <el-tag
                      v-for="option in quickNoteOptions"
                      :key="option.value"
                      class="quick-note-tag"
                      @click="selectQuickNote(option.label)"
                      :type="editingContent === option.label ? 'primary' : ''"
                      effect="plain">
                      {{ option.label }}
                    </el-tag>
                  </div>
                </div>

                <div class="edit-buttons">
                  <el-button size="mini" type="primary" @click="saveNote(note)" :loading="editLoading">保存</el-button>
                  <el-button size="mini" @click="cancelEdit">取消</el-button>
                </div>
              </div>
              <!-- 显示状态 -->
              <div v-else class="note-content">{{ getNoteContent(note) }}</div>
              <!-- 编辑和删除按钮 -->
              <div v-if="canEditOrDelete(note, scene.code)" class="note-actions">
                <el-button v-if="!note.sceneComplete" type="text" size="mini" @click="editNote(note)" icon="el-icon-edit">编辑</el-button>
                <el-button type="text" size="mini" @click="deleteNote(note)" icon="el-icon-delete"
                  style="color: #f56c6c;">删除</el-button>
              </div>
            </div>
          </div>

          <!-- 当前场景下没有本人备注时显示添加按钮 -->
          <div v-if="scene.code === currentScene && !hasCurrentUserNote(scene.code)" class="add-note-section">
            <!-- 添加按钮状态 -->
            <div v-if="addingSceneCode !== scene.code" class="add-note-button">
              <el-button type="primary" size="small" @click="startAddNote(scene.code)" icon="el-icon-plus">
                添加备注
              </el-button>
            </div>

            <!-- 添加备注输入状态 -->
            <div v-else class="note-add-container">
              <el-input type="textarea" :rows="4" v-model="addingContent" placeholder="请输入备注内容" maxlength="500"
                show-word-limit>
              </el-input>

              <!-- 场景02的快速填写标签 -->
              <div v-if="scene.code === '02'" class="quick-note-tags" v-show="quickNoteOptions.length > 0">
                <div class="tags-label">快速填写：</div>
                <div class="tags-container">
                  <el-tag
                    v-for="option in quickNoteOptions"
                    :key="option.value"
                    class="quick-note-tag"
                    @click="selectQuickNoteForAdd(option.label)"
                    :type="addingContent === option.label ? 'primary' : ''"
                    effect="plain">
                    {{ option.label }}
                  </el-tag>
                </div>
              </div>

              <div class="edit-buttons">
                <el-button size="mini" type="primary" @click="saveNewNote(scene.code)"
                  :loading="addLoading">保存</el-button>
                <el-button size="mini" @click="cancelAddNote">取消</el-button>
              </div>
            </div>
          </div>

          <!-- 空状态提示 -->
          <div
            v-if="getSceneNotes(scene.code).length === 0 && (scene.code !== currentScene || hasCurrentUserNote(scene.code))"
            class="empty-notes">
            暂无相关备注
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>


  </div>
</template>

<script>
import { updateNote, deleteNote, insertNote, getByOrderSnAndScene, markCompleted } from '../../api/WkbNote'
import { dictCategoryItems } from '../../api/SysDictItem'

export default {
  name: 'OrderNotesDrawer',
  props: {
    notes: {
      type: Array,
      default: () => []
    },
    showDebug: {
      type: Boolean,
      default: false
    },
    orderSn: {
      type: String,
      default: ''
    },
    currentScene: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeTab: '01',
      editLoading: false,
      // 内联编辑相关
      editingNoteId: null, // 当前正在编辑的备注ID
      editingContent: '', // 编辑中的内容
      // 新增备注相关
      addingSceneCode: null, // 当前正在添加备注的场景代码
      addingContent: '', // 新增备注的内容
      addLoading: false, // 新增备注的加载状态
      // 本地备注数据副本
      localNotes: [],
      // 标记是否正在进行本地操作
      isLocalOperation: false,
      // 记录已删除的备注ID，防止被重新添加
      deletedNoteIds: new Set(),
      // 快速填写选项
      quickNoteOptions: [],
      // 场景列表
      sceneList: [
        { code: '01', name: '采购登记', routePath: 'OrdPurchase' },
        { code: '02', name: '未完整填写物流编号', routePath: 'OrdLogisticsNotComplete' },
        { code: '03', name: '已填物流编号未入库', routePath: 'OrdLogisticsDoneNotWarehoused' },
        { code: '04', name: '货物已入库未出库', routePath: 'OrdWarehousedNotOutbound' },
        { code: '05', name: '已采购但出库前取消', routePath: 'OrdRefund' },
        { code: '06', name: '货物已入库重新采购', routePath: 'OrdRepurchase' },
        { code: '07', name: '已出库售后', routePath: 'OrdAfterSale' },
        // { code: '08', name: '物流理赔', routePath: 'OrdClaim' },
      ]
    }
  },
  computed: {
    // 获取当前登录用户ID
    currentUserId() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.id || userEntity.userId
    }
  },
  watch: {
    // 监听当前场景变化，自动切换到对应tab
    currentScene(newScene) {
      if (newScene) {
        this.activeTab = newScene
      }
    },
    // 监听notes变化，同步到本地数据
    notes: {
      handler(newNotes) {
        // 如果正在进行本地操作，延迟同步以避免覆盖本地更改
        if (this.isLocalOperation) {
          setTimeout(() => {
            this.isLocalOperation = false
            // 合并外部数据和本地数据，优先保留本地更改
            this.mergeNotesData(newNotes)
          }, 300) // 增加延迟时间到300ms，确保删除操作完全完成
        } else {
          // 即使不是本地操作，也要过滤已删除的备注
          const filteredNotes = newNotes ? newNotes.filter(note => !this.deletedNoteIds.has(note.id)) : []
          this.localNotes = [...filteredNotes]
          // 清理已删除的备注ID记录，避免内存泄漏
          this.cleanupDeletedNoteIds(newNotes)
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    console.log('OrderNotesDrawer组件收到的原始notes:', JSON.stringify(this.notes))
    console.log('OrderNotesDrawer组件收到的订单号:', this.orderSn)
    console.log('OrderNotesDrawer组件收到的当前场景:', this.currentScene)

    // 根据当前场景设置默认激活的tab
    if (this.currentScene) {
      this.activeTab = this.currentScene
    }

    // 初始化本地数据
    this.localNotes = this.notes ? [...this.notes] : []

    // 加载快速填写选项
    this.loadQuickNoteOptions()
  },
  methods: {
    // 检查是否可以编辑或删除备注
    canEditOrDelete(note, scene) {
      // 当前场景匹配且用户ID是本人时可以编辑删除
      return scene === this.currentScene && note.createUserId === this.currentUserId
    },

    // 检查是否可以修改状态
    canModifyStatus(note) {
      // 只有本人的待处理备注可以修改状态
      return note.createUserId === this.currentUserId && !note.sceneComplete
    },

    // 检查当前用户在指定场景下是否有备注
    hasCurrentUserNote(sceneCode) {
      if (!this.localNotes || !Array.isArray(this.localNotes)) return false
      return this.localNotes.some(note =>
        note.scene === sceneCode &&
        note.createUserId === this.currentUserId
      )
    },

    // 编辑备注
    editNote(note) {
      this.editingNoteId = note.id
      this.editingContent = note.content || ''
    },

    // 取消编辑
    cancelEdit() {
      this.editingNoteId = null
      this.editingContent = ''
    },

    // 开始添加备注
    startAddNote(sceneCode) {
      this.addingSceneCode = sceneCode
      this.addingContent = ''
    },

    // 取消添加备注
    cancelAddNote() {
      this.addingSceneCode = null
      this.addingContent = ''
    },

    // 删除备注
    deleteNote(note) {
      this.$confirm('确定要删除这条备注吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 先记录要删除的备注ID，防止后续数据同步时重新显示
        this.deletedNoteIds.add(note.id)

        deleteNote({ idList: [note.id] }).then(res => {
          if (res.success) {
            this.$message.success('删除成功')
            // 标记正在进行本地操作，延长时间确保数据同步完成
            this.isLocalOperation = true
            // 立即从本地数据中移除该备注
            this.removeNoteFromLocal(note.id)
            this.$emit('note-updated')
          } else {
            this.$message.error(res.message || '删除失败')
            // 删除失败时移除删除标记
            this.deletedNoteIds.delete(note.id)
          }
        }).catch(err => {
          this.$message.error('删除失败：' + err.message)
          // 删除失败时移除删除标记
          this.deletedNoteIds.delete(note.id)
        })
      }).catch(() => {
        // 用户取消删除时移除删除标记
        this.deletedNoteIds.delete(note.id)
      })
    },

    // 保存备注
    saveNote(note) {
      // 验证内容
      if (!this.editingContent || this.editingContent.trim() === '') {
        this.$message.error('备注内容不能为空')
        return
      }

      if (this.editingContent.length > 500) {
        this.$message.error('备注内容不能超过500个字符')
        return
      }

      this.editLoading = true
      updateNote({
        id: note.id,
        content: this.editingContent.trim()
      }).then(res => {
        this.editLoading = false
        if (res.success) {
          this.$message.success('更新成功')
          // 标记正在进行本地操作
          this.isLocalOperation = true
          // 立即更新本地数据中的备注内容
          this.updateNoteInLocal(note.id, this.editingContent.trim())
          this.cancelEdit()
          this.$emit('note-updated')
        } else {
          this.$message.error(res.message || '更新失败')
        }
      }).catch(err => {
        this.editLoading = false
        this.$message.error('更新失败：' + err.message)
      })
    },

    // 保存新备注
    saveNewNote(sceneCode) {
      // 验证内容
      if (!this.addingContent || this.addingContent.trim() === '') {
        this.$message.error('备注内容不能为空')
        return
      }

      if (this.addingContent.length > 500) {
        this.$message.error('备注内容不能超过500个字符')
        return
      }

      this.addLoading = true
      insertNote({
        orderSn: this.orderSn,
        content: this.addingContent.trim(),
        scene: sceneCode
      }).then(res => {
        this.addLoading = false
        if (res.success) {
          this.$message.success('备注已保存')
          // 标记正在进行本地操作
          this.isLocalOperation = true
          // 重新获取该场景下的完整备注数据，确保ID正确
          this.refreshSceneNote(sceneCode)
          this.cancelAddNote()
          this.$emit('note-updated')
        } else {
          this.$message.error(res.message || '保存失败')
        }
      }).catch(err => {
        this.addLoading = false
        this.$message.error('保存备注失败：' + err.message)
      })
    },

    // 获取指定场景的备注
    getSceneNotes(scene) {
      if (!this.localNotes || !Array.isArray(this.localNotes)) {
        return []
      }
      const sceneNotes = this.localNotes.filter(note => note.scene === scene)
      // 按创建时间倒序排列，最新的在最上面
      return sceneNotes.sort((a, b) => {
        const timeA = new Date(a.createTime || a.updateTime || 0).getTime()
        const timeB = new Date(b.createTime || b.updateTime || 0).getTime()
        return timeB - timeA // 倒序，最新的在前
      })
    },

    // 从本地数据中移除备注
    removeNoteFromLocal(noteId) {
      if (!this.localNotes || !Array.isArray(this.localNotes)) return

      console.log('删除备注，ID:', noteId)
      console.log('当前本地备注:', this.localNotes.map(n => ({ id: n.id, content: n.content.substring(0, 20) })))

      // 确保删除的备注ID已被记录
      this.deletedNoteIds.add(noteId)

      // 使用filter方法确保彻底删除，避免splice可能的问题
      const originalLength = this.localNotes.length
      this.localNotes = this.localNotes.filter(note => note.id !== noteId)

      if (this.localNotes.length < originalLength) {
        console.log('成功删除备注，ID:', noteId)
        console.log('删除后本地备注:', this.localNotes.map(n => ({ id: n.id, content: n.content.substring(0, 20) })))
      } else {
        console.warn('未找到要删除的备注，ID:', noteId)
        // 尝试按场景和用户删除（处理ID不匹配的情况）
        const beforeLength = this.localNotes.length
        this.localNotes = this.localNotes.filter(note =>
          !(note.scene === this.currentScene && note.createUserId === this.currentUserId)
        )
        if (this.localNotes.length < beforeLength) {
          console.log('通过场景和用户ID删除了备注')
        }
      }
    },

    // 更新本地数据中的备注内容
    updateNoteInLocal(noteId, newContent) {
      if (!this.localNotes || !Array.isArray(this.localNotes)) return
      const note = this.localNotes.find(note => note.id === noteId)
      if (note) {
        note.content = newContent
        // 更新修改时间（如果API返回了新的时间，可以在这里设置）
        note.updateTime = new Date().toISOString()
      }
    },

    // 添加新备注到本地数据
    addNoteToLocal(noteData, sceneCode) {
      if (!this.localNotes) {
        this.localNotes = []
      }

      // 构建新的备注对象
      const newNote = {
        id: (noteData && noteData.id) || Date.now(), // 如果API返回了ID使用API的，否则使用时间戳
        content: this.addingContent.trim(),
        scene: sceneCode,
        orderSn: this.orderSn,
        createUserId: this.currentUserId,
        createUserName: this.getCurrentUserName(),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      // 如果API返回了其他数据，合并到新备注对象中
      if (noteData) {
        Object.assign(newNote, noteData)
        // 确保用户名不被覆盖
        if (!newNote.createUserName) {
          newNote.createUserName = this.getCurrentUserName()
        }
      }

      this.localNotes.push(newNote)
    },

    // 获取当前用户名
    getCurrentUserName() {
      const userEntity = this.$store.getters.userEntity || JSON.parse(localStorage.getItem('userEntity') || '{}')
      return userEntity.realName || userEntity.username || '当前用户'
    },

    // 合并外部数据和本地数据
    mergeNotesData(newNotes) {
      if (!newNotes || !Array.isArray(newNotes)) {
        return
      }

      console.log('开始合并数据，外部数据:', newNotes.length, '本地数据:', (this.localNotes && this.localNotes.length) || 0)
      console.log('已删除的备注ID:', Array.from(this.deletedNoteIds))

      // 先过滤掉已删除的备注
      const filteredNewNotes = newNotes.filter(note => {
        const isDeleted = this.deletedNoteIds.has(note.id)
        if (isDeleted) {
          console.log('过滤已删除的备注:', note.id)
        }
        return !isDeleted
      })

      // 如果本地数据为空，直接使用过滤后的外部数据
      if (!this.localNotes || this.localNotes.length === 0) {
        this.localNotes = [...filteredNewNotes]
        return
      }

      // 创建一个Map来快速查找本地数据
      const localNotesMap = new Map()
      this.localNotes.forEach(note => {
        // 只保留未被删除的本地数据
        if (!this.deletedNoteIds.has(note.id)) {
          localNotesMap.set(note.id, note)
        }
      })

      // 合并数据：优先保留本地更改，添加新的外部数据
      const mergedNotes = []

      // 添加所有过滤后的外部数据，如果本地有相同ID的数据则保留本地版本
      filteredNewNotes.forEach(externalNote => {
        const localNote = localNotesMap.get(externalNote.id)
        if (localNote) {
          // 保留本地版本
          mergedNotes.push(localNote)
          localNotesMap.delete(externalNote.id)
        } else {
          // 添加新的外部数据
          mergedNotes.push(externalNote)
        }
      })

      // 添加本地独有的数据（新增的备注），但要确保不是已删除的
      localNotesMap.forEach(localNote => {
        if (!this.deletedNoteIds.has(localNote.id)) {
          mergedNotes.push(localNote)
        }
      })

      console.log('合并完成，最终数据:', mergedNotes.length)
      this.localNotes = mergedNotes
    },

    // 清理已删除的备注ID记录
    cleanupDeletedNoteIds(currentNotes) {
      if (!currentNotes || !Array.isArray(currentNotes)) return

      // 获取当前所有备注的ID
      const currentNoteIds = new Set(currentNotes.map(note => note.id))

      // 清理那些在当前数据中已经不存在的删除记录
      this.deletedNoteIds.forEach(deletedId => {
        if (!currentNoteIds.has(deletedId)) {
          this.deletedNoteIds.delete(deletedId)
          console.log('清理已删除的备注ID记录:', deletedId)
        }
      })
    },

    // 刷新指定场景的备注数据
    refreshSceneNote(sceneCode) {
      getByOrderSnAndScene({
        orderSn: this.orderSn,
        scene: sceneCode
      }).then(res => {
        if (res.success && res.data) {
          // 确保服务器返回的数据包含用户名
          const noteData = { ...res.data }
          if (!noteData.createUserName) {
            noteData.createUserName = this.getCurrentUserName()
          }

          // 找到本地数据中该场景下当前用户的备注并更新
          const existingIndex = this.localNotes.findIndex(note =>
            note.scene === sceneCode &&
            note.createUserId === this.currentUserId
          )

          console.log('刷新场景备注，场景:', sceneCode, '现有索引:', existingIndex)
          console.log('服务器返回数据:', noteData)

          if (existingIndex !== -1) {
            // 更新现有备注（替换临时数据）
            console.log('替换现有备注，原ID:', this.localNotes[existingIndex].id, '新ID:', noteData.id)
            this.localNotes.splice(existingIndex, 1, noteData)
          } else {
            // 添加新备注
            console.log('添加新备注')
            this.localNotes.push(noteData)
          }
        }
      }).catch(err => {
        console.error('刷新备注数据失败：', err)
        // 如果刷新失败，仍然使用原来的方法添加本地数据
        this.addNoteToLocal(null, sceneCode)
      })
    },

    // 获取备注内容
    getNoteContent(note) {
      return note.content || '无内容'
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 状态文本转换
    getStatusText(status) {
      switch (status) {
        case false: return '待处理';
        case true: return '已处理';
        default: return '未知';
      }
    },

    // 获取状态标签类型
    getStatusType(status) {
      switch (status) {
        case false: return 'warning';
        case true: return 'success';
        default: return 'info';
      }
    },

    // 处理状态点击事件
    handleStatusClick(note) {
      // 检查是否可以修改状态（只有本人的待处理备注可以修改）
      if (!this.canModifyStatus(note)) {
        return;
      }

      this.$confirm('确定要将此备注标记为已处理吗？', '状态变更', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await markCompleted(note.id);
          this.$message.success('状态变更成功');
          // 更新本地数据中的状态
          this.updateNoteStatusInLocal(note.id, true);
          this.$emit('note-updated');
        } catch (error) {
          this.$message.error('状态变更失败');
        }
      });
    },

    // 更新本地数据中的备注状态
    updateNoteStatusInLocal(noteId, sceneComplete) {
      if (!this.localNotes || !Array.isArray(this.localNotes)) return
      const note = this.localNotes.find(note => note.id === noteId)
      if (note) {
        note.sceneComplete = sceneComplete
        // 更新修改时间
        note.updateTime = new Date().toISOString()
      }
    },

    // 加载快速填写选项
    async loadQuickNoteOptions() {
      try {
        const res = await dictCategoryItems({ categoryId: 'SCENE_02_QUICKNOTE' })
        if (res.success && res.data) {
          this.quickNoteOptions = res.data
        }
      } catch (error) {
        console.error('加载快速填写选项失败:', error)
      }
    },

    // 选择快速填写内容（编辑时）
    selectQuickNote(noteText) {
      this.editingContent = noteText
    },

    // 选择快速填写内容（添加时）
    selectQuickNoteForAdd(noteText) {
      this.addingContent = noteText
    }
  }
}
</script>

<style scoped>
.service-notes {
  padding: 0;
}

.order-number {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.order-label {
  font-weight: bold;
  color: #606266;
}

.order-value {
  color: #409EFF;
  font-weight: bold;
  margin-left: 5px;
}

.notes-container {
  margin-top: 10px;
}

.service-note-item {
  margin-bottom: 15px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.note-header-left {
  display: flex;
  align-items: center;
}

.service-name {
  font-weight: bold;
  color: #303133;
  font-size: 13px;
}

.note-header-left .el-tag {
  font-size: 13px;
  height: auto;
  line-height: 1.2;
  padding: 2px 6px;
}

.note-time {
  font-size: 12px;
  color: #909399;
}

.note-actions {
  display: flex;
  gap: 5px;
  justify-content: flex-end;
  margin-top: 8px;
}

.note-content {
  color: #606266;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.empty-notes {
  text-align: center;
  color: #909399;
  padding: 20px;
  font-style: italic;
}

/* 调整tabs样式 */
.el-tabs--left .el-tabs__content {
  padding-left: 20px;
}

.el-tabs--left .el-tabs__nav-wrap {
  margin-right: 20px;
}

/* 内联编辑样式 */
.note-edit-container {
  margin-top: 8px;
}

.edit-buttons {
  margin-top: 8px;
  text-align: right;
}

.edit-buttons .el-button {
  margin-left: 8px;
}

/* 添加备注样式 */
.add-note-section {
  margin-top: 15px;
  padding: 15px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.add-note-button {
  text-align: center;
}

.note-add-container {
  margin-top: 8px;
}

/* 快速填写标签样式 */
.quick-note-tags {
  margin-top: 12px;
}

.tags-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-note-tag {
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #dcdfe6;
}

.quick-note-tag:hover {
  border-color: #409eff;
  color: #409eff;
}

/* 标签包装器样式 */
.tab-label-wrapper {
  position: relative;
  display: inline-block;
}

.tab-label-text {
  display: inline-block;
}

/* 红点样式 */
.red-dot {
  color: #f56c6c;
  font-size: 16px;
  position: absolute;
  top: -1px;
  right: -8px;
  line-height: 1;
}
</style>
