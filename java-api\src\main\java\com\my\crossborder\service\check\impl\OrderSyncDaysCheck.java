package com.my.crossborder.service.check.impl;

import java.lang.reflect.Field;

import org.apache.commons.lang3.StringUtils;

import com.my.crossborder.service.check.AbstractCheck;
import com.my.crossborder.service.check.result.CheckResult;

/**
 * 订单同步日期
 * <AUTHOR>
 */
public class OrderSyncDaysCheck extends AbstractCheck {

	
    @Override
    protected CheckResult doCheck(Object fieldValue, Field field, Object obj) {
        String val = (String) fieldValue;
        
        if (StringUtils.isBlank(val)) {
            return fail(this.fieldLabel + "不能为空");
        }

        if (Integer.parseInt(val) < 30 || Integer.parseInt(val) > 90) {
            return fail(this.fieldLabel + "最小30天，最大90天");
        }
        
        return success();
    }
}
