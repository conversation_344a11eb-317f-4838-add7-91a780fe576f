package com.my.crossborder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款结算状态枚举
 * 对应字典：REFUND_SETTLEMENT_STATUS
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Getter
@AllArgsConstructor
public enum RefundSettlementStatus {
	
	/**
	 * 无需结算
	 */
	NO_NEED("-1", "无需结算"),

    /**
     * 待结算
     */
    PENDING("0", "待结算"),

    /**
     * 已结算
     */
    DONE("1", "已结算"),
    
    ;

    /**
     * 字典值
     */
    private final String value;

    /**
     * 字典名称
     */
    private final String name;

    /**
     * 根据值获取枚举
     * @param value 字典值
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static RefundSettlementStatus getByValue(String value) {
        for (RefundSettlementStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据值获取名称
     * @param value 字典值
     * @return 对应的名称，如果找不到则返回null
     */
    public static String getNameByValue(String value) {
        RefundSettlementStatus status = getByValue(value);
        return status != null ? status.getName() : null;
    }
}
