package com.my.crossborder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseDeleteDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseInsertDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchasePageDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseUpdateDTO;
import com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchaseDetailVO;
import com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchasePageVO;
import com.my.crossborder.mybatis.entity.OrdRepurchase;

/**
 * 重新采购 服务类
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface OrdRepurchaseService extends IService<OrdRepurchase> {

	/**
	 * 新增
	 */
	void insert(OrdRepurchaseInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(OrdRepurchaseUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	OrdRepurchaseDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<OrdRepurchasePageVO> page(OrdRepurchasePageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(OrdRepurchaseDeleteDTO deleteDTO);

	/**
	 * 确认完成
	 * @param id 重新采购记录ID
	 */
	void confirmComplete(Integer id);

}
