package com.my.crossborder.controller.vo.stl_purchase;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 月度采购信息VO
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class MonthlyPurchaseDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 结算标志 true-已结算 false-未结算
     */
    private Boolean settlementFlag;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 结算日期
     */
    private LocalDateTime settlementDate;

    /**
     * 备注
     */
    private String remark;

}
