# 字典表格组件使用指南

本项目提供了一套完整的字典表格解决方案，用于自动处理 el-table 中字典类型字段的显示转换。

## 功能特性

- 🚀 **自动缓存**：字典数据自动缓存，避免重复请求
- 🔄 **多种用法**：提供4种不同的使用方式，满足各种场景
- 📦 **开箱即用**：全局注册，无需手动导入
- ⚡ **性能优化**：支持预加载和批量转换
- 🛡️ **容错处理**：网络异常时优雅降级

## 核心组件

### 1. DictService (字典服务)
负责字典数据的获取、缓存和转换

### 2. DictTableColumn (字典表格列)
直接替换 `el-table-column`，自动处理字典转换

### 3. DictTable (智能表格)
包装 `el-table`，统一处理多个字典字段

## 使用方法

### 方法一：DictTableColumn 组件 (推荐)

最简单的用法，直接替换普通的 `el-table-column`：

```vue
<el-table :data="tableData">
  <!-- 普通列 -->
  <el-table-column prop="pipelineName" label="管道名称"></el-table-column>

  <!-- 字典列 -->
  <dict-table-column
    prop="pipelineLevel"
    label="管道级别"
    category-id="PIPE_LEVEL"
    width="100">
  </dict-table-column>
</el-table>
```

### 方法二：DictTable 组件

适用于有多个字典字段的表格：

```vue
<template>
  <dict-table
    :data="tableData"
    :dict-mapping="dictMapping"
    border>
    <el-table-column prop="pipelineName" label="管道名称"></el-table-column>
    <!-- 使用转换后的字段名 -->
    <el-table-column prop="pipelineLevelLabel" label="管道级别"></el-table-column>
  </dict-table>
</template>

<script>
export default {
  data() {
    return {
      dictMapping: {
        pipelineLevel: 'PIPE_LEVEL',
        status: 'STATUS_TYPE'
      }
    }
  }
}
</script>
```

### 方法三：使用全局方法

在模板中使用字典转换方法：

```vue
<el-table :data="tableData">
  <el-table-column label="管道级别">
    <template slot-scope="scope">
      {{ dictLabel('PIPE_LEVEL', scope.row.pipelineLevel) }}
    </template>
  </el-table-column>
</el-table>
```

### 方法四：使用过滤器

最简洁的用法：

```vue
<el-table :data="tableData">
  <el-table-column label="管道级别">
    <template slot-scope="scope">
      {{ scope.row.pipelineLevel | dictLabel('PIPE_LEVEL') }}
    </template>
  </el-table-column>
</el-table>
```

## JavaScript 中使用

### 预加载字典数据

```javascript
export default {
  async created() {
    // 预加载页面需要的字典数据
    await this.preloadDicts(['PIPE_LEVEL', 'STATUS_TYPE'])
  }
}
```

### 异步获取字典标签

```javascript
// 异步获取（会自动请求API）
const label = await this.getDictLabel('PIPE_LEVEL', 'GC1')

// 同步获取（仅从缓存获取）
const labelSync = this.dictLabel('PIPE_LEVEL', 'GC1')
```

### 批量转换数据

```javascript
const convertedData = await this.convertTableDictFields(this.tableData, {
  pipelineLevel: 'PIPE_LEVEL',
  status: 'STATUS_TYPE'
})
```

## API 参考

### DictTableColumn 属性

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| category-id | String | 是 | - | 字典分类ID |
| prop | String | 是 | - | 字段属性名 |
| label | String | 是 | - | 列标题 |
| width | String/Number | 否 | - | 列宽度 |
| min-width | String/Number | 否 | - | 最小列宽度 |
| align | String | 否 | center | 对齐方式 |
| empty-text | String | 否 | '-' | 空值显示文本 |

### DictTable 属性

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| data | Array | 是 | [] | 表格数据 |
| dict-mapping | Object | 否 | {} | 字典映射配置 |
| auto-convert | Boolean | 否 | true | 是否自动转换 |

### 全局方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| dictLabel(categoryId, value) | categoryId: 字典分类ID<br>value: 字典值 | String | 同步获取字典标签 |
| getDictLabel(categoryId, value) | 同上 | Promise\<String\> | 异步获取字典标签 |
| preloadDicts(categoryIds) | categoryIds: 字典分类ID数组 | Promise | 预加载字典数据 |
| convertTableDictFields(data, mapping) | data: 数据数组<br>mapping: 字典映射 | Promise\<Array\> | 批量转换字典字段 |

## 最佳实践

### 1. 页面初始化时预加载字典

```javascript
export default {
  async created() {
    // 预加载页面需要的所有字典
    await this.preloadDicts(['PIPE_LEVEL', 'STATUS_TYPE', 'PRIORITY_LEVEL'])

    // 然后再加载页面数据
    this.loadPageData()
  }
}
```

### 2. 统一管理字典分类

建议在项目中创建字典常量文件：

```javascript
// constants/dictTypes.js
export const DICT_TYPES = {
  PIPE_LEVEL: 'PIPE_LEVEL',
  STATUS_TYPE: 'STATUS_TYPE',
  PRIORITY_LEVEL: 'PRIORITY_LEVEL'
}
```

### 3. 错误处理

字典组件会自动处理网络错误，在获取失败时显示原始值。

## 注意事项

1. **字典分类ID**：确保传入的 `category-id` 与后台字典配置一致
2. **缓存过期**：字典数据默认缓存30分钟，可通过 `dictService.cacheExpireTime` 调整
3. **性能考虑**：大量数据时建议使用方法二（DictTable）进行批量转换
4. **兼容性**：组件完全兼容 Element UI 的 Table 组件所有属性和事件

## 故障排除

### 字典不显示
1. 检查字典分类ID是否正确
2. 检查网络请求是否成功
3. 检查字典数据格式是否正确

### 性能问题
1. 使用预加载减少请求次数
2. 对于大数据量使用 DictTable 组件
3. 合理设置缓存时间

## 更新日志

- v1.0.0: 初始版本，提供基础字典转换功能
- v1.1.0: 添加 DictTable 组件，支持批量转换
- v1.2.0: 添加全局方法和过滤器
- v1.3.0: 优化缓存机制和错误处理
