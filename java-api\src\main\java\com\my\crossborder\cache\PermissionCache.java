package com.my.crossborder.cache;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.my.crossborder.controller.vo.sys_menu_ref_role.SysMenuRefRolePermissionVO;
import com.my.crossborder.mybatis.mapper.SysMenuRefRoleMapper;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;

/**
 * 权限缓存
 * <AUTHOR>
 * @date 2023年4月22日
 */
@Component
@RequiredArgsConstructor
public class PermissionCache {
	
	private final SysMenuRefRoleMapper sysMenuRefRoleMapper;
	
	/** 菜单缓存 */
	private Map<Integer, List<String>> menuMap;
	
	/** 权限缓存 */
	private Map<Integer, List<String>> permissionMap;
	
	
	/**
	 * 重置缓存
	 */
	@PostConstruct
	public void reloadCache() {
		// menuId和菜单
		List<SysMenuRefRolePermissionVO> voList = this.sysMenuRefRoleMapper.menuIdAndPermission();
		Set<Integer> roleIdSet = voList.stream()
				.map(t -> t.getRoleId())
				.collect(Collectors.toSet());
		
		this.menuMap = Maps.newHashMap();
		this.permissionMap = Maps.newHashMap();
		roleIdSet.forEach(roleId -> {
			// menuId映射
			List<String> menuIds = voList.stream()
					.filter(t -> t.getRoleId().intValue() == roleId.intValue())
					.map(t -> t.getMenuId())
					.collect(Collectors.toList());
			if (!CollectionUtil.isEmpty(menuIds)) {
				this.menuMap.put(roleId, menuIds);
			}
			
			// permission映射
			List<String> permissions = voList.stream()
					.filter(t -> t.getRoleId().intValue() == roleId.intValue())
					.filter(t -> t.getMenu() != null && !t.getMenu())
					.filter(t -> t.getPermission() != null)
					.map(t -> t.getPermission())
					.collect(Collectors.toList());
			if (!CollectionUtil.isEmpty(permissions)) {
				this.permissionMap.put(roleId, permissions);
			}
		});
	}
	
	/**
	 * 根据角色ID查询菜单ID列表
	 * @param roleId 角色ID
	 * @return 菜单ID列表
	 */
	public List<String> getMenuIds(Integer roleId) {
		return this.menuMap.getOrDefault(roleId, Lists.newLinkedList());
	}

	/**
	 * 返回权限集合
	 * @return
	 */
	public List<String> getPermissions(Integer roleId) {
		return this.permissionMap.getOrDefault(roleId, Lists.newLinkedList());
	}

	
}
