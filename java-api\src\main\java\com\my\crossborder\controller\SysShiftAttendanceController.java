package com.my.crossborder.controller;

import java.time.LocalDate;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.my.crossborder.controller.dto.sys_shift.SysShiftPunchInDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_shift_attendance.MonthAttendanceFull;
import com.my.crossborder.controller.vo.sys_shift_attendance.MonthAttendanceSummary;
import com.my.crossborder.controller.vo.sys_shift_attendance.TodayAttendanceStatus;
import com.my.crossborder.service.SysShiftAttendanceService;

import lombok.RequiredArgsConstructor;

/**
 * 考勤表
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/api/sys-shift-attendance")
@RequiredArgsConstructor
public class SysShiftAttendanceController {

	private final SysShiftAttendanceService sysShiftAttendanceService;

	/**
	 * 今日考勤状态
	 */
	@GetMapping("/today-attendance-status")
	public StdResp<TodayAttendanceStatus> getTodayAttendanceStatus(@RequestParam Integer userId,
			@RequestParam LocalDate date) {
		TodayAttendanceStatus data = this.sysShiftAttendanceService.todayAttendanceStatus(userId, date);
		return StdResp.success(data);
	}

	/**
	 * 执行打卡
	 */
	@PostMapping("/punch-in")
	public StdResp<?> punchIn(@Valid @RequestBody SysShiftPunchInDTO punchInDTO) {
		this.sysShiftAttendanceService.punchIn();
		return StdResp.success();
	}

	/**
	 * 获取月度考勤统计
	 */
	@GetMapping("/monthly-attendance-stats")
	public StdResp<MonthAttendanceSummary> monthAttendanceSummary(@RequestParam Integer userId,
			@RequestParam Integer year, @RequestParam Integer month) {
		MonthAttendanceSummary data = this.sysShiftAttendanceService.monthAttendanceSummary(userId, year, month);
		return StdResp.success(data);
	}

	/**
	 * 获取月度考勤数据
	 */
	@GetMapping("/monthly-attendance-data")
	public StdResp<MonthAttendanceFull> monthlyAttendanceFull(@RequestParam Integer userId, @RequestParam Integer year,
			@RequestParam Integer month) {
		MonthAttendanceFull data = this.sysShiftAttendanceService.monthlyAttendanceFull(userId, year, month);
		return StdResp.success(data);
	}

	/**
	 * 获取指定日期的排班详情
	 */
	@GetMapping("/date-schedule-detail")
	public StdResp<Map<String, Object>> dateShift(@RequestParam Integer userId, @RequestParam String date) {
		Map<String, Object> result = this.sysShiftAttendanceService.dateShift(userId, date);
		return StdResp.success(result);
	}

 
}
