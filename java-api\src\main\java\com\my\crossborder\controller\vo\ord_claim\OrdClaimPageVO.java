package com.my.crossborder.controller.vo.ord_claim;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.my.crossborder.controller.vo.AttachmentVO;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_物流理赔
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdClaimPageVO 
                    extends AttachmentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 问题描述
     */
    private String issue;

    /**
     * 问题类别 字典claim_issue_type
     */
    private String issueType;

    /**
     * 订单项id
     */
    private String orderItemId;

    /**
     * 采购金额
     */
    private BigDecimal purchaseCost;

    /**
     * 补寄单号
     */
    private String waybillNumber;

    /**
     * 处理办法 字典claim_close_way
     */
    private String closeWay;

    /**
     * 理赔进度 字典claim_status
     */
    private String claimStatus;

    /**
     * 录入人id
     */
    private Integer issueUserId;

    /**
     * 录入时间
     */
    private LocalDateTime issueTime;

    /**
     * 处理人id
     */
    private Integer closeUserId;
    
    /**
     * 处理人姓名
     */
    private String closeUserName;

    /**
     * 处理时间
     */
    private LocalDateTime closeTime;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 商品售价
     */
    private BigDecimal productPrice;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品规格
     */
    private String itemModelName;

    /**
     * 商品图片
     */
    private String itemImage;

    /**
     * 商品型号SKU
     */
    private String itemModelSku;

    /**
     * 商品价格
     */
    private BigDecimal itemPrice;

    /**
     * 工作笔记列表
     */
    private List<WkbNoteDetailVO> notes;
    
}
