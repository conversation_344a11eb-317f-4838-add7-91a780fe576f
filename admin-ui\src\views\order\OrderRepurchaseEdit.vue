<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="handleDialogClose">
    <el-form :model="repurchaseForm" :rules="repurchaseRules" ref="repurchaseForm" label-width="120px">
      <el-form-item label="订单号" prop="orderSn">
        <order-selector v-model="repurchaseForm.orderSn" placeholder="请选择订单" @select="handleOrderSelect" style="width: 100%;"></order-selector>
      </el-form-item>

      <el-form-item label="问题描述" prop="issue">
        <el-input v-model="repurchaseForm.issue" type="textarea" :rows="3"
          placeholder="请详细描述遇到的问题"></el-input>
      </el-form-item>

      <el-form-item label="处理办法" prop="closeWay">
        <dict-select
          v-model="repurchaseForm.closeWay"
          category-id="REPURCHASE_CLOSE_WAY"
          placeholder="请选择处理办法"
          style="width: 100%;">
        </dict-select>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button type="primary" @click="submitRepurchaseForm">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { insertOrdRepurchase, updateOrdRepurchase } from '../../api/OrdRepurchase'
import DictSelect from '../../components/DictSelect'
import OrderSelector from '../../components/OrderSelector'

export default {
  name: 'OrderRepurchaseEdit',
  components: {
    DictSelect,
    OrderSelector
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      repurchaseForm: {
        id: null,
        orderSn: '',
        issue: '',
        closeWay: '',
        closed: false
      },
      repurchaseRules: {
        orderSn: [
          { required: true, message: '请输入订单号', trigger: 'blur' }
        ],
        issue: [
          { required: true, message: '请输入问题描述', trigger: 'blur' }
        ],
        closeWay: [
          { required: true, message: '请选择处理办法', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return this.isEdit ? '编辑重新采购' : '添加重新采购'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.editData) {
        this.repurchaseForm = { ...this.editData }
      } else {
        this.repurchaseForm = {
          id: null,
          orderSn: '',
          issue: '',
          closeWay: '',
          closed: false
        }
      }
    },

    // 关闭对话框
    handleDialogClose() {
      this.$refs.repurchaseForm.resetFields()
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    },

    // 处理订单选择
    handleOrderSelect(order) {
      // 订单选择后的处理逻辑，可以在这里添加额外的业务逻辑
      console.log('选择的订单:', order)
    },

    // 提交表单
    submitRepurchaseForm() {
      this.$refs.repurchaseForm.validate((valid) => {
        if (valid) {
          // 格式化时间为后端期望的格式 (YYYY-MM-DD HH:mm:ss)
          const now = new Date()
          const issueTime = now.getFullYear() + '-' +
            String(now.getMonth() + 1).padStart(2, '0') + '-' +
            String(now.getDate()).padStart(2, '0') + ' ' +
            String(now.getHours()).padStart(2, '0') + ':' +
            String(now.getMinutes()).padStart(2, '0') + ':' +
            String(now.getSeconds()).padStart(2, '0')

          const formData = {
            ...this.repurchaseForm,
            issueTime: issueTime
          }

          if (this.isEdit && this.repurchaseForm.id) {
            // 编辑现有记录
            updateOrdRepurchase(formData)
              .then(res => {
                if (res.success) {
                  this.$message.success('修改成功')
                  this.dialogVisible = false
                  this.$emit('submit')
                } else {
                  this.$message.error('修改失败：' + res.message)
                }
              })
              .catch(err => {
                this.$message.error('修改失败：' + err.message)
              })
          } else {
            // 添加新记录
            insertOrdRepurchase(formData)
              .then(res => {
                if (res.success) {
                  this.$message.success('添加成功')
                  this.dialogVisible = false
                  this.$emit('submit')
                } else {
                  this.$message.error('添加失败：' + res.message)
                }
              })
              .catch(err => {
                this.$message.error('添加失败：' + err.message)
              })
          }
        } else {
          this.$message.warning('请完成必填项')
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
/* 组件样式 */
</style>
