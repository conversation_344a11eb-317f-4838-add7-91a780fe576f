package com.my.crossborder.service.impl;

import com.my.crossborder.mybatis.entity.OrdRepurchase;
import com.my.crossborder.mybatis.mapper.OrdRepurchaseMapper;
import com.my.crossborder.service.OrdRepurchaseService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseInsertDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchasePageDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseUpdateDTO;
import com.my.crossborder.controller.dto.ord_repurchase.OrdRepurchaseDeleteDTO;
import com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchaseDetailVO;
import com.my.crossborder.controller.vo.ord_repurchase.OrdRepurchasePageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;

import org.springframework.stereotype.Service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 重新采购 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class OrdRepurchaseServiceImpl extends ServiceImpl<OrdRepurchaseMapper, OrdRepurchase> implements OrdRepurchaseService {


	@Transactional
	@Override
	public void insert(OrdRepurchaseInsertDTO insertDTO) {
		OrdRepurchase entity = BeanUtil.copyProperties(insertDTO, OrdRepurchase.class);
		entity.setIssueUserId(StpUtil.getLoginIdAsInt());
		entity.setIssueTime(LocalDateTime.now());
		entity.setCloseStatus("0");
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(OrdRepurchaseUpdateDTO updateDTO) {
		OrdRepurchase entity = BeanUtil.copyProperties(updateDTO, OrdRepurchase.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public OrdRepurchaseDetailVO detail(Integer id) {
		OrdRepurchase entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, OrdRepurchaseDetailVO.class);
	}

	@Override
	public Page<OrdRepurchasePageVO> page(OrdRepurchasePageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(OrdRepurchaseDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Transactional
	@Override
	public void confirmComplete(Integer id) {
		// 查询当前记录
		OrdRepurchase entity = this.baseMapper.selectById(id);
		if (entity == null) {
			throw new RuntimeException("重新采购记录不存在");
		}

		// 检查当前状态是否为"0"（未处理）
		if (!"0".equals(entity.getCloseStatus())) {
			throw new RuntimeException("只能确认处理状态为未处理的记录");
		}

		// 更新状态为"1"（已处理），设置处理时间和处理人
		entity.setCloseStatus("1");
		entity.setCloseTime(LocalDateTime.now());
		entity.setCloseUserId(StpUtil.getLoginIdAsInt());

		this.baseMapper.updateById(entity);
	}
}
