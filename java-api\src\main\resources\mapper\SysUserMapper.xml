<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysUser">
        <id column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="real_name" property="realName" />
        <result column="status" property="status" />
        <result column="phone" property="phone" />
        <result column="phone_secondary" property="phoneSecondary" />
        <result column="remark" property="remark" />
        <result column="disable" property="disable" />
        <result column="disable_time" property="disableTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, role_id, username, password, real_name, status, phone, phone_secondary, remark, disable, disable_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_user.SysUserPageVO">
		SELECT
			user_id, role_id, username, password, real_name, status, phone, phone_secondary, remark, disable, disable_time
		FROM
			sys_user AS t1
		<where>
        	1=1
	        <if test="userId != null and userId != ''">
	           	AND t1.user_id = #{userId}
            </if>
	        <if test="roleId != null and roleId != ''">
	           	AND t1.role_id = #{roleId}
            </if>
	        <if test="username != null and username != ''">
	           	AND t1.username like concat('%', #{username}, '%')
            </if>
	        <if test="realName != null and realName != ''">
	           	AND t1.real_name like concat('%', #{realName}, '%')
            </if>
	        <if test="status != null and status != ''">
	           	AND t1.status = #{status}
            </if>
	        <if test="phone != null and phone != ''">
	           	AND t1.phone like concat('%', #{phone}, '%')
            </if>
	        <if test="phoneSecondary != null and phoneSecondary != ''">
	           	AND t1.phone_secondary like concat('%', #{phoneSecondary}, '%')
            </if>
	        <if test="remark != null and remark != ''">
	           	AND t1.remark = #{remark}
            </if>
	        <if test="disable != null">
	           	AND t1.disable = #{disable}
            </if>
	        <if test="disableTime != null">
	           	AND t1.disable_time = #{disableTime}
            </if>
	        <if test="excludeRoleId != null">
	           	AND t1.role_id != #{excludeRoleId}
            </if>
        </where>
    </select>

</mapper>
