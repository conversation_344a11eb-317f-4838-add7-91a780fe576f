package com.my.crossborder.forest.erp990.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 * 用于表示订单在电商平台中的各种处理状态
 * 
 * <AUTHOR>
 * @date 2025
 */
@Getter
@AllArgsConstructor
public enum OrderStatesEnum {
    
    /** 待出货 */
    PENDING_SHIPMENT("001", "待出货"),
    
    /** 已出货 */
    SHIPPED("002", "已出货"),
    
    /** 不成立 */
    INVALID("003", "不成立"),   // 意思是已同意取消订单
    
    /** 不成立-申请取消订单 */
    INVALID_CANCEL_REQUESTED("004", "不成立-申请取消订单"),
    
    /** 已完成 */
    COMPLETED("005", "已完成"),
    
    /** 退货退款 */
    RETURNED_REFUNDED("006", "退货退款"),
    
    /** 尚未付款 */
    UNPAID("007", "尚未付款"),
    
    /** 未知 */
    UNKNOWN("999", "未知");
    
    /** 状态代码 */
    private final String code;
    
    /** 状态描述 */
    private final String description;
    
    /**
     * 根据代码获取枚举
     * @param code 状态代码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static OrderStatesEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OrderStatesEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 