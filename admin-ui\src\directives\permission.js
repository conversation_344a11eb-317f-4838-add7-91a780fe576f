import store from '../vuex/store'

/**
 * 权限检查函数
 * @param {HTMLElement} el - DOM元素
 * @param {Object} binding - 指令绑定对象
 */
function checkPermission(el, binding) {
    const { value } = binding
    const permissions = store.getters.permissions

    // console.log("检查权限", value, "当前权限列表", permissions)

    if (value && value instanceof Array) {
        if (value.length > 0) {
            const hasPermission = permissions.some(permission => {
                return value.includes(permission)
            })

            // console.log("权限检查结果", hasPermission ? "有权限" : "无权限")

            if (!hasPermission) {
                el.disabled = true
                el.classList.add('is-disabled')
                el.title = '无权限操作'
                console.log("无权限操作 => ", value.join(','))
            }
        }
    } else {
        throw new Error(`需要指定权限，如 v-permission="['user:add']"`)
    }
}

export default {
    inserted(el, binding) {
        checkPermission(el, binding)
    },
    update(el, binding) {
        checkPermission(el, binding)
    }
}
