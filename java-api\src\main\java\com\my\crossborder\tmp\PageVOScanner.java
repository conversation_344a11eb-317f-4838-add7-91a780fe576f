package com.my.crossborder.tmp;

import java.io.File;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import org.assertj.core.util.Lists;

/**
 * PageVO扫描工具类，用于检查PageVO类是否包含id字段
 */
public class PageVOScanner {

    private static final String BASE_PACKAGE = "com.my.crossborder.controller.vo";

    public static void main(String[] args) {
        scanAndPrintPageVOClasses();
    }

    /**
     * 扫描并打印所有PageVO类及其id字段信息
     */
    public static void scanAndPrintPageVOClasses() {
        try {
            List<Class<?>> pageVOClasses = findClasses(BASE_PACKAGE, "PageVO");
            
            System.out.println("扫描结果: " + BASE_PACKAGE + " 包及子包下的PageVO类");
            System.out.println("============================================");
            
            List<String> prefixList = Lists.newArrayList("Eqm","Ins","Rsk","Pol");
            for (Class<?> clazz : pageVOClasses) {
            	String prefix = clazz.getSimpleName().subSequence(0, 3).toString();
            	if (!prefixList.contains(prefix)) {
            		continue;
            	}
                boolean hasIdField = checkIdField(clazz);
                System.out.print("类名: " + clazz.getName() + "\t");
                System.out.println("包含id字段: " + (hasIdField ? "是" : "否 =========>"));
            }
            
            System.out.println("扫描完成，共找到 " + pageVOClasses.size() + " 个PageVO类");
        } catch (Exception e) {
            System.err.println("扫描过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查类是否包含id字段
     * @param clazz 要检查的类
     * @return 是否包含id字段
     */
    private static boolean checkIdField(Class<?> clazz) {
        try {
            // 检查当前类及其父类的所有字段
            Class<?> currentClass = clazz;
            while (currentClass != null) {
                for (Field field : currentClass.getDeclaredFields()) {
                    if ("id".equals(field.getName())) {
                        return true;
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
            return false;
        } catch (Exception e) {
            System.err.println("检查类 " + clazz.getName() + " 的字段时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 递归查找指定包下所有包含特定名称的类
     * @param packageName 包名
     * @param classNameContains 类名包含的字符串
     * @return 找到的类列表
     * @throws Exception
     */
    private static List<Class<?>> findClasses(String packageName, String classNameContains) throws Exception {
        List<Class<?>> classes = new ArrayList<>();
        String path = packageName.replace('.', '/');
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        URL resource = classLoader.getResource(path);
        
        if (resource == null) {
            throw new IllegalArgumentException("包 " + packageName + " 未找到");
        }
        
        File directory = new File(resource.getFile());
        if (!directory.exists()) {
            throw new IllegalArgumentException("目录 " + directory.getAbsolutePath() + " 不存在");
        }
        
        findClassesInDirectory(packageName, directory, classNameContains, classes);
        return classes;
    }

    /**
     * 递归查找目录中的类
     */
    private static void findClassesInDirectory(String packageName, File directory, 
                                             String classNameContains, List<Class<?>> classes) throws ClassNotFoundException {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归处理子目录
                findClassesInDirectory(packageName + "." + file.getName(), 
                                       file, classNameContains, classes);
            } else if (file.getName().endsWith(".class")) {
                // 处理类文件
                String className = packageName + '.' + file.getName().substring(0, file.getName().length() - 6);
                if (className.contains(classNameContains)) {
                    Class<?> clazz = Class.forName(className);
                    classes.add(clazz);
                }
            }
        }
    }
}