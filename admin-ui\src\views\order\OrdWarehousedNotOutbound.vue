<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">订单管理</el-breadcrumb-item>
      <el-breadcrumb-item>货物已入库未出库</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容容器 -->
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="店铺">
          <el-select v-model="formInline.shopIds" placeholder="请选择店铺" clearable multiple style="width: 180px;">
            <el-option v-for="shop in shopList" :key="shop.id" :label="shop.shopName" :value="shop.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="formInline.orderSn" placeholder="请输入订单号" clearable style="width: 180px;"></el-input>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="formInline.orderStates" placeholder="订单状态" clearable style="width: 120px;">
            <el-option v-for="item in orderStatusList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table ref="table" :data="tableData" border style="width: 100%;" v-loading="loading" @expand-change="handleExpandChange">
        <!-- 展开列 -->
        <el-table-column type="expand">
          <template slot-scope="scope">
            <div class="order-items-section">
              <el-table :data="scope.row.orderItems" border style="width: 100%; ">
                <!-- <el-table-column type="index" label="序号" width="60" align="center"></el-table-column> -->
                <el-table-column label="产品图片" width="150" align="center">
                  <template slot-scope="item">
                    <img :src="item.row.itemImage || '/static/img/default-product.png'"
                      style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;" @error="handleImageError" />
                  </template>
                </el-table-column>
                <el-table-column prop="itemName" label="产品名称" min-width="200" show-overflow-tooltip>
                  <template slot-scope="item">
                    <span class="item-name clickable-item-name"
                          @click="copyItemName(item.row.itemName)"
                          :title="'点击复制商品名称: ' + item.row.itemName">
                      {{ item.row.itemName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="itemModelName" label="规格" min-width="150" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="amount" label="数量" width="90" align="center">
                </el-table-column>
                <el-table-column prop="itemPrice" label="单价" width="100" align="center"></el-table-column>
                <el-table-column prop="expressNo" label="快递编号" min-width="150" align="center">
                  <template slot-scope="item">
                    <span v-if="item.row.expressNo"
                          class="express-no clickable-express"
                          @click="copyExpressNo(item.row.expressNo)"
                          :title="'点击复制快递号: ' + item.row.expressNo">
                      {{ item.row.expressNo }}
                    </span>
                    <span v-else class="no-express">未填写</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="shopName" label="店铺名称" width="130" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="orderSn" label="订单号" width="180" align="center">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold; cursor: pointer;"
                  @click="copyOrderSn(scope.row.orderSn)"
                  :title="'点击复制订单号'">
              {{ scope.row.orderSn }}
            </span>
          </template>
        </el-table-column>
        <dict-table-column prop="orderStates" label="订单状态" category-id="ERP_ORDER_STATUS" width="110" align="center">
        </dict-table-column>
        <el-table-column prop="createTime" label="下单时间" width="180" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="timePassed" label="库内停留时长" width="180" align="center">
          <template slot-scope="scope">
            <span :class="getTimeDurationClass(scope.row.orderItems)">
              {{ calculateTimeDuration(scope.row.orderItems) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="订单金额" width="120" align="center">
          <template slot-scope="scope">
            <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.totalPrice }}{{ scope.row.currency }}</span>
          </template>
        </el-table-column>
        <!-- 使用NoteColumn组件 -->
        <NoteColumn :current-scene="currentScene" />
        <el-table-column prop="userName" label="操作" min-width="120" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="openNotesDrawer(scope.row)" icon="el-icon-tickets">备注</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-table-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>
    </div>

    <!-- 使用EditNoteDialog组件 -->
    <EditNoteDialog
      :visible.sync="noteDialogVisible"
      :orderSn="currentOrderSn"
      :scene="currentScene"
      sceneName="货物已入库未出库"
      @saved="handleNoteSaved" />

    <!-- 备注抽屉 -->
    <el-drawer
      title="订单备注"
      :visible.sync="notesDrawerVisible"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose">
      <div style="padding: 20px;">
        <OrderNotesDrawer
          :notes="currentOrderNotes"
          :orderSn="currentOrderSn"
          :currentScene="currentScene"
          :showDebug="false"
          @note-updated="handleNoteUpdated" />
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { warehousedNotOutbound, getOrderStates } from '../../api/ErpOrder'
import { getAllEnabledShops } from '../../api/SysShop'
import Pagination from '../../components/Pagination'
import NoteColumn from '../../components/NoteColumn'
import noteDrawerMixin from '../../mixins/noteDrawerMixin'
import copyMixin from '../../mixins/copyMixin'

export default {
  name: 'OrdWarehousedNotOutbound',
  mixins: [copyMixin, noteDrawerMixin],
  components: {
    Pagination,
    NoteColumn
  },
  data() {
    return {
      // 搜索表单数据
      formInline: {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        orderStates: ''
      },
      // 表格数据
      tableData: [],
      // 店铺列表
      shopList: [],
      // 订单状态列表
      orderStatusList: [],
      // 分页数据
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      currentScene: '04'  // 货物已入库未出库
    }
  },
  created() {
    // 检查URL参数中是否有orderSn
    if (this.$route.query.orderSn) {
      this.formInline.orderSn = this.$route.query.orderSn;
    }
    this.getPageData()
    this.loadShops()
    this.loadOrderStates()
  },
  methods: {
    // 获取分页数据
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = {
          current: this.formInline.current,
          size: this.formInline.size,
          shopIds: this.formInline.shopIds && this.formInline.shopIds.length > 0 ? this.formInline.shopIds.map(id => String(id)) : undefined,
          orderSn: this.formInline.orderSn || undefined,
          orderStates: this.formInline.orderStates || undefined
        }
      }

      warehousedNotOutbound(parameter)
        .then(res => {
          this.loading = false
          this.tableData = res.data.records
          this.pageParam.currentPage = res.data.current
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
        .catch(err => {
          this.loading = false
          this.$message.error('获取数据失败：' + err.message)
        })
    },

    // 加载店铺数据
    loadShops() {
      getAllEnabledShops()
        .then(res => {
          if (res.success && res.data && res.data.records) {
            this.shopList = res.data.records
          }
        })
        .catch(err => {
          console.error('加载店铺数据失败：', err)
        })
    },

    // 加载订单状态数据
    loadOrderStates() {
      getOrderStates()
        .then(res => {
          if (res.success && res.data) {
            this.orderStatusList = res.data
          }
        })
        .catch(err => {
          console.error('加载订单状态数据失败：', err)
        })
    },

    // 分页回调
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },

    // 搜索
    onSearch() {
      this.formInline.current = 1
      this.getPageData()
    },

    // 重置
    onReset() {
      this.formInline = {
        current: 1,
        size: 10,
        shopIds: [],
        orderSn: '',
        orderStates: ''
      }
      this.getPageData()
    },

    // 表格展开变化事件
    handleExpandChange(row, expandedRows) {
      console.log('展开行变化：', row, expandedRows)
    },

    // 切换行展开状态
    toggleRowExpansion(row) {
      this.$refs.table ? this.$refs.table.toggleRowExpansion(row) : null
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        'WAIT_BUYER_CONFIRM_GOODS': 'warning',
        'WAIT_SELLER_SEND_GOODS': 'info',
        'TRADE_BUYER_SIGNED': 'success',
        'TRADE_CLOSED': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/img/default-product.png'
    },

    // 计算库内停留时长
    calculateTimeDuration(orderItems) {
      if (!orderItems || orderItems.length === 0) return '-'

      // 找到所有有效的入库时间
      const putInTimes = orderItems
        .filter(item => item.putInTime)
        .map(item => new Date(item.putInTime))

      // 如果没有有效的入库时间，返回"-"
      if (putInTimes.length === 0) return '-'

      // 找到最小的入库时间
      const minPutInTime = new Date(Math.min(...putInTimes))
      const now = new Date()
      const diffInMs = now - minPutInTime

      // 转换为小时
      const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60))

      if (diffInHours < 24) {
        return `${diffInHours}小时`
      } else {
        const days = Math.floor(diffInHours / 24)
        const remainingHours = diffInHours % 24
        return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`
      }
    },

    // 获取时长样式类
    getTimeDurationClass(orderItems) {
      if (!orderItems || orderItems.length === 0) return ''

      // 找到所有有效的入库时间
      const putInTimes = orderItems
        .filter(item => item.putInTime)
        .map(item => new Date(item.putInTime))

      // 如果没有有效的入库时间，返回默认样式
      if (putInTimes.length === 0) return ''

      // 找到最小的入库时间
      const minPutInTime = new Date(Math.min(...putInTimes))
      const now = new Date()
      const diffInMs = now - minPutInTime
      const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60))

      if (diffInHours >= 72) return 'duration-critical'  // 72小时以上：紧急
      if (diffInHours >= 48) return 'duration-warning'   // 48-72小时：警告
      if (diffInHours >= 24) return 'duration-attention' // 24-48小时：注意
      return 'duration-normal'                           // 24小时内：正常
    }
  }
}
</script>

<style scoped>
.content-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.empty-table-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

/* 订单项展开区域样式 */
.order-items-section {
  background-color: #f8f9fa;
  /* padding: 15px; */
  border-radius: 6px;
  /* margin: 10px; */
}

.order-items-section h4 {
  color: #409EFF;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.price {
  color: #E6A23C;
  font-weight: bold;
}

.express-no {
  color: #67C23A;
  font-weight: bold;
  background-color: #F0F9FF;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #67C23A;
}

.no-express {
  color: #909399;
  font-style: italic;
}

.clickable-express {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-express:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* 未处理时长样式 */
.duration-normal {
  color: #67C23A;
  font-weight: bold;
}

.duration-attention {
  color: #E6A23C;
  font-weight: bold;
}

.duration-warning {
  color: #F56C6C;
  font-weight: bold;
}

.duration-critical {
  color: #F56C6C;
  font-weight: bold;
  background-color: #FEF0F0;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #F56C6C;
}

/* 商品名称复制样式 */
.clickable-item-name {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-item-name:hover {
  opacity: 0.8;
  transform: scale(1.02);
}
</style>
