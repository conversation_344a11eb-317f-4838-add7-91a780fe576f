-- 数据库迁移脚本：将 ord_purchase_item 表的 create_user_id 字段重命名为 purchase_user_id
-- 执行日期：2025-07-14
-- 说明：将采购订单明细表的创建人ID字段重命名为采购人ID，更准确地反映字段含义

-- 1. 重命名字段
ALTER TABLE `ord_purchase_item` 
CHANGE COLUMN `create_user_id` `purchase_user_id` int(11) DEFAULT NULL COMMENT '采购人id';

-- 2. 删除旧索引
DROP INDEX `idx_create_user_id` ON `ord_purchase_item`;

-- 3. 创建新索引
CREATE INDEX `idx_purchase_user_id` ON `ord_purchase_item` (`purchase_user_id`);

-- 4. 更新复合索引
DROP INDEX `idx_ord_purchase_item_purchase_date_user` ON `ord_purchase_item`;
CREATE INDEX `idx_ord_purchase_item_purchase_date_user` ON `ord_purchase_item` (`purchase_date`, `purchase_user_id`);

-- 5. 验证修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ord_purchase_item' 
    AND COLUMN_NAME = 'purchase_user_id';

-- 6. 验证索引
SHOW INDEX FROM `ord_purchase_item` WHERE Key_name LIKE '%purchase_user%';

-- 执行完成提示
SELECT '字段重命名完成：create_user_id -> purchase_user_id' as message;
