package com.my.crossborder.forest.erp990.vo;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor @AllArgsConstructor @Builder @Data  @EqualsAndHashCode(callSuper=false)
public class CaptchaimageVO 
					extends Erp990VO {

    private static final String PREFIX = "data:image/jpeg;base64,";

    
	@JSONField(name = "captchaOnOff", ordinal = 1)
    String captchaOnOff;

    @JSONField(name = "img", ordinal = 3)
    String img;

    @J<PERSON><PERSON>ield(name = "uuid", ordinal = 5)
    String uuid;
    
    
    public String getImgBase64() {
    	return PREFIX + this.img;
    }

}