package com.my.crossborder.enums;

/**
 * 理赔状态枚举
 * CLAIM_STATUS
 */
public enum ClaimStatusEnum {
    
    /**
     * 待赔付
     */
    PENDING("0", "待赔付"),
    
    /**
     * 已赔付
     */
    COMPLETED("1", "已赔付");
    
    private final String code;
    private final String desc;
    
    ClaimStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static ClaimStatusEnum getByCode(String code) {
        for (ClaimStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     */
    public static String getDescByCode(String code) {
        ClaimStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : null;
    }
}
