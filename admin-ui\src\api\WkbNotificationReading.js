import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 分页查询我的通知公告
export const notificationReadingPage = (params) => { return reqGet("/wkb-notification-reading/page", params) };

// 查询所有我的未读公告
export const notificationReadingAllUnread = (params) => { return reqGet("/wkb-notification-reading/all-unread", params) };

// 获取我的通知公告详情
export const notificationReadingDetail = (id) => { return reqGet("/wkb-notification-reading/" + id) };

// 删除我的通知公告
export const notificationReadingDelete = (params) => { return reqDelete("/wkb-notification-reading", params) };
