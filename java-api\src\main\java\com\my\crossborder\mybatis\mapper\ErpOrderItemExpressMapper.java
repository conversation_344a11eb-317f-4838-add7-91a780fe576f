package com.my.crossborder.mybatis.mapper;

import com.my.crossborder.controller.dto.erp_order_item_express.ErpOrderItemExpressPageDTO;
import com.my.crossborder.controller.vo.erp_order_item_express.ErpOrderItemExpressPageVO;
import com.my.crossborder.mybatis.entity.ErpOrderItemExpress;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 订单项_快递信息 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface ErpOrderItemExpressMapper extends BaseMapper<ErpOrderItemExpress> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<ErpOrderItemExpressPageVO> page(ErpOrderItemExpressPageDTO pageDTO);
	
}
