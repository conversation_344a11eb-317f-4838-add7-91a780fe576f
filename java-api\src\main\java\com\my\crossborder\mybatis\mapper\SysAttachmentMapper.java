package com.my.crossborder.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_attachment.SysAttachmentPageDTO;
import com.my.crossborder.controller.vo.sys_attachment.SysAttachmentPageVO;
import com.my.crossborder.mybatis.entity.SysAttachment;

/**
 * 附件表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface SysAttachmentMapper extends BaseMapper<SysAttachment> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysAttachmentPageVO> page(SysAttachmentPageDTO pageDTO);
	
}
