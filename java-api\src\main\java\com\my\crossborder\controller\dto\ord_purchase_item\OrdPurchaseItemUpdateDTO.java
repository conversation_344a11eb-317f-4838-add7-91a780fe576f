package com.my.crossborder.controller.dto.ord_purchase_item;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_采购订单明细表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseItemUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单明细id
     */
	@NotBlank(message="orderItemId不能为空")
    private String orderItemId;

    /**
     * 采购途径 字典PURCHASE_CHANNEL
     */
	@NotBlank(message="purchaseChannel不能为空")
    private String purchaseChannel;

    /**
     * 采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 快递单号
     */
    private String expressNo;



}
