package com.my.crossborder.controller;


import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.crossborder.controller.dto.sys_role.SysRoleDeleteDTO;
import com.my.crossborder.controller.dto.sys_role.SysRoleInsertDTO;
import com.my.crossborder.controller.dto.sys_role.SysRolePageDTO;
import com.my.crossborder.controller.dto.sys_role.SysRoleUpdateDTO;
import com.my.crossborder.controller.vo.StdResp;
import com.my.crossborder.controller.vo.sys_role.SysRoleDetailVO;
import com.my.crossborder.controller.vo.sys_role.SysRolePageVO;
import com.my.crossborder.service.SysRoleService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 系统角色表 
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/api/sys-role")
@RequiredArgsConstructor
public class SysRoleController {

    private final SysRoleService sysRoleService;

    /**
    * 新增
    */
    @SaCheckPermission("sys-role:insert")
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysRoleInsertDTO insertDTO) {
    	this.sysRoleService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @SaCheckPermission("sys-role:update")
    @PutMapping("")
    public StdResp<?> update(@Valid @RequestBody SysRoleUpdateDTO updateDTO) {
    	this.sysRoleService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @SaCheckPermission("sys-role:view")
    @GetMapping("/{id}")
    public StdResp<SysRoleDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.sysRoleService.detail(id));
    }
	
    /**
     * 分页
     */
    @SaCheckPermission("sys-role:view")
    @GetMapping(value = "page")
    public StdResp<Page<SysRolePageVO>> page(SysRolePageDTO pageDTO) {
        Page<SysRolePageVO> page = this.sysRoleService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @SaCheckPermission("sys-role:delete")
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysRoleDeleteDTO deleteDTO) {
    	this.sysRoleService.delete(deleteDTO);
		return StdResp.success();
    }
    
}