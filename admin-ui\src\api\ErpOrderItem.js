import { reqDelete, reqGet, reqPost, reqPut } from './axiosFun'

/**
 * 分页查询订单项
 * @param {Object} params 查询参数
 */
export function erpOrderItemPage(params) {
  return reqGet('/erp-order-item/page', params)
}

/**
 * 根据ID查询订单项详情
 * @param {String} id 订单项ID
 */
export function erpOrderItemDetail(id) {
  return reqGet(`/erp-order-item/${id}`)
}

/**
 * 新增订单项
 * @param {Object} data 订单项数据
 */
export function insertErpOrderItem(data) {
  return reqPost('/erp-order-item', data)
}

/**
 * 修改订单项
 * @param {Object} data 订单项数据
 */
export function updateErpOrderItem(data) {
  return reqPut('/erp-order-item', data)
}

/**
 * 删除订单项
 * @param {Object} data 删除参数
 */
export function deleteErpOrderItem(data) {
  return reqDelete('/erp-order-item', data)
}
