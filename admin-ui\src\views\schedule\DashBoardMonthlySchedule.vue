<template>
  <div class="monthly-schedule-container">
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><i class="el-icon-loading"></i></el-icon>
      <span>加载中...</span>
    </div>

    <div v-else-if="scheduleList.length === 0" class="empty-container">
      <i class="el-icon-calendar"></i>
      <p>本月暂无排班</p>
    </div>

    <div v-else class="schedule-content">
      <!-- 统计信息 -->
      <div class="schedule-stats">
        <div class="stat-item">
          <span class="stat-number">{{ scheduleList.length }}</span>
          <span class="stat-label">排班天数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ uniqueShopsCount }}</span>
          <span class="stat-label">涉及店铺</span>
        </div>
      </div>

      <!-- 排班列表 -->
      <div class="schedule-list">
        <div
          v-for="item in scheduleList"
          :key="item.date"
          class="schedule-item">
          <div class="schedule-date">
            <div class="date-main">{{ formatDateMain(item.date) }}</div>
            <div class="date-weekday">{{ getWeekday(item.date) }}</div>
          </div>
          <div class="schedule-shops">
            <div
              v-for="shop in item.shops"
              :key="shop.shopId"
              class="shop-tag">
              {{ shop.shopName }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { monthShift } from '../../api/SysShift'

export default {
  name: 'DashBoardMonthlySchedule',
  props: {
    userId: {
      type: [String, Number],
      required: true
    },
    year: {
      type: Number,
      default: () => new Date().getFullYear()
    },
    month: {
      type: Number,
      default: () => new Date().getMonth() + 1
    }
  },
  data() {
    return {
      loading: false,
      scheduleData: {}
    }
  },
  computed: {
    scheduleList() {
      const list = []

      // 将scheduleData转换为排序后的列表
      Object.keys(this.scheduleData).forEach(dateStr => {
        const shops = this.scheduleData[dateStr]
        if (shops && shops.length > 0) {
          list.push({
            date: dateStr,
            shops: shops
          })
        }
      })

      // 按日期倒序排序（大的日期在最上面）
      return list.sort((a, b) => new Date(b.date) - new Date(a.date))
    },

    uniqueShopsCount() {
      const allShops = new Set()
      this.scheduleList.forEach(item => {
        item.shops.forEach(shop => {
          allShops.add(shop.shopName)
        })
      })
      return allShops.size
    }
  },
  watch: {
    userId: {
      immediate: true,
      handler() {
        if (this.userId) {
          this.loadScheduleData()
        }
      }
    }
  },
  methods: {
    async loadScheduleData() {
      if (!this.userId) return

      this.loading = true
      try {
        const response = await monthShift({
          userId: this.userId,
          year: this.year,
          month: this.month
        })

        if (response && response.success && response.data) {
          this.scheduleData = response.data.scheduleData || {}
        } else {
          this.scheduleData = {}
        }
      } catch (error) {
        console.error('加载排班数据失败:', error)
        this.$message.error('加载排班数据失败')
        this.scheduleData = {}
      } finally {
        this.loading = false
      }
    },

    formatDateMain(dateStr) {
      const date = new Date(dateStr)
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    },

    getWeekday(dateStr) {
      const date = new Date(dateStr)
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      return weekdays[date.getDay()]
    },

    // 外部调用的刷新方法
    refresh() {
      this.loadScheduleData()
    }
  }
}
</script>

<style scoped>
.monthly-schedule-container {
  max-height: 500px;
  overflow-y: auto;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #999;
}

.loading-container .el-icon {
  font-size: 32px;
  margin-bottom: 15px;
  color: #409EFF;
}

.empty-container i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #C0C4CC;
}

/* 统计信息 */
.schedule-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 排班列表 */
.schedule-list {
  space-y: 12px;
}

.schedule-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.schedule-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.schedule-date {
  flex: 0 0 100px;
  text-align: center;
  border-right: 1px solid #eee;
  padding-right: 20px;
  margin-right: 20px;
}

.date-main {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.date-weekday {
  font-size: 12px;
  color: #999;
}

.schedule-shops {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.shop-tag {
  display: inline-block;
  padding: 4px 12px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 16px;
  font-size: 12px;
  border: 1px solid #bbdefb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schedule-stats {
    gap: 20px;
    padding: 15px;
  }

  .schedule-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .schedule-date {
    flex: none;
    border-right: none;
    border-bottom: 1px solid #eee;
    padding-right: 0;
    padding-bottom: 15px;
    margin-right: 0;
    margin-bottom: 15px;
    width: 100%;
    text-align: left;
  }

  .schedule-shops {
    width: 100%;
  }
}
</style>
