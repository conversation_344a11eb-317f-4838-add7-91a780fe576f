package com.my.crossborder.controller.dto.ord_purchase_centralized_order;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.ord_purchase_centralized_order.OrdPurchaseCentralizedOrderPageVO;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_集中采购订单
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class OrdPurchaseCentralizedOrderPageDTO 
						extends PageDTO<OrdPurchaseCentralizedOrderPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批量采购id
     */
    private Integer purchaseId;

    /**
     * 订单号
     */
    private String orderSn;

}
