package com.my.crossborder.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationDeleteDTO;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationInsertDTO;
import com.my.crossborder.controller.dto.wkb_notification.WkbNotificationPageDTO;
import com.my.crossborder.controller.vo.wkb_notification.WkbNotificationDetailVO;
import com.my.crossborder.controller.vo.wkb_notification.WkbNotificationPageVO;
import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.mybatis.entity.WkbNotification;
import com.my.crossborder.mybatis.mapper.WkbNotificationMapper;
import com.my.crossborder.service.SysUserService;
import com.my.crossborder.service.WkbNotificationReadingService;
import com.my.crossborder.service.WkbNotificationService;
import com.my.crossborder.util.ColumnLambda;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 通知表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
@RequiredArgsConstructor
public class WkbNotificationServiceImpl extends ServiceImpl<WkbNotificationMapper, WkbNotification> implements WkbNotificationService {

	private final WkbNotificationReadingService wkbNotificationReadingService;
	private final SysUserService sysUserService;
	

	@Transactional
	@Override
	public void insert(WkbNotificationInsertDTO insertDTO) {
		WkbNotification entity = BeanUtil.copyProperties(insertDTO, WkbNotification.class);
		entity.setPublishUserId(StpUtil.getLoginIdAsInt());
		entity.setPublishTime(LocalDateTime.now());
		
		// 设置接收人角色ID列表，使用逗号分隔
		List<Integer> roleIdList = insertDTO.getRoleIdList();
		if (CollectionUtil.isNotEmpty(roleIdList)) {
			entity.setReceiveRoleIds(StrUtil.join(",", roleIdList));
		}
		
		this.save(entity);
		
		// 添加未读公告
		Integer notificationId = entity.getId();
		this.wkbNotificationReadingService.insertNotRead(notificationId, roleIdList);
	}

//	@Transactional
//	@Override
//	public void update(WkbNotificationUpdateDTO updateDTO) {
//		WkbNotification entity = BeanUtil.copyProperties(updateDTO, WkbNotification.class);
//		this.baseMapper.updateById(entity);
//	}

	@Override
	public WkbNotificationDetailVO detail(Integer id) {
		WkbNotification entity = this.baseMapper.selectById(id);
		WkbNotificationDetailVO vo = BeanUtil.copyProperties(entity, WkbNotificationDetailVO.class);
		this.wkbNotificationReadingService.markRead(id);
		// 返回作者姓名
		SysUser sysUser = this.sysUserService.getById(vo.getPublishUserId());
		vo.setPublishUserName(sysUser.getRealName());
		return vo;
	}

	@Override
	public Page<WkbNotificationPageVO> page(WkbNotificationPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<WkbNotification>().columnsToString(WkbNotification::getId)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(WkbNotificationDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
		// 同时删除已读表
		this.wkbNotificationReadingService.deleteByNotificatinId(deleteDTO.getIdList());
	}	
}
