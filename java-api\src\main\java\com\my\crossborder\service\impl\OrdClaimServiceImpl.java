package com.my.crossborder.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimDeleteDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimInsertDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimPageDTO;
import com.my.crossborder.controller.dto.ord_claim.OrdClaimUpdateDTO;
import com.my.crossborder.controller.vo.ord_claim.OrdClaimDetailVO;
import com.my.crossborder.controller.vo.ord_claim.OrdClaimPageVO;
import com.my.crossborder.controller.vo.wkb_note.WkbNoteDetailVO;
import com.my.crossborder.enums.ClaimStatusEnum;
import com.my.crossborder.mybatis.entity.OrdClaim;
import com.my.crossborder.mybatis.mapper.OrdClaimMapper;
import com.my.crossborder.mybatis.mapper.WkbNoteMapper;
import com.my.crossborder.service.OrdClaimService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;

/**
 * 物流理赔 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
@RequiredArgsConstructor
public class OrdClaimServiceImpl extends ServiceImpl<OrdClaimMapper, OrdClaim> implements OrdClaimService {

	private final WkbNoteMapper wkbNoteMapper;


	@Transactional
	@Override
	public void insert(OrdClaimInsertDTO insertDTO) {
		OrdClaim entity = BeanUtil.copyProperties(insertDTO, OrdClaim.class);
		// 自动设置录入人和录入时间
		entity.setIssueUserId(StpUtil.getLoginIdAsInt());
		entity.setIssueTime(LocalDateTime.now());
		// 自动设置更新时间
		entity.setUpdateTime(LocalDateTime.now());
		// 设置理赔进度默认值
		if (entity.getClaimStatus() == null) {
			entity.setClaimStatus(ClaimStatusEnum.PENDING.getCode());
		}
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(OrdClaimUpdateDTO updateDTO) {
		OrdClaim entity = BeanUtil.copyProperties(updateDTO, OrdClaim.class);
		// 自动设置更新时间
		entity.setUpdateTime(LocalDateTime.now());
		this.baseMapper.updateById(entity);
	}

	@Override
	public OrdClaimDetailVO detail(Integer id) {
		OrdClaim entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, OrdClaimDetailVO.class);
	}

	@Override
	public Page<OrdClaimPageVO> page(OrdClaimPageDTO pageDTO) {
		Page<OrdClaimPageVO> page = this.baseMapper.page(pageDTO);
		this.batchSetNotes(page);
		return page;
	}

	@Transactional
	@Override
	public void delete(OrdClaimDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Transactional
	@Override
	public void confirmComplete(Integer id) {
		OrdClaim entity = new OrdClaim();
		entity.setId(id);
		entity.setClaimStatus(ClaimStatusEnum.COMPLETED.getCode()); // 设置为已完成状态
		entity.setCloseTime(LocalDateTime.now()); // 设置完成时间
		entity.setCloseUserId(StpUtil.getLoginIdAsInt()); // 设置完成人
		entity.setUpdateTime(LocalDateTime.now()); // 设置更新时间
		this.baseMapper.updateById(entity);
	}

	/**
	 * 批量设置备注信息到分页结果中（泛型版本）
	 * @param orderPage 订单分页结果
	 */
	private void batchSetNotes(Page<OrdClaimPageVO> orderPage) {
		List<OrdClaimPageVO> records = orderPage.getRecords();
		if (records == null || records.isEmpty()) {
			return;
		}

		// 收集所有订单号 
		Set<String> orderSnList = records.stream()
				.map(OrdClaimPageVO::getOrderSn)
				.collect(Collectors.toSet());
 
		// 一次性查询所有备注信息
		List<WkbNoteDetailVO> allNotes = this.wkbNoteMapper.selectByOrderSnSet(orderSnList);
 
		// 将备注信息按订单号分组
		Map<String, List<WkbNoteDetailVO>> noteMap = allNotes.stream()
				.collect(Collectors.groupingBy(WkbNoteDetailVO::getOrderSn));

		// 为每个订单设置对应的备注
		records.forEach(order -> {
			String orderSn = order.getOrderSn();
			order.setNotes(noteMap.getOrDefault(orderSn, Lists.newLinkedList()));
		});
	}
}
