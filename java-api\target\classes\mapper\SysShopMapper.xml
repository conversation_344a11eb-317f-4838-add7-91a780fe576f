<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.crossborder.mybatis.mapper.SysShopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.crossborder.mybatis.entity.SysShop">
        <id column="id" property="id" />
        <result column="shop_name" property="shopName" />
        <result column="opening_date" property="openingDate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="disable" property="disable" />
        <result column="disable_time" property="disableTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_name, opening_date, create_time, update_time, disable, disable_time
    </sql>

	<!-- 分页 -->
    <select id="page" resultType="com.my.crossborder.controller.vo.sys_shop.SysShopPageVO">
		SELECT
			t1.id,
			t1.shop_name AS shopName,
			t1.opening_date AS openingDate,
			t1.create_time AS createTime,
			t1.update_time AS updateTime,
			t1.disable,
			t1.disable_time AS disableTime,
			GROUP_CONCAT(u.real_name ORDER BY u.real_name SEPARATOR ', ') AS partnerNames,
			GROUP_CONCAT(DISTINCT sp.partner_user_id ORDER BY sp.partner_user_id SEPARATOR ',') AS partnerIdsStr
		FROM
			sys_shop AS t1
		LEFT JOIN sys_shop_partner sp ON t1.id = sp.shop_id
		LEFT JOIN sys_user u ON sp.partner_user_id = u.user_id AND u.disable = 0
		<where>
        	1=1
        	AND (t1.disable IS NULL OR t1.disable = 0)
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="shopName != null and shopName != ''">
	           	AND t1.shop_name LIKE CONCAT('%', #{shopName}, '%')
            </if>
	        <if test="openingDate != null and openingDate != ''">
	           	AND t1.opening_date = #{openingDate}
            </if>
	        <if test="partnerId != null and partnerId != ''">
	           	AND EXISTS (
	           		SELECT 1 FROM sys_shop_partner sp2
	           		WHERE sp2.shop_id = t1.id AND sp2.partner_user_id = #{partnerId}
	           	)
            </if>
	        <if test="shopIds != null and shopIds.size() > 0">
	           	AND t1.id IN
	           	<foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
	           		#{shopId}
	           	</foreach>
            </if>
        </where>
        GROUP BY t1.id, t1.shop_name, t1.opening_date, t1.create_time, t1.update_time, t1.disable, t1.disable_time
        ORDER BY t1.create_time DESC
    </select>

</mapper>
