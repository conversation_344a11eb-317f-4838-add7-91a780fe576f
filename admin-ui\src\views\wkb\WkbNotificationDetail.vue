<template>
  <div class="notification-detail-container">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right" style="margin-bottom: 20px;">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">工作台</el-breadcrumb-item>
      <el-breadcrumb-item>通知公告</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="content-wrapper">
      <!-- 左侧通知公告列表 -->
      <div class="notification-list-panel">
        <div class="panel-header">
          <h3>通知公告</h3>
            <!-- <dict-select
              category-id="NOTIFICATION_TYPE"
              placeholder="公告类型"
              style="width: 120px">
            </dict-select> -->
        </div>

        <!-- tab切换 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="未读" name="unread"></el-tab-pane>
          <!-- <el-tab-pane label="已读" name="read"></el-tab-pane> -->
          <el-tab-pane label="全部" name="all"></el-tab-pane>
        </el-tabs>

        <!-- 通知公告列表 -->
        <div class="notification-items">
          <div
            v-for="notification in filteredNotifications"
            :key="notification.id"
            :class="['notification-item', { 'active': selectedNotificationId === notification.id, 'unread': !notification.read }]"
            @click="selectNotification(notification)">
            <div class="notification-item-header">
              <span class="notification-title">{{ notification.title }}</span>
              <span class="notification-time">
                <el-tag type='warning' v-if="!notification.read"><i class="el-icon-bell"> 未读</i></el-tag></span>
            </div>
            <div class="notification-summary">{{ notification.summary }}</div>
            <div class="notification-meta">
              <el-tag :type="getNotificationTagType(notification.type)" size="mini">{{ getNotificationTypeName(notification.type) }}</el-tag>
              <span class="publish-time">{{ notification.publishTime }}</span>
            </div>
          </div>
          <div v-if="filteredNotifications.length === 0" class="empty-placeholder">
            <i class="el-icon-warning-outline" style="font-size: 32px; color: #C0C4CC; margin-bottom: 10px;"></i>
            <p>暂无数据</p>
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container" style="text-align: center;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageParams.current"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageParams.size"
            layout="total, prev, pager, next"
            :total="pageParams.total">
          </el-pagination>
        </div>
      </div>

      <!-- 右侧通知公告详情 -->
      <div class="notification-detail-panel">
        <div v-if="selectedNotification" class="detail-content">
          <div class="detail-header">
            <el-tag :type="getNotificationTagType(selectedNotification.type)" size="small">
              {{ getNotificationTypeName(selectedNotification.type) }}
            </el-tag>
            <h2>{{ selectedNotification.title }}</h2>
            <div class="detail-actions">
              <el-dropdown @command="handleCommand">
                <el-button size="small">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="delete" style="color: #F56C6C"><i class="el-icon-delete"></i> 删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>

          <div class="detail-info">
            <el-row :gutter="22">
              <el-col :span="22" style="text-align: center;">
                <span>{{ selectedNotification.publishTime }}</span>
                <span id="publishUserName">发布人：{{ selectedNotification.publishUserName }} </span>
              </el-col>
            </el-row>
          </div>

          <div class="detail-content-body">
            <h4>通知内容：</h4>
            <div class="content-text" v-html="selectedNotification.content"></div>
          </div>

          <!-- <div class="detail-receivers" v-if="selectedNotification.receivers">
            <h4>接收对象</h4>
            <el-tag
              v-for="receiver in selectedNotification.receivers"
              :key="receiver"
              style="margin-right: 8px; margin-bottom: 8px;">
              {{ receiver }}
            </el-tag>
          </div> -->

          <!-- <div class="detail-attachments" v-if="selectedNotification.attachments">
            <h4>相关附件</h4>
            <div class="attachment-list">
              <div
                v-for="attachment in selectedNotification.attachments"
                :key="attachment.name"
                class="attachment-item">
                <i class="el-icon-document"></i>
                <span>{{ attachment.name }}</span>
                <el-button type="text" size="mini" @click="downloadAttachment(attachment)">下载</el-button>
              </div>
            </div>
          </div> -->
        </div>

        <div v-else class="empty-detail">
          <i class="el-icon-bell" style="font-size: 64px; color: #C0C4CC;"></i>
          <p>请选择一个通知公告查看详情</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { notificationDetail } from '../../api/WkbNotification'
import { notificationReadingPage, notificationReadingDelete } from '../../api/WkbNotificationReading'
import { dictCategoryItems } from '../../api/SysDictItem'
import DictSelect from '../../components/DictSelect.vue'

export default {
  name: 'NotificationDetail',
  components: { DictSelect },
  data() {
    return {
      activeTab: 'all',
      selectedNotificationId: null,
      selectedNotification: null,
      notifications: [],
      notificationTypes: [], // 通知类型字典
      loading: false,
      pageParams: {
        current: 1,
        size: 10,
        total: 0
      }
    }
  },
  created() {
    this.loadNotificationTypes()
    this.loadNotifications()
  },
  computed: {
    filteredNotifications() {
      if (this.activeTab === 'unread') {
        return this.notifications.filter(notification => !notification.read)
      }
      return this.notifications
    }
  },
  methods: {
    // 加载通知类型字典
    loadNotificationTypes() {
      dictCategoryItems({ categoryId: 'NOTIFICATION_TYPE' })
        .then(res => {
          if (res.success && res.data) {
            this.notificationTypes = res.data;
          }
        })
        .catch(err => {
          console.error('获取通知类型字典失败:', err);
        });
    },
    // 获取通知类型名称
    getNotificationTypeName(type) {
      const item = this.notificationTypes.find(t => t.value === type);
      return item ? item.label : type;
    },
    // 获取通知类型标签样式
    getNotificationTagType(value) {
      if (!value) return '';
      const item = this.notificationTypes.find(item => item.value === value);
      return item ? item.color : '';
    },
    // 加载通知列表
    async loadNotifications() {
      this.loading = true
      try {
        const params = {
          current: this.pageParams.current,
          size: this.pageParams.size,
          read: this.activeTab === 'unread' ? false : undefined
        }
        const res = await notificationReadingPage(params)
        if (res.success) {
          this.notifications = res.data.records
          this.pageParams.total = res.data.total

          // 如果有ID参数，选择对应的通知
          const notificationId = this.$route.query.id
          if (notificationId) {
            const notification = this.notifications.find(n => n.id == notificationId)
            if (notification) {
              this.selectNotification(notification)
            }
          } else if (this.notifications.length > 0) {
            // 默认选择第一个通知
            this.selectNotification(this.notifications[0])
          }
        }
      } catch (error) {
        this.$message.error('加载通知列表失败：' + (error.message || '未知错误'))
      }
      this.loading = false
    },
    // 选择通知
    async selectNotification(notification) {
      this.selectedNotificationId = notification.id
      try {
        console.log('pre-detail', notification)
        // 获取通知详情
        const res = await notificationDetail(notification.notificationId)
        if (res.success) {
          this.selectedNotification = res.data
          // 如果未读，标记为已读并从缓存中移除
          if (!notification.read) {
            notification.read = true
            // 从store缓存中移除该通知
            this.$store.commit('removeNotificationFromCache', notification.notificationId)
          }
        }
      } catch (error) {
        console.log('error', error)
        this.$message.error('获取通知详情失败：' + (error.message || '未知错误'))
      }
    },
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.loadNotifications()
    },
    handleAdd() {
      this.$router.push('/dashboard')
    },
    async handleCommand(command) {
      switch (command) {
        case 'edit':
          this.$message.info('编辑通知公告')
          break
        case 'delete':
          try {
            await this.$confirm('确认删除该通知公告?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            await notificationReadingDelete({ idList: [this.selectedNotificationId] })
            this.$message.success('删除成功')
            this.loadNotifications()
          } catch (error) {
            if (error !== 'cancel') {
              this.$message.error('删除失败：' + (error.message || '未知错误'))
            }
          }
          break
        case 'mark':
          try {
            await markNotificationRead(this.selectedNotificationId)
            this.selectedNotification.read = true
            this.$message.success('已标记为已读')
          } catch (error) {
            this.$message.error('标记失败：' + (error.message || '未知错误'))
          }
          break
      }
    },
    formatTime(timeStr) {
      if (!timeStr) return ''
      return timeStr.split(' ')[1].substring(0, 5)
    },
    downloadAttachment(attachment) {
      this.$message.success(`正在下载：${attachment.name}`)
    },
    handleSizeChange(size) {
      this.pageParams.size = size
      this.loadNotifications()
    },
    handleCurrentChange(current) {
      this.pageParams.current = current
      this.loadNotifications()
    }
  }
}
</script>

<style scoped>
.notification-detail-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  text-align: center;
}

.content-wrapper {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.notification-list-panel {
  width: 400px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notification-detail-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
}

.notification-items {
  flex: 1;
  overflow-y: auto;
}

.notification-item {
  padding: 15px;
  border-bottom: 1px solid #EBEEF5;
  cursor: pointer;
  transition: background-color 0.3s;
  border-left: 3px solid transparent;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.active {
  background-color: #ecf5ff;
  border-left-color: #409EFF;
}

.notification-item.unread {
  background-color: #fef0f0;
}

.notification-item.unread .notification-title {
  font-weight: bold;
}

.notification-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.notification-title {
  font-size: 14px;
  color: #303133;
  flex: 1;
  margin-right: 10px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.notification-summary {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.publish-time {
  font-size: 12px;
  color: #909399;
}

.detail-content h2 {
  margin: 0 0 20px 0;
  color: #303133;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.detail-actions {
  display: flex;
  gap: 10px;
}

.detail-info {
  margin-bottom: 30px;
}

.info-item {
  margin-bottom: 15px;
}

.info-item .label {

  color: #606266;
  margin-right: 10px;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
}

.detail-content-body,
.detail-receivers,
.detail-attachments {
  margin-bottom: 30px;
}

.detail-content-body h4,
.detail-receivers h4,
.detail-attachments h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.content-text {
  color: #606266;
  line-height: 1.6;
}

.content-text p {
  margin-bottom: 10px;
}

.content-text h5 {
  color: #303133;
  margin: 15px 0 8px 0;
}

.content-text ul {
  margin: 10px 0;
  padding-left: 20px;
}

.attachment-list {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #EBEEF5;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-item i {
  margin-right: 10px;
  color: #909399;
}

.attachment-item span {
  flex: 1;
  color: #303133;
}

.empty-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.empty-detail p {
  margin-top: 20px;
  font-size: 16px;
}

.pagination-container {
  margin-top: 15px;
  padding: 10px 0;
  text-align: right;
}

#publishUserName {
  display: inline-block;
  margin-left: 20px;;
}
</style>
