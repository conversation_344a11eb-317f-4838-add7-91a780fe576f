import { reqGet, reqPost, reqPut, reqDelete } from './axiosFun'

/**
 * 根据订单号和场景查询工作笔记
 * @param {Object} params - 查询参数 { orderSn, scene }
 * @returns {Promise} - 返回请求Promise
 */
export function getByOrderSnAndScene(params) {
  return reqGet('/wkb-note/by-order', params)
}

/**
 * 新增工作笔记
 * @param {Object} data - 笔记数据
 * @returns {Promise} - 返回请求Promise
 */
export function insertNote(data) {
  return reqPost('/wkb-note', data)
}

/**
 * 更新工作笔记
 * @param {Object} data - 笔记数据
 * @returns {Promise} - 返回请求Promise
 */
export function updateNote(data) {
  return reqPut('/wkb-note', data)
}

/**
 * 删除工作笔记
 * @param {Object} data - { idList: [1, 2, 3] }
 * @returns {Promise} - 返回请求Promise
 */
export function deleteNote(data) {
  return reqDelete('/wkb-note', data)
}

/**
 * 分页查询工作笔记
 * @param {Object} params - 分页查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getNotePage(params) {
  return reqGet('/wkb-note/page', params)
}

/**
 * 标记笔记为已完成
 * @param {number} id - 笔记ID
 * @returns {Promise} - 返回请求Promise
 */
export function markCompleted(id) {
  return reqPut(`/wkb-note/${id}/complete`)
}
