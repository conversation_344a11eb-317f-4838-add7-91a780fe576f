package com.my.crossborder.controller.dto.sys_exchange_rate_day_range;

import java.io.Serializable;
import java.time.LocalDate;

import com.alibaba.fastjson.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.crossborder.controller.vo.sys_exchange_rate_day_range.SysExchangeRateDayRangePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_汇率日期区间
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysExchangeRateDayRangePageDTO 
						extends PageDTO<SysExchangeRateDayRangePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
    private LocalDate day;

}
