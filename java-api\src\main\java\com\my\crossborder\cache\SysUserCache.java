package com.my.crossborder.cache;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.my.crossborder.mybatis.entity.SysUser;
import com.my.crossborder.mybatis.mapper.SysUserMapper;

import lombok.RequiredArgsConstructor;

/**
 * 用户缓存
 * <AUTHOR>
 * @date 2023年4月22日
 */
@Component
@RequiredArgsConstructor
public class SysUserCache {
	
	private final SysUserMapper sysUserMapper;

	private List<SysUser> entityList;
	
	/**
	 * 初始缓存
	 */
	@PostConstruct
	public void reloadCache() {
		Wrapper<SysUser> wrapper = Wrappers.lambdaQuery(SysUser.class)
				.eq(SysUser::getDisable, Boolean.FALSE);
		List<SysUser> result = this.sysUserMapper.selectList(wrapper);
		result.forEach(t -> {
			t.setPassword(null);
		});
		this.entityList = result;
	}
	
	/**
	 * 按角色id查询
	 * @param roleId 
	 * @return
	 */
	public List<SysUser> list(List<Integer> idList) {
		return this.entityList.stream()
				.filter(t -> idList.contains(t.getUserId()))
				.sorted((t1, t2) -> t1.getUserId().compareTo(t2.getUserId()))
				.collect(Collectors.toList());
	}
	
	/**
	 * 按角色id查询
	 * @param roleId 
	 * @return
	 */
	public List<SysUser> listByRoleIdList(List<Integer> roleIdList) {
		return this.entityList.stream()
				.filter(t -> roleIdList.contains(t.getRoleId()))
				.sorted((t1, t2) -> t1.getUserId().compareTo(t2.getUserId()))
				.collect(Collectors.toList());
	}
	
    /**
     * 按角色id查询
     * @param roleId 
     * @return
     */
	public List<SysUser> listByRoleId(Integer roleId) {
		return this.listByRoleIdList(Lists.newArrayList(roleId));
	}
	
	/**
	 * 按主键查询
	 * @param userId
	 * @return
	 */
	public SysUser getById(Integer userId) {
		return this.entityList.stream()
				.filter(t -> t.getUserId().intValue() == userId.intValue())
				.findFirst()
				.orElse(null);
	}

	
}
