package com.my.crossborder.enums;

/**
 * 理赔问题类别枚举
 * CLAIM_ISSUE_TYPE
 */
public enum ClaimIssueTypeEnum {
    
    /**
     * 发错
     */
    WRONG_ITEM("1", "发错"),
    
    /**
     * 漏发
     */
    MISSING_ITEM("2", "漏发"),
    
    /**
     * 多发
     */
    EXTRA_ITEM("3", "多发");
    
    private final String code;
    private final String desc;
    
    ClaimIssueTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static ClaimIssueTypeEnum getByCode(String code) {
        for (ClaimIssueTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取描述
     */
    public static String getDescByCode(String code) {
        ClaimIssueTypeEnum type = getByCode(code);
        return type != null ? type.getDesc() : null;
    }
}
