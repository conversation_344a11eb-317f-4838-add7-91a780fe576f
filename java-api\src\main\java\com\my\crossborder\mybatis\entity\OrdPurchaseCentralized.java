package com.my.crossborder.mybatis.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 集中采购
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ord_purchase_centralized")
public class OrdPurchaseCentralized implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 采购单号
     */
    private String purchaseNumber;

    /**
     * 品名
     */
    private String productName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 采购途径 字典PURCHASE_CENTRALIZED_CHANNEL
     */
    private String purchaseChannel;

    /**
     * 采购人id
     */
    private Integer purchaseUserId;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 采购状态 字典PURCHASE_CENTRALIZED_STATUS
     */
    private String purchaseStatus;

    /**
     * 确认人id
     */
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


}
